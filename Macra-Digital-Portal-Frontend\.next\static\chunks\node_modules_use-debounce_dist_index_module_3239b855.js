(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/use-debounce/dist/index.module.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useDebounce": ()=>a,
    "useDebouncedCallback": ()=>c,
    "useThrottledCallback": ()=>o
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function c(e, u, c, i) {
    var a = this, o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0), v = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null), m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]), d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(), p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(e), w = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(!0);
    p.current = e;
    var s = "undefined" != typeof window, x = !u && 0 !== u && s;
    if ("function" != typeof e) throw new TypeError("Expected a function");
    u = +u || 0;
    var h = !!(c = c || {}).leading, y = !("trailing" in c) || !!c.trailing, F = "maxWait" in c, A = "debounceOnServer" in c && !!c.debounceOnServer, D = F ? Math.max(+c.maxWait || 0, u) : null;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        return w.current = !0, function() {
            w.current = !1;
        };
    }, []);
    var T = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        var r = function(r) {
            var n = m.current, t = d.current;
            return m.current = d.current = null, f.current = r, l.current = l.current || r, g.current = p.current.apply(t, n);
        }, n = function(r, n) {
            x && cancelAnimationFrame(v.current), v.current = x ? requestAnimationFrame(r) : setTimeout(r, n);
        }, t = function(r) {
            if (!w.current) return !1;
            var n = r - o.current;
            return !o.current || n >= u || n < 0 || F && r - f.current >= D;
        }, e = function(n) {
            return v.current = null, y && m.current ? r(n) : (m.current = d.current = null, g.current);
        }, c = function r() {
            var c = Date.now();
            if (h && l.current === f.current && T(), t(c)) return e(c);
            if (w.current) {
                var i = u - (c - o.current), a = F ? Math.min(i, D - (c - f.current)) : i;
                n(r, a);
            }
        }, T = function() {
            i && i({});
        }, W = function() {
            if (s || A) {
                var e = Date.now(), i = t(e);
                if (m.current = [].slice.call(arguments), d.current = a, o.current = e, i) {
                    if (!v.current && w.current) return f.current = o.current, n(c, u), h ? r(o.current) : g.current;
                    if (F) return n(c, u), r(o.current);
                }
                return v.current || n(c, u), g.current;
            }
        };
        return W.cancel = function() {
            v.current && (x ? cancelAnimationFrame(v.current) : clearTimeout(v.current)), f.current = 0, m.current = o.current = d.current = v.current = null;
        }, W.isPending = function() {
            return !!v.current;
        }, W.flush = function() {
            return v.current ? e(Date.now()) : g.current;
        }, W;
    }, [
        h,
        F,
        u,
        D,
        y,
        x,
        s,
        A,
        i
    ]);
    return T;
}
function i(r, n) {
    return r === n;
}
function a(n, t, a) {
    var o = a && a.equalityFn || i, f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(n), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({})[1], v = c((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(function(r) {
        f.current = r, l({});
    }, [
        l
    ]), t, a, l), m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(n);
    return o(m.current, n) || (v(n), m.current = n), [
        f.current,
        v
    ];
}
function o(r, n, t) {
    var e = void 0 === t ? {} : t, u = e.leading, i = e.trailing;
    return c(r, n, {
        maxWait: n,
        leading: void 0 === u || u,
        trailing: void 0 === i || i
    });
}
;
 //# sourceMappingURL=index.module.js.map
}),
}]);

//# sourceMappingURL=node_modules_use-debounce_dist_index_module_3239b855.js.map