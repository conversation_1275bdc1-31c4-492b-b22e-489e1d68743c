import { Repository } from 'typeorm';
import { Documents } from '../entities/documents.entity';
import { User } from '../entities/user.entity';
import { CreateDocumentDto } from '../dto/document/create-document.dto';
import { UpdateDocumentDto } from '../dto/document/update-document.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { MinioService } from 'src/common/services/minio.service';
import { ActivityNotesService } from '../services/activity-notes.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
export declare class DocumentsService {
    private documentsRepository;
    private usersRepository;
    private readonly minioService;
    private readonly activityNotesService;
    private readonly notificationHelperService;
    constructor(documentsRepository: Repository<Documents>, usersRepository: Repository<User>, minioService: MinioService, activityNotesService: ActivityNotesService, notificationHelperService: NotificationHelperService);
    private readonly paginateConfig;
    create(createDocumentDto: CreateDocumentDto, createdBy: string): Promise<Documents>;
    uploadFile(file: Express.Multer.File, createDocumentDto: CreateDocumentDto, createdBy: string): Promise<Documents>;
    findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Documents>>;
    findOne(id: string): Promise<Documents>;
    findByApplication(applicationId: string): Promise<Documents[]>;
    findByEntity(entityType: string, entityId: string): Promise<Documents[]>;
    findByDocumentType(documentType: string): Promise<Documents[]>;
    findRequiredDocuments(): Promise<Documents[]>;
    update(id: string, updateDocumentDto: UpdateDocumentDto, updatedBy: string): Promise<Documents>;
    remove(id: string): Promise<void>;
    getDocumentStats(): Promise<any>;
    getDocumentsByMimeType(mimeType: string): Promise<Documents[]>;
    getTotalFileSize(): Promise<number>;
    getFileStream(filePath: string): Promise<NodeJS.ReadableStream>;
    getFileUrl(filePath: string, expiry?: number): Promise<string>;
    approveDocument(documentId: string, approvedBy: string, comment?: string): Promise<Documents>;
    rejectDocument(documentId: string, rejectedBy: string, comment?: string): Promise<Documents>;
    deleteFile(filePath: string): Promise<void>;
    private getFolderByDocumentType;
    private getAllowedMimeTypes;
    private generateDocumentApprovalEmailTemplate;
    private generateDocumentRejectionEmailTemplate;
}
