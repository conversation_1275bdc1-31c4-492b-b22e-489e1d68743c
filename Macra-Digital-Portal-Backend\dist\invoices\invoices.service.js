"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoicesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const invoices_entity_1 = require("../entities/invoices.entity");
const applications_entity_1 = require("../entities/applications.entity");
const applicant_entity_1 = require("../entities/applicant.entity");
const license_categories_entity_1 = require("../entities/license-categories.entity");
const payment_entity_1 = require("../entities/payment.entity");
const notification_helper_service_1 = require("../notifications/notification-helper.service");
const applications_service_1 = require("../applications/applications.service");
let InvoicesService = class InvoicesService {
    constructor(invoicesRepository, applicationsRepository, applicantsRepository, licenseCategoriesRepository, paymentsRepository, notificationHelper, applicationsService) {
        this.invoicesRepository = invoicesRepository;
        this.applicationsRepository = applicationsRepository;
        this.applicantsRepository = applicantsRepository;
        this.licenseCategoriesRepository = licenseCategoriesRepository;
        this.paymentsRepository = paymentsRepository;
        this.notificationHelper = notificationHelper;
        this.applicationsService = applicationsService;
    }
    async create(createDto, userId) {
        try {
            if (!userId) {
                throw new common_1.BadRequestException('User authentication required to create invoice');
            }
            const invoiceNumber = await this.generateInvoiceNumber();
            const invoice = this.invoicesRepository.create({
                ...createDto,
                invoice_number: invoiceNumber,
                status: invoices_entity_1.InvoiceStatus.PENDING,
                issue_date: new Date(),
                due_date: createDto.due_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                created_by: userId,
            });
            const savedInvoice = await this.invoicesRepository.save(invoice);
            return savedInvoice;
        }
        catch (error) {
            throw error;
        }
    }
    async findAll(filters = {}) {
        console.log('🔍 InvoicesService.findAll called with filters:', filters);
        const query = this.invoicesRepository.createQueryBuilder('invoice')
            .leftJoinAndSelect('invoice.client', 'client')
            .leftJoinAndSelect('invoice.creator', 'creator')
            .leftJoinAndSelect('invoice.updater', 'updater');
        if (filters.status) {
            console.log('🔍 Adding status filter:', filters.status);
            query.andWhere('invoice.status = :status', { status: filters.status });
        }
        if (filters.entity_type) {
            query.andWhere('invoice.entity_type = :entity_type', { entity_type: filters.entity_type });
        }
        if (filters.entity_id) {
            query.andWhere('invoice.entity_id = :entity_id', { entity_id: filters.entity_id });
        }
        if (filters.client_id) {
            query.andWhere('invoice.client_id = :client_id', { client_id: filters.client_id });
        }
        query.orderBy('invoice.created_at', 'DESC');
        const result = await query.getMany();
        return result;
    }
    async findOne(id) {
        const invoice = await this.invoicesRepository.findOne({
            where: { invoice_id: id },
            relations: ['client', 'creator', 'updater'],
        });
        if (!invoice) {
            throw new common_1.NotFoundException(`Invoice with ID ${id} not found`);
        }
        const amountPaid = await this.getPaidAmountForInvoice(invoice.invoice_id);
        const balance = Math.max(0, Number(invoice.amount) - amountPaid);
        return { ...invoice, balance: balance };
    }
    async findByEntity(entityType, entityId) {
        return await this.invoicesRepository.find({
            where: { entity_type: entityType, entity_id: entityId },
            relations: ['client', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async update(id, updateDto, userId) {
        if (!userId) {
            throw new common_1.BadRequestException('User authentication required to update invoice');
        }
        const invoice = await this.findOne(id);
        Object.assign(invoice, updateDto);
        invoice.updated_by = userId;
        return await this.invoicesRepository.save(invoice);
    }
    async remove(id) {
        const invoice = await this.findOne(id);
        await this.invoicesRepository.softDelete(id);
    }
    async sendInvoice(id, userId) {
        if (!userId) {
            throw new common_1.BadRequestException('User authentication required to send invoice');
        }
        const invoice = await this.findOne(id);
        if (invoice.status !== invoices_entity_1.InvoiceStatus.PENDING) {
            throw new common_1.BadRequestException('Only pending invoices can be sent');
        }
        invoice.status = invoices_entity_1.InvoiceStatus.SENT;
        invoice.updated_by = userId;
        return await this.invoicesRepository.save(invoice);
    }
    async markAsPaid(id, userId) {
        if (!userId) {
            throw new common_1.BadRequestException('User authentication required to mark invoice as paid');
        }
        const invoice = await this.findOne(id);
        if (invoice.status === invoices_entity_1.InvoiceStatus.PAID) {
            throw new common_1.BadRequestException('Invoice is already marked as paid');
        }
        invoice.status = invoices_entity_1.InvoiceStatus.PAID;
        invoice.updated_by = userId;
        return await this.invoicesRepository.save(invoice);
    }
    async generateApplicationInvoice(applicationId, data, userId) {
        try {
            if (!userId) {
                throw new common_1.BadRequestException('User authentication required to generate invoice');
            }
            if (!data || typeof data !== 'object') {
                throw new common_1.BadRequestException('Invalid invoice data format');
            }
            if (!data.amount || isNaN(Number(data.amount)) || Number(data.amount) <= 0) {
                throw new common_1.BadRequestException(`Invoice amount must be greater than 0, received: ${data.amount}`);
            }
            if (!data.description || typeof data.description !== 'string' || data.description.trim().length === 0) {
                throw new common_1.BadRequestException('Invoice description is required');
            }
            const application = await this.applicationsRepository.findOne({
                where: { application_id: applicationId },
                relations: ['applicant', 'license_category'],
            });
            if (!application) {
                throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
            }
            if (!application.applicant) {
                throw new common_1.NotFoundException(`Applicant not found for application ${applicationId}`);
            }
            const existingInvoice = await this.invoicesRepository.findOne({
                where: { entity_type: 'application', entity_id: applicationId },
            });
            let invoice;
            if (existingInvoice) {
                existingInvoice.amount = data.amount;
                existingInvoice.description = data.description;
                existingInvoice.items = data.items;
                existingInvoice.updated_by = userId;
                if (existingInvoice.status === 'paid' || existingInvoice.status === 'cancelled') {
                    existingInvoice.status = invoices_entity_1.InvoiceStatus.PENDING;
                }
                invoice = await this.invoicesRepository.save(existingInvoice);
            }
            else {
                const createInvoiceDto = {
                    client_id: application.applicant.applicant_id,
                    amount: Number(data.amount),
                    entity_type: 'application',
                    entity_id: applicationId,
                    description: data.description.trim(),
                    items: data.items || [],
                };
                try {
                    invoice = await this.create(createInvoiceDto, userId);
                }
                catch (createError) {
                    throw new common_1.InternalServerErrorException(`Failed to create invoice: ${createError.message}`);
                }
            }
            try {
                if (this.applicationsService && typeof this.applicationsService.updateStatus === 'function') {
                    await this.applicationsService.updateStatus(applicationId, 'pending_payment', userId);
                }
                else {
                    console.warn(`⚠️ ApplicationsService not available, skipping status update`);
                }
            }
            catch (error) {
                console.error(`❌ Failed to update application status for ${applicationId}:`, error);
            }
            try {
                await this.sendInvoiceEmail(invoice, application);
                console.log(`✅ Invoice email sent to ${application.applicant.email}`);
            }
            catch (error) {
                console.error(`❌ Failed to send invoice email for ${invoice.invoice_id}:`, error);
            }
            return invoice;
        }
        catch (error) {
            console.error(`❌ Error generating invoice for application ${applicationId}:`, error);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Failed to generate invoice for application ${applicationId}: ${error.message}`);
        }
    }
    async generateUniqueInvoiceNumber(entityManager) {
        const repository = entityManager ? entityManager.getRepository(invoices_entity_1.Invoices) : this.invoicesRepository;
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const prefix = `INV-${year}${month}`;
        try {
            const latestInvoice = await repository
                .createQueryBuilder('invoice')
                .where('invoice.invoice_number LIKE :pattern', {
                pattern: `${prefix}-%`
            })
                .orderBy('invoice.invoice_number', 'DESC')
                .getOne();
            let nextSequence = 1;
            if (latestInvoice) {
                const parts = latestInvoice.invoice_number.split('-');
                if (parts.length === 3) {
                    const lastSequence = parseInt(parts[2], 10);
                    if (!isNaN(lastSequence)) {
                        nextSequence = lastSequence + 1;
                    }
                }
            }
            let attempts = 0;
            const maxAttempts = 100;
            while (attempts < maxAttempts) {
                const sequence = String(nextSequence).padStart(4, '0');
                const invoiceNumber = `${prefix}-${sequence}`;
                const existingInvoice = await repository.findOne({
                    where: { invoice_number: invoiceNumber }
                });
                if (!existingInvoice) {
                    console.log(`✅ Generated unique invoice number: ${invoiceNumber}`);
                    return invoiceNumber;
                }
                nextSequence++;
                attempts++;
                console.warn(`⚠️ Invoice number ${invoiceNumber} already exists, trying next sequence: ${nextSequence}`);
            }
            throw new Error(`Could not generate unique invoice number after ${maxAttempts} attempts`);
        }
        catch (error) {
            console.error('Error generating invoice number:', error);
            let attempts = 0;
            const maxAttempts = 10;
            while (attempts < maxAttempts) {
                const timestamp = Date.now().toString().slice(-6);
                const fallbackNumber = `${prefix}-${timestamp}`;
                const existingInvoice = await repository.findOne({
                    where: { invoice_number: fallbackNumber }
                });
                if (!existingInvoice) {
                    console.log(`✅ Generated fallback invoice number: ${fallbackNumber}`);
                    return fallbackNumber;
                }
                attempts++;
                await new Promise(resolve => setTimeout(resolve, 1));
            }
            const randomSuffix = Math.random().toString(36).substring(2, 6).toUpperCase();
            const finalFallback = `${prefix}-${randomSuffix}`;
            console.warn(`⚠️ Using final fallback invoice number: ${finalFallback}`);
            return finalFallback;
        }
    }
    async generateInvoiceNumber() {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const prefix = `INV-${year}${month}`;
        try {
            const latestInvoice = await this.invoicesRepository
                .createQueryBuilder('invoice')
                .where('invoice.invoice_number LIKE :pattern', {
                pattern: `${prefix}-%`
            })
                .orderBy('invoice.invoice_number', 'DESC')
                .getOne();
            let nextSequence = 1;
            if (latestInvoice) {
                const parts = latestInvoice.invoice_number.split('-');
                if (parts.length === 3) {
                    const lastSequence = parseInt(parts[2], 10);
                    if (!isNaN(lastSequence)) {
                        nextSequence = lastSequence + 1;
                    }
                }
            }
            let attempts = 0;
            const maxAttempts = 10;
            while (attempts < maxAttempts) {
                const sequence = String(nextSequence).padStart(4, '0');
                const invoiceNumber = `${prefix}-${sequence}`;
                const existingInvoice = await this.invoicesRepository.findOne({
                    where: { invoice_number: invoiceNumber }
                });
                if (!existingInvoice) {
                    return invoiceNumber;
                }
                nextSequence++;
                attempts++;
            }
            const timestamp = Date.now().toString().slice(-6);
            const fallbackNumber = `${prefix}-${timestamp}`;
            return fallbackNumber;
        }
        catch (error) {
            const timestamp = Date.now().toString().slice(-6);
            const fallbackNumber = `${prefix}-${timestamp}`;
            return fallbackNumber;
        }
    }
    async getApplicationInvoiceStatus(applicationId) {
        const invoices = await this.findByEntity('application', applicationId);
        if (invoices.length === 0) {
            return { hasInvoice: false, status: 'none' };
        }
        const latestInvoice = invoices[0];
        let status = 'pending';
        if (latestInvoice.status === invoices_entity_1.InvoiceStatus.PAID) {
            status = 'paid';
        }
        else if (latestInvoice.status === invoices_entity_1.InvoiceStatus.OVERDUE) {
            status = 'overdue';
        }
        else if (latestInvoice.status === invoices_entity_1.InvoiceStatus.SENT || latestInvoice.status === invoices_entity_1.InvoiceStatus.PENDING) {
            const dueDate = new Date(latestInvoice.due_date);
            const now = new Date();
            if (now > dueDate) {
                status = 'overdue';
                await this.update(latestInvoice.invoice_id, { status: invoices_entity_1.InvoiceStatus.OVERDUE }, latestInvoice.created_by);
                latestInvoice.status = invoices_entity_1.InvoiceStatus.OVERDUE;
            }
            else {
                status = 'pending';
            }
        }
        return {
            hasInvoice: true,
            invoice: latestInvoice,
            status
        };
    }
    async getApplicationDetailsForInvoice(applicationId) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: applicationId },
            relations: ['applicant', 'license_category'],
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
        }
        const categoryFee = application.license_category.fee || 0;
        const defaultInvoiceData = {
            amount: categoryFee,
            description: `License Application Fee - ${application.license_category.name}`,
            items: [
                {
                    item_id: `license_fee_${Date.now()}`,
                    description: `${application.license_category.name} License Fee`,
                    quantity: 1,
                    unit_price: categoryFee
                }
            ]
        };
        return {
            application,
            defaultInvoiceData
        };
    }
    async sendInvoiceEmail(invoice, application) {
        try {
            console.log(`🔧 Preparing to send invoice email for invoice ${invoice.invoice_number}`);
            if (!application) {
                console.error(`❌ Application object is null or undefined`);
                throw new Error('Application object is required');
            }
            if (!application.applicant) {
                console.error(`❌ Applicant not found for application ${application.application_id}`);
                throw new Error('Applicant not found');
            }
            if (!application.applicant.email) {
                console.error(`❌ Applicant email not found for ${application.applicant.applicant_id}`);
                throw new Error('Applicant email not found');
            }
            console.log(`📧 Sending invoice email to ${application.applicant.email}`);
            if (!invoice.invoice_number) {
                console.error(`❌ Invoice number is missing`);
                throw new Error('Invoice number is required');
            }
            if (!invoice.due_date) {
                console.error(`❌ Invoice due date is missing`);
                throw new Error('Invoice due date is required');
            }
            let dueDateStr = '';
            try {
                dueDateStr = invoice.due_date.toLocaleDateString();
            }
            catch (dateError) {
                console.warn(`⚠️ Error formatting due date, using string representation:`, dateError);
                dueDateStr = String(invoice.due_date);
            }
            try {
                await this.notificationHelper.notifyInvoiceGenerated(application.application_id, application.applicant.applicant_id, application.applicant.email, application.application_number, invoice.invoice_number, Number(invoice.amount), dueDateStr, invoice.description, invoice.created_by, application.applicant.name, application.license_category?.name || 'License');
                console.log(`✅ Invoice email sent successfully to ${application.applicant.email}`);
            }
            catch (notificationError) {
                console.error('❌ Failed to send invoice email via notification helper:', notificationError);
                throw notificationError;
            }
        }
        catch (error) {
            console.error(`❌ Error in sendInvoiceEmail:`, error);
        }
    }
    async getPaidAmountForInvoice(invoiceId) {
        const paidPayments = await this.paymentsRepository.find({
            where: {
                invoice_id: invoiceId,
                status: 'approved'
            }
        });
        return paidPayments.reduce((sum, payment) => sum + Number(payment.amount), 0);
    }
    async getInvoicesByCustomer(userId, filters) {
        try {
            const userApplications = await this.applicationsRepository.find({
                where: { created_by: userId },
                select: ['application_id'],
            });
            if (userApplications.length === 0) {
                return {
                    data: [],
                    meta: {
                        itemsPerPage: filters.limit || 10,
                        totalItems: 0,
                        currentPage: filters.page || 1,
                        totalPages: 0,
                    },
                };
            }
            const applicationIds = userApplications.map(app => app.application_id);
            console.log(`📋 Found ${applicationIds.length} applications for user ${userId}`);
            const queryBuilder = this.invoicesRepository
                .createQueryBuilder('invoice')
                .leftJoinAndSelect('invoice.client', 'client')
                .where('invoice.entity_type = :entityType', { entityType: 'application' })
                .andWhere('invoice.entity_id IN (:...applicationIds)', { applicationIds });
            if (filters.status) {
                const statuses = filters.status.split(',').map((s) => s.trim());
                queryBuilder.andWhere('invoice.status IN (:...statuses)', { statuses });
            }
            if (filters.search) {
                queryBuilder.andWhere('(invoice.invoice_number LIKE :search OR invoice.description LIKE :search)', { search: `%${filters.search}%` });
            }
            const page = filters.page || 1;
            const limit = filters.limit || 10;
            const offset = (page - 1) * limit;
            queryBuilder
                .orderBy('invoice.created_at', 'DESC')
                .skip(offset)
                .take(limit);
            const [invoices, totalItems] = await queryBuilder.getManyAndCount();
            const enhancedInvoices = await Promise.all(invoices.map(async (invoice) => {
                try {
                    const paidPayments = await this.paymentsRepository
                        .createQueryBuilder('payment')
                        .where('payment.invoice_id = :invoiceId', { invoiceId: invoice.invoice_id })
                        .orWhere('payment.invoice_number = :invoiceNumber', { invoiceNumber: invoice.invoice_number })
                        .andWhere('payment.status = :status', { status: 'approved' })
                        .getMany();
                    const totalPaid = paidPayments.reduce((sum, payment) => sum + parseFloat(payment.amount.toString()), 0);
                    const balance = Math.max(0, parseFloat(invoice.amount.toString()) - totalPaid);
                    return {
                        ...invoice,
                        paid_amount: totalPaid,
                        balance: balance,
                        payment_status: balance === 0 ? 'FULLY_PAID' : 'PENDING'
                    };
                }
                catch (error) {
                    console.error(`Error calculating payments for invoice ${invoice.invoice_id}:`, error);
                    return {
                        ...invoice,
                        paid_amount: 0,
                        balance: parseFloat(invoice.amount.toString()),
                        payment_status: 'PENDING'
                    };
                }
            }));
            return {
                data: enhancedInvoices,
                meta: {
                    itemsPerPage: limit,
                    totalItems,
                    currentPage: page,
                    totalPages: Math.ceil(totalItems / limit),
                },
            };
        }
        catch (error) {
            console.error(`❌ Error getting invoices for customer ${userId}:`, error);
            throw new common_1.InternalServerErrorException(`Failed to get customer invoices: ${error.message}`);
        }
    }
    async getInvoiceStatisticsByCustomer(userId) {
        try {
            console.log(`📊 Getting invoice statistics for customer: ${userId}`);
            const userApplications = await this.applicationsRepository.find({
                where: { created_by: userId },
                select: ['application_id'],
            });
            if (userApplications.length === 0) {
                return {
                    totalInvoices: 0,
                    pendingInvoices: 0,
                    paidInvoices: 0,
                    overdueInvoices: 0,
                    totalAmount: 0,
                    pendingAmount: 0,
                    paidAmount: 0,
                    overdueAmount: 0,
                };
            }
            const applicationIds = userApplications.map(app => app.application_id);
            const invoices = await this.invoicesRepository
                .createQueryBuilder('invoice')
                .where('invoice.entity_type = :entityType', { entityType: 'application' })
                .andWhere('invoice.entity_id IN (:...applicationIds)', { applicationIds })
                .getMany();
            const stats = {
                totalInvoices: invoices.length,
                pendingInvoices: invoices.filter(inv => inv.status === 'pending').length,
                paidInvoices: invoices.filter(inv => inv.status === 'paid').length,
                overdueInvoices: invoices.filter(inv => inv.status === 'overdue').length,
                totalAmount: invoices.reduce((sum, inv) => sum + Number(inv.amount), 0),
                pendingAmount: invoices.filter(inv => inv.status === 'pending').reduce((sum, inv) => sum + Number(inv.amount), 0),
                paidAmount: invoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + Number(inv.amount), 0),
                overdueAmount: invoices.filter(inv => inv.status === 'overdue').reduce((sum, inv) => sum + Number(inv.amount), 0),
            };
            console.log(`✅ Invoice statistics for customer ${userId}:`, stats);
            return stats;
        }
        catch (error) {
            console.error(`❌ Error getting invoice statistics for customer ${userId}:`, error);
            throw new common_1.InternalServerErrorException(`Failed to get customer invoice statistics: ${error.message}`);
        }
    }
    async getInvoiceByCustomer(invoiceId, userId) {
        try {
            console.log(`🔍 Getting invoice ${invoiceId} for customer: ${userId}`);
            const userApplications = await this.applicationsRepository.find({
                where: { created_by: userId },
                select: ['application_id'],
            });
            if (userApplications.length === 0) {
                throw new common_1.NotFoundException(`Invoice not found or not accessible`);
            }
            const applicationIds = userApplications.map(app => app.application_id);
            const invoice = await this.invoicesRepository
                .createQueryBuilder('invoice')
                .leftJoinAndSelect('invoice.client', 'client')
                .where('invoice.invoice_id = :invoiceId', { invoiceId })
                .andWhere('invoice.entity_type = :entityType', { entityType: 'application' })
                .andWhere('invoice.entity_id IN (:...applicationIds)', { applicationIds })
                .getOne();
            if (!invoice) {
                throw new common_1.NotFoundException(`Invoice not found or not accessible`);
            }
            console.log(`✅ Found invoice ${invoiceId} for customer ${userId}`);
            return invoice;
        }
        catch (error) {
            console.error(`❌ Error getting invoice ${invoiceId} for customer ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Failed to get customer invoice: ${error.message}`);
        }
    }
    async getApplicationInvoicesByCustomer(applicationId, userId) {
        try {
            console.log(`🔍 Getting invoices for application ${applicationId} by customer: ${userId}`);
            const application = await this.applicationsRepository.findOne({
                where: {
                    application_id: applicationId,
                    created_by: userId
                },
            });
            if (!application) {
                throw new common_1.NotFoundException(`Application not found or not accessible`);
            }
            const invoices = await this.invoicesRepository.find({
                where: {
                    entity_type: 'application',
                    entity_id: applicationId,
                },
                relations: ['client'],
                order: { created_at: 'DESC' },
            });
            console.log(`✅ Found ${invoices.length} invoices for application ${applicationId}`);
            return invoices;
        }
        catch (error) {
            console.error(`❌ Error getting application invoices for customer ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Failed to get application invoices: ${error.message}`);
        }
    }
    async getApplicationInvoiceStatusByCustomer(applicationId, userId) {
        try {
            console.log(`🔍 Getting invoice status for application ${applicationId} by customer: ${userId}`);
            const application = await this.applicationsRepository.findOne({
                where: {
                    application_id: applicationId,
                    created_by: userId
                },
            });
            if (!application) {
                throw new common_1.NotFoundException(`Application not found or not accessible`);
            }
            const invoices = await this.invoicesRepository.find({
                where: {
                    entity_type: 'application',
                    entity_id: applicationId,
                },
                order: { created_at: 'DESC' },
            });
            if (invoices.length === 0) {
                return { hasInvoice: false, status: 'none' };
            }
            const latestInvoice = invoices[0];
            let status = 'pending';
            if (latestInvoice.status === 'paid') {
                status = 'paid';
            }
            else if (latestInvoice.status === 'overdue') {
                status = 'overdue';
            }
            else if (latestInvoice.status === 'sent' || latestInvoice.status === 'pending') {
                const dueDate = new Date(latestInvoice.due_date);
                const now = new Date();
                if (now > dueDate) {
                    status = 'overdue';
                }
                else {
                    status = 'pending';
                }
            }
            console.log(`✅ Invoice status for application ${applicationId}: ${status}`);
            return {
                hasInvoice: true,
                invoice: latestInvoice,
                status,
            };
        }
        catch (error) {
            console.error(`❌ Error getting application invoice status for customer ${userId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Failed to get application invoice status: ${error.message}`);
        }
    }
};
exports.InvoicesService = InvoicesService;
exports.InvoicesService = InvoicesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(invoices_entity_1.Invoices)),
    __param(1, (0, typeorm_1.InjectRepository)(applications_entity_1.Applications)),
    __param(2, (0, typeorm_1.InjectRepository)(applicant_entity_1.Applicants)),
    __param(3, (0, typeorm_1.InjectRepository)(license_categories_entity_1.LicenseCategories)),
    __param(4, (0, typeorm_1.InjectRepository)(payment_entity_1.Payment)),
    __param(6, (0, common_1.Inject)((0, common_1.forwardRef)(() => applications_service_1.ApplicationsService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        notification_helper_service_1.NotificationHelperService,
        applications_service_1.ApplicationsService])
], InvoicesService);
//# sourceMappingURL=invoices.service.js.map