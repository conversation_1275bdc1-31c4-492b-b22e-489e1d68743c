# Types Migration Guide

This guide helps you migrate from the old scattered type definitions to the new centralized types structure.

## 🚀 Quick Migration Steps

### 1. Update Import Statements

**Before:**
```typescript
// Old scattered imports
import { PaginateQuery } from '@/types/user';
import { PaginatedResponse } from '@/services/userService';
import { ApiResponse } from '@/lib/customer-api';
```

**After:**
```typescript
// New centralized imports
import { PaginateQuery, PaginatedResponse, ApiResponse } from '@/types';
```

### 2. Use Generic Types Instead of Specific Ones

**Before:**
```typescript
// Service-specific response types
interface UsersResponse {
  data: User[];
  meta: { ... };
  links: { ... };
}

interface TasksResponse {
  data: Task[];
  meta: { ... };
  links: { ... };
}
```

**After:**
```typescript
// Generic reusable types
type UsersResponse = PaginatedResponse<User>;
type TasksResponse = PaginatedResponse<Task>;
```

### 3. Extend Base Entity Types

**Before:**
```typescript
interface CustomEntity {
  id: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  // ... other fields
}
```

**After:**
```typescript
interface CustomEntity extends BaseEntity {
  id: string;
  // created_at, updated_at, created_by, updated_by inherited
  // ... other fields
}
```

### 4. Use UserReference for Related Users

**Before:**
```typescript
interface MyEntity {
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  updater?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}
```

**After:**
```typescript
interface MyEntity extends BaseEntity {
  creator?: UserReference;
  updater?: UserReference;
}
```

## 📋 Common Migration Patterns

### Service Files

**Before:**
```typescript
// services/myService.ts
export interface PaginatedResponse<T> {
  data: T[];
  meta: { ... };
  links: { ... };
}

export interface PaginateQuery {
  page?: number;
  limit?: number;
  // ...
}

export interface MyEntity {
  id: string;
  created_at: string;
  updated_at: string;
  // ...
}
```

**After:**
```typescript
// services/myService.ts
import { PaginatedResponse, PaginateQuery, BaseEntity, UserReference } from '@/types';

export interface MyEntity extends BaseEntity {
  id: string;
  // created_at, updated_at inherited
  // ...
}

export type MyEntitiesResponse = PaginatedResponse<MyEntity>;
```

### Component Files

**Before:**
```typescript
// components/MyComponent.tsx
import { User } from '@/types/user';
import { PaginateQuery } from '@/types/user';
import { Task } from '@/types/task';
```

**After:**
```typescript
// components/MyComponent.tsx
import { User, PaginateQuery, Task } from '@/types';
```

### API Client Files

**Before:**
```typescript
// lib/apiClient.ts
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  // ...
}
```

**After:**
```typescript
// lib/apiClient.ts
import { ApiResponse, PaginatedResponse } from '@/types';
// Remove duplicate type definitions
```

## 🔍 Find and Replace Patterns

Use these regex patterns to quickly update your codebase:

### 1. Update PaginateQuery Imports
```regex
Find: import.*PaginateQuery.*from.*@/types/user.*
Replace: import { PaginateQuery } from '@/types';
```

### 2. Update PaginatedResponse Imports
```regex
Find: import.*PaginatedResponse.*from.*@/services/.*
Replace: import { PaginatedResponse } from '@/types';
```

### 3. Convert to Generic Response Types
```regex
Find: interface (\w+)Response \{\s*data: (\w+)\[\];\s*meta:.*\}
Replace: type $1Response = PaginatedResponse<$2>;
```

## ⚠️ Breaking Changes

### 1. LicenseType Naming Conflict
The `LicenseType` from `license.ts` conflicts with `LicenseType` from `license-type.ts`.

**Solution:**
```typescript
// Use explicit import for license type entity
import { LicenseTypeEntity } from '@/types';
// or
import { LicenseType as LicenseTypeEntity } from '@/types/license-type';
```

### 2. Removed Duplicate Interfaces
These interfaces were removed from service files (use centralized versions):
- `PaginateQuery` (use from `@/types`)
- `PaginatedResponse<T>` (use from `@/types`)
- `ApiResponse<T>` (use from `@/types`)

### 3. BaseEntity Changes
All entity interfaces should now extend `BaseEntity`:
```typescript
// Before
interface MyEntity {
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

// After
interface MyEntity extends BaseEntity {
  // created_at, updated_at, created_by, updated_by inherited
}
```

## 🧪 Testing Your Migration

### 1. Check Imports
Ensure all imports resolve correctly:
```bash
npm run type-check
```

### 2. Verify Generic Types
Test that generic types work as expected:
```typescript
const users: PaginatedResponse<User> = await userService.getUsers();
const tasks: PaginatedResponse<Task> = await taskService.getTasks();
```

### 3. Check Entity Extensions
Verify that BaseEntity extensions work:
```typescript
const entity: MyEntity = {
  id: '123',
  created_at: '2024-01-01',
  updated_at: '2024-01-01',
  // ... other fields
};
```

## 📚 New Type Categories Available

### Core Types (from `@/types`)
- `PaginatedResponse<T>` - Generic paginated response
- `PaginateQuery` - Query parameters for pagination
- `ApiResponse<T>` - Standard API response wrapper
- `BaseEntity` - Common entity fields
- `UserReference` - User reference for relations

### Entity Types
- `User`, `Task`, `Invoice`, `Payment` - Core business entities
- `Application`, `LicenseCategory` - License management
- `Department`, `Organization` - Organizational entities

### Service Types (from `@/types/service`)
- `ServiceResponse<T>` - Service operation results
- `ValidationResult` - Validation outcomes
- `FileUploadResponse` - File upload results
- `BatchResult<T>` - Batch operation results

### UI Types
- `ModalProps` - Modal component props
- `TableColumn<T>` - Table configuration
- `FormField`, `FormConfig` - Form definitions
- `ToastNotification` - Toast notifications

## 🎯 Best Practices After Migration

1. **Always import from `@/types`** unless you need specific types
2. **Use generic types** instead of creating specific ones
3. **Extend BaseEntity** for all database entities
4. **Use UserReference** for user relations
5. **Leverage service types** for cross-cutting concerns

## 🆘 Troubleshooting

### Type Not Found
If you get "Type not found" errors:
1. Check if the type is exported from `@/types/index.ts`
2. Verify the import statement syntax
3. Check for naming conflicts

### Circular Dependencies
If you encounter circular dependencies:
1. Move shared types to `@/types`
2. Use type-only imports: `import type { ... }`
3. Consider splitting large type files

### Generic Type Issues
If generic types don't work as expected:
1. Ensure you're using the correct generic syntax
2. Check that the base type is properly defined
3. Verify type constraints are met

## 📞 Need Help?

If you encounter issues during migration:
1. Check the `README.md` in the types folder
2. Look at existing migrated files for examples
3. Refer to the `formatters.example.tsx` for usage patterns
