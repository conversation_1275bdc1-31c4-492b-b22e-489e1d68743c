import { DataSource, Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
import { Role } from '../../entities/role.entity';
import { Permission } from '../../entities/permission.entity';
import { UserIdentification } from '../../entities/user-identification.entity';
import { IdentificationType } from '../../entities/identification-type.entity';
import { LicenseSeederService } from './license.seeder.service';
import { Organization } from 'src/entities/organization.entity';
import { Department } from 'src/entities/department.entity';
import { OrganizationSeederService } from './organizations.seeder';
import { DepartmentSeederService } from './departments.seeder';
import { PostalCodeSeederService } from './postal-code.seeder';
export declare class SeederService {
    private dataSource;
    private usersRepository;
    private rolesRepository;
    private permissionsRepository;
    private userIdentificationsRepository;
    private identificationTypesRepository;
    private licenseSeederService;
    private organizationSeederService;
    private departmentSeederService;
    private postalCodeSeederService;
    private organizationRepository;
    private departmentRepository;
    constructor(dataSource: DataSource, usersRepository: Repository<User>, rolesRepository: Repository<Role>, permissionsRepository: Repository<Permission>, userIdentificationsRepository: Repository<UserIdentification>, identificationTypesRepository: Repository<IdentificationType>, licenseSeederService: LicenseSeederService, organizationSeederService: OrganizationSeederService, departmentSeederService: DepartmentSeederService, postalCodeSeederService: PostalCodeSeederService, organizationRepository: Repository<Organization>, departmentRepository: Repository<Department>);
    createCustomPermission(name: string, description: string, category: string): Promise<Permission>;
    generateCrudPermissions(entityName: string, category?: string): Promise<Permission[]>;
    seedPermissions(): Promise<Permission[]>;
    seedCustomPermissions(): Promise<Permission[]>;
    seedRoles(permissions: Permission[]): Promise<Role[]>;
    seedUsers(roles: Role[]): Promise<User[]>;
    seedUserIdentifications(_users: User[]): Promise<UserIdentification[]>;
    handleForeignKeyConstraints(): Promise<void>;
    ensureDatabaseSchema(): Promise<void>;
    seedAllSafe(): Promise<void>;
    resolveConstraintConflicts(): Promise<void>;
    seedAll(): Promise<void>;
    clearAll(): Promise<void>;
}
