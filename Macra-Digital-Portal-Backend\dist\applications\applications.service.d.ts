import { Repository } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { ApplicationStatusHistory } from '../entities/application-status-history.entity';
import { User } from '../entities/user.entity';
import { CreateApplicationDto } from '../dto/application/create-application.dto';
import { UpdateApplicationDto } from '../dto/application/update-application.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { ApplicationTaskHelperService } from './application-task-helper.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { TasksService } from '../tasks/tasks.service';
import { UpdateApplicationStatusDto, ApplicationStatusTrackingResponseDto, ApplicationStatusHistoryResponseDto } from '../dto/application-status/update-application-status.dto';
import { LicensesService } from 'src/licenses/licenses.service';
import { ActivityNotesService } from '../services/activity-notes.service';
export declare class ApplicationsService {
    private applicationsRepository;
    private statusHistoryRepository;
    private usersRepository;
    private applicationTaskHelper;
    private notificationHelper;
    private tasksService;
    private licensesService;
    private activityNotesService;
    constructor(applicationsRepository: Repository<Applications>, statusHistoryRepository: Repository<ApplicationStatusHistory>, usersRepository: Repository<User>, applicationTaskHelper: ApplicationTaskHelperService, notificationHelper: NotificationHelperService, tasksService: TasksService, licensesService: LicensesService, activityNotesService: ActivityNotesService);
    private readonly paginateConfig;
    create(createApplicationDto: CreateApplicationDto, createdBy: string): Promise<Applications>;
    private generateApplicationNumber;
    findAll(query: PaginateQuery, userRoles?: string[], userId?: string): Promise<Paginated<Applications>>;
    findUserApplications(query: PaginateQuery): Promise<Paginated<Applications>>;
    findOne(id: string): Promise<Applications>;
    findByApplicant(applicantId: string): Promise<Applications[]>;
    findByStatus(status: string): Promise<Applications[]>;
    update(id: string, updateApplicationDto: UpdateApplicationDto, updatedBy: string): Promise<Applications>;
    remove(id: string): Promise<void>;
    updateStatus(id: string, status: string, updatedBy: string): Promise<Applications>;
    updateApplicationStatus(applicationId: string, updateStatusDto: UpdateApplicationStatusDto, userId: string): Promise<ApplicationStatusTrackingResponseDto>;
    getApplicationStatusTracking(applicationId: string): Promise<ApplicationStatusTrackingResponseDto>;
    getApplicationsByStatus(status: string): Promise<ApplicationStatusTrackingResponseDto[]>;
    getStatusHistory(applicationId: string): Promise<ApplicationStatusHistoryResponseDto[]>;
    private validateStatusTransition;
    private calculateStepAndProgress;
    updateProgress(id: string, currentStep: number, progressPercentage: number, updatedBy: string): Promise<Applications>;
    getApplicationStats(): Promise<any>;
    assignApplication(applicationId: string, assignedTo: string, assignedBy: string): Promise<Applications>;
    getUnassignedApplications(query: PaginateQuery): Promise<Paginated<Applications>>;
    getAssignedApplications(userId: string, query: PaginateQuery): Promise<Paginated<Applications>>;
    findAllDebug(query: PaginateQuery): Promise<Paginated<Applications>>;
}
