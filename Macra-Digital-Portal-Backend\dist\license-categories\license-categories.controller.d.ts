import { LicenseCategoriesService } from './license-categories.service';
import { CreateLicenseCategoryDto } from '../dto/license-categories/create-license-category.dto';
import { UpdateLicenseCategoryDto } from '../dto/license-categories/update-license-category.dto';
import { LicenseCategories } from '../entities/license-categories.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class LicenseCategoriesController {
    private readonly licenseCategoriesService;
    constructor(licenseCategoriesService: LicenseCategoriesService);
    findAll(query: PaginateQuery): Promise<PaginatedResult<LicenseCategories>>;
    findByLicenseType(licenseTypeId: string): Promise<LicenseCategories[]>;
    getCategoryTree(licenseTypeId: string): Promise<LicenseCategories[]>;
    getRootCategories(licenseTypeId: string): Promise<LicenseCategories[]>;
    getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategories[]>;
    getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategories[]>;
    findOne(id: string): Promise<LicenseCategories>;
    create(createLicenseCategoryDto: CreateLicenseCategoryDto, req: any): Promise<LicenseCategories>;
    update(id: string, updateLicenseCategoryDto: UpdateLicenseCategoryDto, req: any): Promise<LicenseCategories>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
