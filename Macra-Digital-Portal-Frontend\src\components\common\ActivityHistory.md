# ActivityHistory Component

A comprehensive, reusable React component for displaying activity notes and history for any entity type with advanced search and filtering capabilities.

## Overview

The `ActivityHistory` component provides a unified interface for viewing activity notes across different entity types (applications, users, organizations, etc.). It includes real-time search, advanced filtering, and automatic refresh capabilities.

## Features

- ✅ **Entity Agnostic**: Works with any entity type (application, user, organization, etc.)
- ✅ **Real-time Search**: Search across note content, creator names, types, and categories
- ✅ **Advanced Filtering**: Filter by note type, category, and priority
- ✅ **Responsive Design**: Mobile-friendly interface with proper responsive behavior
- ✅ **Loading States**: Visual feedback during data loading
- ✅ **Error Handling**: Graceful error handling with user-friendly messages
- ✅ **External Refresh**: Support for external refresh triggers
- ✅ **Customizable**: Configurable height, styling, and feature toggles
- ✅ **Dark Mode**: Full dark mode support
- ✅ **TypeScript**: Complete type safety

## Props Interface

```typescript
interface ActivityHistoryProps {
  entityType: string;                    // Required: Type of entity
  entityId: string;                      // Required: Entity identifier
  title?: string;                        // Optional: Custom title
  showSearch?: boolean;                  // Optional: Show search (default: true)
  showFilters?: boolean;                 // Optional: Show filters (default: true)
  maxHeight?: string;                    // Optional: Max height class (default: "max-h-96")
  className?: string;                    // Optional: Additional CSS classes
  onNotesChange?: (notes: ActivityNote[]) => void; // Optional: Notes change callback
  refreshTrigger?: number;               // Optional: External refresh trigger
}
```

## Usage Examples

### Basic Usage

```typescript
import ActivityHistory from '@/components/common/ActivityHistory';

const MyComponent = () => {
  return (
    <ActivityHistory
      entityType="application"
      entityId="APP-2024-001"
    />
  );
};
```

### Advanced Usage with All Features

```typescript
import ActivityHistory from '@/components/common/ActivityHistory';
import { ActivityNote } from '@/services/activityNotesService';

const MyComponent = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [notesCount, setNotesCount] = useState(0);

  const handleNotesChange = (notes: ActivityNote[]) => {
    setNotesCount(notes.length);
    console.log('Notes updated:', notes);
  };

  const triggerRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div>
      <button onClick={triggerRefresh}>Refresh History</button>
      
      <ActivityHistory
        entityType="application"
        entityId="APP-2024-001"
        title="Application Activity Log"
        showSearch={true}
        showFilters={true}
        maxHeight="max-h-64"
        onNotesChange={handleNotesChange}
        refreshTrigger={refreshTrigger}
        className="border-2 border-blue-200"
      />
      
      <p>Total Notes: {notesCount}</p>
    </div>
  );
};
```

### Integration with Modal

```typescript
import ActivityHistory from '@/components/common/ActivityHistory';

const ActivityModal = ({ entityType, entityId, isOpen, onClose }) => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleNewActivity = () => {
    // After creating new activity note
    setRefreshTrigger(prev => prev + 1);
  };

  if (!isOpen) return null;

  return (
    <div className="modal">
      <div className="modal-content">
        <h2>Activity History</h2>
        
        <ActivityHistory
          entityType={entityType}
          entityId={entityId}
          refreshTrigger={refreshTrigger}
          className="border-0"
        />
        
        <button onClick={onClose}>Close</button>
      </div>
    </div>
  );
};
```

## Supported Entity Types

The component works with any entity type that has activity notes:

- **application** - Application activity and status changes
- **user** - User account activities and changes
- **organization** - Organization profile updates and activities
- **license** - License issuance and management activities
- **invoice** - Invoice generation and payment activities
- **payment** - Payment processing and status updates
- **task** - Task assignments and completion activities

## Search Functionality

The search feature searches across multiple fields:

- **Note Content**: Full-text search in note descriptions
- **Creator Names**: Search by first name and last name of note creators
- **Note Types**: Search by note type (evaluation_comment, status_update, etc.)
- **Categories**: Search by note categories (communication, review, etc.)

### Search Examples

```typescript
// Users can search for:
"status update"     // Finds notes with "status" or "update" in content
"John Doe"         // Finds notes created by John Doe
"evaluation"       // Finds evaluation-related notes
"communication"    // Finds communication category notes
```

## Filtering Options

### Note Type Filters
- **evaluation_comment** - Comments during evaluation process
- **status_update** - Application status changes
- **system_log** - System-generated logs
- **review_note** - Review and assessment notes
- **approval_note** - Approval decisions and notes
- **rejection_note** - Rejection reasons and notes

### Category Filters
- **communication** - Email and message communications
- **review** - Review and evaluation activities
- **administrative** - Administrative actions
- **technical** - Technical operations and updates

### Priority Filters
- **normal** - Standard priority notes
- **high** - High priority notes requiring attention
- **critical** - Critical issues and urgent matters

## Component Structure

The component renders:

1. **Header Section**:
   - Title with note count
   - Refresh button
   
2. **Search and Filters** (if enabled):
   - Search input field
   - Filter dropdowns for type, category, priority
   - Clear filters button
   
3. **Content Area**:
   - Loading state with spinner
   - Empty state with helpful message
   - Notes list with rich formatting
   
4. **Note Cards**:
   - Creator avatar and information
   - Note type badges with icons
   - Timestamps and metadata
   - Priority indicators
   - Email notification badges
   - Attachment indicators

## Styling and Customization

### Height Control
```typescript
// Different height options
<ActivityHistory maxHeight="max-h-32" />   // Small
<ActivityHistory maxHeight="max-h-64" />   // Medium  
<ActivityHistory maxHeight="max-h-96" />   // Large (default)
<ActivityHistory maxHeight="max-h-screen" /> // Full screen
```

### Custom Styling
```typescript
// Add custom classes
<ActivityHistory 
  className="border-2 border-blue-500 rounded-xl shadow-lg"
  entityType="application"
  entityId="APP-001"
/>
```

### Feature Toggles
```typescript
// Minimal version without search/filters
<ActivityHistory 
  entityType="application"
  entityId="APP-001"
  showSearch={false}
  showFilters={false}
  title="Simple Activity Log"
/>
```

## Integration with ActivityNotesModal

The component is designed to replace the old activity notes display in modals:

```typescript
// Before (in ActivityNotesModal)
const [notes, setNotes] = useState([]);
const [loading, setLoading] = useState(false);
// ... complex notes loading and display logic

// After (using ActivityHistory)
<ActivityHistory
  entityType="application"
  entityId={applicationId}
  refreshTrigger={refreshTrigger}
/>
```

## Performance Considerations

- **Efficient Filtering**: Client-side filtering with useMemo for performance
- **Lazy Loading**: Only loads data when component is mounted
- **Debounced Search**: Search is optimized to prevent excessive API calls
- **Memory Management**: Proper cleanup of state and effects

## Error Handling

The component handles various error scenarios:

- **Network Errors**: Connection issues and timeouts
- **API Errors**: Server errors and invalid responses
- **Data Errors**: Malformed or missing data
- **Permission Errors**: Unauthorized access attempts

## Accessibility Features

- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **High Contrast**: Works with high contrast modes
- **Focus Management**: Clear focus indicators and logical tab order

## Browser Support

- Modern browsers (Chrome 90+, Firefox 88+, Safari 14+)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for all screen sizes

## Dependencies

- `@/services/activityNotesService` - Activity notes API service
- React hooks (useState, useEffect, useMemo)
- Tailwind CSS for styling
- RemixIcon for icons

## Migration Guide

### From ActivityNotesModal

1. **Replace Notes Display Section**:
   ```typescript
   // Remove old notes display logic
   // Add ActivityHistory component
   <ActivityHistory
     entityType="application"
     entityId={applicationId}
     refreshTrigger={refreshTrigger}
   />
   ```

2. **Update Refresh Logic**:
   ```typescript
   // Instead of calling loadNotes()
   setRefreshTrigger(prev => prev + 1);
   ```

3. **Remove Unused Code**:
   - Remove notes state management
   - Remove loading states for notes
   - Remove note display helper functions

## Best Practices

1. **Use Appropriate Entity Types**: Ensure entity type matches your data model
2. **Handle Refresh Triggers**: Increment refreshTrigger after creating new notes
3. **Customize for Context**: Adjust title, height, and features based on usage
4. **Monitor Performance**: Use onNotesChange callback for performance monitoring
5. **Provide Feedback**: Use loading states and error handling appropriately

## Future Enhancements

- **Real-time Updates**: WebSocket integration for live updates
- **Export Functionality**: Export activity history to PDF/CSV
- **Advanced Search**: Full-text search with highlighting
- **Bulk Operations**: Select and manage multiple notes
- **Custom Note Types**: Support for custom note type definitions
