"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeederModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const seeder_service_1 = require("./seeder.service");
const postal_code_seeder_1 = require("./postal-code.seeder");
const license_seeder_service_1 = require("./license.seeder.service");
const ceir_seeder_service_1 = require("./ceir.seeder.service");
const user_entity_1 = require("../../entities/user.entity");
const role_entity_1 = require("../../entities/role.entity");
const permission_entity_1 = require("../../entities/permission.entity");
const user_identification_entity_1 = require("../../entities/user-identification.entity");
const identification_type_entity_1 = require("../../entities/identification-type.entity");
const postal_code_entity_1 = require("../../entities/postal-code.entity");
const license_types_entity_1 = require("../../entities/license-types.entity");
const license_categories_entity_1 = require("../../entities/license-categories.entity");
const license_category_document_entity_1 = require("../../entities/license-category-document.entity");
const organization_entity_1 = require("../../entities/organization.entity");
const department_entity_1 = require("../../entities/department.entity");
const ceir_equipment_type_categories_entity_1 = require("../../ceir/entities/ceir-equipment-type-categories.entity");
const ceir_technical_standards_entity_1 = require("../../ceir/entities/ceir-technical-standards.entity");
const departments_seeder_1 = require("./departments.seeder");
const organizations_seeder_1 = require("./organizations.seeder");
let SeederModule = class SeederModule {
};
exports.SeederModule = SeederModule;
exports.SeederModule = SeederModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                role_entity_1.Role,
                permission_entity_1.Permission,
                user_identification_entity_1.UserIdentification,
                identification_type_entity_1.IdentificationType,
                postal_code_entity_1.PostalCode,
                license_types_entity_1.LicenseTypes,
                license_categories_entity_1.LicenseCategories,
                license_category_document_entity_1.LicenseCategoryDocument,
                organization_entity_1.Organization,
                department_entity_1.Department,
                ceir_equipment_type_categories_entity_1.CeirEquipmentTypeCategories,
                ceir_technical_standards_entity_1.CeirTechnicalStandards
            ])],
        providers: [seeder_service_1.SeederService, postal_code_seeder_1.PostalCodeSeederService, license_seeder_service_1.LicenseSeederService, departments_seeder_1.DepartmentSeederService, organizations_seeder_1.OrganizationSeederService, ceir_seeder_service_1.CeirSeederService],
        exports: [seeder_service_1.SeederService, postal_code_seeder_1.PostalCodeSeederService, license_seeder_service_1.LicenseSeederService, departments_seeder_1.DepartmentSeederService, organizations_seeder_1.OrganizationSeederService, ceir_seeder_service_1.CeirSeederService],
    })
], SeederModule);
//# sourceMappingURL=seeder.module.js.map