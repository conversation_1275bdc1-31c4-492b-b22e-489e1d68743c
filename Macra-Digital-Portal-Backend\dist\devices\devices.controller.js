"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevicesController = void 0;
const common_1 = require("@nestjs/common");
const devices_service_1 = require("./devices.service");
const create_device_dto_1 = require("../dto/devices/create-device.dto");
const update_device_dto_1 = require("../dto/devices/update-device.dto");
const batch_validate_imei_dto_1 = require("../dto/devices/batch-validate-imei.dto");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let DevicesController = class DevicesController {
    constructor(devicesService) {
        this.devicesService = devicesService;
    }
    async createDevice(dto, req) {
        return this.devicesService.createDevice(dto, req.user?.userId);
    }
    async findAllDevices() {
        return this.devicesService.findAllDevices();
    }
    async findOneDevice(id) {
        return this.devicesService.findOneDevice(id);
    }
    async findDevicesByApplication(applicationId) {
        return this.devicesService.findDevicesByApplication(applicationId);
    }
    async batchValidateImeis(dto) {
        return this.devicesService.batchValidateImeis(dto.imeis);
    }
    async updateDevice(id, dto, req) {
        return this.devicesService.updateDevice(id, dto, req.user?.userId);
    }
    async removeDevice(id) {
        await this.devicesService.removeDevice(id);
        return { message: 'Device soft deleted successfully' };
    }
};
exports.DevicesController = DevicesController;
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create Device' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Device created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Device with IMEI or serial number already exists' }),
    (0, swagger_1.ApiBody)({ type: create_device_dto_1.CreateDeviceDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'Device',
        description: 'Created a device',
    }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_device_dto_1.CreateDeviceDto, Object]),
    __metadata("design:returntype", Promise)
], DevicesController.prototype, "createDevice", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'List all Devices' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of devices' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'Device',
        description: 'Viewed all devices',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DevicesController.prototype, "findAllDevices", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a Device by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Device found' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Device not found' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'Device',
        description: 'Viewed a device by ID',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DevicesController.prototype, "findOneDevice", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Get)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Devices by Application ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Devices found' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'No devices found for application' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'Device',
        description: 'Viewed devices by application ID',
    }),
    __param(0, (0, common_1.Param)('applicationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DevicesController.prototype, "findDevicesByApplication", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Post)('imei/batch-validate'),
    (0, swagger_1.ApiOperation)({ summary: 'Batch validate multiple IMEIs' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'IMEIs validated successfully',
        type: [batch_validate_imei_dto_1.BatchValidationResult]
    }),
    (0, swagger_1.ApiBody)({ type: batch_validate_imei_dto_1.BatchValidateImeiDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'Device',
        description: 'Batch validated IMEIs',
    }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [batch_validate_imei_dto_1.BatchValidateImeiDto]),
    __metadata("design:returntype", Promise)
], DevicesController.prototype, "batchValidateImeis", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update Device' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Device updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Device with IMEI or serial number already exists' }),
    (0, swagger_1.ApiBody)({ type: update_device_dto_1.UpdateDeviceDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'Device',
        description: 'Updated a device',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_device_dto_1.UpdateDeviceDto, Object]),
    __metadata("design:returntype", Promise)
], DevicesController.prototype, "updateDevice", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete a Device' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Device soft deleted successfully' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'Device',
        description: 'Deleted a device',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DevicesController.prototype, "removeDevice", null);
exports.DevicesController = DevicesController = __decorate([
    (0, swagger_1.ApiTags)('Devices - Type Approval'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('devices'),
    __metadata("design:paramtypes", [devices_service_1.DevicesService])
], DevicesController);
//# sourceMappingURL=devices.controller.js.map