"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseCategoryDocumentsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const license_category_documents_service_1 = require("./license-category-documents.service");
const license_category_documents_controller_1 = require("./license-category-documents.controller");
const license_category_document_entity_1 = require("../entities/license-category-document.entity");
const user_entity_1 = require("../entities/user.entity");
let LicenseCategoryDocumentsModule = class LicenseCategoryDocumentsModule {
};
exports.LicenseCategoryDocumentsModule = LicenseCategoryDocumentsModule;
exports.LicenseCategoryDocumentsModule = LicenseCategoryDocumentsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([license_category_document_entity_1.LicenseCategoryDocument, user_entity_1.User])],
        controllers: [license_category_documents_controller_1.LicenseCategoryDocumentsController],
        providers: [license_category_documents_service_1.LicenseCategoryDocumentsService],
        exports: [license_category_documents_service_1.LicenseCategoryDocumentsService],
    })
], LicenseCategoryDocumentsModule);
//# sourceMappingURL=license-category-documents.module.js.map