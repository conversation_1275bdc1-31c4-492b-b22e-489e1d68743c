import { DevicesService } from './devices.service';
import { CreateDeviceDto } from 'src/dto/devices/create-device.dto';
import { UpdateDeviceDto } from 'src/dto/devices/update-device.dto';
import { BatchValidateImeiDto, BatchValidationResult } from 'src/dto/devices/batch-validate-imei.dto';
export declare class DevicesController {
    private readonly devicesService;
    constructor(devicesService: DevicesService);
    createDevice(dto: CreateDeviceDto, req: any): Promise<import("../entities").Device>;
    findAllDevices(): Promise<import("../entities").Device[]>;
    findOneDevice(id: string): Promise<import("../entities").Device>;
    findDevicesByApplication(applicationId: string): Promise<import("../entities").Device[]>;
    batchValidateImeis(dto: BatchValidateImeiDto): Promise<BatchValidationResult[]>;
    updateDevice(id: string, dto: UpdateDeviceDto, req: any): Promise<import("../entities").Device>;
    removeDevice(id: string): Promise<{
        message: string;
    }>;
}
