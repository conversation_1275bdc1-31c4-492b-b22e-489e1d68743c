"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const email_service_1 = require("./services/email.service");
const device_info_service_1 = require("./services/device-info.service");
const validation_service_1 = require("./services/validation.service");
const pdf_service_1 = require("./services/pdf.service");
const verification_service_1 = require("./services/verification.service");
const licenses_entity_1 = require("../entities/licenses.entity");
let CommonModule = class CommonModule {
};
exports.CommonModule = CommonModule;
exports.CommonModule = CommonModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([licenses_entity_1.Licenses])],
        providers: [email_service_1.EmailService, device_info_service_1.DeviceInfoService, validation_service_1.ValidationService, pdf_service_1.PDFService, verification_service_1.VerificationService],
        exports: [email_service_1.EmailService, device_info_service_1.DeviceInfoService, validation_service_1.ValidationService, pdf_service_1.PDFService, verification_service_1.VerificationService],
    })
], CommonModule);
//# sourceMappingURL=common.module.js.map