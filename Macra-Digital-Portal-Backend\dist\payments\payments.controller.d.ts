import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from '../dto/payments/create-payment.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { UpdatePaymentDto } from '../dto/payments/update-payment.dto';
export declare class PaymentsController {
    private readonly paymentsService;
    constructor(paymentsService: PaymentsService);
    create(createPaymentDto: CreatePaymentDto): Promise<import("../entities").Payment>;
    findAll(query: PaginateQuery, status?: string, paymentType?: string, dateRange?: 'last-30' | 'last-90' | 'last-year', search?: string, req?: any): Promise<PaginatedResult<any>>;
    getStatistics(req: any): Promise<{
        totalPayments: number;
        paidPayments: number;
        pendingPayments: number;
        overduePayments: number;
        totalAmount: number;
        paidAmount: number;
        pendingAmount: number;
    }>;
    findOne(id: string): Promise<any>;
    update(id: string, updatePaymentDto: UpdatePaymentDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("../entities").Payment | null;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
    markOverduePayments(): Promise<void>;
    getPaymentsByEntity(entityType: string, entityId: string, query: PaginateQuery): Promise<PaginatedResult<any>>;
    createPaymentForEntity(entityType: string, entityId: string, createPaymentDto: Omit<CreatePaymentDto, 'entity_type' | 'entity_id'>): Promise<import("../entities").Payment>;
    getCustomerPayments(status?: string, paymentType?: string, page?: number, limit?: number, search?: string, dateRange?: 'last-30' | 'last-90' | 'last-year', req?: any): Promise<any>;
    getCustomerPaymentStatistics(req: any): Promise<any>;
    getCustomerPayment(id: string, req: any): Promise<import("../entities").Payment>;
    getCustomerApplicationPayments(applicationId: string, req: any): Promise<import("../entities").Payment[]>;
    getCustomerInvoicePayments(invoiceId: string, req: any): Promise<import("../entities").Payment[]>;
    linkProofOfPayment(invoiceId: string, proofData: {
        documentId: string;
        amount?: number;
        paymentMethod?: string;
        transactionReference?: string;
        notes?: string;
        payment_id?: string;
    }, req: any): Promise<{
        success: boolean;
        message: string;
        data: any;
    }>;
}
