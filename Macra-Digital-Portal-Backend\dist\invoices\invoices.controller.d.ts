import { InvoicesService, CreateInvoiceDto, UpdateInvoiceDto, InvoiceFilters } from './invoices.service';
export declare class InvoicesController {
    private readonly invoicesService;
    constructor(invoicesService: InvoicesService);
    create(createInvoiceDto: CreateInvoiceDto, req: any): Promise<import("../entities").Invoices>;
    findAll(filters: InvoiceFilters, req?: any): Promise<import("../entities").Invoices[]>;
    findByEntity(entityType: string, entityId: string): Promise<import("../entities").Invoices[]>;
    findOne(id: string): Promise<any>;
    update(id: string, updateInvoiceDto: UpdateInvoiceDto, req: any): Promise<import("../entities").Invoices>;
    remove(id: string): Promise<void>;
    sendInvoice(id: string, req: any): Promise<import("../entities").Invoices>;
    markAsPaid(id: string, req: any): Promise<import("../entities").Invoices>;
    generateApplicationInvoice(applicationId: string, data: {
        amount: number;
        description: string;
        items?: any[];
    }, req: any): Promise<import("../entities").Invoices>;
    getApplicationInvoiceStatus(applicationId: string): Promise<{
        hasInvoice: boolean;
        invoice?: import("../entities").Invoices;
        status?: "paid" | "pending" | "overdue" | "none";
    }>;
    getApplicationDetailsForInvoice(applicationId: string): Promise<{
        application: import("../entities").Applications;
        defaultInvoiceData: {
            amount: number;
            description: string;
            items: any[];
        };
    }>;
}
