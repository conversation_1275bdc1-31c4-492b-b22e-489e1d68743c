import { ActivityNotesService } from '../services/activity-notes.service';
import { CreateActivityNoteDto, UpdateActivityNoteDto, ActivityNoteQueryDto } from '../dto/activity-notes.dto';
import { ActivityNote } from '../entities/activity-notes.entity';
export declare class ActivityNotesController {
    private readonly activityNotesService;
    constructor(activityNotesService: ActivityNotesService);
    create(createDto: CreateActivityNoteDto, req: any): Promise<ActivityNote>;
    findAll(queryDto: ActivityNoteQueryDto): Promise<ActivityNote[]>;
    findByEntity(entityType: string, entityId: string): Promise<ActivityNote[]>;
    findByEntityAndStep(entityType: string, entityId: string, step: string): Promise<ActivityNote[]>;
    findOne(id: string): Promise<ActivityNote>;
    update(id: string, updateDto: UpdateActivityNoteDto, req: any): Promise<ActivityNote>;
    archive(id: string, req: any): Promise<ActivityNote>;
    softDelete(id: string, req: any): Promise<void>;
    hardDelete(id: string, req: any): Promise<void>;
    createEvaluationComment(body: {
        applicationId: string;
        step: string;
        comment: string;
        metadata?: Record<string, any>;
    }, req: any): Promise<ActivityNote>;
    createStatusUpdate(body: {
        applicationId: string;
        statusChange: string;
        metadata?: Record<string, any>;
    }, req: any): Promise<ActivityNote>;
}
