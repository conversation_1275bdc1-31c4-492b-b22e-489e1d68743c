'use client';

import { OTPInputProps } from '@/types/user';
import React, { useState, useRef, useEffect, KeyboardEvent, ClipboardEvent } from 'react';


const OTPInput: React.FC<OTPInputProps> = ({
  length = 6,
  value,
  onChange,
  onComplete,
  disabled = false,
  autoFocus = false,
  placeholder = '',
  hasError = false,
  className = '',
  type = 'text',
  numericOnly = true,
}) => {
  const [, setActiveIndex] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // Auto-focus first input if autoFocus is true
  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  // Call onComplete when value reaches the required length
  useEffect(() => {
    if (value.length === length && onComplete) {
      onComplete(value);
    }
  }, [value, length, onComplete]);

  // Convert value string to array for individual inputs
  const valueArray = value.split('').slice(0, length);
  while (valueArray.length < length) {
    valueArray.push('');
  }

  const focusInput = (index: number) => {
    if (inputRefs.current[index]) {
      inputRefs.current[index]?.focus();
      setActiveIndex(index);
    }
  };

  const handleInputChange = (index: number, inputValue: string) => {
    // Filter input based on numericOnly setting
    let filteredValue = inputValue;
    if (numericOnly) {
      filteredValue = inputValue.replace(/\D/g, '');
    }

    // Take only the last character if multiple characters are entered
    const newChar = filteredValue.slice(-1);

    // Update the value array
    const newValueArray = [...valueArray];
    newValueArray[index] = newChar;

    // Create new value string
    const newValue = newValueArray.join('');
    onChange(newValue);

    // Move to next input if a character was entered
    if (newChar && index < length - 1) {
      focusInput(index + 1);
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    switch (e.key) {
      case 'Backspace':
        e.preventDefault();
        if (valueArray[index]) {
          // Clear current input
          const newValueArray = [...valueArray];
          newValueArray[index] = '';
          onChange(newValueArray.join(''));
        } else if (index > 0) {
          // Move to previous input and clear it
          const newValueArray = [...valueArray];
          newValueArray[index - 1] = '';
          onChange(newValueArray.join(''));
          focusInput(index - 1);
        }
        break;

      case 'Delete':
        e.preventDefault();
        if (valueArray[index]) {
          const newValueArray = [...valueArray];
          newValueArray[index] = '';
          onChange(newValueArray.join(''));
        }
        break;

      case 'ArrowLeft':
        e.preventDefault();
        if (index > 0) {
          focusInput(index - 1);
        }
        break;

      case 'ArrowRight':
        e.preventDefault();
        if (index < length - 1) {
          focusInput(index + 1);
        }
        break;

      case 'Home':
        e.preventDefault();
        focusInput(0);
        break;

      case 'End':
        e.preventDefault();
        focusInput(length - 1);
        break;

      default:
        // Allow numeric input if numericOnly is true
        if (numericOnly && !/^\d$/.test(e.key) && !['Tab'].includes(e.key)) {
          e.preventDefault();
        }
        break;
    }
  };

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    
    // Filter pasted data based on numericOnly setting
    let filteredData = pastedData;
    if (numericOnly) {
      filteredData = pastedData.replace(/\D/g, '');
    }

    // Take only the required length
    const newValue = filteredData.slice(0, length);
    onChange(newValue);

    // Focus the next empty input or the last input
    const nextIndex = Math.min(newValue.length, length - 1);
    focusInput(nextIndex);
  };

  const handleFocus = (index: number) => {
    setActiveIndex(index);
  };

  const baseInputClass = `
    w-12 h-12 text-center text-lg font-mono border rounded-md
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    transition-colors duration-200
    ${hasError 
      ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
      : 'border-gray-300 dark:border-gray-600'
    }
    ${disabled 
      ? 'bg-gray-100 dark:bg-gray-700 cursor-not-allowed opacity-50' 
      : 'bg-white dark:bg-gray-800'
    }
    text-gray-900 dark:text-white
    placeholder-gray-400 dark:placeholder-gray-500
  `.trim().replace(/\s+/g, ' ');

  return (
    <div className={`flex gap-2 justify-center ${className}`}>
      {valueArray.map((digit, index) => (
        <input
          key={index}
          ref={(el) => { inputRefs.current[index] = el; }}
          type={type}
          value={digit}
          onChange={(e) => handleInputChange(index, e.target.value)}
          onKeyDown={(e) => handleKeyDown(index, e)}
          onPaste={handlePaste}
          onFocus={() => handleFocus(index)}
          disabled={disabled}
          placeholder={placeholder}
          className={baseInputClass}
          maxLength={1}
          autoComplete="one-time-code"
          inputMode={numericOnly ? 'numeric' : 'text'}
          pattern={numericOnly ? '[0-9]*' : undefined}
        />
      ))}
    </div>
  );
};

export default OTPInput;
