{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/documentations/layout.tsx"], "sourcesContent": ["export default function DocumentationsLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return children;\r\n}\r\n"], "names": [], "mappings": ";;;AAAe,SAAS,qBAAqB,EAC3C,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}