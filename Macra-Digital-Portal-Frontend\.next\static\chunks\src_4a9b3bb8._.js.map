{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/ui/card.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className = '', ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={`rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-sm ${className}`}\r\n    {...props}\r\n  />\r\n));\r\nCard.displayName = 'Card';\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className = '', ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={`flex flex-col space-y-1.5 p-6 ${className}`}\r\n    {...props}\r\n  />\r\n));\r\nCardHeader.displayName = 'CardHeader';\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className = '', ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={`text-2xl font-semibold leading-none tracking-tight ${className}`}\r\n    {...props}\r\n  />\r\n));\r\nCardTitle.displayName = 'CardTitle';\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className = '', ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={`text-sm text-gray-500 dark:text-gray-400 ${className}`}\r\n    {...props}\r\n  />\r\n));\r\nCardDescription.displayName = 'CardDescription';\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className = '', ...props }, ref) => (\r\n  <div ref={ref} className={`p-4 pt-0 ${className}`} {...props} />\r\n));\r\nCardContent.displayName = 'CardContent';\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className = '', ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={`flex items-center p-6 pt-0 ${className}`}\r\n    {...props}\r\n  />\r\n));\r\nCardFooter.displayName = 'CardFooter';\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAG3B,QAA+B;QAA9B,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO;yBAC7B,6LAAC;QACC,KAAK;QACL,WAAW,AAAC,+HAAwI,OAAV;QACzI,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGjC,QAA+B;QAA9B,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO;yBAC7B,6LAAC;QACC,KAAK;QACL,WAAW,AAAC,iCAA0C,OAAV;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,QAA+B;QAA9B,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO;yBAC7B,6LAAC;QACC,KAAK;QACL,WAAW,AAAC,sDAA+D,OAAV;QAChE,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,QAA+B;QAA9B,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO;yBAC7B,6LAAC;QACC,KAAK;QACL,WAAW,AAAC,4CAAqD,OAAV;QACtD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGlC,QAA+B;QAA9B,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO;yBAC7B,6LAAC;QAAI,KAAK;QAAK,WAAW,AAAC,YAAqB,OAAV;QAAc,GAAG,KAAK;;;;;;;;AAE9D,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QAGjC,QAA+B;QAA9B,EAAE,YAAY,EAAE,EAAE,GAAG,OAAO;yBAC7B,6LAAC;QACC,KAAK;QACL,WAAW,AAAC,8BAAuC,OAAV;QACxC,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/ui/button.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\r\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\r\n  size?: 'default' | 'sm' | 'lg' | 'icon';\r\n}\r\n\r\nconst buttonVariants = {\r\n  variant: {\r\n    default: 'bg-red-600 text-white hover:bg-red-700',\r\n    destructive: 'bg-red-500 text-white hover:bg-red-600',\r\n    outline: 'border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-700',\r\n    secondary: 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700',\r\n    ghost: 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-900 dark:text-gray-100',\r\n    link: 'text-red-600 underline-offset-4 hover:underline',\r\n  },\r\n  size: {\r\n    default: 'h-10 px-4 py-2',\r\n    sm: 'h-9 rounded-md px-3',\r\n    lg: 'h-11 rounded-md px-8',\r\n    icon: 'h-10 w-10',\r\n  },\r\n};\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className = '', variant = 'default', size = 'default', ...props }, ref) => {\r\n    const baseClasses = 'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';\r\n    const variantClasses = buttonVariants.variant[variant];\r\n    const sizeClasses = buttonVariants.size[size];\r\n\r\n    return (\r\n      <button\r\n        className={`${baseClasses} ${variantClasses} ${sizeClasses} ${className}`}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nButton.displayName = 'Button';\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAQA,MAAM,iBAAiB;IACrB,SAAS;QACP,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IACA,MAAM;QACJ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;AACF;AAEA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAAsE;QAArE,EAAE,YAAY,EAAE,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO;IAClE,MAAM,cAAc;IACpB,MAAM,iBAAiB,eAAe,OAAO,CAAC,QAAQ;IACtD,MAAM,cAAc,eAAe,IAAI,CAAC,KAAK;IAE7C,qBACE,6LAAC;QACC,WAAW,AAAC,GAAiB,OAAf,aAAY,KAAqB,OAAlB,gBAAe,KAAkB,OAAf,aAAY,KAAa,OAAV;QAC9D,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/documentations/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\n\r\ninterface DocumentationModule {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  icon: string;\r\n  filename: string;\r\n}\r\n\r\nconst documentationModules: DocumentationModule[] = [\r\n  {\r\n    id: 'authentication',\r\n    title: 'Authentication',\r\n    description: 'Registration, Login, and Password Management',\r\n    icon: 'ri-user-line',\r\n    filename: 'authentication.md'\r\n  },\r\n  {\r\n    id: 'user-management',\r\n    title: 'User Management',\r\n    description: 'Managing user accounts and permissions (Admin only)',\r\n    icon: 'ri-team-line',\r\n    filename: 'user-management.md'\r\n  },\r\n  {\r\n    id: 'license-applications',\r\n    title: 'License Applications',\r\n    description: 'How to apply for telecommunications licenses',\r\n    icon: 'ri-file-text-line',\r\n    filename: 'license-applications.md'\r\n  },\r\n  {\r\n    id: 'document-management',\r\n    title: 'Document Management',\r\n    description: 'Uploading, viewing, and managing documents',\r\n    icon: 'ri-folder-line',\r\n    filename: 'document-management.md'\r\n  },\r\n  {\r\n    id: 'application-tracking',\r\n    title: 'Application Tracking',\r\n    description: 'Monitoring your application status and progress',\r\n    icon: 'ri-search-line',\r\n    filename: 'application-tracking.md'\r\n  },\r\n  {\r\n    id: 'payments',\r\n    title: 'Payments & Billing',\r\n    description: 'Processing payments and managing billing',\r\n    icon: 'ri-money-dollar-circle-line',\r\n    filename: 'payments.md'\r\n  },\r\n  {\r\n    id: 'notifications',\r\n    title: 'Notifications',\r\n    description: 'Managing alerts and communication preferences',\r\n    icon: 'ri-notification-line',\r\n    filename: 'notifications.md'\r\n  },\r\n  {\r\n    id: 'reports',\r\n    title: 'Reports & Analytics',\r\n    description: 'Generating reports and viewing analytics',\r\n    icon: 'ri-bar-chart-line',\r\n    filename: 'reports.md'\r\n  }\r\n];\r\n\r\nexport default function DocumentationsPage() {\r\n  const [selectedModule, setSelectedModule] = useState<string>('authentication');\r\n  const [markdownContent, setMarkdownContent] = useState<string>('');\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n  const [error, setError] = useState<string>('');\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState<boolean>(false);\r\n\r\n  // Add custom styles for line clamping\r\n  React.useEffect(() => {\r\n    const style = document.createElement('style');\r\n    style.textContent = `\r\n      .line-clamp-2 {\r\n        display: -webkit-box;\r\n        -webkit-line-clamp: 2;\r\n        -webkit-box-orient: vertical;\r\n        overflow: hidden;\r\n      }\r\n    `;\r\n    document.head.appendChild(style);\r\n    return () => {\r\n      if (document.head.contains(style)) {\r\n        document.head.removeChild(style);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadMarkdownContent(selectedModule);\r\n  }, [selectedModule]);\r\n\r\n  // Handle escape key to close mobile sidebar\r\n  useEffect(() => {\r\n    const handleEscape = (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape' && isMobileSidebarOpen) {\r\n        setIsMobileSidebarOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleEscape);\r\n    return () => document.removeEventListener('keydown', handleEscape);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  // Prevent body scroll when mobile sidebar is open\r\n  useEffect(() => {\r\n    if (isMobileSidebarOpen) {\r\n      document.body.style.overflow = 'hidden';\r\n    } else {\r\n      document.body.style.overflow = 'unset';\r\n    }\r\n\r\n    return () => {\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const loadMarkdownContent = async (moduleId: string) => {\r\n    setLoading(true);\r\n    setError('');\r\n    \r\n    try {\r\n      const module = documentationModules.find(m => m.id === moduleId);\r\n      if (!module) {\r\n        throw new Error('Module not found');\r\n      }\r\n\r\n      const response = await fetch(`/docs/${module.filename}`);\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to load documentation: ${response.statusText}`);\r\n      }\r\n      \r\n      const content = await response.text();\r\n      console.log('Loaded markdown content:', content.substring(0, 200) + '...');\r\n      setMarkdownContent(content);\r\n    } catch (err) {\r\n      console.error('Error loading markdown:', err);\r\n      setError('Failed to load documentation. Please try again later.');\r\n      setMarkdownContent('');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const selectedModuleInfo = documentationModules.find(m => m.id === selectedModule);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\r\n      {/* Header */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"max-w-full mx-auto px-4 sm:px-6 lg:px-4 py-2 lg:py-3\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-3 min-w-0 flex-1\">\r\n              <div className=\"flex-shrink-0\">\r\n                <i className=\"ri-book-open-line text-xl lg:text-2xl text-blue-600 dark:text-blue-400\"></i>\r\n              </div>\r\n              <div className=\"min-w-0 flex-1\">\r\n                <h1 className=\"text-lg lg:text-2xl font-bold text-gray-900 dark:text-white truncate\">\r\n                  MACRA Digital Portal Documentation\r\n                </h1>\r\n                <p className=\"text-xs lg:text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n                  Comprehensive guides and tutorials for using the portal\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Mobile Menu Toggle */}\r\n            <button\r\n              onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}\r\n              className=\"lg:hidden flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\r\n              aria-label=\"Toggle documentation menu\"\r\n            >\r\n              <i className={`ri-${isMobileSidebarOpen ? 'close' : 'menu'}-line text-lg`}></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"max-w-full mx-auto px-2 lg:px-4 py-2 lg:py-4 overflow-hidden relative\">\r\n        {/* Mobile Overlay */}\r\n        {isMobileSidebarOpen && (\r\n          <div\r\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\r\n            onClick={() => setIsMobileSidebarOpen(false)}\r\n          />\r\n        )}\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-2 lg:gap-4 min-h-0\">\r\n          {/* Sidebar - Module Menu */}\r\n          <div className={`lg:col-span-1 order-2 lg:order-1 ${\r\n            isMobileSidebarOpen\r\n              ? 'fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-gray-900 lg:relative lg:inset-auto lg:z-auto lg:w-auto lg:bg-transparent'\r\n              : 'hidden lg:block'\r\n          }`}>\r\n            <Card className=\"lg:sticky h-full lg:h-auto\">\r\n              <CardHeader className=\"p-0\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <CardTitle className=\"text-lg font-semibold text-gray-900 dark:text-white\">\r\n                    Documentation Modules\r\n                  </CardTitle>\r\n                  {/* Mobile Close Button */}\r\n                  <button\r\n                    onClick={() => setIsMobileSidebarOpen(false)}\r\n                    className=\"lg:hidden flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\r\n                    aria-label=\"Close menu\"\r\n                  >\r\n                    <i className=\"ri-close-line text-sm\"></i>\r\n                  </button>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className=\"p-0\">\r\n                <div className=\"max-h-[calc(100vh-200px)] lg:max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden\">\r\n                  <div className=\"space-y-1 p-2\">\r\n                    {documentationModules.map((module) => (\r\n                      <Button\r\n                        key={module.id}\r\n                        variant={selectedModule === module.id ? \"default\" : \"ghost\"}\r\n                        className={`w-full justify-start text-left h-auto p-2 mb-2 rounded-lg transition-all duration-200 ${\r\n                          selectedModule === module.id\r\n                            ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-md'\r\n                            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:shadow-sm'\r\n                        }`}\r\n                        onClick={() => {\r\n                          setSelectedModule(module.id);\r\n                          setIsMobileSidebarOpen(false);\r\n                        }}\r\n                      >\r\n                        <div className=\"flex items-start space-x-2 w-full min-w-0\">\r\n                          <i className={`${module.icon} text-lg flex-shrink-0 mt-0.5`}></i>\r\n                          <div className=\"text-left flex-1 min-w-0 overflow-hidden\">\r\n                            <div className=\"font-medium truncate\">{module.title}</div>\r\n                            <div className={`text-xs mt-1 leading-relaxed line-clamp-2 ${\r\n                              selectedModule === module.id\r\n                                ? 'text-blue-100'\r\n                                : 'text-gray-500 dark:text-gray-400'\r\n                            }`}>\r\n                              {module.description}\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </Button>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n\r\n          {/* Main Content Area */}\r\n          <div className=\"lg:col-span-3 order-1 lg:order-2 min-w-0 relative\">\r\n            {/* Mobile Module Selector Button */}\r\n            <button\r\n              onClick={() => setIsMobileSidebarOpen(true)}\r\n              className=\"lg:hidden fixed bottom-6 right-6 z-30 flex items-center space-x-2 bg-blue-600 text-white px-4 py-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors\"\r\n              aria-label=\"Open documentation menu\"\r\n            >\r\n              <i className={`${selectedModuleInfo?.icon} text-lg`}></i>\r\n              <span className=\"text-sm font-medium max-w-[120px] truncate\">\r\n                {selectedModuleInfo?.title}\r\n              </span>\r\n              <i className=\"ri-arrow-up-s-line text-lg\"></i>\r\n            </button>\r\n\r\n            <Card className=\"h-full\">\r\n              <CardHeader className=\"pb-2\">\r\n                <div className=\"flex items-center justify-between min-w-0\">\r\n                  <div className=\"flex items-center space-x-3 min-w-0 flex-1\">\r\n                    <i className={`${selectedModuleInfo?.icon} text-2xl text-blue-600 dark:text-blue-400 flex-shrink-0`}></i>\r\n                    <div className=\"min-w-0 flex-1\">\r\n                      <CardTitle className=\"text-xl font-bold text-gray-900 dark:text-white truncate\">\r\n                        {selectedModuleInfo?.title}\r\n                      </CardTitle>\r\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2\">\r\n                        {selectedModuleInfo?.description}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Mobile Menu Button in Header */}\r\n                  <button\r\n                    onClick={() => setIsMobileSidebarOpen(true)}\r\n                    className=\"lg:hidden flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors ml-3\"\r\n                    aria-label=\"Open documentation menu\"\r\n                  >\r\n                    <i className=\"ri-menu-line text-lg\"></i>\r\n                  </button>\r\n                </div>\r\n              </CardHeader>\r\n              <CardContent className=\"px-4 sm:px-4 pb-2\">\r\n                <div className=\"max-h-[600px] lg:max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden pr-2 lg:pr-2\">\r\n                  {loading && (\r\n                    <div className=\"flex items-center justify-center py-12\">\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\r\n                        <span className=\"text-gray-600 dark:text-gray-400\">Loading documentation...</span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {error && (\r\n                    <div className=\"flex items-center justify-center py-12\">\r\n                      <div className=\"text-center\">\r\n                        <i className=\"ri-error-warning-line text-4xl text-red-500 mb-4\"></i>\r\n                        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n                          Failed to Load Documentation\r\n                        </h3>\r\n                        <p className=\"text-gray-600 dark:text-gray-400 mb-4\">{error}</p>\r\n                        <Button \r\n                          onClick={() => loadMarkdownContent(selectedModule)}\r\n                          variant=\"outline\"\r\n                        >\r\n                          <i className=\"ri-refresh-line mr-2\"></i>\r\n                          Try Again\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {!loading && !error && markdownContent && (\r\n                    <div className=\"prose prose-gray dark:prose-invert max-w-none overflow-hidden\">\r\n                      <ReactMarkdown\r\n                        remarkPlugins={[remarkGfm]}\r\n                        components={{\r\n                          h1: ({ children }) => (\r\n                            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-8 pb-4 border-b-2 border-gray-200 dark:border-gray-700\">\r\n                              {children}\r\n                            </h1>\r\n                          ),\r\n                          h2: ({ children }) => (\r\n                            <h2 className=\"text-2xl font-semibold text-gray-900 dark:text-white mt-10 mb-6 pb-2 border-b border-gray-200 dark:border-gray-700\">\r\n                              {children}\r\n                            </h2>\r\n                          ),\r\n                          h3: ({ children }) => (\r\n                            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mt-8 mb-4\">\r\n                              {children}\r\n                            </h3>\r\n                          ),\r\n                          h4: ({ children }) => (\r\n                            <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mt-6 mb-3\">\r\n                              {children}\r\n                            </h4>\r\n                          ),\r\n                          p: ({ children }) => (\r\n                            <p className=\"text-gray-700 dark:text-gray-300 mb-6 leading-relaxed text-base break-words\">\r\n                              {children}\r\n                            </p>\r\n                          ),\r\n                          ul: ({ children }) => (\r\n                            <ul className=\"list-disc list-inside text-gray-700 dark:text-gray-300 mb-6 space-y-2 pl-4\">\r\n                              {children}\r\n                            </ul>\r\n                          ),\r\n                          ol: ({ children }) => (\r\n                            <ol className=\"list-decimal list-inside text-gray-700 dark:text-gray-300 mb-6 space-y-2 pl-4\">\r\n                              {children}\r\n                            </ol>\r\n                          ),\r\n                          li: ({ children }) => (\r\n                            <li className=\"text-gray-700 dark:text-gray-300 leading-relaxed\">\r\n                              {children}\r\n                            </li>\r\n                          ),\r\n                          blockquote: ({ children }) => (\r\n                            <blockquote className=\"border-l-4 border-blue-500 pl-6 py-4 bg-blue-50 dark:bg-blue-900/20 text-gray-700 dark:text-gray-300 mb-6 rounded-r-lg\">\r\n                              {children}\r\n                            </blockquote>\r\n                          ),\r\n                          code: ({ children, className }) => {\r\n                            const isInline = !className;\r\n                            if (isInline) {\r\n                              return (\r\n                                <code className=\"bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-2 py-1 rounded text-sm font-mono break-words\">\r\n                                  {children}\r\n                                </code>\r\n                              );\r\n                            }\r\n                            return (\r\n                              <code className=\"block bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-4 rounded-lg text-sm font-mono overflow-x-auto mb-6 whitespace-pre-wrap break-words\">\r\n                                {children}\r\n                              </code>\r\n                            );\r\n                          },\r\n                          strong: ({ children }) => (\r\n                            <strong className=\"font-semibold text-gray-900 dark:text-white\">\r\n                              {children}\r\n                            </strong>\r\n                          ),\r\n                          em: ({ children }) => (\r\n                            <em className=\"italic text-gray-700 dark:text-gray-300\">\r\n                              {children}\r\n                            </em>\r\n                          ),\r\n                          img: ({ src, alt }) => (\r\n                            <img\r\n                              src={src}\r\n                              alt={alt || ''}\r\n                              className=\"max-w-full h-auto rounded-lg shadow-md mb-6 border border-gray-200 dark:border-gray-700\"\r\n                              onError={(e) => {\r\n                                console.error('Image failed to load:', src);\r\n                                e.currentTarget.style.display = 'none';\r\n                              }}\r\n                            />\r\n                          ),\r\n                        }}\r\n                      >\r\n                        {markdownContent}\r\n                      </ReactMarkdown>\r\n                    </div>\r\n                  )}\r\n\r\n                  {!loading && !error && !markdownContent && (\r\n                    <div className=\"flex items-center justify-center py-12\">\r\n                      <div className=\"text-center\">\r\n                        <i className=\"ri-file-text-line text-4xl text-gray-400 mb-4\"></i>\r\n                        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n                          Documentation Coming Soon\r\n                        </h3>\r\n                        <p className=\"text-gray-600 dark:text-gray-400\">\r\n                          This documentation module is currently being prepared.\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAgBA,MAAM,uBAA8C;IAClD;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;IACZ;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAExE,sCAAsC;IACtC,6JAAA,CAAA,UAAK,CAAC,SAAS;wCAAC;YACd,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,WAAW,GAAI;YAQrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B;gDAAO;oBACL,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ;wBACjC,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC5B;gBACF;;QACF;uCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,oBAAoB;QACtB;uCAAG;QAAC;KAAe;IAEnB,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;6DAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,YAAY,qBAAqB;wBACjD,uBAAuB;oBACzB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;gDAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;uCAAG;QAAC;KAAoB;IAExB,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,qBAAqB;gBACvB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;gDAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;uCAAG;QAAC;KAAoB;IAExB,MAAM,sBAAsB,OAAO;QACjC,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,SAAS,qBAAqB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACvD,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,SAAwB,OAAhB,OAAO,QAAQ;YACrD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,AAAC,iCAAoD,OAApB,SAAS,UAAU;YACtE;YAEA,MAAM,UAAU,MAAM,SAAS,IAAI;YACnC,QAAQ,GAAG,CAAC,4BAA4B,QAAQ,SAAS,CAAC,GAAG,OAAO;YACpE,mBAAmB;QACrB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;YACT,mBAAmB;QACrB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,qBAAqB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEnE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuE;;;;;;0DAGrF,6LAAC;gDAAE,WAAU;0DAA2D;;;;;;;;;;;;;;;;;;0CAO5E,6LAAC;gCACC,SAAS,IAAM,uBAAuB,CAAC;gCACvC,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCAAE,WAAW,AAAC,MAA4C,OAAvC,sBAAsB,UAAU,QAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMnE,6LAAC;gBAAI,WAAU;;oBAEZ,qCACC,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,uBAAuB;;;;;;kCAI1C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAW,AAAC,oCAIhB,OAHC,sBACI,+HACA;0CAEJ,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAsD;;;;;;kEAI3E,6LAAC;wDACC,SAAS,IAAM,uBAAuB;wDACtC,WAAU;wDACV,cAAW;kEAEX,cAAA,6LAAC;4DAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAInB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACZ,qBAAqB,GAAG,CAAC,CAAC,uBACzB,6LAAC,qIAAA,CAAA,SAAM;4DAEL,SAAS,mBAAmB,OAAO,EAAE,GAAG,YAAY;4DACpD,WAAW,AAAC,yFAIX,OAHC,mBAAmB,OAAO,EAAE,GACxB,uDACA;4DAEN,SAAS;gEACP,kBAAkB,OAAO,EAAE;gEAC3B,uBAAuB;4DACzB;sEAEA,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAW,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;;;;;;kFAC7B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;0FAAwB,OAAO,KAAK;;;;;;0FACnD,6LAAC;gFAAI,WAAW,AAAC,6CAIhB,OAHC,mBAAmB,OAAO,EAAE,GACxB,kBACA;0FAEH,OAAO,WAAW;;;;;;;;;;;;;;;;;;2DArBpB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAkC5B,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCACC,SAAS,IAAM,uBAAuB;wCACtC,WAAU;wCACV,cAAW;;0DAEX,6LAAC;gDAAE,WAAW,AAAC,GAA2B,OAAzB,+BAAA,yCAAA,mBAAoB,IAAI,EAAC;;;;;;0DAC1C,6LAAC;gDAAK,WAAU;0DACb,+BAAA,yCAAA,mBAAoB,KAAK;;;;;;0DAE5B,6LAAC;gDAAE,WAAU;;;;;;;;;;;;kDAGf,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAW,AAAC,GAA2B,OAAzB,+BAAA,yCAAA,mBAAoB,IAAI,EAAC;;;;;;8EAC1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;sFAClB,+BAAA,yCAAA,mBAAoB,KAAK;;;;;;sFAE5B,6LAAC;4EAAE,WAAU;sFACV,+BAAA,yCAAA,mBAAoB,WAAW;;;;;;;;;;;;;;;;;;sEAMtC,6LAAC;4DACC,SAAS,IAAM,uBAAuB;4DACtC,WAAU;4DACV,cAAW;sEAEX,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAInB,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,yBACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;;;;;;wDAKxD,uBACC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;;;;;;kFACb,6LAAC;wEAAG,WAAU;kFAAyD;;;;;;kFAGvE,6LAAC;wEAAE,WAAU;kFAAyC;;;;;;kFACtD,6LAAC,qIAAA,CAAA,SAAM;wEACL,SAAS,IAAM,oBAAoB;wEACnC,SAAQ;;0FAER,6LAAC;gFAAE,WAAU;;;;;;4EAA2B;;;;;;;;;;;;;;;;;;wDAO/C,CAAC,WAAW,CAAC,SAAS,iCACrB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;gEACZ,eAAe;oEAAC,gJAAA,CAAA,UAAS;iEAAC;gEAC1B,YAAY;oEACV,IAAI;4EAAC,EAAE,QAAQ,EAAE;6FACf,6LAAC;4EAAG,WAAU;sFACX;;;;;;;oEAGL,IAAI;4EAAC,EAAE,QAAQ,EAAE;6FACf,6LAAC;4EAAG,WAAU;sFACX;;;;;;;oEAGL,IAAI;4EAAC,EAAE,QAAQ,EAAE;6FACf,6LAAC;4EAAG,WAAU;sFACX;;;;;;;oEAGL,IAAI;4EAAC,EAAE,QAAQ,EAAE;6FACf,6LAAC;4EAAG,WAAU;sFACX;;;;;;;oEAGL,GAAG;4EAAC,EAAE,QAAQ,EAAE;6FACd,6LAAC;4EAAE,WAAU;sFACV;;;;;;;oEAGL,IAAI;4EAAC,EAAE,QAAQ,EAAE;6FACf,6LAAC;4EAAG,WAAU;sFACX;;;;;;;oEAGL,IAAI;4EAAC,EAAE,QAAQ,EAAE;6FACf,6LAAC;4EAAG,WAAU;sFACX;;;;;;;oEAGL,IAAI;4EAAC,EAAE,QAAQ,EAAE;6FACf,6LAAC;4EAAG,WAAU;sFACX;;;;;;;oEAGL,YAAY;4EAAC,EAAE,QAAQ,EAAE;6FACvB,6LAAC;4EAAW,WAAU;sFACnB;;;;;;;oEAGL,MAAM;4EAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;wEAC5B,MAAM,WAAW,CAAC;wEAClB,IAAI,UAAU;4EACZ,qBACE,6LAAC;gFAAK,WAAU;0FACb;;;;;;wEAGP;wEACA,qBACE,6LAAC;4EAAK,WAAU;sFACb;;;;;;oEAGP;oEACA,QAAQ;4EAAC,EAAE,QAAQ,EAAE;6FACnB,6LAAC;4EAAO,WAAU;sFACf;;;;;;;oEAGL,IAAI;4EAAC,EAAE,QAAQ,EAAE;6FACf,6LAAC;4EAAG,WAAU;sFACX;;;;;;;oEAGL,KAAK;4EAAC,EAAE,GAAG,EAAE,GAAG,EAAE;6FAChB,6LAAC;4EACC,KAAK;4EACL,KAAK,OAAO;4EACZ,WAAU;4EACV,SAAS,CAAC;gFACR,QAAQ,KAAK,CAAC,yBAAyB;gFACvC,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;4EAClC;;;;;;;gEAGN;0EAEC;;;;;;;;;;;wDAKN,CAAC,WAAW,CAAC,SAAS,CAAC,iCACtB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;;;;;;kFACb,6LAAC;wEAAG,WAAU;kFAAyD;;;;;;kFAGvE,6LAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcxE;GAlXwB;KAAA", "debugId": null}}]}