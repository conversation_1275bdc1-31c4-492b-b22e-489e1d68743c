{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,KAOjB;QAPiB,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU,GAPiB;;IAQnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,AAAC,GAAgB,OAAd,YAAW,KAAyB,OAAtB,oBAAmB,KAAgD,OAA7C,YAAY,SAAS,kBAAkB,IAAG,KAAa,OAAV;IAEzG,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,CAAA,iBAAA,2BAAA,KAAM,UAAU,IAAG,AAAC,KAAoB,OAAhB,KAAK,UAAU,IAAK;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["import { LicenseCategory } from \".\";\r\nimport { User } from \"./user\";\r\n\r\nexport interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  validity?: number;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  categories?: LicenseCategory[];\r\n  iconColor?: string;\r\n  icon?: string;\r\n  iconBg?: string;\r\n}\r\n\r\nexport interface StatusAction {\r\n  status: ApplicationStatus;\r\n  label: string;\r\n  icon: string;\r\n  color: string;\r\n  hoverColor: string;\r\n  roles?: string[],\r\n  description: string;\r\n  confirmMessage?: string;\r\n}\r\n\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application extends Record<string, unknown> {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: string;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: Applicant;\r\n  license_category?:LicenseCategory;\r\n  assignee?:User;\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  PENDING = 'pending',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  PENDING_PAYMENT = 'pending_payment',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n  PASS_EVALUATION = 'pass_evaluation',\r\n  WAITING_FOR_APPROVAL = 'waiting_for_approval',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface License extends Record<string, unknown> {\r\n  license_id: string;\r\n  license_number: string;\r\n  description?: string;\r\n  application_id: string;\r\n  applicant_id: string;\r\n  license_type_id: string;\r\n  status: LicenseStatus;\r\n  issue_date: string;\r\n  expiry_date: string;\r\n  issued_by: string;\r\n  code?: string;\r\n  conditions?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  application: Application;\r\n  issuer?:User;\r\n}\r\n\r\nexport enum LicenseStatus {\r\n  ACTIVE = 'active',\r\n  EXPIRED = 'expired',\r\n  SUSPENDED = 'suspended',\r\n  REVOKED = 'revoked',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAkEO,IAAA,AAAK,2CAAA;;;;;;;;;;;;WAAA;;AAyCL,IAAA,AAAK,uCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\nimport { AppNotification, NotificationSummary } from '@/types/notification';\r\n\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications/my-notifications?${queryParams.toString()}`;\r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response).data;\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.length || 0,\r\n        unread_count: data.length || 0,\r\n        notifications: Array.isArray(data) ? data : []\r\n      };\r\n    } catch (error: any) {\r\n      // Convert params to string format for URLSearchParams\r\n      const stringParams: Record<string, string> = {};\r\n      if (params) {\r\n        if (params.page) stringParams.page = params.page.toString();\r\n        if (params.limit) stringParams.limit = params.limit.toString();\r\n        if (params.type) stringParams.type = params.type;\r\n        if (params.status) stringParams.status = params.status;\r\n      }\r\n\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications/my-notifications?${new URLSearchParams(stringParams).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/mark-read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read (implemented client-side by marking each notification individually)\r\n  async markAllAsRead(): Promise<void> {\r\n    // This is handled by the useNotifications hook by calling markAsRead for each unread notification\r\n    // We don't need a separate backend endpoint for this\r\n    throw new Error('markAllAsRead should be handled by useNotifications hook');\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Send activity note notification (creates notification which auto-creates activity note)\r\n  async sendActivityNoteNotification(data: {\r\n    applicationId: string;\r\n    message: string;\r\n    noteType?: string;\r\n    category?: string;\r\n    priority?: 'low' | 'normal' | 'high';\r\n    attachmentsCount?: number;\r\n  }): Promise<any> {\r\n    const response = await apiClient.post('/notifications/activity-note', {\r\n      application_id: data.applicationId,\r\n      message: data.message,\r\n      note_type: data.noteType || 'evaluation_comment',\r\n      category: data.category || 'communication',\r\n      priority: data.priority || 'normal',\r\n      attachments_count: data.attachmentsCount || 0,\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: string,\r\n  newStatus: string,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.DRAFT]: {\r\n      title: 'Application Draft Saved',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been saved as a draft. You can continue editing and submit it when ready.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING]: {\r\n      title: 'Application Pending',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is pending initial review. We'll notify you once the review begins.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING_PAYMENT]: {\r\n      title: 'Payment Required',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been approved for payment. Please complete the payment to proceed with license issuance.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus as keyof typeof messages] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAIO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,AAAC,mCAAyD,OAAvB,YAAY,QAAQ;YACxE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAE9C,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,MAAM,IAAI;gBAC5B,cAAc,KAAK,MAAM,IAAI;gBAC7B,eAAe,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;YAChD;QACF,EAAE,OAAO,OAAY;gBAYP,iBACF;YAZV,sDAAsD;YACtD,MAAM,eAAuC,CAAC;YAC9C,IAAI,QAAQ;gBACV,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ;gBACzD,IAAI,OAAO,KAAK,EAAE,aAAa,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ;gBAC5D,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI;gBAChD,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,GAAG,OAAO,MAAM;YACxD;YAEA,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,IAAI,KAAI;gBAClC,QAAQ,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,KAAI;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,AAAC,mCAA+E,OAA7C,IAAI,gBAAgB,cAAc,QAAQ;YACzF;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,AAAC,kBAAgC,OAAf,gBAAe;QACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qGAAqG;IACrG,MAAM;QACJ,kGAAkG;QAClG,qDAAqD;QACrD,MAAM,IAAI,MAAM;IAClB;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,kBAAgC,OAAf;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0FAA0F;IAC1F,MAAM,8BAA6B,IAOlC;QACC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB,KAAK,aAAa;YAClC,SAAS,KAAK,OAAO;YACrB,WAAW,KAAK,QAAQ,IAAI;YAC5B,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ,IAAI;YAC3B,mBAAmB,KAAK,gBAAgB,IAAI;QAC9C;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,AAAC,KAAuB,OAAnB,oBAAmB,iBAAe;IACjF,MAAM,WAAW,OAAO,AAAC,WAAe,OAAL,QAAS;IAE5C,MAAM,WAAW;QACf,CAAC,0HAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YACzB,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAqE,OAArD,mBAAkB,qCAAkD,OAAf,cAAwB,OAAT,UAAS;YAClI,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkE,OAAlD,mBAAkB,kCAA+C,OAAf,cAAwB,OAAT,UAAS;YAC/H,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,EAAE;YACnC,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,AAAC,yBAA4D,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACxF,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAiE,OAAjD,mBAAkB,iCAA2D,OAA5B,UAAU,OAAO,CAAC,KAAK,MAAK;QAClI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAmC,IAAI;AACzD;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,AAAC,QAAkB,OAAX,KAAK,GAAG;IAEvC,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,AAAC,OAAwB,OAAlB;QACvB,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,0HAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,0HAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,0HAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode, useEffect } from 'react';\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: ReactNode;\r\n  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';\r\n  showCloseButton?: boolean;\r\n  closeOnOverlayClick?: boolean;\r\n}\r\n\r\nexport default function Modal({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  showCloseButton = true,\r\n  closeOnOverlayClick = true,\r\n}: ModalProps) {\r\n  // Handle escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape' && isOpen) {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent body scroll when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'sm:max-w-sm';\r\n      case 'md':\r\n        return 'sm:max-w-md';\r\n      case 'lg':\r\n        return 'sm:max-w-lg';\r\n      case 'xl':\r\n        return 'sm:max-w-xl';\r\n      case '2xl':\r\n        return 'sm:max-w-2xl';\r\n      default:\r\n        return 'sm:max-w-md';\r\n    }\r\n  };\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {\r\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"fixed inset-0\"\r\n        onClick={handleOverlayClick}\r\n        aria-hidden=\"true\"\r\n      />\r\n      \r\n      <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full ${getSizeClasses()} mx-4 transform transition-all max-h-[90vh] flex flex-col`}>\r\n        {/* Header - Fixed */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            {title}\r\n          </h3>\r\n          {showCloseButton && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Scrollable Content */}\r\n        <div className=\"flex-1 overflow-hidden\">\r\n          <div className=\"overflow-y-auto h-full\">\r\n            <div className=\"p-6\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAce,SAAS,MAAM,KAQjB;QARiB,EAC5B,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EACf,GARiB;;IAS5B,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;wBACpC;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,yCAAyC;gBACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YACvD;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS;gBACT,eAAY;;;;;;0BAGd,6LAAC;gBAAI,WAAW,AAAC,kEAAkF,OAAjB,kBAAiB;;kCAEjG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,iCACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GA3FwB;KAAA", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Notification } from '@/hooks/useNotifications';\r\n\r\ninterface NotificationItemProps {\r\n  notification: Notification;\r\n  onMarkAsRead: (id: string) => void;\r\n  onNotificationClick?: (notification: Notification) => void;\r\n}\r\n\r\nconst NotificationItem: React.FC<NotificationItemProps> = ({\r\n  notification,\r\n  onMarkAsRead,\r\n  onNotificationClick,\r\n}) => {\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) {\r\n      return 'Just now';\r\n    } else if (diffInSeconds < 3600) {\r\n      const minutes = Math.floor(diffInSeconds / 60);\r\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 86400) {\r\n      const hours = Math.floor(diffInSeconds / 3600);\r\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 604800) {\r\n      const days = Math.floor(diffInSeconds / 86400);\r\n      return `${days} day${days > 1 ? 's' : ''} ago`;\r\n    } else {\r\n      return date.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const getNotificationIcon = (type: string) => {\r\n    switch (type.toLowerCase()) {\r\n      case 'email':\r\n        return 'ri-mail-line';\r\n      case 'sms':\r\n        return 'ri-message-3-line';\r\n      case 'in_app':\r\n        return 'ri-notification-3-line';\r\n      case 'application_status':\r\n        return 'ri-file-list-3-line';\r\n      case 'evaluation_assigned':\r\n        return 'ri-user-settings-line';\r\n      case 'payment_due':\r\n        return 'ri-money-dollar-circle-line';\r\n      case 'license_expiry':\r\n        return 'ri-calendar-event-line';\r\n      case 'system_alert':\r\n        return 'ri-alert-line';\r\n      default:\r\n        return 'ri-notification-3-line';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority.toLowerCase()) {\r\n      case 'urgent':\r\n        return 'text-red-600 dark:text-red-400';\r\n      case 'high':\r\n        return 'text-orange-600 dark:text-orange-400';\r\n      case 'medium':\r\n        return 'text-yellow-600 dark:text-yellow-400';\r\n      case 'low':\r\n        return 'text-green-600 dark:text-green-400';\r\n      default:\r\n        return 'text-gray-600 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const handleItemClick = () => {\r\n    if (!notification.is_read) {\r\n      onMarkAsRead(notification.notification_id);\r\n    }\r\n    if (onNotificationClick) {\r\n      onNotificationClick(notification);\r\n    }\r\n  };\r\n\r\n  const handleMarkAsReadClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onMarkAsRead(notification.notification_id);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200 ${\r\n        !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''\r\n      }`}\r\n      onClick={handleItemClick}\r\n    >\r\n      <div className=\"flex items-start space-x-3\">\r\n        {/* Notification Icon */}\r\n        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\r\n          !notification.is_read \r\n            ? 'bg-blue-100 dark:bg-blue-900/50' \r\n            : 'bg-gray-100 dark:bg-gray-700'\r\n        }`}>\r\n          <i className={`${getNotificationIcon(notification.type)} text-sm ${\r\n            !notification.is_read \r\n              ? 'text-blue-600 dark:text-blue-400' \r\n              : 'text-gray-600 dark:text-gray-400'\r\n          }`}></i>\r\n        </div>\r\n\r\n        {/* Notification Content */}\r\n        <div className=\"flex-1 min-w-0\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex-1\">\r\n              <h4 className={`text-sm font-medium ${\r\n                !notification.is_read \r\n                  ? 'text-gray-900 dark:text-gray-100' \r\n                  : 'text-gray-700 dark:text-gray-300'\r\n              }`}>\r\n                {notification.subject}\r\n              </h4>\r\n              <p className={`mt-1 text-sm ${\r\n                !notification.is_read \r\n                  ? 'text-gray-700 dark:text-gray-300' \r\n                  : 'text-gray-500 dark:text-gray-400'\r\n              }`}>\r\n                {notification.message}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Priority Indicator */}\r\n            {notification.priority && notification.priority !== 'medium' && (\r\n              <div className={`flex-shrink-0 ml-2 ${getPriorityColor(notification.priority)}`}>\r\n                <i className=\"ri-flag-line text-xs\"></i>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Metadata */}\r\n          <div className=\"mt-2 flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\r\n              <span>{formatTimeAgo(notification.created_at)}</span>\r\n              {notification.entity_type && (\r\n                <span className=\"capitalize\">{notification.entity_type}</span>\r\n              )}\r\n            </div>\r\n\r\n            {/* Actions */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              {!notification.is_read && (\r\n                <button\r\n                  onClick={handleMarkAsReadClick}\r\n                  className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 hover:text-blue-800 dark:hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200\"\r\n                  title=\"Mark as read\"\r\n                >\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Mark as Read\r\n                </button>\r\n              )}\r\n              {notification.is_read && (\r\n                <span className=\"text-xs text-green-600 dark:text-green-400 flex items-center\">\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Read\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Unread Indicator */}\r\n        {!notification.is_read && (\r\n          <div className=\"flex-shrink-0 w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full\"></div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationItem;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,MAAM,mBAAoD;QAAC,EACzD,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACpB;IACC,MAAM,gBAAgB,CAAC;QACrB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,gBAAgB,MAAM;YAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;YAC3C,OAAO,AAAC,GAAmB,OAAjB,SAAQ,WAAgC,OAAvB,UAAU,IAAI,MAAM,IAAG;QACpD,OAAO,IAAI,gBAAgB,OAAO;YAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,AAAC,GAAe,OAAb,OAAM,SAA4B,OAArB,QAAQ,IAAI,MAAM,IAAG;QAC9C,OAAO,IAAI,gBAAgB,QAAQ;YACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;YACxC,OAAO,AAAC,GAAa,OAAX,MAAK,QAA0B,OAApB,OAAO,IAAI,MAAM,IAAG;QAC3C,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,aAAa,aAAa,eAAe;QAC3C;QACA,IAAI,qBAAqB;YACvB,oBAAoB;QACtB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,EAAE,eAAe;QACjB,aAAa,aAAa,eAAe;IAC3C;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,2IAEX,OADC,CAAC,aAAa,OAAO,GAAG,mCAAmC;QAE7D,SAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAW,AAAC,uEAIhB,OAHC,CAAC,aAAa,OAAO,GACjB,oCACA;8BAEJ,cAAA,6LAAC;wBAAE,WAAW,AAAC,GACb,OADe,oBAAoB,aAAa,IAAI,GAAE,aAIvD,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;;;;;;;;;;;8BAKR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,AAAC,uBAIf,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;sDAEH,aAAa,OAAO;;;;;;sDAEvB,6LAAC;4CAAE,WAAW,AAAC,gBAId,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;sDAEH,aAAa,OAAO;;;;;;;;;;;;gCAKxB,aAAa,QAAQ,IAAI,aAAa,QAAQ,KAAK,0BAClD,6LAAC;oCAAI,WAAW,AAAC,sBAA6D,OAAxC,iBAAiB,aAAa,QAAQ;8CAC1E,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAM,cAAc,aAAa,UAAU;;;;;;wCAC3C,aAAa,WAAW,kBACvB,6LAAC;4CAAK,WAAU;sDAAc,aAAa,WAAW;;;;;;;;;;;;8CAK1D,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,aAAa,OAAO,kBACpB,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAM;;8DAEN,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;wCAIzC,aAAa,OAAO,kBACnB,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;gBAS/C,CAAC,aAAa,OAAO,kBACpB,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;KArKM;uCAuKS", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useNotifications.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\nexport interface NotificationCounts {\r\n  total: number;\r\n  unread: number;\r\n}\r\n\r\nexport interface UseNotificationsReturn {\r\n  notifications: AppNotification[];\r\n  unreadCount: number;\r\n  totalCount: number;\r\n  loading: boolean;\r\n  error: string | null;\r\n  fetchNotifications: () => Promise<void>;\r\n  markAsRead: (id: string) => Promise<void>;\r\n  markAllAsRead: () => Promise<void>;\r\n  refreshNotifications: () => Promise<void>;\r\n  getNotificationCounts: () => Promise<void>;\r\n}\r\n\r\nexport const useNotifications = (): UseNotificationsReturn => {\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [totalCount, setTotalCount] = useState(0);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { user, isAuthenticated } = useAuth();\r\n  const { showError } = useToast();\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      console.log('🔍 Fetching notifications for user:', {\r\n        userId: user.user_id,\r\n        email: user.email\r\n      });\r\n\r\n      const data = await notificationService.getUserNotifications({\r\n        page: 1,\r\n        limit: 50 // Get more notifications for better UX\r\n      });\r\n\r\n      if (data && data.notifications) {\r\n        setNotifications(data.notifications);\r\n        setUnreadCount(data.unread_count);\r\n        setTotalCount(data.total_count);\r\n      } else {\r\n        setNotifications([]);\r\n        setUnreadCount(0);\r\n        setTotalCount(0);\r\n      }\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';\r\n      setError(errorMessage);\r\n      console.error('Error fetching notifications:', err);\r\n      // Don't show toast error for initial load failures\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [isAuthenticated, user]);\r\n\r\n  const markAsRead = useCallback(async (id: string) => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await notificationService.markAsRead(id);\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notification =>\r\n          notification.notification_id === id\r\n            ? { ...notification, status: 'read', read_at: new Date().toISOString() }\r\n            : notification\r\n        )\r\n      );\r\n\r\n      // Update unread count\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark notification as read');\r\n      console.error('Error marking notification as read:', err);\r\n    }\r\n  }, [isAuthenticated, showError]);\r\n\r\n  const markAllAsRead = useCallback(async () => {\r\n    if (!isAuthenticated || notifications.length === 0) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Mark all unread notifications as read\r\n      const unreadNotifications = notifications.filter(n => n.status === 'unread');\r\n\r\n      for (const notification of unreadNotifications) {\r\n        await markAsRead(notification.notification_id);\r\n      }\r\n\r\n      setUnreadCount(0);\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark all notifications as read');\r\n      console.error('Error marking all notifications as read:', err);\r\n    }\r\n  }, [isAuthenticated, notifications, markAsRead, showError]);\r\n\r\n  const getNotificationCounts = useCallback(async () => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const counts = await notificationService.getNotificationCount();\r\n      setUnreadCount(counts.unread);\r\n      setTotalCount(counts.total);\r\n    } catch (err) {\r\n      console.error('Error getting notification counts:', err);\r\n    }\r\n  }, [isAuthenticated]);\r\n\r\n  const refreshNotifications = useCallback(async () => {\r\n    await fetchNotifications();\r\n  }, [fetchNotifications]);\r\n\r\n  // Initial fetch when component mounts and user is authenticated\r\n  useEffect(() => {\r\n    if (isAuthenticated && user) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  // Set up polling for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    const interval = setInterval(() => {\r\n      fetchNotifications();\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  return {\r\n    notifications,\r\n    unreadCount,\r\n    totalCount,\r\n    loading,\r\n    error,\r\n    fetchNotifications,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n    getNotificationCounts,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;;AALA;;;;;AA0BO,MAAM,mBAAmB;;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAE7B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACF,QAAQ,GAAG,CAAC,uCAAuC;oBACjD,QAAQ,KAAK,OAAO;oBACpB,OAAO,KAAK,KAAK;gBACnB;gBAEA,MAAM,OAAO,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;oBAC1D,MAAM;oBACN,OAAO,GAAG,uCAAuC;gBACnD;gBAEA,IAAI,QAAQ,KAAK,aAAa,EAAE;oBAC9B,iBAAiB,KAAK,aAAa;oBACnC,eAAe,KAAK,YAAY;oBAChC,cAAc,KAAK,WAAW;gBAChC,OAAO;oBACL,iBAAiB,EAAE;oBACnB,eAAe;oBACf,cAAc;gBAChB;YACF,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mDAAmD;YACrD,SAAU;gBACR,WAAW;YACb;QACF;2DAAG;QAAC;QAAiB;KAAK;IAE1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACpC,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,yIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;gBAErC,qBAAqB;gBACrB;gEAAiB,CAAA,OACf,KAAK,GAAG;wEAAC,CAAA,eACP,aAAa,eAAe,KAAK,KAC7B;oCAAE,GAAG,YAAY;oCAAE,QAAQ;oCAAQ,SAAS,IAAI,OAAO,WAAW;gCAAG,IACrE;;;gBAIR,sBAAsB;gBACtB;gEAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;;YAC5C,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;mDAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAChC,IAAI,CAAC,mBAAmB,cAAc,MAAM,KAAK,GAAG;gBAClD;YACF;YAEA,IAAI;gBACF,wCAAwC;gBACxC,MAAM,sBAAsB,cAAc,MAAM;uFAAC,CAAA,IAAK,EAAE,MAAM,KAAK;;gBAEnE,KAAK,MAAM,gBAAgB,oBAAqB;oBAC9C,MAAM,WAAW,aAAa,eAAe;gBAC/C;gBAEA,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,4CAA4C;YAC5D;QACF;sDAAG;QAAC;QAAiB;QAAe;QAAY;KAAU;IAE1D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YACxC,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,SAAS,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB;gBAC7D,eAAe,OAAO,MAAM;gBAC5B,cAAc,OAAO,KAAK;YAC5B,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;8DAAG;QAAC;KAAgB;IAEpB,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACvC,MAAM;QACR;6DAAG;QAAC;KAAmB;IAEvB,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,mBAAmB,MAAM;gBAC3B;YACF;QACF;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,MAAM,WAAW;uDAAY;oBAC3B;gBACF;sDAAG,QAAQ,aAAa;YAExB;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlJa;;QAMuB,kIAAA,CAAA,UAAO;QACnB,mIAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Modal from '@/components/common/Modal';\r\nimport NotificationItem from './NotificationItem';\r\nimport Loader from '@/components/Loader';\r\nimport { useNotifications } from '@/hooks/useNotifications';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n}) => {\r\n  const {\r\n    notifications,\r\n    unreadCount,\r\n    loading,\r\n    error,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n  } = useNotifications();\r\n  \r\n  const { showSuccess, showError } = useToast();\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('unread');\r\n  const [localLoading, setLocalLoading] = useState(false);\r\n\r\n  // Filter notifications based on selected filter\r\n  const filteredNotifications = notifications.filter(notification => {\r\n    if (filter === 'unread') {\r\n      return !notification.read_at;\r\n    }\r\n    return true;\r\n  });\r\n\r\n\r\n  // Refresh notifications when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      refreshNotifications();\r\n    }\r\n  }, [isOpen, refreshNotifications]);\r\n\r\n  const handleMarkAsRead = async (id: string) => {\r\n    try {\r\n      await markAsRead(id);\r\n      showSuccess('Notification marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    if (unreadCount === 0) {\r\n      showError('No unread notifications to mark');\r\n      return;\r\n    }\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await markAllAsRead();\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleNotificationClick = (notification: AppNotification) => {\r\n    // Handle notification click - could navigate to related page\r\n    if (notification.entity_type === 'application' && notification.entity_id) {\r\n      // Navigate to application details\r\n      window.location.href = `/customer/my-licenses?application_id=${notification.entity_id}`;\r\n    }\r\n  };\r\n\r\n  const handleRefresh = async () => {\r\n    setLocalLoading(true);\r\n    try {\r\n      await refreshNotifications();\r\n    } catch (error) {\r\n      console.error('Error refreshing notifications:', error);\r\n      showError('Failed to refresh notifications');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title=\"Notifications\"\r\n      size=\"lg\"\r\n    >\r\n      <div className=\"flex flex-col h-96\">\r\n        {/* Header with filters and actions */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Filter buttons */}\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'all'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                All ({notifications.length})\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'unread'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                Unread ({unreadCount})\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action buttons */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <button\r\n              onClick={handleRefresh}\r\n              disabled={loading || localLoading}\r\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50\"\r\n              title=\"Refresh notifications\"\r\n            >\r\n              <i className={`ri-refresh-line ${(loading || localLoading) ? 'animate-spin' : ''}`}></i>\r\n            </button>\r\n            \r\n            {unreadCount > 0 && (\r\n              <button\r\n                onClick={handleMarkAllAsRead}\r\n                disabled={loading || localLoading}\r\n                className=\"px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Notifications list */}\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          {loading && notifications.length === 0 ? (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <Loader message=\"Loading notifications...\" />\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-error-warning-line text-4xl text-red-500 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                Error Loading Notifications\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n                {error}\r\n              </p>\r\n              <button\r\n                onClick={handleRefresh}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          ) : filteredNotifications.length === 0 ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                {filter === 'unread' ? 'No Unread Notifications' : 'No Notifications'}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {filter === 'unread' \r\n                  ? 'All caught up! You have no unread notifications.'\r\n                  : 'You have no notifications at this time.'\r\n                }\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\r\n              {filteredNotifications.map((notification) => (\r\n                <NotificationItem\r\n                  key={notification.notification_id}\r\n                  notification={notification}\r\n                  onMarkAsRead={handleMarkAsRead}\r\n                  onNotificationClick={handleNotificationClick}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        {filteredNotifications.length > 0 && (\r\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700 text-center\">\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Showing {filteredNotifications.length} of {notifications.length} notifications\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAeA,MAAM,oBAAsD;QAAC,EAC3D,MAAM,EACN,OAAO,EACR;;IACC,MAAM,EACJ,aAAa,EACb,WAAW,EACX,OAAO,EACP,KAAK,EACL,UAAU,EACV,aAAa,EACb,oBAAoB,EACrB,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gDAAgD;IAChD,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW,UAAU;YACvB,OAAO,CAAC,aAAa,OAAO;QAC9B;QACA,OAAO;IACT;IAGA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;sCAAG;QAAC;QAAQ;KAAqB;IAEjC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW;YACjB,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,GAAG;YACrB,UAAU;YACV;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,6DAA6D;QAC7D,IAAI,aAAa,WAAW,KAAK,iBAAiB,aAAa,SAAS,EAAE;YACxE,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,AAAC,wCAA8D,OAAvB,aAAa,SAAS;QACvF;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,wIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,+DAIX,OAHC,WAAW,QACP,qEACA;;4CAEP;4CACO,cAAc,MAAM;4CAAC;;;;;;;kDAE7B,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,+DAIX,OAHC,WAAW,WACP,qEACA;;4CAEP;4CACU;4CAAY;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCAAE,WAAW,AAAC,mBAAkE,OAAhD,AAAC,WAAW,eAAgB,iBAAiB;;;;;;;;;;;gCAG/E,cAAc,mBACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;8BACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;mEAEhB,sBACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,6LAAC;gCAAE,WAAU;0CACV;;;;;;0CAEH,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;mEAID,sBAAsB,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CACX,WAAW,WAAW,4BAA4B;;;;;;0CAErD,6LAAC;gCAAE,WAAU;0CACV,WAAW,WACR,qDACA;;;;;;;;;;;iFAKR,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC,0JAAA,CAAA,UAAgB;gCAEf,cAAc;gCACd,cAAc;gCACd,qBAAqB;+BAHhB,aAAa,eAAe;;;;;;;;;;;;;;;gBAW1C,sBAAsB,MAAM,GAAG,mBAC9B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAA2C;4BAC7C,sBAAsB,MAAM;4BAAC;4BAAK,cAAc,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAO9E;GAzMM;;QAYA,mIAAA,CAAA,mBAAgB;QAEe,mIAAA,CAAA,WAAQ;;;KAdvC;uCA2MS", "debugId": null}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport NotificationModal from '../notifications/NotificationModal';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n}\r\n\r\nconst NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\r\n  const { user } = useAuth();\r\n  const { showError } = useToast();\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  // Fetch notification count\r\n  const fetchNotificationCount = async () => {\r\n    if (!user) return;\r\n    \r\n    try {\r\n      const data = await notificationService.getNotificationCount();\r\n      setUnreadCount(data.unread);\r\n    } catch (error) {\r\n      console.error('Error fetching notification count:', error);\r\n      showError('Failed to load notification count');\r\n    }\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchNotificationCount();\r\n  }, [user]);\r\n\r\n  // Poll for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    const interval = setInterval(fetchNotificationCount, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Handle modal close and refresh count\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    fetchNotificationCount(); // Refresh count when modal closes\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className={`relative ${className}`}>\r\n        {/* Notification Bell */}\r\n        <button\r\n          onClick={() => setIsModalOpen(true)}\r\n          className=\"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n          title=\"Notifications\"\r\n        >\r\n          <i className=\"ri-notification-line text-xl\"></i>\r\n          {unreadCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1\">\r\n              {unreadCount > 99 ? '99+' : unreadCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYA,MAAM,mBAAoD;QAAC,EAAE,YAAY,EAAE,EAAE;;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC3D,eAAe,KAAK,MAAM;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW,YAAY,wBAAwB;YACrD;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,eAAe;QACf,0BAA0B,kCAAkC;IAC9D;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,6LAAC;gBAAI,WAAW,AAAC,YAAqB,OAAV;0BAE1B,cAAA,6LAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;oBACV,OAAM;;sCAEN,6LAAC;4BAAE,WAAU;;;;;;wBACZ,cAAc,mBACb,6LAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOpC,6LAAC,2JAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;GA/DM;;QACa,kIAAA,CAAA,UAAO;QACF,mIAAA,CAAA,WAAQ;;;KAF1B;uCAiES", "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\nimport NotificationBell from '../common/NotificationBell';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'My Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Invoices & Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <NotificationBell className=\"mr-4\" />\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAqBA,MAAM,iBAAgD;QAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM;gBACpC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;aACD;kDAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM;gBACjC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;+CAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,MAAM,gBAAgB;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,cAAc,OAAO;kEAAC,CAAA;4BACpB,OAAO,QAAQ,CAAC;wBAClB;;gBACF;;YAEA,4DAA4D;YAC5D,MAAM,QAAQ,WAAW,eAAe;YACxC;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,uBAAuB,CAAC;QAC1B;0DAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAChD,MAAM,eAA0C;gBAC9C,aAAa;gBACb,yBAAyB;gBACzB,0BAA0B;gBAC1B,iCAAiC;gBACjC,sBAAsB;gBACtB,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,6BAA6B;gBAC7B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;YAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,AAAC,WAAe,OAAL,MAAK;YACtD,WAAW;YACX,uBAAuB;QACzB;qDAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,2CAA2C;YAC3C,OAAO,QAAQ,CAAC;QAClB;qDAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,sBAAsB,CAAC;QACzB;yDAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,qCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,6LAAC;gBAAM,WAAW,AAAC,6KAE8D,OAA7E,sBAAsB,kBAAkB,sCAAqC;0BAE/E,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,AAAC,0JAKT,OAHC,KAAK,OAAO,GACV,8GACA,yHACH;;8DAGH,6LAAC;oDAAI,WAAW,AAAC,iDAAqG,OAArD,KAAK,OAAO,GAAG,mCAAmC;8DACjH,cAAA,6LAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAqB5B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,6LAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;iHAGb,6LAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mJAAA,CAAA,UAAgB;4CAAC,WAAU;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,gIAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,CAAA,iBAAA,2BAAA,KAAM,aAAa,KAAI;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,6LAAC,qIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlTM;;QAGa,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACP,kIAAA,CAAA,UAAO;QACD,qIAAA,CAAA,aAAU;;;KAN7B;uCAoTS", "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/payments/PaymentTabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface TabItem {\r\n  id: string;\r\n  label: string;\r\n  content: React.ReactNode;\r\n  icon?: string;\r\n}\r\n\r\ninterface PaymentTabsProps {\r\n  tabs: TabItem[];\r\n  activeTab: string;\r\n  onTabChange: (tabId: string) => void;\r\n}\r\n\r\nconst PaymentTabs = ({ tabs, activeTab, onTabChange }: PaymentTabsProps) => {\r\n  return (\r\n    <div className=\"w-full\">\r\n      {/* Tab Navigation */}\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n        <nav className=\"-mb-px flex space-x-8 px-6\" aria-label=\"Tabs\">\r\n          {tabs.map((tab) => (\r\n            <button\r\n              key={tab.id}\r\n              onClick={() => onTabChange(tab.id)}\r\n              className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 flex items-center space-x-2 ${\r\n                activeTab === tab.id\r\n                  ? 'border-primary text-primary dark:text-primary'\r\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'\r\n              }`}\r\n              aria-current={activeTab === tab.id ? 'page' : undefined}\r\n            >\r\n              {tab.icon && <i className={`${tab.icon} text-lg`}></i>}\r\n              <span>{tab.label}</span>\r\n            </button>\r\n          ))}\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      <div className=\"mt-6\">\r\n        {tabs.map((tab) => (\r\n          <div\r\n            key={tab.id}\r\n            className={`${activeTab === tab.id ? 'block' : 'hidden'}`}\r\n            role=\"tabpanel\"\r\n            aria-labelledby={`${tab.id}-tab`}\r\n          >\r\n            {tab.content}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PaymentTabs;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAiBA,MAAM,cAAc;QAAC,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAoB;IACrE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAA6B,cAAW;8BACpD,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;4BAEC,SAAS,IAAM,YAAY,IAAI,EAAE;4BACjC,WAAW,AAAC,yHAIX,OAHC,cAAc,IAAI,EAAE,GAChB,kDACA;4BAEN,gBAAc,cAAc,IAAI,EAAE,GAAG,SAAS;;gCAE7C,IAAI,IAAI,kBAAI,6LAAC;oCAAE,WAAW,AAAC,GAAW,OAAT,IAAI,IAAI,EAAC;;;;;;8CACvC,6LAAC;8CAAM,IAAI,KAAK;;;;;;;2BAVX,IAAI,EAAE;;;;;;;;;;;;;;;0BAiBnB,6LAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC;wBAEC,WAAW,AAAC,GAA4C,OAA1C,cAAc,IAAI,EAAE,GAAG,UAAU;wBAC/C,MAAK;wBACL,mBAAiB,AAAC,GAAS,OAAP,IAAI,EAAE,EAAC;kCAE1B,IAAI,OAAO;uBALP,IAAI,EAAE;;;;;;;;;;;;;;;;AAWvB;KAvCM;uCAyCS", "debugId": null}}, {"offset": {"line": 2255, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Pagination.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\n// Import the pagination response type from the existing structure\r\ninterface PaginationMeta {\r\n  itemsPerPage: number;\r\n  totalItems: number;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  sortBy: [string, string][];\r\n  searchBy: string[];\r\n  search: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\ninterface PaginationProps {\r\n  meta: PaginationMeta;\r\n  onPageChange: (page: number) => void;\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  showFirstLast?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showInfo?: boolean;\r\n  maxVisiblePages?: number;\r\n  pageSizeOptions?: number[];\r\n  className?: string;\r\n}\r\n\r\nconst Pagination: React.FC<PaginationProps> = ({\r\n  meta,\r\n  onPageChange,\r\n  onPageSizeChange,\r\n  showFirstLast = true,\r\n  showPageSizeSelector = true,\r\n  showInfo = true,\r\n  maxVisiblePages = 7,\r\n  pageSizeOptions = [10, 25, 50, 100],\r\n  className = ''\r\n}) => {\r\n  const { currentPage, totalPages, totalItems, itemsPerPage } = meta;\r\n\r\n  // Don't render if there's only one page or no pages and no additional features\r\n  if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate current items range\r\n  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;\r\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\r\n\r\n  // Calculate which pages to show\r\n  const getVisiblePages = (): (number | string)[] => {\r\n    const pages: (number | string)[] = [];\r\n    \r\n    // If total pages is less than or equal to maxVisiblePages, show all\r\n    if (totalPages <= maxVisiblePages) {\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        pages.push(i);\r\n      }\r\n      return pages;\r\n    }\r\n\r\n    // Always show first page\r\n    pages.push(1);\r\n\r\n    // Calculate start and end of the visible range around current page\r\n    const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current\r\n    let startPage = Math.max(2, currentPage - sidePages);\r\n    let endPage = Math.min(totalPages - 1, currentPage + sidePages);\r\n\r\n    // Adjust if we're near the beginning\r\n    if (currentPage <= sidePages + 2) {\r\n      startPage = 2;\r\n      endPage = Math.min(totalPages - 1, maxVisiblePages - 1);\r\n    }\r\n\r\n    // Adjust if we're near the end\r\n    if (currentPage >= totalPages - sidePages - 1) {\r\n      startPage = Math.max(2, totalPages - maxVisiblePages + 2);\r\n      endPage = totalPages - 1;\r\n    }\r\n\r\n    // Add ellipsis after first page if needed\r\n    if (startPage > 2) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Add pages in the visible range\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    // Add ellipsis before last page if needed\r\n    if (endPage < totalPages - 1) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Always show last page (if it's not already included)\r\n    if (totalPages > 1) {\r\n      pages.push(totalPages);\r\n    }\r\n\r\n    return pages;\r\n  };\r\n\r\n  const visiblePages = getVisiblePages();\r\n\r\n  const handlePageClick = (page: number | string) => {\r\n    if (typeof page === 'number' && page !== currentPage) {\r\n      onPageChange(page);\r\n    }\r\n  };\r\n\r\n  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newPageSize = parseInt(event.target.value);\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(newPageSize);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentPage > 1) {\r\n      onPageChange(currentPage - 1);\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentPage < totalPages) {\r\n      onPageChange(currentPage + 1);\r\n    }\r\n  };\r\n\r\n  const handleFirst = () => {\r\n    if (currentPage !== 1) {\r\n      onPageChange(1);\r\n    }\r\n  };\r\n\r\n  const handleLast = () => {\r\n    if (currentPage !== totalPages) {\r\n      onPageChange(totalPages);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>\r\n      {/* Left side - Info and page size selector */}\r\n      <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\r\n        {/* Items info */}\r\n        {showInfo && totalItems > 0 && (\r\n          <div className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n            Showing <span className=\"font-medium\">{startItem}</span> to{' '}\r\n            <span className=\"font-medium\">{endItem}</span> of{' '}\r\n            <span className=\"font-medium\">{totalItems}</span> results\r\n          </div>\r\n        )}\r\n\r\n        {/* Page size selector */}\r\n        {showPageSizeSelector && onPageSizeChange && (\r\n          <div className=\"flex items-center space-x-2\">\r\n            <select\r\n              value={itemsPerPage}\r\n              onChange={handlePageSizeChange}\r\n              className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\r\n            >\r\n              {pageSizeOptions.map((size) => (\r\n                <option key={size} value={size}>\r\n                  {size} per page\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side - Pagination controls */}\r\n      {totalPages > 1 && (\r\n        <nav className=\"flex items-center space-x-1\" aria-label=\"Pagination\">\r\n          {/* First page button */}\r\n          {showFirstLast && currentPage > 1 && (\r\n            <button\r\n              onClick={handleFirst}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to first page\"\r\n            >\r\n              <i className=\"ri-skip-back-line\"></i>\r\n            </button>\r\n          )}\r\n\r\n          {/* Previous button */}\r\n          <button\r\n            onClick={handlePrevious}\r\n            disabled={currentPage === 1}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === 1\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`}\r\n            aria-label=\"Go to previous page\"\r\n          >\r\n            <i className=\"ri-arrow-left-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Page numbers */}\r\n          {visiblePages.map((page, index) => (\r\n            <React.Fragment key={index}>\r\n              {page === '...' ? (\r\n                <span className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400\">\r\n                  ...\r\n                </span>\r\n              ) : (\r\n                <button\r\n                  onClick={() => handlePageClick(page)}\r\n                  className={`inline-flex items-center px-4 py-2 text-sm font-medium border ${\r\n                    page === currentPage\r\n                      ? 'text-white bg-red-600 border-red-600 hover:bg-red-700'\r\n                      : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n                  }`}\r\n                  aria-label={`Go to page ${page}`}\r\n                  aria-current={page === currentPage ? 'page' : undefined}\r\n                >\r\n                  {page}\r\n                </button>\r\n              )}\r\n            </React.Fragment>\r\n          ))}\r\n\r\n          {/* Next button */}\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={currentPage === totalPages}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === totalPages\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`}\r\n            aria-label=\"Go to next page\"\r\n          >\r\n            <i className=\"ri-arrow-right-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Last page button */}\r\n          {showFirstLast && currentPage < totalPages && (\r\n            <button\r\n              onClick={handleLast}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to last page\"\r\n            >\r\n              <i className=\"ri-skip-forward-line\"></i>\r\n            </button>\r\n          )}\r\n        </nav>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Pagination;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA4BA,MAAM,aAAwC;QAAC,EAC7C,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,WAAW,IAAI,EACf,kBAAkB,CAAC,EACnB,kBAAkB;QAAC;QAAI;QAAI;QAAI;KAAI,EACnC,YAAY,EAAE,EACf;IACC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;IAE9D,+EAA+E;IAC/E,IAAI,cAAc,KAAK,CAAC,wBAAwB,CAAC,UAAU;QACzD,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,YAAY,aAAa,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,IAAI;IAC1E,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,oEAAoE;QACpE,IAAI,cAAc,iBAAiB;YACjC,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;YACA,OAAO;QACT;QAEA,yBAAyB;QACzB,MAAM,IAAI,CAAC;QAEX,mEAAmE;QACnE,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,IAAI,kCAAkC;QAC3F,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC1C,IAAI,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;QAErD,qCAAqC;QACrC,IAAI,eAAe,YAAY,GAAG;YAChC,YAAY;YACZ,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,kBAAkB;QACvD;QAEA,+BAA+B;QAC/B,IAAI,eAAe,aAAa,YAAY,GAAG;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,kBAAkB;YACvD,UAAU,aAAa;QACzB;QAEA,0CAA0C;QAC1C,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,CAAC;QACb;QAEA,iCAAiC;QACjC,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,0CAA0C;QAC1C,IAAI,UAAU,aAAa,GAAG;YAC5B,MAAM,IAAI,CAAC;QACb;QAEA,uDAAuD;QACvD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,YAAY,SAAS,aAAa;YACpD,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,SAAS,MAAM,MAAM,CAAC,KAAK;QAC/C,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB,GAAG;YACrB,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB,YAAY;YAC9B,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,mKAA4K,OAAV;;0BAEjL,6LAAC;gBAAI,WAAU;;oBAEZ,YAAY,aAAa,mBACxB,6LAAC;wBAAI,WAAU;;4BAA2C;0CAChD,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAiB;4BAAI;0CAC5D,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAe;4BAAI;0CAClD,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAkB;;;;;;;oBAKpD,wBAAwB,kCACvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU;4BACV,WAAU;sCAET,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;oCAAkB,OAAO;;wCACvB;wCAAK;;mCADK;;;;;;;;;;;;;;;;;;;;;YAUtB,aAAa,mBACZ,6LAAC;gBAAI,WAAU;gBAA8B,cAAW;;oBAErD,iBAAiB,cAAc,mBAC9B,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAKjB,6LAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,AAAC,iEAIR,OAHF,gBAAgB,IACZ,0HACA,wLACL,KAA0D,OAAvD,iBAAiB,cAAc,IAAI,KAAK;wBAC5C,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;sCACZ,SAAS,sBACR,6LAAC;gCAAK,WAAU;0CAAgK;;;;;yFAIhL,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,AAAC,iEAIX,OAHC,SAAS,cACL,0DACA;gCAEN,cAAY,AAAC,cAAkB,OAAL;gCAC1B,gBAAc,SAAS,cAAc,SAAS;0CAE7C;;;;;;2BAhBc;;;;;kCAuBvB,6LAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,AAAC,iEAIR,OAHF,gBAAgB,aACZ,0HACA,wLACL,KAAmE,OAAhE,iBAAiB,cAAc,aAAa,KAAK;wBACrD,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,iBAAiB,cAAc,4BAC9B,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAO3B;KAnOM;uCAqOS", "debugId": null}}, {"offset": {"line": 2552, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/DataTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { PaginateQuery } from '../../types';\r\nimport Pagination from './Pagination';\r\nimport '../../styles/DataTable.css';\r\n\r\n// Generic paginated response interface to handle different response types\r\ninterface GenericPaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select?: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\ninterface Column<T> {\r\n  key: keyof T | string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  searchable?: boolean;\r\n  render?: (value: unknown, item: T) => React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface DataTableProps<T> {\r\n  columns: Column<T>[];\r\n  data: GenericPaginatedResponse<T> | null;\r\n  loading?: boolean;\r\n  onQueryChange: (query: PaginateQuery) => void;\r\n  searchPlaceholder?: string;\r\n  className?: string;\r\n  emptyStateIcon?: string;\r\n  emptyStateMessage?: string;\r\n}\r\n\r\nexport default function DataTable<T extends Record<string, unknown>>({\r\n  columns,\r\n  data,\r\n  loading = false,\r\n  onQueryChange,\r\n  searchPlaceholder = \"Search...\",\r\n  className = \"\",\r\n  emptyStateIcon = \"ri-inbox-line\",\r\n  emptyStateMessage = \"No data found\",\r\n}: DataTableProps<T>) {\r\n  const [query, setQuery] = useState<PaginateQuery>({\r\n    page: 1,\r\n    limit: 10,\r\n    search: '',\r\n    sortBy: [],\r\n  });\r\n  const [searchInput, setSearchInput] = useState('');\r\n\r\n  const handleSearch = useCallback((search: string) => {\r\n    try {\r\n      const newQuery = { ...query, search, page: 1 };\r\n      setQuery(newQuery);\r\n      onQueryChange(newQuery);\r\n    } catch (error) {\r\n      console.error('Error handling search:', error);\r\n    }\r\n  }, [query, onQueryChange]);\r\n\r\n  // Debounced search effect\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (searchInput !== query.search) {\r\n        handleSearch(searchInput);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchInput, query.search, handleSearch]);\r\n\r\n  const handleSort = (columnKey: string) => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    let newSortBy: string[] = [];\r\n\r\n    if (!currentSort) {\r\n      newSortBy = [`${columnKey}:ASC`];\r\n    } else if (currentSort.endsWith(':ASC')) {\r\n      newSortBy = [`${columnKey}:DESC`];\r\n    } else {\r\n      newSortBy = [];\r\n    }\r\n\r\n    const newQuery = { ...query, sortBy: newSortBy, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    const newQuery = { ...query, page };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const getSortDirection = (columnKey: string): 'asc' | 'desc' | null => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    if (!currentSort) return null;\r\n    return currentSort.endsWith(':ASC') ? 'asc' : 'desc';\r\n  };\r\n\r\n  // Handle null data case early\r\n  if (!data) {\r\n    return (\r\n      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n        <div className=\"p-6 text-center text-gray-500 dark:text-gray-400\">\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n            <span className=\"ml-2\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    const newQuery = { ...query, limit: newPageSize, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n      {/* Search Bar */}\r\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <i className=\"ri-search-line text-gray-400 dark:text-gray-500\"></i>\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder={searchPlaceholder}\r\n            value={searchInput}\r\n            onChange={(e) => setSearchInput(e.target.value)}\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className=\"overflow-x-auto data-table-container\">\r\n        <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n            <tr>\r\n              {columns.map((column) => (\r\n                <th\r\n                  key={String(column.key)}\r\n                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${\r\n                    column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''\r\n                  } ${column.className || ''}`}\r\n                  onClick={() => column.sortable && handleSort(String(column.key))}\r\n                >\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>{column.label}</span>\r\n                    {column.sortable && (\r\n                      <div className=\"flex flex-col\">\r\n                        <i className={`ri-arrow-up-s-line text-xs ${\r\n                          getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${\r\n                          getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n            {loading ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n                    <span className=\"ml-2\">Loading...</span>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : !data?.data || data.data.length === 0 ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex flex-col items-center justify-center py-8\">\r\n                    <i className={`${emptyStateIcon} text-4xl mb-2`}></i>\r\n                    <p>{emptyStateMessage}</p>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : (\r\n              data.data.map((item, index) => (\r\n                <tr key={index} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  {columns.map((column) => (\r\n                    <td key={String(column.key)} className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100\">\r\n                      {column.render\r\n                        ? column.render(item[column.key as keyof T], item)\r\n                        : String(item[column.key as keyof T] || '')\r\n                      }\r\n                    </td>\r\n                  ))}\r\n                </tr>\r\n              ))\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {data?.meta && data.meta.totalItems !== undefined && data.meta.currentPage !== undefined && data.meta.totalPages !== undefined && (\r\n        <Pagination\r\n          meta={{\r\n            ...data.meta,\r\n            totalItems: data.meta.totalItems,\r\n            currentPage: data.meta.currentPage,\r\n            totalPages: data.meta.totalPages,\r\n          }}\r\n          onPageChange={handlePageChange}\r\n          onPageSizeChange={handlePageSizeChange}\r\n          showFirstLast={true}\r\n          showPageSizeSelector={true}\r\n          showInfo={true}\r\n          maxVisiblePages={7}\r\n          pageSizeOptions={[10, 25, 50, 100]}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;;AAkDe,SAAS,UAA6C,KASjD;QATiD,EACnE,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,aAAa,EACb,oBAAoB,WAAW,EAC/B,YAAY,EAAE,EACd,iBAAiB,eAAe,EAChC,oBAAoB,eAAe,EACjB,GATiD;;IAUnE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YAChC,IAAI;gBACF,MAAM,WAAW;oBAAE,GAAG,KAAK;oBAAE;oBAAQ,MAAM;gBAAE;gBAC7C,SAAS;gBACT,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;QACF;8CAAG;QAAC;QAAO;KAAc;IAEzB,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,YAAY;iDAAW;oBAC3B,IAAI,gBAAgB,MAAM,MAAM,EAAE;wBAChC,aAAa;oBACf;gBACF;gDAAG;YAEH;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;QAAa,MAAM,MAAM;QAAE;KAAa;IAE5C,MAAM,aAAa,CAAC;YACE;QAApB,MAAM,eAAc,gBAAA,MAAM,MAAM,cAAZ,oCAAA,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,YAAsB,EAAE;QAE5B,IAAI,CAAC,aAAa;YAChB,YAAY;gBAAE,GAAY,OAAV,WAAU;aAAM;QAClC,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;YACvC,YAAY;gBAAE,GAAY,OAAV,WAAU;aAAO;QACnC,OAAO;YACL,YAAY,EAAE;QAChB;QAEA,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,QAAQ;YAAW,MAAM;QAAE;QACxD,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAK;QAClC,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;YACJ;QAApB,MAAM,eAAc,gBAAA,MAAM,MAAM,cAAZ,oCAAA,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,YAAY,QAAQ,CAAC,UAAU,QAAQ;IAChD;IAEA,8BAA8B;IAC9B,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAW,AAAC,+DAAwE,OAAV;sBAC7E,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,OAAO;YAAa,MAAM;QAAE;QACzD,SAAS;QACT,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,+DAAwE,OAAV;;0BAE7E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,6LAAC;4BACC,MAAK;4BACL,aAAa;4BACb,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;0CACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAEC,WAAW,AAAC,qGAER,OADF,OAAO,QAAQ,GAAG,4DAA4D,IAC/E,KAA0B,OAAvB,OAAO,SAAS,IAAI;wCACxB,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,OAAO,GAAG;kDAE9D,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAM,OAAO,KAAK;;;;;;gDAClB,OAAO,QAAQ,kBACd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAW,AAAC,8BAEd,OADC,iBAAiB,OAAO,OAAO,GAAG,OAAO,QAAQ,iBAAiB;;;;;;sEAEpE,6LAAC;4DAAE,WAAW,AAAC,sCAEd,OADC,iBAAiB,OAAO,OAAO,GAAG,OAAO,SAAS,iBAAiB;;;;;;;;;;;;;;;;;;uCAdtE,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;sCAuB9B,6LAAC;4BAAM,WAAU;sCACd,wBACC,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;uCAI3B,EAAC,iBAAA,2BAAA,KAAM,IAAI,KAAI,KAAK,IAAI,CAAC,MAAM,KAAK,kBACtC,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAW,AAAC,GAAiB,OAAf,gBAAe;;;;;;0DAChC,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;uCAKV,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;oCAAe,WAAU;8CACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;4CAA4B,WAAU;sDACpC,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAY,EAAE,QAC3C,OAAO,IAAI,CAAC,OAAO,GAAG,CAAY,IAAI;2CAHnC,OAAO,OAAO,GAAG;;;;;mCAFrB;;;;;;;;;;;;;;;;;;;;;YAiBlB,CAAA,iBAAA,2BAAA,KAAM,IAAI,KAAI,KAAK,IAAI,CAAC,UAAU,KAAK,aAAa,KAAK,IAAI,CAAC,WAAW,KAAK,aAAa,KAAK,IAAI,CAAC,UAAU,KAAK,2BACnH,6LAAC,6IAAA,CAAA,UAAU;gBACT,MAAM;oBACJ,GAAG,KAAK,IAAI;oBACZ,YAAY,KAAK,IAAI,CAAC,UAAU;oBAChC,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC,YAAY,KAAK,IAAI,CAAC,UAAU;gBAClC;gBACA,cAAc;gBACd,kBAAkB;gBAClB,eAAe;gBACf,sBAAsB;gBACtB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;oBAAC;oBAAI;oBAAI;oBAAI;iBAAI;;;;;;;;;;;;AAK5C;GAjMwB;KAAA", "debugId": null}}, {"offset": {"line": 2957, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\r\n  label?: string;\r\n  error?: string;\r\n  helperText?: string;\r\n  required?: boolean;\r\n  options: SelectOption[];\r\n  placeholder?: string;\r\n  className?: string;\r\n  containerClassName?: string;\r\n  onChange?: (value: string) => void;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  required = false,\r\n  options = [],\r\n  placeholder = 'Select an option...',\r\n  className = '',\r\n  containerClassName = '',\r\n  onChange,\r\n  id,\r\n  value,\r\n  ...props\r\n}, ref) => {\r\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\r\n  \r\n  const baseSelectClasses = `\r\n    w-full px-3 py-2 border rounded-md shadow-sm \r\n    focus:outline-none focus:ring-2 focus:ring-offset-2 \r\n    disabled:opacity-50 disabled:cursor-not-allowed\r\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\r\n    transition-colors duration-200\r\n    appearance-none bg-white\r\n    bg-no-repeat bg-right bg-[length:16px_16px]\r\n    pr-10\r\n  `;\r\n  \r\n  const selectClasses = error\r\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\r\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    if (onChange) {\r\n      onChange(e.target.value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-1 ${containerClassName}`}>\r\n      {label && (\r\n        <label \r\n          htmlFor={selectId}\r\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\r\n        >\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <div className=\"relative\">\r\n        <select\r\n          ref={ref}\r\n          id={selectId}\r\n          value={value || ''}\r\n          onChange={handleChange}\r\n          className={`${selectClasses} ${className}`}\r\n          {...props}\r\n        >\r\n          {placeholder && (\r\n            <option value=\"\" disabled>\r\n              {placeholder}\r\n            </option>\r\n          )}\r\n          \r\n          {options.map((option) => (\r\n            <option \r\n              key={option.value} \r\n              value={option.value}\r\n              disabled={option.disabled}\r\n            >\r\n              {option.label}\r\n            </option>\r\n          ))}\r\n        </select>\r\n        \r\n        {/* Custom dropdown arrow */}\r\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\r\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\r\n        </div>\r\n      </div>\r\n      \r\n      {error && (\r\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAkC,QAavD;QAbwD,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ;IACC,MAAM,WAAW,MAAM,AAAC,UAAiD,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAEtE,MAAM,oBAAqB;IAW3B,MAAM,gBAAgB,QAClB,AAAC,GAAoB,OAAlB,mBAAkB,iFACrB,AAAC,GAAoB,OAAlB,mBAAkB;IAEzB,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAA+B,OAAnB;;YAC1B,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,AAAC,GAAmB,OAAjB,eAAc,KAAa,OAAV;wBAC9B,GAAG,KAAK;;4BAER,6BACC,6LAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 3099, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/invoiceService.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { InvoiceFilters, CreateInvoiceDto, UpdateInvoiceDto, InvoiceItem, Invoice } from '@/types/invoice';\r\n\r\n\r\n\r\nclass InvoiceService {\r\n  private baseUrl = '/invoices';\r\n\r\n  async getInvoices(filters?: InvoiceFilters): Promise<Invoice[]> {\r\n    try {\r\n      const response = await apiClient.get(this.baseUrl, { params: filters });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to fetch invoices:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getInvoiceById(id: string): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.get(`${this.baseUrl}/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to fetch invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async getInvoicesByEntity(entityType: string, entityId: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`${this.baseUrl}/entity/${entityType}/${entityId}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to fetch invoices for entity:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async createInvoice(data: CreateInvoiceDto): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.post(this.baseUrl, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to create invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async updateInvoice(id: string, data: UpdateInvoiceDto): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.put(`${this.baseUrl}/${id}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to update invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async deleteInvoice(id: string): Promise<void> {\r\n    try {\r\n      await apiClient.delete(`${this.baseUrl}/${id}`);\r\n    } catch (error) {\r\n      console.error('Failed to delete invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async sendInvoice(id: string): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.post(`${this.baseUrl}/${id}/send`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to send invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async markAsPaid(id: string): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.post(`${this.baseUrl}/${id}/mark-paid`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to mark invoice as paid:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Helper method to generate invoice for application\r\n  async generateApplicationInvoice(applicationId: string, data: {\r\n    amount: number;\r\n    description: string;\r\n    items?: InvoiceItem[];\r\n  }): Promise<Invoice> {\r\n    try {\r\n      const response = await apiClient.post(`${this.baseUrl}/generate/application/${applicationId}`, data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to generate application invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Helper method to get application invoice status\r\n  async getApplicationInvoiceStatus(applicationId: string): Promise<{\r\n    hasInvoice: boolean;\r\n    invoice?: Invoice;\r\n    status?: 'paid' | 'pending' | 'overdue' | 'none';\r\n  }> {\r\n    try {\r\n      const response = await this.getInvoicesByEntity('application', applicationId);\r\n      const invoices: Invoice[]  = response.data || response\r\n      \r\n      if (invoices.length === 0) {\r\n        return { hasInvoice: false, status: 'none' };\r\n      }\r\n\r\n      // Get the most recent invoice\r\n      const sortedInvoices = [...invoices].sort((a, b) =>\r\n        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n      );\r\n      const latestInvoice = sortedInvoices.length > 0 ? sortedInvoices[0] : null;\r\n\r\n      if (!latestInvoice) {\r\n        return { hasInvoice: false, status: 'none' };\r\n      }\r\n\r\n      let status: 'paid' | 'pending' | 'overdue' = 'pending';\r\n      \r\n      if (latestInvoice.status === 'paid') {\r\n        status = 'paid';\r\n      } else if (latestInvoice.status === 'overdue') {\r\n        status = 'overdue';\r\n      } else if (latestInvoice.status === 'sent' || latestInvoice.status === 'draft') {\r\n        // Check if overdue\r\n        const dueDate = new Date(latestInvoice.due_date);\r\n        const now = new Date();\r\n        if (now > dueDate) {\r\n          status = 'overdue';\r\n        } else {\r\n          status = 'pending';\r\n        }\r\n      }\r\n\r\n      return {\r\n        hasInvoice: true,\r\n        invoice: latestInvoice,\r\n        status\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to get application invoice status:', error);\r\n      return { hasInvoice: false, status: 'none' };\r\n    }\r\n  }\r\n\r\n  // Helper method to get application details for invoice generation\r\n  async getApplicationDetailsForInvoice(applicationId: string): Promise<{\r\n    application: any;\r\n    defaultInvoiceData: {\r\n      amount: number;\r\n      description: string;\r\n      items: InvoiceItem[];\r\n    };\r\n  }> {\r\n    try {\r\n      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}/details`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Failed to get application details for invoice:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\nexport const invoiceService = new InvoiceService();\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAQA,MAAM;IAGJ,MAAM,YAAY,OAAwB,EAAsB;QAC9D,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE;gBAAE,QAAQ;YAAQ;YACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,eAAe,EAAU,EAAoB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH;YACxD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,UAAkB,EAAE,QAAgB,EAAgB;QAC5E,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAyB,OAAvB,IAAI,CAAC,OAAO,EAAC,YAAwB,OAAd,YAAW,KAAY,OAAT;YAC7E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,MAAM,cAAc,IAAsB,EAAoB;QAC5D,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACpD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,cAAc,EAAU,EAAE,IAAsB,EAAoB;QACxE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,KAAM;YAC9D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,cAAc,EAAU,EAAiB;QAC7C,IAAI;YACF,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,MAAM,YAAY,EAAU,EAAoB;QAC9C,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,IAAG;YAC5D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,MAAM,WAAW,EAAU,EAAoB;QAC7C,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAM,OAAH,IAAG;YAC5D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,MAAM,2BAA2B,aAAqB,EAAE,IAIvD,EAAoB;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,GAAuC,OAArC,IAAI,CAAC,OAAO,EAAC,0BAAsC,OAAd,gBAAiB;YAC/F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,MAAM,4BAA4B,aAAqB,EAIpD;QACD,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,mBAAmB,CAAC,eAAe;YAC/D,MAAM,WAAuB,SAAS,IAAI,IAAI;YAE9C,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,OAAO;oBAAE,YAAY;oBAAO,QAAQ;gBAAO;YAC7C;YAEA,8BAA8B;YAC9B,MAAM,iBAAiB;mBAAI;aAAS,CAAC,IAAI,CAAC,CAAC,GAAG,IAC5C,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;YAEnE,MAAM,gBAAgB,eAAe,MAAM,GAAG,IAAI,cAAc,CAAC,EAAE,GAAG;YAEtE,IAAI,CAAC,eAAe;gBAClB,OAAO;oBAAE,YAAY;oBAAO,QAAQ;gBAAO;YAC7C;YAEA,IAAI,SAAyC;YAE7C,IAAI,cAAc,MAAM,KAAK,QAAQ;gBACnC,SAAS;YACX,OAAO,IAAI,cAAc,MAAM,KAAK,WAAW;gBAC7C,SAAS;YACX,OAAO,IAAI,cAAc,MAAM,KAAK,UAAU,cAAc,MAAM,KAAK,SAAS;gBAC9E,mBAAmB;gBACnB,MAAM,UAAU,IAAI,KAAK,cAAc,QAAQ;gBAC/C,MAAM,MAAM,IAAI;gBAChB,IAAI,MAAM,SAAS;oBACjB,SAAS;gBACX,OAAO;oBACL,SAAS;gBACX;YACF;YAEA,OAAO;gBACL,YAAY;gBACZ,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE,YAAY;gBAAO,QAAQ;YAAO;QAC7C;IACF;IAEA,kEAAkE;IAClE,MAAM,gCAAgC,aAAqB,EAOxD;QACD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAA8B,OAA5B,IAAI,CAAC,OAAO,EAAC,iBAA6B,OAAd,eAAc;YAClF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;QACR;IACF;;QApKA,+KAAQ,WAAU;;AAqKpB;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 3267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: string): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application with improved error handling\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    try {\r\n      console.log('Updating application:', id, 'with data:', data);\r\n      const response = await apiClient.put(`/applications/${id}`, data, {\r\n        timeout: 30000, // 30 second timeout\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('Error updating application:', error);\r\n\r\n      // Handle specific error cases\r\n      if (error.code === 'ECONNABORTED') {\r\n        throw new Error('Request timeout - please try again');\r\n      }\r\n\r\n      if (error.response?.status === 400) {\r\n        const message = error.response?.data?.message || 'Invalid application data';\r\n        console.error('400 Bad Request details:', error.response?.data);\r\n        throw new Error(`Bad Request: ${message}`);\r\n      }\r\n\r\n      if (error.response?.status === 429) {\r\n        throw new Error('Too many requests - please wait a moment and try again');\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 0 // Start with 0% progress\r\n      });\r\n\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      // Estimate progress based on section name\r\n      const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n      const sectionIndex = sectionOrder.indexOf(sectionName);\r\n      completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n\r\n      // Calculate progress based on completed sections (excluding reviewSubmit from total)\r\n      const totalSections = 6; // Total number of form sections (excluding reviewSubmit)\r\n      const progressPercentage = Math.min(Math.round((completedSections / totalSections) * 100), 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', processApiResponse(response));\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications (filtered by authenticated user)\r\n  async getUserApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filter?: Record<string, string>;\r\n  }): Promise<any> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.search) queryParams.append('search', params.search);\r\n      if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n      if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n\r\n      // Add filters\r\n      if (params?.filter) {\r\n        Object.entries(params.filter).forEach(([key, value]) => {\r\n          if (value) queryParams.append(`filter.${key}`, value);\r\n        });\r\n      }\r\n\r\n      const url = `/applications/user-applications${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await apiClient.get(url);\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Return paginated response structure for DataTable compatibility\r\n      if (processedResponse?.data && processedResponse?.meta) {\r\n        return processedResponse;\r\n      }\r\n\r\n      // Handle legacy non-paginated response\r\n      let applications = [];\r\n      if (processedResponse?.data) {\r\n        applications = Array.isArray(processedResponse.data) ? processedResponse.data : [];\r\n      } else if (Array.isArray(processedResponse)) {\r\n        applications = processedResponse;\r\n      } else if (processedResponse) {\r\n        applications = [processedResponse];\r\n      }\r\n\r\n      // Return in paginated format for backward compatibility\r\n      return {\r\n        data: applications,\r\n        meta: {\r\n          itemsPerPage: params?.limit || 10,\r\n          totalItems: applications.length,\r\n          currentPage: params?.page || 1,\r\n          totalPages: Math.ceil(applications.length / (params?.limit || 10)),\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: params?.search || '',\r\n          select: [],\r\n        },\r\n        links: { current: '' },\r\n      };\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get data from entity-specific APIs for validation\r\n      let formData: Record<string, any> = {};\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application status\r\n  async updateStatus(applicationId: string, status: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/status`, { status });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error updating application status:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Assign application to an officer\r\n  async assignApplication(applicationId: string, assignedTo: string): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.patch(`/applications/${applicationId}/assign`, { assignedTo });\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('Error assigning application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;YAUK,iBAGA,kBAGA;QAfJ,MAAM,cAAc,IAAI;QAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,mBAAA,8BAAA,kBAAA,OAAQ,OAAO,cAAf,sCAAA,gBAAiB,aAAa,EAAE;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,mBAAA,8BAAA,mBAAA,OAAQ,OAAO,cAAf,uCAAA,iBAAiB,iBAAiB,EAAE;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,mBAAA,8BAAA,mBAAA,OAAQ,OAAO,cAAf,uCAAA,iBAAiB,MAAM,EAAE;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAuC,OAAvB,YAAY,QAAQ;QAC1E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAuC,OAAvB,YAAY,QAAQ;QAC1E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAmB,OAAH;QACtD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,8BAAyC,OAAZ;QACnE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAc;QAC1C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,2BAAiC,OAAP;QAChE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAc;QACtD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAoC,OAApB,IAAG,mBAAwB,OAAP;QAC1E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAClC,AAAC,iBAA2C,OAA3B,IAAG,0BAA0D,OAAlC,aAAY,wBAAyC,OAAnB;QAEhF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,kDAAkD;IAClD,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,yBAAyB,IAAI,cAAc;YACvD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAAmB,OAAH,KAAM,MAAM;gBAChE,SAAS;YACX;YACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;gBAQf,iBAMA;YAbJ,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,8BAA8B;YAC9B,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;oBAClB,sBAAA,kBAC0B;gBAD1C,MAAM,UAAU,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;gBACjD,QAAQ,KAAK,CAAC,6BAA4B,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,IAAI;gBAC9D,MAAM,IAAI,MAAM,AAAC,gBAAuB,OAAR;YAClC;YAEA,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,iBAAmB,OAAH;QACzD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,AAAC,OAAiB,OAAX,SAAQ,KAAc,OAAX,SAAQ,KAAa,OAAV;YAEvD,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,AAAC,2BAAuC,OAAb,KAAK,OAAO,EAAC;YAC1D;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,EAAE,yBAAyB;YAClD;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,0CAA0C;YAC1C,MAAM,eAAe;gBAAC;gBAAiB;gBAAkB;gBAAgB;gBAAgB;gBAAgB;gBAAgB;aAAe;YACxI,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAE3D,qFAAqF;YACrF,MAAM,gBAAgB,GAAG,yDAAyD;YAClF,MAAM,qBAAqB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB,MAAM;YAE3F,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAA0C,OAA1B,eAAc,cAAwB,OAAZ;YAChF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAA8B,OAAd,gBAAiB;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,2DAA2D;IAC3D,MAAM,qBAAoB,MAOzB;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAC9D,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAC9D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;YAEvE,cAAc;YACd,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE;gBAClB,OAAO,OAAO,CAAC,OAAO,MAAM,EAAE,OAAO,CAAC;wBAAC,CAAC,KAAK,MAAM;oBACjD,IAAI,OAAO,YAAY,MAAM,CAAC,AAAC,UAAa,OAAJ,MAAO;gBACjD;YACF;YAEA,MAAM,MAAM,AAAC,kCAA4F,OAA3D,YAAY,QAAQ,KAAK,AAAC,IAA0B,OAAvB,YAAY,QAAQ,MAAO;YACtG,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,kEAAkE;YAClE,IAAI,CAAA,8BAAA,wCAAA,kBAAmB,IAAI,MAAI,8BAAA,wCAAA,kBAAmB,IAAI,GAAE;gBACtD,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,8BAAA,wCAAA,kBAAmB,IAAI,EAAE;gBAC3B,eAAe,MAAM,OAAO,CAAC,kBAAkB,IAAI,IAAI,kBAAkB,IAAI,GAAG,EAAE;YACpF,OAAO,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBAC3C,eAAe;YACjB,OAAO,IAAI,mBAAmB;gBAC5B,eAAe;oBAAC;iBAAkB;YACpC;YAEA,wDAAwD;YACxD,OAAO;gBACL,MAAM;gBACN,MAAM;oBACJ,cAAc,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI;oBAC/B,YAAY,aAAa,MAAM;oBAC/B,aAAa,CAAA,mBAAA,6BAAA,OAAQ,IAAI,KAAI;oBAC7B,YAAY,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG,CAAC,CAAA,mBAAA,6BAAA,OAAQ,KAAK,KAAI,EAAE;oBAChE,QAAQ,EAAE;oBACV,UAAU,EAAE;oBACZ,QAAQ,CAAA,mBAAA,6BAAA,OAAQ,MAAM,KAAI;oBAC1B,QAAQ,EAAE;gBACZ;gBACA,OAAO;oBAAE,SAAS;gBAAG;YACvB;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,iBAA8B,OAAd,gBAAiB;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,oDAAoD;YACpD,IAAI,WAAgC,CAAC;YAErC,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,AAAC,GAAU,OAAR,SAAQ;gBACzB;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,cAAa,aAAqB,EAAE,MAAc;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,AAAC,iBAA8B,OAAd,eAAc,YAAU;gBAAE;YAAO;YACzF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,mCAAmC;IACnC,MAAM,mBAAkB,aAAqB,EAAE,UAAkB;QAC/D,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,AAAC,iBAA8B,OAAd,eAAc,YAAU;gBAAE;YAAW;YAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 3593, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/paymentService.ts"], "sourcesContent": ["import { customerApi } from '@/lib/customer-api';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { invoiceService } from './invoiceService';\r\nimport { applicationService } from './applicationService';\r\nimport { Payment, PaymentFilters, PaymentStatistics } from '@/types/invoice';\r\nimport { PaginatedResponse, PaginateQuery } from '@/types';\r\nimport { apiClient } from '@/lib';\r\n\r\nclass PaymentService {\r\n\r\n\r\n  // Helper method to get user application IDs\r\n  private async getUserApplicationIds(): Promise<string[]> {\r\n    try {\r\n      console.log('📋 Fetching user applications...');\r\n      const applicationsPromise = applicationService.getUserApplications();\r\n      const timeoutPromise = new Promise((_, reject) =>\r\n        setTimeout(() => reject(new Error('Applications fetch timeout')), 10000)\r\n      );\r\n\r\n      const userApplications = await Promise.race([applicationsPromise, timeoutPromise]);\r\n      const applicationIds = userApplications.data?.map((app: any) => app.application_id) || [];\r\n      console.log(`📋 Found ${applicationIds.length} user applications`);\r\n      return applicationIds;\r\n    } catch (error) {\r\n      console.warn('Failed to fetch user applications:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // Helper method to create empty response\r\n  private createEmptyResponse(query: PaginateQuery & PaymentFilters): PaginatedResponse<Payment> {\r\n    return {\r\n      data: [],\r\n      meta: {\r\n        itemsPerPage: query.limit || 10,\r\n        totalItems: 0,\r\n        currentPage: query.page || 1,\r\n        totalPages: 0,\r\n        sortBy: [],\r\n        searchBy: [],\r\n        search: query.search || '',\r\n        select: [],\r\n      },\r\n      links: { current: '' },\r\n    };\r\n  }\r\n\r\n  // Helper method to apply filters\r\n  private applyFilters(payments: any[], query: PaginateQuery & PaymentFilters): any[] {\r\n    let filteredPayments = payments;\r\n\r\n    if (query.status) {\r\n      const allowedStatuses = query.status.split(',').map((s: string) => s.trim().toUpperCase());\r\n      filteredPayments = filteredPayments.filter(payment =>\r\n        allowedStatuses.includes(payment.status.toUpperCase())\r\n      );\r\n    }\r\n\r\n    if (query.payment_type) {\r\n      filteredPayments = filteredPayments.filter(payment =>\r\n        payment.payment_type === query.payment_type\r\n      );\r\n    }\r\n\r\n    if (query.search) {\r\n      const searchTerm = query.search.toLowerCase();\r\n      filteredPayments = filteredPayments.filter(payment =>\r\n        payment.invoice_number?.toLowerCase().includes(searchTerm) ||\r\n        payment.description?.toLowerCase().includes(searchTerm) ||\r\n        payment.payment_type?.toLowerCase().includes(searchTerm)\r\n      );\r\n    }\r\n\r\n    if (query.dateRange && typeof query.dateRange === 'object') {\r\n      const { start, end } = query.dateRange as { start?: string; end?: string };\r\n      if (start || end) {\r\n        filteredPayments = filteredPayments.filter(payment => {\r\n          const paymentDate = new Date(payment.created_at);\r\n          if (start && paymentDate < new Date(start)) return false;\r\n          if (end && paymentDate > new Date(end)) return false;\r\n          return true;\r\n        });\r\n      }\r\n    }\r\n\r\n    return filteredPayments;\r\n  }\r\n\r\n\r\n  // Helper method to get payments for applications\r\n  private async getPaymentsForApplications(applicationIds: string[]): Promise<any[]> {\r\n    const allPayments: any[] = [];\r\n\r\n    for (const applicationId of applicationIds) {\r\n      try {\r\n        console.log(`📄 Getting invoices for application: ${applicationId}`);\r\n        const response = await invoiceService.getInvoicesByEntity('application', applicationId);\r\n        const invoices = processApiResponse(response);\r\n        return invoices;\r\n      } catch (error) {\r\n        console.warn(`⚠️ Could not get invoices for application ${applicationId}:`, error);\r\n      }\r\n    }\r\n\r\n    return allPayments;\r\n  }\r\n  async getPayments(query: PaginateQuery & PaymentFilters, isCustomer?: boolean): Promise<PaginatedResponse<Payment>> {\r\n    try {\r\n      const params = this.buildQueryParams(query);\r\n      if (isCustomer) {\r\n        // Use customer-specific endpoint for customers\r\n        console.log('🔒 Using customer-specific payments endpoint');\r\n        return customerApi.getPayments(params);\r\n      } else {\r\n        // Use general payments endpoint for admin/staff (shows all payments)\r\n        const response = await apiClient.get('/payments', { params });\r\n        return processApiResponse(response);\r\n      }\r\n    } catch (error: any) {\r\n      // Handle specific error cases\r\n      if (error.response?.status === 403) {\r\n        return await this.getPaymentsByApplications(query);\r\n      }\r\n\r\n      if (error.response?.status === 404) {\r\n        return await this.getPaymentsByApplications(query);\r\n      }\r\n      return await this.getPaymentsByApplications(query);\r\n    }\r\n  }\r\n\r\n  // Helper method to build query parameters\r\n  private buildQueryParams(query: PaginateQuery & PaymentFilters): any {\r\n    const params: any = {};\r\n    if (query.page) params.page = query.page;\r\n    if (query.limit) params.limit = query.limit;\r\n    if (query.status) params.status = query.status;\r\n    if (query.payment_type) params.paymentType = query.payment_type;\r\n    if (query.search) params.search = query.search;\r\n    if (query.dateRange) params.dateRange = query.dateRange;\r\n    return params;\r\n  }\r\n\r\n  private async getPaymentsByApplications(query: PaginateQuery & PaymentFilters): Promise<PaginatedResponse<Payment>> {\r\n    try {\r\n      // Get user's applications to filter payments\r\n      const applicationIds = await this.getUserApplicationIds();\r\n\r\n      if (applicationIds.length === 0) {\r\n        console.log('ℹ️ No applications found for user, returning empty payments');\r\n        return this.createEmptyResponse(query);\r\n      }\r\n\r\n      // Get payments for user's applications\r\n      const allPayments = await this.getPaymentsForApplications(applicationIds);\r\n      console.log(`📊 Total payments found: ${allPayments.length}`);\r\n      return processApiResponse(allPayments);\r\n    } catch (error) {\r\n      console.error('❌ Error getting user payments:', error);\r\n      // Return empty data if API is not available\r\n      return this.createEmptyResponse(query);\r\n    }\r\n  }\r\n\r\n  async getInvoices(query: PaginateQuery & PaymentFilters, isCustomer: boolean = false): Promise<PaginatedResponse<Payment>> {\r\n\r\n\r\n    try {\r\n      const params: any = {};\r\n      if (query.page) params.page = query.page;\r\n      if (query.limit) params.limit = query.limit;\r\n      // For invoices, we want pending and overdue statuses\r\n      params.status = query.status || 'pending';\r\n      if (query.search) params.search = query.search;\r\n      if (isCustomer) {\r\n        // Use customer-specific endpoint for customers\r\n        return await customerApi.getInvoices(params);\r\n      } else {\r\n        // Use general invoices endpoint for admin/staff (shows all invoices)\r\n        const response = await apiClient.get('/invoices', { params });\r\n        return processApiResponse(response);\r\n      }\r\n    } catch (apiError: any) {\r\n      console.error('❌ Error in getInvoices:', apiError);\r\n\r\n      // Check if it's a permission error (403) or other API error\r\n      if (apiError?.response?.status === 403) {\r\n        console.warn('⚠️ Permission denied for invoices API, using application-based approach');\r\n      }\r\n      // Fallback: get invoices by applications\r\n      return await this.getInvoicesByApplications(query);\r\n    }\r\n  }\r\n\r\n  private async getInvoicesByApplications(query: PaginateQuery & PaymentFilters): Promise<PaginatedResponse<Payment>> {\r\n    try {\r\n      // Use the existing getInvoices endpoint which should handle user filtering on the backend\r\n      const params = this.buildQueryParams(query);\r\n      // Set default status for invoices if not specified\r\n      if (!params.status) {\r\n        params.status = 'draft,pending,overdue';\r\n      }\r\n\r\n      const response = await customerApi.getInvoices(params);\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Return the processed response directly - backend should handle pagination and filtering\r\n      return processedResponse;\r\n    } catch (error) {\r\n      console.error('PaymentService.getInvoicesByApplications error:', error);\r\n      return this.createEmptyResponse(query);\r\n    }\r\n  }\r\n\r\n  async getPaymentById(id: string): Promise<Payment> {\r\n    try {\r\n      const response = await apiClient.get(`/payments/${id}`);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('PaymentService.getPaymentById error:', error);\r\n      throw new Error('Payment not found');\r\n    }\r\n  }\r\n\r\n  async getPaymentStatistics(isCustomer?: boolean): Promise<PaymentStatistics | null> {\r\n    try {\r\n      try {\r\n        let response;\r\n        if (isCustomer) {\r\n          // Use customer-specific endpoint for customers\r\n          response = await customerApi.getPaymentStatistics();\r\n        } else {\r\n          // Use general statistics endpoint for admin/staff (all statistics)\r\n          console.log('👨‍💼 Using admin/staff statistics endpoint (all statistics)');\r\n          response = await apiClient.get('/payments/statistics');\r\n        }\r\n\r\n        const processedResponse = processApiResponse(response).data;\r\n\r\n        // Transform backend response to our format\r\n        return {\r\n          total: processedResponse.totalPayments || 0,\r\n          pending: processedResponse.pendingPayments || 0,\r\n          paid: processedResponse.paidPayments || 0,\r\n          overdue: processedResponse.overduePayments || 0,\r\n          cancelled: processedResponse.cancelledPayments || 0,\r\n          totalAmount: processedResponse.totalAmount || 0,\r\n          pendingAmount: processedResponse.pendingAmount || 0,\r\n          paidAmount: processedResponse.paidAmount || 0,\r\n          overdueAmount: processedResponse.overdueAmount || 0,\r\n        };\r\n      } catch (apiError: any) {\r\n        console.warn('⚠️ Backend statistics API failed:', apiError);\r\n\r\n        // Check if it's a permission error (403) or other API error\r\n        if (apiError?.response?.status === 403) {\r\n          console.warn('⚠️ Permission denied for statistics API');\r\n        }\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error calculating payment statistics:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n\r\n  async getPaymentsByEntity(_entityType: string, _entityId: string, query: PaginateQuery): Promise<PaginatedResponse<Payment>| null> {\r\n    try {\r\n      // Customer API doesn't have entity-specific payments, so we'll filter from all payments\r\n      const params: any = {};\r\n      if (query.page) params.page = query.page;\r\n      if (query.limit) params.limit = query.limit;\r\n      const response = await customerApi.getPayments(params);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('PaymentService.getPaymentsByEntity error:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  async updatePayment(_id: string, _data: Partial<Payment>): Promise<Payment> {\r\n    try {\r\n      // Customer API doesn't have update payment method, so we'll throw an error\r\n      throw new Error('Payment updates not supported in customer portal');\r\n    } catch (error) {\r\n      throw new Error('Payment updates not supported in customer portal');\r\n    }\r\n  }\r\n\r\n  async createPayment(data: Omit<Payment, 'payment_id' | 'created_at' | 'updated_at'>): Promise<Payment> {\r\n    try {\r\n      const paymentData = {\r\n        amount: data.amount,\r\n        currency: data.currency,\r\n        dueDate: data.due_date,\r\n        issueDate: data.issue_date,\r\n        description: data.description,\r\n        paymentType: data.payment_type.replace('_', ' '),\r\n        clientName: data.user?.first_name + ' ' + data.user?.last_name || 'Customer',\r\n        clientEmail: data.user?.email || '<EMAIL>',\r\n        paymentMethod: data.payment_method,\r\n        notes: data.notes,\r\n      };\r\n\r\n      const response = await customerApi.createPayment(paymentData);\r\n      const processedResponse = processApiResponse(response);\r\n\r\n      // Transform response back to our format\r\n      return processedResponse;\r\n    } catch (error) {\r\n      throw new Error('Failed to create payment');\r\n    }\r\n  }\r\n\r\n  async deletePayment(_id: string): Promise<void> {\r\n    throw new Error('Payment deletion not supported in customer portal');\r\n  }\r\n\r\n  // Proof of payment upload\r\n  async uploadProofOfPayment(paymentId: string, file: File, data: any): Promise<any> {\r\n    try {\r\n      const formData = new FormData();\r\n\r\n      formData.append('file', file);\r\n      Object.keys(data).forEach(key => {\r\n        formData.append(key, data[key]);\r\n      });\r\n\r\n      const response = await customerApi.uploadProofOfPayment(paymentId, formData);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.log('Proof of payment API not available, simulating upload');\r\n\r\n      // Fallback to simulation if API is not available\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n\r\n      return {\r\n        success: true,\r\n        message: 'Proof of payment uploaded successfully',\r\n        uploadId: `upload_${Date.now()}`,\r\n        status: 'pending_review'\r\n      };\r\n    }\r\n  }\r\n\r\n  private mapInvoiceTypeToPaymentType(entityType: string): string {\r\n    switch (entityType) {\r\n      case 'application': return 'APPLICATION_FEE';\r\n      case 'license': return 'LICENSE_FEE';\r\n      case 'renewal': return 'RENEWAL_FEE';\r\n      case 'procurement': return 'PROCUREMENT_FEE';\r\n      default: return 'LICENSE_FEE';\r\n    }\r\n  }\r\n\r\n\r\n  async getInvoicePayments(invoiceId: string): Promise<any> {\r\n    try {\r\n      const response = await customerApi.getInvoicePayments(invoiceId);\r\n      const processedResponse = processApiResponse(response);\r\n      return {\r\n        success: true,\r\n        data: processedResponse.data || processedResponse,\r\n        message: 'Invoice payments retrieved successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('PaymentService.getInvoicePayments error:', error);\r\n      return {\r\n        success: false,\r\n        data: [],\r\n        message: 'Failed to get invoice payments'\r\n      };\r\n    }\r\n  }\r\n\r\n  async approvePayment(paymentId: string): Promise<any> {\r\n    try {\r\n      // This would be an admin/staff endpoint to approve payments\r\n      const response = await fetch(`/api/payments/${paymentId}/approve`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to approve payment');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return {\r\n        success: true,\r\n        data,\r\n        message: 'Payment approved successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error approving payment:', error);\r\n      return {\r\n        success: false,\r\n        data: null,\r\n        message: error instanceof Error ? error.message : 'Failed to approve payment'\r\n      };\r\n    }\r\n  }\r\n\r\n  async rejectPayment(paymentId: string, reason: string): Promise<any> {\r\n    try {\r\n      // This would be an admin/staff endpoint to reject payments\r\n      const response = await fetch(`/api/payments/${paymentId}/reject`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ reason })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to reject payment');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return {\r\n        success: true,\r\n        data,\r\n        message: 'Payment rejected successfully'\r\n      };\r\n    } catch (error) {\r\n      console.error('Error rejecting payment:', error);\r\n      return {\r\n        success: false,\r\n        data: null,\r\n        message: error instanceof Error ? error.message : 'Failed to reject payment'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nexport const paymentService = new PaymentService();\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAGA;AAAA;;;;;;AAEA,MAAM;IAGJ,4CAA4C;IAC5C,MAAc,wBAA2C;QACvD,IAAI;gBAQqB;YAPvB,QAAQ,GAAG,CAAC;YACZ,MAAM,sBAAsB,wIAAA,CAAA,qBAAkB,CAAC,mBAAmB;YAClE,MAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,SACrC,WAAW,IAAM,OAAO,IAAI,MAAM,gCAAgC;YAGpE,MAAM,mBAAmB,MAAM,QAAQ,IAAI,CAAC;gBAAC;gBAAqB;aAAe;YACjF,MAAM,iBAAiB,EAAA,yBAAA,iBAAiB,IAAI,cAArB,6CAAA,uBAAuB,GAAG,CAAC,CAAC,MAAa,IAAI,cAAc,MAAK,EAAE;YACzF,QAAQ,GAAG,CAAC,AAAC,YAAiC,OAAtB,eAAe,MAAM,EAAC;YAC9C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,sCAAsC;YACnD,OAAO,EAAE;QACX;IACF;IAEA,yCAAyC;IACjC,oBAAoB,KAAqC,EAA8B;QAC7F,OAAO;YACL,MAAM,EAAE;YACR,MAAM;gBACJ,cAAc,MAAM,KAAK,IAAI;gBAC7B,YAAY;gBACZ,aAAa,MAAM,IAAI,IAAI;gBAC3B,YAAY;gBACZ,QAAQ,EAAE;gBACV,UAAU,EAAE;gBACZ,QAAQ,MAAM,MAAM,IAAI;gBACxB,QAAQ,EAAE;YACZ;YACA,OAAO;gBAAE,SAAS;YAAG;QACvB;IACF;IAEA,iCAAiC;IACzB,aAAa,QAAe,EAAE,KAAqC,EAAS;QAClF,IAAI,mBAAmB;QAEvB,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,kBAAkB,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,EAAE,IAAI,GAAG,WAAW;YACvF,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,gBAAgB,QAAQ,CAAC,QAAQ,MAAM,CAAC,WAAW;QAEvD;QAEA,IAAI,MAAM,YAAY,EAAE;YACtB,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,YAAY,KAAK,MAAM,YAAY;QAE/C;QAEA,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,aAAa,MAAM,MAAM,CAAC,WAAW;YAC3C,mBAAmB,iBAAiB,MAAM,CAAC,CAAA;oBACzC,yBACA,sBACA;uBAFA,EAAA,0BAAA,QAAQ,cAAc,cAAtB,8CAAA,wBAAwB,WAAW,GAAG,QAAQ,CAAC,kBAC/C,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,qBAAqB,WAAW,GAAG,QAAQ,CAAC,kBAC5C,wBAAA,QAAQ,YAAY,cAApB,4CAAA,sBAAsB,WAAW,GAAG,QAAQ,CAAC;;QAEjD;QAEA,IAAI,MAAM,SAAS,IAAI,OAAO,MAAM,SAAS,KAAK,UAAU;YAC1D,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,MAAM,SAAS;YACtC,IAAI,SAAS,KAAK;gBAChB,mBAAmB,iBAAiB,MAAM,CAAC,CAAA;oBACzC,MAAM,cAAc,IAAI,KAAK,QAAQ,UAAU;oBAC/C,IAAI,SAAS,cAAc,IAAI,KAAK,QAAQ,OAAO;oBACnD,IAAI,OAAO,cAAc,IAAI,KAAK,MAAM,OAAO;oBAC/C,OAAO;gBACT;YACF;QACF;QAEA,OAAO;IACT;IAGA,iDAAiD;IACjD,MAAc,2BAA2B,cAAwB,EAAkB;QACjF,MAAM,cAAqB,EAAE;QAE7B,KAAK,MAAM,iBAAiB,eAAgB;YAC1C,IAAI;gBACF,QAAQ,GAAG,CAAC,AAAC,wCAAqD,OAAd;gBACpD,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,eAAe;gBACzE,MAAM,WAAW,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;gBACpC,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,AAAC,6CAA0D,OAAd,eAAc,MAAI;YAC9E;QACF;QAEA,OAAO;IACT;IACA,MAAM,YAAY,KAAqC,EAAE,UAAoB,EAAuC;QAClH,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC;YACrC,IAAI,YAAY;gBACd,+CAA+C;gBAC/C,QAAQ,GAAG,CAAC;gBACZ,OAAO,gIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YACjC,OAAO;gBACL,qEAAqE;gBACrE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,aAAa;oBAAE;gBAAO;gBAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B;QACF,EAAE,OAAO,OAAY;gBAEf,iBAIA;YALJ,8BAA8B;YAC9B,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;YAC9C;YAEA,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBAClC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;YAC9C;YACA,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;QAC9C;IACF;IAEA,0CAA0C;IAClC,iBAAiB,KAAqC,EAAO;QACnE,MAAM,SAAc,CAAC;QACrB,IAAI,MAAM,IAAI,EAAE,OAAO,IAAI,GAAG,MAAM,IAAI;QACxC,IAAI,MAAM,KAAK,EAAE,OAAO,KAAK,GAAG,MAAM,KAAK;QAC3C,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAM,MAAM;QAC9C,IAAI,MAAM,YAAY,EAAE,OAAO,WAAW,GAAG,MAAM,YAAY;QAC/D,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAM,MAAM;QAC9C,IAAI,MAAM,SAAS,EAAE,OAAO,SAAS,GAAG,MAAM,SAAS;QACvD,OAAO;IACT;IAEA,MAAc,0BAA0B,KAAqC,EAAuC;QAClH,IAAI;YACF,6CAA6C;YAC7C,MAAM,iBAAiB,MAAM,IAAI,CAAC,qBAAqB;YAEvD,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAClC;YAEA,uCAAuC;YACvC,MAAM,cAAc,MAAM,IAAI,CAAC,0BAA0B,CAAC;YAC1D,QAAQ,GAAG,CAAC,AAAC,4BAA8C,OAAnB,YAAY,MAAM;YAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,4CAA4C;YAC5C,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC;IACF;IAEA,MAAM,YAAY,KAAqC,EAAoE;YAAlE,aAAA,iEAAsB;QAG7E,IAAI;YACF,MAAM,SAAc,CAAC;YACrB,IAAI,MAAM,IAAI,EAAE,OAAO,IAAI,GAAG,MAAM,IAAI;YACxC,IAAI,MAAM,KAAK,EAAE,OAAO,KAAK,GAAG,MAAM,KAAK;YAC3C,qDAAqD;YACrD,OAAO,MAAM,GAAG,MAAM,MAAM,IAAI;YAChC,IAAI,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAM,MAAM;YAC9C,IAAI,YAAY;gBACd,+CAA+C;gBAC/C,OAAO,MAAM,gIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YACvC,OAAO;gBACL,qEAAqE;gBACrE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,aAAa;oBAAE;gBAAO;gBAC3D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC5B;QACF,EAAE,OAAO,UAAe;gBAIlB;YAHJ,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,4DAA4D;YAC5D,IAAI,CAAA,qBAAA,gCAAA,qBAAA,SAAU,QAAQ,cAAlB,yCAAA,mBAAoB,MAAM,MAAK,KAAK;gBACtC,QAAQ,IAAI,CAAC;YACf;YACA,yCAAyC;YACzC,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC;QAC9C;IACF;IAEA,MAAc,0BAA0B,KAAqC,EAAuC;QAClH,IAAI;YACF,0FAA0F;YAC1F,MAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC;YACrC,mDAAmD;YACnD,IAAI,CAAC,OAAO,MAAM,EAAE;gBAClB,OAAO,MAAM,GAAG;YAClB;YAEA,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YAC/C,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,0FAA0F;YAC1F,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,OAAO,IAAI,CAAC,mBAAmB,CAAC;QAClC;IACF;IAEA,MAAM,eAAe,EAAU,EAAoB;QACjD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAe,OAAH;YAClD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,qBAAqB,UAAoB,EAAqC;QAClF,IAAI;YACF,IAAI;gBACF,IAAI;gBACJ,IAAI,YAAY;oBACd,+CAA+C;oBAC/C,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,oBAAoB;gBACnD,OAAO;oBACL,mEAAmE;oBACnE,QAAQ,GAAG,CAAC;oBACZ,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;gBACjC;gBAEA,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;gBAE3D,2CAA2C;gBAC3C,OAAO;oBACL,OAAO,kBAAkB,aAAa,IAAI;oBAC1C,SAAS,kBAAkB,eAAe,IAAI;oBAC9C,MAAM,kBAAkB,YAAY,IAAI;oBACxC,SAAS,kBAAkB,eAAe,IAAI;oBAC9C,WAAW,kBAAkB,iBAAiB,IAAI;oBAClD,aAAa,kBAAkB,WAAW,IAAI;oBAC9C,eAAe,kBAAkB,aAAa,IAAI;oBAClD,YAAY,kBAAkB,UAAU,IAAI;oBAC5C,eAAe,kBAAkB,aAAa,IAAI;gBACpD;YACF,EAAE,OAAO,UAAe;oBAIlB;gBAHJ,QAAQ,IAAI,CAAC,qCAAqC;gBAElD,4DAA4D;gBAC5D,IAAI,CAAA,qBAAA,gCAAA,qBAAA,SAAU,QAAQ,cAAlB,yCAAA,mBAAoB,MAAM,MAAK,KAAK;oBACtC,QAAQ,IAAI,CAAC;gBACf;gBACA,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;QACT;IACF;IAGA,MAAM,oBAAoB,WAAmB,EAAE,SAAiB,EAAE,KAAoB,EAA6C;QACjI,IAAI;YACF,wFAAwF;YACxF,MAAM,SAAc,CAAC;YACrB,IAAI,MAAM,IAAI,EAAE,OAAO,IAAI,GAAG,MAAM,IAAI;YACxC,IAAI,MAAM,KAAK,EAAE,OAAO,KAAK,GAAG,MAAM,KAAK;YAC3C,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,WAAW,CAAC;YAC/C,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;QACT;IACF;IAEA,MAAM,cAAc,GAAW,EAAE,KAAuB,EAAoB;QAC1E,IAAI;YACF,2EAA2E;YAC3E,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,cAAc,IAA+D,EAAoB;QACrG,IAAI;gBAQY,YAA8B,aAC7B;YARf,MAAM,cAAc;gBAClB,QAAQ,KAAK,MAAM;gBACnB,UAAU,KAAK,QAAQ;gBACvB,SAAS,KAAK,QAAQ;gBACtB,WAAW,KAAK,UAAU;gBAC1B,aAAa,KAAK,WAAW;gBAC7B,aAAa,KAAK,YAAY,CAAC,OAAO,CAAC,KAAK;gBAC5C,YAAY,EAAA,aAAA,KAAK,IAAI,cAAT,iCAAA,WAAW,UAAU,IAAG,QAAM,cAAA,KAAK,IAAI,cAAT,kCAAA,YAAW,SAAS,KAAI;gBAClE,aAAa,EAAA,cAAA,KAAK,IAAI,cAAT,kCAAA,YAAW,KAAK,KAAI;gBACjC,eAAe,KAAK,cAAc;gBAClC,OAAO,KAAK,KAAK;YACnB;YAEA,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;YACjD,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,wCAAwC;YACxC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,cAAc,GAAW,EAAiB;QAC9C,MAAM,IAAI,MAAM;IAClB;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB,SAAiB,EAAE,IAAU,EAAE,IAAS,EAAgB;QACjF,IAAI;YACF,MAAM,WAAW,IAAI;YAErB,SAAS,MAAM,CAAC,QAAQ;YACxB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;gBACxB,SAAS,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI;YAChC;YAEA,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC,WAAW;YACnE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YAEZ,iDAAiD;YACjD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,UAAU,AAAC,UAAoB,OAAX,KAAK,GAAG;gBAC5B,QAAQ;YACV;QACF;IACF;IAEQ,4BAA4B,UAAkB,EAAU;QAC9D,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAGA,MAAM,mBAAmB,SAAiB,EAAgB;QACxD,IAAI;YACF,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;YACtD,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAC7C,OAAO;gBACL,SAAS;gBACT,MAAM,kBAAkB,IAAI,IAAI;gBAChC,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,OAAO;gBACL,SAAS;gBACT,MAAM,EAAE;gBACR,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe,SAAiB,EAAgB;QACpD,IAAI;YACF,4DAA4D;YAC5D,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA0B,OAAV,WAAU,aAAW;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAuC,OAA9B,aAAa,OAAO,CAAC;gBAClD;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;gBACA,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;IAEA,MAAM,cAAc,SAAiB,EAAE,MAAc,EAAgB;QACnE,IAAI;YACF,2DAA2D;YAC3D,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA0B,OAAV,WAAU,YAAU;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,UAAuC,OAA9B,aAAa,OAAO,CAAC;gBAClD;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;gBACA,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF;IACF;AACF;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 4012, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formatters.ts"], "sourcesContent": ["/**\r\n * Formats a number as currency with commas for better readability\r\n * For numbers over 5 digits, adds a comma after the first 2 figures\r\n * \r\n * @param amount - The amount to format\r\n * @param currency - The currency code (e.g., 'MWK', 'USD')\r\n * @param minimumFractionDigits - Minimum number of decimal places (default: 0)\r\n * @returns Formatted currency string\r\n */\r\nexport const formatCurrency = (\r\n  amount: number | string,\r\n  currency: string = 'USD',\r\n  minimumFractionDigits: number = 0\r\n): string => {\r\n  // Convert string to number if needed\r\n  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n\r\n  if(currency == '$') currency = 'USD';\r\n  \r\n  // Get the currency symbol\r\n  const formatter = new Intl.NumberFormat('en-MW', {\r\n    style: 'currency',\r\n    currency: currency,\r\n    minimumFractionDigits: minimumFractionDigits,\r\n    useGrouping: false, // We'll handle grouping manually\r\n  });\r\n  \r\n  // Format without grouping to get the base string\r\n  const formatted = formatter.format(numericAmount);\r\n  \r\n  // Extract the numeric part (remove currency symbol and any spaces)\r\n  const parts = formatted.match(/([^\\d]*)(\\d+(?:\\.\\d+)?)(.*)/);\r\n  if (!parts) return formatted;\r\n  \r\n  const [, prefix, numericPart, suffix] = parts;\r\n  \r\n  // Format the number with custom grouping\r\n  let formattedNumber = numericPart;\r\n  \r\n  // For numbers with 5 or more digits, we want to ensure there's a comma after the first 2 figures\r\n  if (numericPart.replace(/\\D/g, '').length >= 5) {\r\n    // Split the integer and decimal parts\r\n    const [integerPart, decimalPart] = numericPart.split('.');\r\n    \r\n    // Format the integer part with commas\r\n    // First, add a comma after the first 2 digits\r\n    let formattedInteger = integerPart.slice(0, 2) + ',' + integerPart.slice(2);\r\n    \r\n    // Then add commas for the rest of the number every 3 digits\r\n    formattedInteger = formattedInteger.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    \r\n    // Combine the parts back\r\n    formattedNumber = formattedInteger + (decimalPart ? '.' + decimalPart : '');\r\n  } else {\r\n    // For smaller numbers, use standard grouping (every 3 digits)\r\n    formattedNumber = numericPart.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n  }\r\n  \r\n  // Combine everything back\r\n  return prefix + formattedNumber + suffix;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED AMOUNT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format amount with currency (alternative to formatCurrency for consistency)\r\n * @param amount - The amount to format\r\n * @param currency - Currency code (default: '$')\r\n * @param locale - Locale for formatting (default: 'en-US')\r\n */\r\nexport const formatAmount = (\r\n  amount: number | string,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return `$ 0.00`;\r\n\r\n  return `${currency} ${numAmount.toLocaleString(locale, {\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2\r\n  })}`;\r\n};\r\n\r\n/**\r\n * Format amount without currency symbol\r\n * @param amount - The amount to format\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatNumber = (amount: number | string, decimals: number = 2): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return '0.00';\r\n\r\n  return numAmount.toLocaleString('en-US', {\r\n    minimumFractionDigits: decimals,\r\n    maximumFractionDigits: decimals\r\n  });\r\n};\r\n\r\n/**\r\n * Format license category fee with special handling for \"Short Code Allocation\"\r\n * @param fee - The fee amount (string or number)\r\n * @param categoryName - The name of the license category\r\n * @param currency - Currency code (default: 'MWK')\r\n */\r\nexport const formatLicenseCategoryFee = (\r\n  fee: string | number,\r\n  categoryName: string,\r\n): string => {\r\n  // Check if fee is 0 or empty\r\n  if (!fee || fee === '0' || parseFloat(fee.toString()) === 0) {\r\n    // Show \"Free\" for categories with 0 fee\r\n    return \"Free\";\r\n  }\r\n  // Format as currency for non-zero fees\r\n  return formatCurrency(fee);\r\n};\r\n\r\n/**\r\n * Format percentage\r\n * @param value - The value to format as percentage\r\n * @param decimals - Number of decimal places (default: 1)\r\n */\r\nexport const formatPercentage = (value: number | string, decimals: number = 1): string => {\r\n  const numValue = typeof value === 'string' ? parseFloat(value) : value;\r\n  if (isNaN(numValue)) return '0%';\r\n\r\n  return `${numValue.toFixed(decimals)}%`;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED DATE & TIME FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Formats a date string to a readable format\r\n *\r\n * @param dateString - The date string to format\r\n * @param options - Intl.DateTimeFormatOptions\r\n * @returns Formatted date string\r\n */\r\nexport const formatDate = (\r\n  dateString: string | Date,\r\n  options: Intl.DateTimeFormatOptions = {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric'\r\n  }\r\n): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return new Intl.DateTimeFormat('en-MW', options).format(date);\r\n};\r\n\r\n/**\r\n * Format date in long format (e.g., \"January 15, 2024\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateLong = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return date.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'long',\r\n    day: 'numeric'\r\n  });\r\n};\r\n\r\n/**\r\n * Format time (e.g., \"2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid time';\r\n\r\n  return date.toLocaleTimeString('en-US', {\r\n    hour: 'numeric',\r\n    minute: '2-digit',\r\n    hour12: true\r\n  });\r\n};\r\n\r\n/**\r\n * Format datetime (e.g., \"Jan 15, 2024 at 2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid datetime';\r\n\r\n  return `${formatDate(date)} at ${formatTime(date)}`;\r\n};\r\n\r\n/**\r\n * Format relative time (e.g., \"2 hours ago\", \"in 3 days\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatRelativeTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  const now = new Date();\r\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n  if (diffInSeconds < 60) return 'Just now';\r\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\r\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\r\n  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\r\n  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;\r\n\r\n  return `${Math.floor(diffInSeconds / 31536000)} years ago`;\r\n};\r\n\r\n/**\r\n * Format date for input fields (YYYY-MM-DD)\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateForInput = (dateString: string | Date): string => {\r\n  if (!dateString) return '';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return '';\r\n\r\n  return date.toISOString().split('T')[0];\r\n};\r\n\r\n// ============================================================================\r\n// STRING CASE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Convert string to camelCase\r\n * @param str - String to convert\r\n */\r\nexport const toCamelCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => {\r\n      return index === 0 ? word.toLowerCase() : word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to PascalCase\r\n * @param str - String to convert\r\n */\r\nexport const toPascalCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word) => {\r\n      return word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to kebab-case\r\n * @param str - String to convert\r\n */\r\nexport const toKebabCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1-$2')\r\n    .replace(/[\\s_]+/g, '-')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to snake_case\r\n * @param str - String to convert\r\n */\r\nexport const toSnakeCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1_$2')\r\n    .replace(/[\\s-]+/g, '_')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to Title Case\r\n * @param str - String to convert\r\n */\r\nexport const toTitleCase = (str: string): string => {\r\n  return str.replace(/\\w\\S*/g, (txt) => {\r\n    return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();\r\n  });\r\n};\r\n\r\n/**\r\n * Convert string to Sentence case\r\n * @param str - String to convert\r\n */\r\nexport const toSentenceCase = (str: string): string => {\r\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\r\n};\r\n\r\n// ============================================================================\r\n// TEXT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n * @param text - Text to truncate\r\n * @param maxLength - Maximum length before truncation\r\n * @param suffix - Suffix to add (default: '...')\r\n */\r\nexport const truncateText = (text: string, maxLength: number, suffix: string = '...'): string => {\r\n  if (!text || text.length <= maxLength) return text || '';\r\n  return text.substring(0, maxLength - suffix.length) + suffix;\r\n};\r\n\r\n/**\r\n * Capitalize first letter of each word\r\n * @param str - String to capitalize\r\n */\r\nexport const capitalizeWords = (str: string): string => {\r\n  return str.replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Remove extra whitespace and normalize spacing\r\n * @param str - String to normalize\r\n */\r\nexport const normalizeWhitespace = (str: string): string => {\r\n  return str.replace(/\\s+/g, ' ').trim();\r\n};\r\n\r\n/**\r\n * Extract initials from a name\r\n * @param name - Full name\r\n * @param maxInitials - Maximum number of initials (default: 2)\r\n */\r\nexport const getInitials = (name: string, maxInitials: number = 2): string => {\r\n  if (!name) return '';\r\n\r\n  return name\r\n    .split(' ')\r\n    .filter(word => word.length > 0)\r\n    .slice(0, maxInitials)\r\n    .map(word => word.charAt(0).toUpperCase())\r\n    .join('');\r\n};\r\n\r\n/**\r\n * Convert text to slug format (URL-friendly)\r\n * @param text - Text to convert\r\n */\r\nexport const toSlug = (text: string): string => {\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\r\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\r\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\r\n};\r\n\r\n/**\r\n * Highlight search terms in text\r\n * @param text - Text to highlight\r\n * @param searchTerm - Term to highlight\r\n * @param className - CSS class for highlighting (default: 'highlight')\r\n */\r\nexport const highlightText = (text: string, searchTerm: string, className: string = 'highlight'): string => {\r\n  if (!searchTerm) return text;\r\n\r\n  const regex = new RegExp(`(${searchTerm})`, 'gi');\r\n  return text.replace(regex, `<span class=\"${className}\">$1</span>`);\r\n};\r\n\r\n// ============================================================================\r\n// PHONE & EMAIL FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format phone number\r\n * @param phone - Phone number to format\r\n * @param format - Format type ('international' | 'national' | 'minimal')\r\n */\r\nexport const formatPhone = (phone: string, format: 'international' | 'national' | 'minimal' = 'national'): string => {\r\n  if (!phone) return '';\r\n\r\n  // Remove all non-digit characters\r\n  const digits = phone.replace(/\\D/g, '');\r\n\r\n  if (digits.length < 10) return phone; // Return original if too short\r\n\r\n  switch (format) {\r\n    case 'international':\r\n      return `+${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)} ${digits.slice(9)}`;\r\n    case 'national':\r\n      return `(${digits.slice(-10, -7)}) ${digits.slice(-7, -4)}-${digits.slice(-4)}`;\r\n    case 'minimal':\r\n      return `${digits.slice(-10, -7)}.${digits.slice(-7, -4)}.${digits.slice(-4)}`;\r\n    default:\r\n      return phone;\r\n  }\r\n};\r\n\r\n/**\r\n * Mask email for privacy (e.g., \"j***@example.com\")\r\n * @param email - Email to mask\r\n */\r\nexport const maskEmail = (email: string): string => {\r\n  if (!email || !email.includes('@')) return email;\r\n\r\n  const [username, domain] = email.split('@');\r\n  if (username.length <= 2) return email;\r\n\r\n  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);\r\n  return `${maskedUsername}@${domain}`;\r\n};\r\n\r\n/**\r\n * Validate email format\r\n * @param email - Email to validate\r\n */\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\n// ============================================================================\r\n// ID & REFERENCE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format application number with prefix\r\n * @param number - Application number\r\n * @param prefix - Prefix to add (default: 'APP')\r\n */\r\nexport const formatApplicationNumber = (number: string | number, prefix: string = 'APP'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format invoice number with prefix\r\n * @param number - Invoice number\r\n * @param prefix - Prefix to add (default: 'INV')\r\n */\r\nexport const formatInvoiceNumber = (number: string | number, prefix: string = 'INV'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format task number with prefix\r\n * @param number - Task number\r\n * @param prefix - Prefix to add (default: 'TASK')\r\n */\r\nexport const formatTaskNumber = (number: string | number, prefix: string = 'TASK'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Generate a random reference ID\r\n * @param length - Length of the ID (default: 8)\r\n * @param prefix - Optional prefix\r\n */\r\nexport const generateReferenceId = (length: number = 8, prefix?: string): string => {\r\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n  let result = '';\r\n\r\n  for (let i = 0; i < length; i++) {\r\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\r\n  }\r\n\r\n  return prefix ? `${prefix}-${result}` : result;\r\n};\r\n\r\n/**\r\n * Convert UUID to user-friendly reference ID for customers\r\n * @param uuid - The UUID to convert\r\n * @param prefix - Optional prefix (default: 'REF')\r\n */\r\nexport const formatCustomerReferenceId = (uuid: string, prefix: string = 'REF'): string => {\r\n  if (!uuid) return '';\r\n\r\n  // Take first 8 characters of UUID (without hyphens) and convert to uppercase\r\n  const cleanUuid = uuid.replace(/-/g, '').toUpperCase();\r\n  const shortId = cleanUuid.substring(0, 8);\r\n\r\n  return `${prefix}-${shortId}`;\r\n};\r\n\r\n/**\r\n * Mask sensitive ID (show only first and last 2 characters)\r\n * @param id - ID to mask\r\n */\r\nexport const maskId = (id: string): string => {\r\n  if (!id || id.length <= 4) return id;\r\n\r\n  const start = id.slice(0, 2);\r\n  const end = id.slice(-2);\r\n  const middle = '*'.repeat(id.length - 4);\r\n\r\n  return `${start}${middle}${end}`;\r\n};\r\n\r\n// ============================================================================\r\n// STATUS & BADGE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format status text for display\r\n * @param status - Status to format\r\n */\r\nexport const formatStatus = (status: string): string => {\r\n  if (!status) return '';\r\n\r\n  return status\r\n    .replace(/_/g, ' ')\r\n    .replace(/\\b\\w/g, char => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Get status color class\r\n * @param status - Status to get color for\r\n */\r\n\r\n\r\nexport const getStatusColor = (status: any): string => {\r\n  const statusLower = status.toLowerCase();\r\n\r\n  switch (statusLower) {\r\n    case 'active':\r\n    case 'approved':\r\n    case 'completed':\r\n    case 'paid':\r\n    case 'success':\r\n      return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';\r\n\r\n    case 'pending':\r\n    case 'in_progress':\r\n    case 'processing':\r\n    case 'review':\r\n      return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';\r\n\r\n    case 'rejected':\r\n    case 'failed':\r\n    case 'error':\r\n    case 'overdue':\r\n    case 'cancelled':\r\n      return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';\r\n\r\n    case 'draft':\r\n    case 'inactive':\r\n    case 'disabled':\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n\r\n    case 'warning':\r\n    case 'attention':\r\n      return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';\r\n\r\n    default:\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n  }\r\n};\r\n\r\n// ============================================================================\r\n// FILE SIZE & VALIDATION FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format file size in human readable format\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatFileSize = (bytes: number, decimals: number = 2): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Get file extension from filename\r\n * @param filename - Filename to extract extension from\r\n */\r\nexport const getFileExtension = (filename: string): string => {\r\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\r\n};\r\n\r\n/**\r\n * Get file type icon class based on extension\r\n * @param filename - Filename to get icon for\r\n */\r\nexport const getFileTypeIcon = (filename: string): string => {\r\n  const extension = getFileExtension(filename).toLowerCase();\r\n\r\n  switch (extension) {\r\n    case 'pdf':\r\n      return 'ri-file-pdf-line text-red-500';\r\n    case 'doc':\r\n    case 'docx':\r\n      return 'ri-file-word-line text-blue-500';\r\n    case 'xls':\r\n    case 'xlsx':\r\n      return 'ri-file-excel-line text-green-500';\r\n    case 'ppt':\r\n    case 'pptx':\r\n      return 'ri-file-ppt-line text-orange-500';\r\n    case 'jpg':\r\n    case 'jpeg':\r\n    case 'png':\r\n    case 'gif':\r\n    case 'bmp':\r\n      return 'ri-image-line text-purple-500';\r\n    case 'zip':\r\n    case 'rar':\r\n    case '7z':\r\n      return 'ri-file-zip-line text-yellow-500';\r\n    case 'txt':\r\n      return 'ri-file-text-line text-gray-500';\r\n    default:\r\n      return 'ri-file-line text-gray-500';\r\n  }\r\n};\r\n\r\nexport const formatHumanReadable = (text : string, caseType = 'first') => {\r\n  if (!text || typeof text !== 'string') return '';\r\n  \r\n  // Clean and normalize the text\r\n  let formatted = text\r\n    .trim()\r\n    .replace(/[-_]+/g, ' ') // Replace hyphens and underscores with spaces\r\n    .replace(/\\s+/g, ' ')  // Collapse multiple spaces\r\n    .toLowerCase();\r\n  \r\n  // Split into words\r\n  const words = formatted.split(' ');\r\n  \r\n  // Format based on caseType\r\n  switch (caseType.toLowerCase()) {\r\n    case 'lower':\r\n      return formatted;\r\n      \r\n    case 'upper':\r\n      return words\r\n        .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n        .join(' ');\r\n        \r\n    case 'first':\r\n    default:\r\n      return words\r\n        .map((word, index) => \r\n          index === 0 \r\n            ? word.charAt(0).toUpperCase() + word.slice(1)\r\n            : word\r\n        )\r\n        .join(' ');\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,iBAAiB,SAC5B;QACA,4EAAmB,OACnB,yFAAgC;IAEhC,qCAAqC;IACrC,MAAM,gBAAgB,OAAO,WAAW,WAAW,WAAW,UAAU;IAExE,IAAG,YAAY,KAAK,WAAW;IAE/B,0BAA0B;IAC1B,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,aAAa;IACf;IAEA,iDAAiD;IACjD,MAAM,YAAY,UAAU,MAAM,CAAC;IAEnC,mEAAmE;IACnE,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,GAAG,QAAQ,aAAa,OAAO,GAAG;IAExC,yCAAyC;IACzC,IAAI,kBAAkB;IAEtB,iGAAiG;IACjG,IAAI,YAAY,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG;QAC9C,sCAAsC;QACtC,MAAM,CAAC,aAAa,YAAY,GAAG,YAAY,KAAK,CAAC;QAErD,sCAAsC;QACtC,8CAA8C;QAC9C,IAAI,mBAAmB,YAAY,KAAK,CAAC,GAAG,KAAK,MAAM,YAAY,KAAK,CAAC;QAEzE,4DAA4D;QAC5D,mBAAmB,iBAAiB,OAAO,CAAC,yBAAyB;QAErE,yBAAyB;QACzB,kBAAkB,mBAAmB,CAAC,cAAc,MAAM,cAAc,EAAE;IAC5E,OAAO;QACL,8DAA8D;QAC9D,kBAAkB,YAAY,OAAO,CAAC,yBAAyB;IACjE;IAEA,0BAA0B;IAC1B,OAAO,SAAS,kBAAkB;AACpC;AAYO,MAAM,eAAe,SAC1B;QACA,4EAAmB,OACnB,0EAAiB;IAEjB,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAQ;IAE9B,OAAO,AAAC,GAAc,OAAZ,UAAS,KAGhB,OAHmB,UAAU,cAAc,CAAC,QAAQ;QACrD,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAOO,MAAM,eAAe,SAAC;QAAyB,4EAAmB;IACvE,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAO;IAE7B,OAAO,UAAU,cAAc,CAAC,SAAS;QACvC,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAQO,MAAM,2BAA2B,CACtC,KACA;IAEA,6BAA6B;IAC7B,IAAI,CAAC,OAAO,QAAQ,OAAO,WAAW,IAAI,QAAQ,QAAQ,GAAG;QAC3D,wCAAwC;QACxC,OAAO;IACT;IACA,uCAAuC;IACvC,OAAO,eAAe;AACxB;AAOO,MAAM,mBAAmB,SAAC;QAAwB,4EAAmB;IAC1E,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;IACjE,IAAI,MAAM,WAAW,OAAO;IAE5B,OAAO,AAAC,GAA6B,OAA3B,SAAS,OAAO,CAAC,WAAU;AACvC;AAaO,MAAM,aAAa,SACxB;QACA,2EAAsC;QACpC,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS,SAAS,MAAM,CAAC;AAC1D;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAMO,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,AAAC,GAAyB,OAAvB,WAAW,OAAM,QAAuB,OAAjB,WAAW;AAC9C;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,AAAC,GAAiC,OAA/B,KAAK,KAAK,CAAC,gBAAgB,KAAI;IACnE,IAAI,gBAAgB,OAAO,OAAO,AAAC,GAAmC,OAAjC,KAAK,KAAK,CAAC,gBAAgB,OAAM;IACtE,IAAI,gBAAgB,SAAS,OAAO,AAAC,GAAoC,OAAlC,KAAK,KAAK,CAAC,gBAAgB,QAAO;IACzE,IAAI,gBAAgB,UAAU,OAAO,AAAC,GAAsC,OAApC,KAAK,KAAK,CAAC,gBAAgB,UAAS;IAE5E,OAAO,AAAC,GAAuC,OAArC,KAAK,KAAK,CAAC,gBAAgB,WAAU;AACjD;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AACzC;AAUO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC,MAAM;QACrC,OAAO,UAAU,IAAI,KAAK,WAAW,KAAK,KAAK,WAAW;IAC5D,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC;QAC/B,OAAO,KAAK,WAAW;IACzB,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC;QAC5B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;IAC/D;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAYO,MAAM,eAAe,SAAC,MAAc;QAAmB,0EAAiB;IAC7E,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,WAAW,OAAO,QAAQ;IACtD,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,OAAO,MAAM,IAAI;AACxD;AAMO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AACxD;AAMO,MAAM,sBAAsB,CAAC;IAClC,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI;AACtC;AAOO,MAAM,cAAc,SAAC;QAAc,+EAAsB;IAC9D,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,KAAK,CAAC,KACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAC7B,KAAK,CAAC,GAAG,aACT,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC;AACV;AAMO,MAAM,SAAS,CAAC;IACrB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,KAAK,kCAAkC;AAChE;AAQO,MAAM,gBAAgB,SAAC,MAAc;QAAoB,6EAAoB;IAClF,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,QAAQ,IAAI,OAAO,AAAC,IAAc,OAAX,YAAW,MAAI;IAC5C,OAAO,KAAK,OAAO,CAAC,OAAO,AAAC,gBAAyB,OAAV,WAAU;AACvD;AAWO,MAAM,cAAc,SAAC;QAAe,0EAAmD;IAC5F,IAAI,CAAC,OAAO,OAAO;IAEnB,kCAAkC;IAClC,MAAM,SAAS,MAAM,OAAO,CAAC,OAAO;IAEpC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO,OAAO,+BAA+B;IAErE,OAAQ;QACN,KAAK;YACH,OAAO,AAAC,IAAyB,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAyB,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAyB,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAmB,OAAhB,OAAO,KAAK,CAAC;QAC5F,KAAK;YACH,OAAO,AAAC,IAA6B,OAA1B,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,IAAG,MAA4B,OAAxB,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,IAAG,KAAoB,OAAjB,OAAO,KAAK,CAAC,CAAC;QAC7E,KAAK;YACH,OAAO,AAAC,GAA2B,OAAzB,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,IAAG,KAA2B,OAAxB,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,IAAG,KAAoB,OAAjB,OAAO,KAAK,CAAC,CAAC;QAC3E;YACE,OAAO;IACX;AACF;AAMO,MAAM,YAAY,CAAC;IACxB,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM,OAAO;IAE3C,MAAM,CAAC,UAAU,OAAO,GAAG,MAAM,KAAK,CAAC;IACvC,IAAI,SAAS,MAAM,IAAI,GAAG,OAAO;IAEjC,MAAM,iBAAiB,SAAS,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC,SAAS,MAAM,GAAG;IAChH,OAAO,AAAC,GAAoB,OAAlB,gBAAe,KAAU,OAAP;AAC9B;AAMO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAWO,MAAM,0BAA0B,SAAC;QAAyB,0EAAiB;IAChF,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,AAAC,GAAY,OAAV,QAAO,KAAmC,OAAhC,OAAO,QAAQ,QAAQ,CAAC,GAAG;AACjD;AAOO,MAAM,sBAAsB,SAAC;QAAyB,0EAAiB;IAC5E,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,AAAC,GAAY,OAAV,QAAO,KAAmC,OAAhC,OAAO,QAAQ,QAAQ,CAAC,GAAG;AACjD;AAOO,MAAM,mBAAmB,SAAC;QAAyB,0EAAiB;IACzE,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,AAAC,GAAY,OAAV,QAAO,KAAmC,OAAhC,OAAO,QAAQ,QAAQ,CAAC,GAAG;AACjD;AAOO,MAAM,sBAAsB;QAAC,0EAAiB,GAAG;IACtD,MAAM,QAAQ;IACd,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IAEA,OAAO,SAAS,AAAC,GAAY,OAAV,QAAO,KAAU,OAAP,UAAW;AAC1C;AAOO,MAAM,4BAA4B,SAAC;QAAc,0EAAiB;IACvE,IAAI,CAAC,MAAM,OAAO;IAElB,6EAA6E;IAC7E,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM,IAAI,WAAW;IACpD,MAAM,UAAU,UAAU,SAAS,CAAC,GAAG;IAEvC,OAAO,AAAC,GAAY,OAAV,QAAO,KAAW,OAAR;AACtB;AAMO,MAAM,SAAS,CAAC;IACrB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,GAAG,OAAO;IAElC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG;IAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC;IACtB,MAAM,SAAS,IAAI,MAAM,CAAC,GAAG,MAAM,GAAG;IAEtC,OAAO,AAAC,GAAU,OAAR,OAAiB,OAAT,QAAa,OAAJ;AAC7B;AAUO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OACJ,OAAO,CAAC,MAAM,KACd,OAAO,CAAC,SAAS,CAAA,OAAQ,KAAK,WAAW;AAC9C;AAQO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,cAAc,OAAO,WAAW;IAEtC,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;YACH,OAAO;QAET;YACE,OAAO;IACX;AACF;AAWO,MAAM,iBAAiB,SAAC;QAAe,4EAAmB;IAC/D,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAE/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAMO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAMO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,iBAAiB,UAAU,WAAW;IAExD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,sBAAsB,SAAC;QAAe,4EAAW;IAC5D,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;IAE9C,+BAA+B;IAC/B,IAAI,YAAY,KACb,IAAI,GACJ,OAAO,CAAC,UAAU,KAAK,8CAA8C;KACrE,OAAO,CAAC,QAAQ,KAAM,2BAA2B;KACjD,WAAW;IAEd,mBAAmB;IACnB,MAAM,QAAQ,UAAU,KAAK,CAAC;IAE9B,2BAA2B;IAC3B,OAAQ,SAAS,WAAW;QAC1B,KAAK;YACH,OAAO;QAET,KAAK;YACH,OAAO,MACJ,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAEV,KAAK;QACL;YACE,OAAO,MACJ,GAAG,CAAC,CAAC,MAAM,QACV,UAAU,IACN,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,KAC1C,MAEL,IAAI,CAAC;IACZ;AACF", "debugId": null}}, {"offset": {"line": 4417, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/payments/InvoicesTab.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport DataTable from '../../common/DataTable';\r\nimport Select from '../../common/Select';\r\nimport { Payment, PaymentFilters } from '@/types/invoice';\r\nimport { PaginatedResponse, PaginateQuery } from '@/types';\r\nimport { paymentService } from '@/services/paymentService';\r\nimport { formatAmount, formatNumber, formatDate, getStatusColor } from '@/utils/formatters';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n\r\ninterface InvoicesTabProps {\r\n  onViewInvoice?: (invoice: any) => void;\r\n  onPayInvoice?: (invoice: any) => void;\r\n  onViewPaymentsDone?: (invoice: any) => void;\r\n  onUploadPayment?: (invoice: any) => void;\r\n}\r\n\r\nconst InvoicesTab = ({ onViewInvoice, onUploadPayment }: InvoicesTabProps) => {\r\n  const [invoicesData, setInvoicesData] = useState<PaginatedResponse<Payment> | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [filters, setFilters] = useState<PaymentFilters>({});\r\n  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });\r\n  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);\r\n  const [showProofModal, setShowProofModal] = useState(false);\r\n  const {user} = useAuth();\r\n\r\n  const loadInvoices = useCallback(async (query: PaginateQuery & PaymentFilters) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const response = await paymentService.getInvoices(query, user?.isCustomer);\r\n      setInvoicesData(response);\r\n    } catch (err) {\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadInvoices({ ...currentQuery, ...filters });\r\n  }, [loadInvoices, currentQuery, filters]);\r\n\r\n  const handleViewProofOfPayment = (invoice: any) => {\r\n    setSelectedInvoice(invoice);\r\n    setShowProofModal(true);\r\n  };\r\n\r\n  const handleQueryChange = (query: PaginateQuery) => {\r\n    setCurrentQuery(query);\r\n  };\r\n\r\n  const handleFilterChange = (key: keyof PaymentFilters, value: string) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      [key]: value || undefined,\r\n    }));\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    setFilters({});\r\n  };\r\n\r\n\r\n  const getTypeColor = (type: string) => {\r\n    switch (type) {\r\n      case 'LICENSE_FEE': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';\r\n      case 'PROCUREMENT_FEE': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';\r\n      case 'APPLICATION_FEE': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';\r\n      case 'RENEWAL_FEE': return 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400';\r\n      case 'PENALTY_FEE': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n\r\n  const invoiceColumns = [\r\n    {\r\n      key: 'invoice_number',\r\n      label: 'Invoice #',\r\n      sortable: true,\r\n      searchable: true,\r\n      render: (value: unknown, item: any) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n          <button  onClick={() => onViewInvoice?.(item)}>\r\n          {String(value)}\r\n          </button>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'entity_type',\r\n      label: 'Type',\r\n      render: (value: unknown, item: any) => (\r\n        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeColor('LICENSE_FEE')}`}>\r\n          {item.entity_type === 'application' ? 'Application Invoice' : String(value)}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'description',\r\n      label: 'Description',\r\n      render: (value: unknown, item: any) => (\r\n        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n          <div className=\"max-w-xs truncate font-medium\" title={String(value)}>\r\n            {String(value)}\r\n          </div>\r\n          {item.entity_type && item.entity_id && (\r\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n              {item.entity_type === 'application' ? '📋 Application' : '📄 ' + item.entity_type}\r\n            </div>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'issue_date',\r\n      label: 'Issue Date',\r\n      sortable: true,\r\n      render: (value: unknown) => (\r\n        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n          {formatDate(String(value))}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'due_date',\r\n      label: 'Due Date',\r\n      sortable: true,\r\n      render: (value: unknown) => (\r\n        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n          {formatDate(String(value))}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'amount',\r\n      label: 'Amount',\r\n      sortable: true,\r\n      render: (value: unknown, item: any) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n          ${formatNumber(Number(value))}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'balance',\r\n      label: 'Balance',\r\n      render: (value: unknown, item: any) => {\r\n        // Use balance from backend or calculate if not available\r\n        const balance = item.balance !== undefined ? Number(item.balance) : Math.max(0, Number(item.amount || 0) - Number(item.paid_amount || 0));\r\n        const isFullyPaid = balance === 0 || item.payment_status === 'FULLY_PAID';\r\n\r\n        return (\r\n          <div className={`text-sm font-medium ${isFullyPaid ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }`}>\r\n            {formatAmount(balance)}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      render: (value: unknown) => (\r\n        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(String(value))}`}>\r\n          {String(value)}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_: unknown, item: any) => (\r\n        <div className=\"flex flex-col space-y-1\">\r\n          {/* First row of actions */}\r\n          <div className=\"flex space-x-2\">\r\n            <button\r\n              onClick={() => onViewInvoice?.(item)}\r\n              className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 border border-blue-200 dark:border-blue-600 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors\"\r\n              title=\"View invoice details\"\r\n            >\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\r\n              </svg>\r\n              View\r\n            </button>\r\n          </div>\r\n\r\n          {/* Second row of actions */}\r\n          <div className=\"flex space-x-2\">\r\n            {(item.status !== 'paid') && user?.isCustomer && (\r\n              <button\r\n                onClick={() => onUploadPayment?.(item)}\r\n                className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 border border-purple-200 dark:border-purple-600 rounded hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors\"\r\n                title=\"Upload proof of payment\"\r\n              >\r\n                <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\r\n                </svg>\r\n                Upload\r\n              </button>\r\n            )}\r\n\r\n            {/* View Payments button - show if there are documents */}\r\n            {item.documents && item.documents.length > 0 && (\r\n              <button\r\n                onClick={() => handleViewProofOfPayment?.(item)}\r\n                className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 border border-green-200 dark:border-green-600 rounded hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors\"\r\n                title=\"View proof of payment documents\"\r\n              >\r\n                <i className=\"ri-file-text-line mr-1\"></i>\r\n                Payments ({item.documents.filter((doc: any) =>\r\n                  doc.document_type === 'proof_of_payment' ||\r\n                  doc.document_type === 'PROOF_OF_PAYMENT'\r\n                ).length})\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n\r\n\r\n  const statusOptions = [\r\n    { value: 'pending', label: 'Pending' },\r\n    { value: 'paid', label: 'Paid' },\r\n    { value: 'overdue', label: 'Overdue' },\r\n  ];\r\n\r\n  const dateRangeOptions = [\r\n    { value: '', label: 'All Time' },\r\n    { value: 'last-30', label: 'Last 30 Days' },\r\n    { value: 'last-90', label: 'Last 90 Days' },\r\n    { value: 'last-year', label: 'Last Year' },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\r\n        <div>\r\n          <h2 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Invoices</h2>\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            View and manage your outstanding invoices\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-3\">\r\n          <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            {/* Total: {invoicesData?.meta.totalItems || 0} invoice{(invoicesData?.meta.totalItems || 0) !== 1 ? 's' : ''} */}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\r\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\r\n          <Select\r\n            label=\"Status\"\r\n            value={filters.status || 'pending'}\r\n            onChange={(value) => handleFilterChange('status', value)}\r\n            options={statusOptions}\r\n          />\r\n          <Select\r\n            label=\"Invoice Type\"\r\n            value={filters.entity_type || ''}\r\n            onChange={(value) => handleFilterChange('entity_type', value)}\r\n            options={[\r\n              { value: '', label: 'All Types' },\r\n              { value: 'application', label: 'Application Invoice' },\r\n              { value: 'license', label: 'License Invoice' },\r\n            ]}\r\n          />\r\n          <Select\r\n            label=\"Date Range\"\r\n            value={filters.dateRange || ''}\r\n            onChange={(value) => handleFilterChange('dateRange', value)}\r\n            options={dateRangeOptions}\r\n          />\r\n          <div className=\"flex items-end\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={clearFilters}\r\n              className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-white bg-white dark:bg-gray-700 border border-red-500 rounded-full hover:bg-red-700 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300\"\r\n            >\r\n              Clear Filters\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded\">\r\n          <p>{error}</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Data Table */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\r\n        <DataTable\r\n          columns={invoiceColumns}\r\n          data={invoicesData}\r\n          loading={loading}\r\n          onQueryChange={handleQueryChange}\r\n          searchPlaceholder=\"Search invoices by number, description...\"\r\n          emptyStateIcon=\"ri-file-list-line\"\r\n          emptyStateMessage=\"No invoices found\"\r\n        />\r\n      </div>\r\n\r\n      {/* Proof of Payment Modal */}\r\n      {showProofModal && selectedInvoice && (\r\n        <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n          <div className=\"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0\">\r\n            {/* Background overlay */}\r\n            <div\r\n              className=\"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75\"\r\n              onClick={() => setShowProofModal(false)}\r\n            ></div>\r\n\r\n            {/* Modal panel */}\r\n            <div className=\"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg\">\r\n              {/* Header */}\r\n              <div className=\"flex items-center justify-between mb-6\">\r\n                <div>\r\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                    Proof of Payment Documents\r\n                  </h3>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                    Invoice #{selectedInvoice.invoice_number} - {formatAmount(selectedInvoice.amount)}\r\n                  </p>\r\n                </div>\r\n                <button\r\n                  onClick={() => setShowProofModal(false)}\r\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n\r\n              {/* Documents List */}\r\n              <div className=\"space-y-4\">\r\n                {selectedInvoice.documents && selectedInvoice.documents.length > 0 ? (\r\n                  selectedInvoice.documents\r\n                    .filter((doc: any) =>\r\n                      doc.document_type === 'proof_of_payment' ||\r\n                      doc.document_type === 'PROOF_OF_PAYMENT'\r\n                    )\r\n                    .map((document: any, index: number) => (\r\n                      <div key={index} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"flex items-center space-x-3\">\r\n                            <div className=\"flex-shrink-0\">\r\n                              <i className=\"ri-file-text-line text-2xl text-blue-600 dark:text-blue-400\"></i>\r\n                            </div>\r\n                            <div>\r\n                              <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                                {document.file_name}\r\n                              </h4>\r\n                              <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                                Uploaded on {formatDate(document.created_at)}\r\n                              </p>\r\n                              {document.file_size && (\r\n                                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                                  Size: {(document.file_size / 1024 / 1024).toFixed(2)} MB\r\n                                </p>\r\n                              )}\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <button\r\n                              onClick={() => window.open(`/api/documents/${document.document_id}/download`, '_blank')}\r\n                              className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\"\r\n                            >\r\n                              <i className=\"ri-download-line mr-1\"></i>\r\n                              Download\r\n                            </button>\r\n                            <button\r\n                              onClick={() => window.open(`/api/documents/${document.document_id}/view`, '_blank')}\r\n                              className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors\"\r\n                            >\r\n                              <i className=\"ri-eye-line mr-1\"></i>\r\n                              View\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ))\r\n                ) : (\r\n                  <div className=\"text-center py-8\">\r\n                    <i className=\"ri-file-text-line text-4xl text-gray-400 dark:text-gray-600 mb-4\"></i>\r\n                    <p className=\"text-gray-500 dark:text-gray-400\">\r\n                      No proof of payment documents found for this invoice.\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Footer */}\r\n              <div className=\"flex justify-end mt-6\">\r\n                <button\r\n                  onClick={() => setShowProofModal(false)}\r\n                  className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n                >\r\n                  Close\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InvoicesTab;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AACA;AACA;;;AATA;;;;;;;AAkBA,MAAM,cAAc;QAAC,EAAE,aAAa,EAAE,eAAe,EAAoB;;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,MAAM;QAAG,OAAO;IAAG;IACrF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAC,IAAI,EAAC,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAErB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,OAAO;YACtC,IAAI;gBACF,WAAW;gBACX,SAAS;gBACT,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,OAAO,iBAAA,2BAAA,KAAM,UAAU;gBACzE,gBAAgB;YAClB,EAAE,OAAO,KAAK,CACd,SAAU;gBACR,WAAW;YACb;QACF;gDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;gBAAE,GAAG,YAAY;gBAAE,GAAG,OAAO;YAAC;QAC7C;gCAAG;QAAC;QAAc;QAAc;KAAQ;IAExC,MAAM,2BAA2B,CAAC;QAChC,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,CAAC,KAA2B;QACrD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE,SAAS;YAClB,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,WAAW,CAAC;IACd;IAGA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAGA,MAAM,iBAAiB;QACrB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,YAAY;YACZ,QAAQ,CAAC,OAAgB,qBACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAQ,SAAS,IAAM,0BAAA,oCAAA,cAAgB;kCACvC,OAAO;;;;;;;;;;;QAId;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAgB,qBACvB,6LAAC;oBAAK,WAAW,AAAC,iEAA4F,OAA5B,aAAa;8BAC5F,KAAK,WAAW,KAAK,gBAAgB,wBAAwB,OAAO;;;;;;QAG3E;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAgB,qBACvB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAgC,OAAO,OAAO;sCAC1D,OAAO;;;;;;wBAET,KAAK,WAAW,IAAI,KAAK,SAAS,kBACjC,6LAAC;4BAAI,WAAU;sCACZ,KAAK,WAAW,KAAK,gBAAgB,mBAAmB,QAAQ,KAAK,WAAW;;;;;;;;;;;;QAK3F;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,6LAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,OAAO;;;;;;QAGzB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,6LAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,OAAO;;;;;;QAGzB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,6LAAC;oBAAI,WAAU;;wBAAuD;wBAClE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,OAAO;;;;;;;QAG5B;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAgB;gBACvB,yDAAyD;gBACzD,MAAM,UAAU,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,OAAO,IAAI,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,MAAM,IAAI,KAAK,OAAO,KAAK,WAAW,IAAI;gBACtI,MAAM,cAAc,YAAY,KAAK,KAAK,cAAc,KAAK;gBAE7D,qBACE,6LAAC;oBAAI,WAAW,AAAC,uBAA6G,OAAvF,cAAc,uCAAuC;8BACzF,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE;;;;;;YAGpB;QACF;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAK,WAAW,AAAC,iEAA8F,OAA9B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;8BACrG,OAAO;;;;;;QAGd;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAY,qBACnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,0BAAA,oCAAA,cAAgB;gCAC/B,WAAU;gCACV,OAAM;;kDAEN,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;;0DACtE,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;0DACrE,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;oCACjE;;;;;;;;;;;;sCAMV,6LAAC;4BAAI,WAAU;;gCACX,KAAK,MAAM,KAAK,WAAW,iBAAA,2BAAA,KAAM,UAAU,mBAC3C,6LAAC;oCACC,SAAS,IAAM,4BAAA,sCAAA,gBAAkB;oCACjC,WAAU;oCACV,OAAM;;sDAEN,6LAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;gCAMT,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,mBACzC,6LAAC;oCACC,SAAS,IAAM,qCAAA,+CAAA,yBAA2B;oCAC1C,WAAU;oCACV,OAAM;;sDAEN,6LAAC;4CAAE,WAAU;;;;;;wCAA6B;wCAC/B,KAAK,SAAS,CAAC,MAAM,CAAC,CAAC,MAChC,IAAI,aAAa,KAAK,sBACtB,IAAI,aAAa,KAAK,oBACtB,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;QAMrB;KACD;IAID,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAW,OAAO;QAAU;KACtC;IAED,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAI,OAAO;QAAW;QAC/B;YAAE,OAAO;YAAW,OAAO;QAAe;QAC1C;YAAE,OAAO;YAAW,OAAO;QAAe;QAC1C;YAAE,OAAO;YAAa,OAAO;QAAY;KAC1C;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAuD;;;;;;0CACrE,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;;0BAOpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,UAAM;4BACL,OAAM;4BACN,OAAO,QAAQ,MAAM,IAAI;4BACzB,UAAU,CAAC,QAAU,mBAAmB,UAAU;4BAClD,SAAS;;;;;;sCAEX,6LAAC,yIAAA,CAAA,UAAM;4BACL,OAAM;4BACN,OAAO,QAAQ,WAAW,IAAI;4BAC9B,UAAU,CAAC,QAAU,mBAAmB,eAAe;4BACvD,SAAS;gCACP;oCAAE,OAAO;oCAAI,OAAO;gCAAY;gCAChC;oCAAE,OAAO;oCAAe,OAAO;gCAAsB;gCACrD;oCAAE,OAAO;oCAAW,OAAO;gCAAkB;6BAC9C;;;;;;sCAEH,6LAAC,yIAAA,CAAA,UAAM;4BACL,OAAM;4BACN,OAAO,QAAQ,SAAS,IAAI;4BAC5B,UAAU,CAAC,QAAU,mBAAmB,aAAa;4BACrD,SAAS;;;;;;sCAEX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;YAQN,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BAAG;;;;;;;;;;;0BAKR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4IAAA,CAAA,UAAS;oBACR,SAAS;oBACT,MAAM;oBACN,SAAS;oBACT,eAAe;oBACf,mBAAkB;oBAClB,gBAAe;oBACf,mBAAkB;;;;;;;;;;;YAKrB,kBAAkB,iCACjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,kBAAkB;;;;;;sCAInC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAuD;;;;;;8DAGrE,6LAAC;oDAAE,WAAU;;wDAA2C;wDAC5C,gBAAgB,cAAc;wDAAC;wDAAI,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,MAAM;;;;;;;;;;;;;sDAGpF,6LAAC;4CACC,SAAS,IAAM,kBAAkB;4CACjC,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAM3E,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,SAAS,IAAI,gBAAgB,SAAS,CAAC,MAAM,GAAG,IAC/D,gBAAgB,SAAS,CACtB,MAAM,CAAC,CAAC,MACP,IAAI,aAAa,KAAK,sBACtB,IAAI,aAAa,KAAK,oBAEvB,GAAG,CAAC,CAAC,UAAe,sBACnB,6LAAC;4CAAgB,WAAU;sDACzB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAE,WAAU;;;;;;;;;;;0EAEf,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFACX,SAAS,SAAS;;;;;;kFAErB,6LAAC;wEAAE,WAAU;;4EAA2C;4EACzC,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;;;;;;;oEAE5C,SAAS,SAAS,kBACjB,6LAAC;wEAAE,WAAU;;4EAA2C;4EAC/C,CAAC,SAAS,SAAS,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;kEAK7D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,kBAAsC,OAArB,SAAS,WAAW,EAAC,cAAY;gEAC9E,WAAU;;kFAEV,6LAAC;wEAAE,WAAU;;;;;;oEAA4B;;;;;;;0EAG3C,6LAAC;gEACC,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,kBAAsC,OAArB,SAAS,WAAW,EAAC,UAAQ;gEAC1E,WAAU;;kFAEV,6LAAC;wEAAE,WAAU;;;;;;oEAAuB;;;;;;;;;;;;;;;;;;;2CAhClC;;;;sGAwCd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;;8CAQtD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAjZM;;QAQW,kIAAA,CAAA,UAAO;;;KARlB;uCAmZS", "debugId": null}}, {"offset": {"line": 5253, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/payments/PaymentsTab.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { paymentService } from '../../../services/paymentService';\r\nimport DataTable from '../../common/DataTable';\r\nimport Select from '../../common/Select';\r\nimport { Payment, PaymentFilters } from '@/types/invoice';\r\nimport { PaginatedResponse, PaginateQuery } from '@/types';\r\nimport { formatAmount, formatDate } from '@/utils/formatters';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\n\r\ninterface PaymentsTabProps {\r\n  onViewPayment?: (payment: any) => void;\r\n  onUploadProof?: (payment: any) => void;\r\n}\r\n\r\nconst PaymentsTab = ({ onViewPayment, onUploadProof }: PaymentsTabProps) => {\r\n  const [paymentsData, setPaymentsData] = useState<PaginatedResponse<Payment> | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [filters, setFilters] = useState<PaymentFilters>({});\r\n  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });\r\n  const [selectedPayment, setSelectedPayment] = useState<any>(null);\r\n  const [showProofModal, setShowProofModal] = useState(false);\r\n  const {user} = useAuth();\r\n\r\n  const loadPayments = useCallback(async (query: PaginateQuery & PaymentFilters) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      const response = await paymentService.getPayments(query, user?.isCustomer);\r\n      setPaymentsData(response);\r\n    } catch (err) {\r\n      console.error('Error loading payments:', err);\r\n      setError('Failed to load payments. Please try again.');\r\n      setPaymentsData({\r\n        data: [],\r\n        meta: {\r\n          itemsPerPage: query.limit || 10,\r\n          totalItems: 0,\r\n          currentPage: query.page || 1,\r\n          totalPages: 0,\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: '',\r\n          select: [],\r\n        },\r\n        links: {\r\n          current: '',\r\n        },\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    loadPayments({ ...currentQuery, ...filters });\r\n  }, [loadPayments, currentQuery, filters]);\r\n\r\n  const handleViewProofOfPayment = (payment: any) => {\r\n    setSelectedPayment(payment);\r\n    setShowProofModal(true);\r\n  };\r\n\r\n  const handleQueryChange = (query: PaginateQuery) => {\r\n    setCurrentQuery(query);\r\n  };\r\n\r\n  const handleFilterChange = (key: keyof PaymentFilters, value: string) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      [key]: value || undefined,\r\n    }));\r\n  };\r\n\r\n  const clearFilters = () => {\r\n    setFilters({});\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'PAID': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';\r\n      case 'PENDING': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';\r\n      case 'OVERDUE': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';\r\n      case 'CANCELLED': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const getTypeColor = (type: string) => {\r\n    switch (type) {\r\n      case 'LICENSE_FEE': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';\r\n      case 'PROCUREMENT_FEE': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';\r\n      case 'APPLICATION_FEE': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';\r\n      case 'RENEWAL_FEE': return 'bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400';\r\n      case 'PENALTY_FEE': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const paymentColumns = [\r\n\r\n    {\r\n      key: 'payment_type',\r\n      label: 'Type',\r\n      render: (value: unknown) => (\r\n        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getTypeColor(String(value))}`}>\r\n          {String(value).replace('_', ' ')}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'description',\r\n      label: 'Description',\r\n      render: (value: unknown, item: any) => (\r\n        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n          <div className=\"max-w-xs truncate font-medium\" title={String(value)}>\r\n            {String(value)}\r\n          </div>\r\n          {item.entity_type && item.entity_id && (\r\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n              {item.entity_type === 'application' ? '📋 Application' : '📄 ' + item.entity_type}\r\n            </div>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'issue_date',\r\n      label: 'Issue Date',\r\n      sortable: true,\r\n      render: (value: unknown) => (\r\n        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n            { value ? formatDate(String(value)) : 'Not specified' }\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'paid_date',\r\n      label: 'Payment Date',\r\n      render: (value: unknown) => (\r\n        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n          {value ? formatDate(String(value)) : 'Not paid'}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'amount',\r\n      label: 'Amount',\r\n      sortable: true,\r\n      render: (value: unknown, item: any) => (\r\n        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n          {formatAmount(Number(value), item.currency)}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      render: (value: unknown) => (\r\n        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(String(value))}`}>\r\n          {String(value)}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'payment_method',\r\n      label: 'Payment Method',\r\n      render: (value: unknown) => (\r\n        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n          {value ? String(value).replace('_', ' ') : 'Not specified'}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'transaction_reference',\r\n      label: 'Reference',\r\n      render: (value: unknown) => (\r\n        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n          {value ? String(value) : 'N/A'}\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const paymentTypes = [\r\n    { value: '', label: 'All Types' },\r\n    { value: 'LICENSE_FEE', label: 'License Fee' },\r\n    { value: 'PROCUREMENT_FEE', label: 'Procurement Fee' },\r\n    { value: 'APPLICATION_FEE', label: 'Application Fee' },\r\n    { value: 'RENEWAL_FEE', label: 'Renewal Fee' },\r\n    { value: 'PENALTY_FEE', label: 'Penalty Fee' },\r\n  ];\r\n\r\n  const statusOptions = [\r\n    { value: 'pending', label: 'Pending' },\r\n    { value: 'paid', label: 'Paid' },\r\n    { value: 'overdue', label: 'Overdue' },\r\n    { value: 'cancelled', label: 'Cancelled' },\r\n  ];\r\n\r\n  const dateRangeOptions = [\r\n    { value: '', label: 'All Time' },\r\n    { value: 'last-30', label: 'Last 30 Days' },\r\n    { value: 'last-90', label: 'Last 90 Days' },\r\n    { value: 'last-year', label: 'Last Year' },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\r\n        <div>\r\n          <h2 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Payment History</h2>\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            View all your payment records and transaction history\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-3\">\r\n          <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            {/* Total: {paymentsData?.meta.totalItems || 0} payment{(paymentsData?.meta.totalItems || 0) !== 1 ? 's' : ''} */}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\r\n        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-4\">\r\n          <Select\r\n            label=\"Status\"\r\n            value={filters.status || 'pending'}\r\n            onChange={(value) => handleFilterChange('status', value)}\r\n            options={statusOptions}\r\n          />\r\n          <Select\r\n            label=\"Payment Type\"\r\n            value={filters.payment_type || ''}\r\n            onChange={(value) => handleFilterChange('payment_type', value)}\r\n            options={paymentTypes}\r\n          />\r\n          <Select\r\n            label=\"Date Range\"\r\n            value={filters.dateRange || ''}\r\n            onChange={(value) => handleFilterChange('dateRange', value)}\r\n            options={dateRangeOptions}\r\n          />\r\n          <div className=\"flex items-end\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={clearFilters}\r\n              className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-white bg-white dark:bg-gray-700 border border-red-500 rounded-full hover:bg-red-700 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300\"\r\n            >\r\n              Clear Filters\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded\">\r\n          <p>{error}</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Data Table */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\r\n        <DataTable\r\n          columns={paymentColumns}\r\n          data={paymentsData}\r\n          loading={loading}\r\n          onQueryChange={handleQueryChange}\r\n          searchPlaceholder=\"Search payments by invoice number, description...\"\r\n          emptyStateIcon=\"ri-money-dollar-circle-line\"\r\n          emptyStateMessage=\"No payments found\"\r\n        />\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PaymentsTab;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AACA;;;AATA;;;;;;;AAgBA,MAAM,cAAc;QAAC,EAAE,aAAa,EAAE,aAAa,EAAoB;;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,MAAM;QAAG,OAAO;IAAG;IACrF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAC,IAAI,EAAC,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAErB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,OAAO;YACtC,IAAI;gBACF,WAAW;gBACX,SAAS;gBACT,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,OAAO,iBAAA,2BAAA,KAAM,UAAU;gBACzE,gBAAgB;YAClB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS;gBACT,gBAAgB;oBACd,MAAM,EAAE;oBACR,MAAM;wBACJ,cAAc,MAAM,KAAK,IAAI;wBAC7B,YAAY;wBACZ,aAAa,MAAM,IAAI,IAAI;wBAC3B,YAAY;wBACZ,QAAQ,EAAE;wBACV,UAAU,EAAE;wBACZ,QAAQ;wBACR,QAAQ,EAAE;oBACZ;oBACA,OAAO;wBACL,SAAS;oBACX;gBACF;YACF,SAAU;gBACR,WAAW;YACb;QACF;gDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;gBAAE,GAAG,YAAY;gBAAE,GAAG,OAAO;YAAC;QAC7C;gCAAG;QAAC;QAAc;QAAc;KAAQ;IAExC,MAAM,2BAA2B,CAAC;QAChC,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,CAAC,KAA2B;QACrD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE,SAAS;YAClB,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,WAAW,CAAC;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAmB,OAAO;YAC/B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB;QAErB;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAK,WAAW,AAAC,iEAA4F,OAA5B,aAAa,OAAO;8BACnG,OAAO,OAAO,OAAO,CAAC,KAAK;;;;;;QAGlC;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAgB,qBACvB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAAgC,OAAO,OAAO;sCAC1D,OAAO;;;;;;wBAET,KAAK,WAAW,IAAI,KAAK,SAAS,kBACjC,6LAAC;4BAAI,WAAU;sCACZ,KAAK,WAAW,KAAK,gBAAgB,mBAAmB,QAAQ,KAAK,WAAW;;;;;;;;;;;;QAK3F;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,sBACP,6LAAC;oBAAI,WAAU;8BACT,QAAQ,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,OAAO,UAAU;;;;;;QAG9C;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,OAAO,UAAU;;;;;;QAG3C;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,6LAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,OAAO,QAAQ,KAAK,QAAQ;;;;;;QAGhD;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAK,WAAW,AAAC,iEAA8F,OAA9B,eAAe,OAAO;8BACrG,OAAO;;;;;;QAGd;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,OAAO,OAAO,OAAO,CAAC,KAAK,OAAO;;;;;;QAGjD;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,sBACP,6LAAC;oBAAI,WAAU;8BACZ,QAAQ,OAAO,SAAS;;;;;;QAG/B;KACD;IAED,MAAM,eAAe;QACnB;YAAE,OAAO;YAAI,OAAO;QAAY;QAChC;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAmB,OAAO;QAAkB;QACrD;YAAE,OAAO;YAAmB,OAAO;QAAkB;QACrD;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAe,OAAO;QAAc;KAC9C;IAED,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAa,OAAO;QAAY;KAC1C;IAED,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAI,OAAO;QAAW;QAC/B;YAAE,OAAO;YAAW,OAAO;QAAe;QAC1C;YAAE,OAAO;YAAW,OAAO;QAAe;QAC1C;YAAE,OAAO;YAAa,OAAO;QAAY;KAC1C;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAuD;;;;;;0CACrE,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;;0BAOpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,UAAM;4BACL,OAAM;4BACN,OAAO,QAAQ,MAAM,IAAI;4BACzB,UAAU,CAAC,QAAU,mBAAmB,UAAU;4BAClD,SAAS;;;;;;sCAEX,6LAAC,yIAAA,CAAA,UAAM;4BACL,OAAM;4BACN,OAAO,QAAQ,YAAY,IAAI;4BAC/B,UAAU,CAAC,QAAU,mBAAmB,gBAAgB;4BACxD,SAAS;;;;;;sCAEX,6LAAC,yIAAA,CAAA,UAAM;4BACL,OAAM;4BACN,OAAO,QAAQ,SAAS,IAAI;4BAC5B,UAAU,CAAC,QAAU,mBAAmB,aAAa;4BACrD,SAAS;;;;;;sCAEX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;YAQN,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;8BAAG;;;;;;;;;;;0BAKR,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4IAAA,CAAA,UAAS;oBACR,SAAS;oBACT,MAAM;oBACN,SAAS;oBACT,eAAe;oBACf,mBAAkB;oBAClB,gBAAe;oBACf,mBAAkB;;;;;;;;;;;;;;;;;AAO5B;GA1QM;;QAQW,kIAAA,CAAA,UAAO;;;KARlB;uCA4QS", "debugId": null}}, {"offset": {"line": 5727, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/payments/InvoiceDetailsModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { customerApi } from '@/lib/customer-api';\r\nimport { Invoice } from '@/types/invoice';\r\nimport { formatAmount, formatDate, formatDateTime, formatNumber } from '@/utils/formatters';\r\n\r\ninterface InvoiceDetailsModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  invoice: Invoice;\r\n}\r\n\r\nconst InvoiceDetailsModal = ({ isOpen, onClose, invoice }: InvoiceDetailsModalProps) => {\r\n  const [downloading, setDownloading] = useState(false);\r\n  const [payments, setPayments] = useState<any[]>([]);\r\n  const [loadingPayments, setLoadingPayments] = useState(false);\r\n\r\n  // Fetch payments for this invoice\r\n  useEffect(() => {\r\n    if (isOpen && invoice?.invoice_id) {\r\n      fetchInvoicePayments();\r\n    }\r\n  }, [isOpen, invoice?.invoice_id]);\r\n\r\n  const fetchInvoicePayments = async () => {\r\n    if (!invoice?.invoice_id) return;\r\n\r\n    setLoadingPayments(true);\r\n    try {\r\n      const response = await customerApi.getInvoicePayments(invoice.invoice_id);\r\n      setPayments(response.data || response || []);\r\n    } catch (error) {\r\n      setPayments([]);\r\n    } finally {\r\n      setLoadingPayments(false);\r\n    }\r\n  };\r\n\r\n  if (!isOpen || !invoice) return null;\r\n\r\n  const handleDownload = async () => {\r\n    try {\r\n      setDownloading(true);\r\n      const blob = await customerApi.downloadInvoice(invoice.invoice_id ?? '') ;\r\n\r\n      // Create download link\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `invoice-${invoice.invoice_number}.pdf`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n\r\n      console.log('✅ Invoice downloaded successfully');\r\n    } catch (error) {\r\n      console.error('❌ Error downloading invoice:', error);\r\n      alert('Failed to download invoice. Please try again.');\r\n    } finally {\r\n      setDownloading(false);\r\n    }\r\n  };\r\n\r\n  const getPaymentStatusBadge = (status: string) => {\r\n    const statusClasses = {\r\n      'PAID': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\r\n      'PENDING': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\r\n      'OVERDUE': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\r\n      'CANCELLED': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',\r\n      'PROCESSING': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\r\n    };\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || statusClasses.PENDING}`}>\r\n        {status}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status.toLowerCase()) {\r\n      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';\r\n      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';\r\n      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';\r\n      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div \r\n          className=\"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75\"\r\n          onClick={onClose}\r\n        ></div>\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <div>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                Invoice Details\r\n              </h3>\r\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                Invoice #{invoice.invoice_number}\r\n              </p>\r\n            </div>\r\n            <button\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n            >\r\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Invoice Information */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\r\n            {/* Left Column */}\r\n            <div className=\"space-y-4\">\r\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\r\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">\r\n                  Invoice Information\r\n                </h4>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-500 dark:text-gray-400\">Invoice Number:</span>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {invoice.invoice_number}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-500 dark:text-gray-400\">Status:</span>\r\n                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice.status)}`}>\r\n                      {invoice.status}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-500 dark:text-gray-400\">Amount:</span>\r\n                    <span className={`px-2 py-1 text-xs font-semibold rounded-full  ${getStatusColor(invoice.status)} `}>\r\n                      ${formatAmount(invoice.amount)}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-500 dark:text-gray-400\">Issue Date:</span>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {formatDate(invoice.issue_date)}\r\n                    </span>\r\n                  </div>\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-sm text-gray-500 dark:text-gray-400\">Due Date:</span>\r\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                      {formatDate(invoice.due_date)}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Description */}\r\n            <div className=\"mb-6\">\r\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\r\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                  Description\r\n                </h4>\r\n                <p className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n                  {invoice.description}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Invoice Items */}\r\n          {invoice.items && invoice.items.length > 0 && (\r\n            <div className=\"mb-6\">\r\n              <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\r\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">\r\n                  Invoice Items\r\n                </h4>\r\n                <div className=\"overflow-x-auto\">\r\n                  <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-600\">\r\n                    <thead>\r\n                      <tr>\r\n                        <th className=\"px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Description\r\n                        </th>\r\n                        <th className=\"px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Quantity\r\n                        </th>\r\n                        <th className=\"px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Unit Price\r\n                        </th>\r\n                        <th className=\"px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Total\r\n                        </th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody className=\"divide-y divide-gray-200 dark:divide-gray-600\">\r\n                      {invoice.items.map((item: any, index: number) => (\r\n                        <tr key={index}>\r\n                          <td className=\"px-3 py-2 text-sm text-gray-900 dark:text-gray-100\">\r\n                            {item.description}\r\n                          </td>\r\n                          <td className=\"px-3 py-2 text-sm text-gray-900 dark:text-gray-100 text-right\">\r\n                            {item.quantity}\r\n                          </td>\r\n                          <td className=\"px-3 py-2 text-sm text-gray-900 dark:text-gray-100 text-right\">\r\n                            ${formatNumber(item.unit_price)}\r\n                          </td>\r\n                          <td className=\"px-3 py-2 text-sm font-medium text-gray-900 dark:text-gray-100 text-right\">\r\n                            ${formatNumber(item.quantity * item.unit_price)}\r\n                          </td>\r\n                        </tr>\r\n                      ))}\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Payments Section */}\r\n          <div className=\"mb-6\">\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                  Payments Made to This Invoice\r\n                </h4>\r\n                {loadingPayments && (\r\n                  <div className=\"flex items-center text-sm text-gray-500\">\r\n                    <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    Loading payments...\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {!loadingPayments && payments.length === 0 && (\r\n                <div className=\"text-center py-6\">\r\n                  <div className=\"text-gray-400 dark:text-gray-500 mb-2\">\r\n                    <i className=\"ri-money-dollar-circle-line text-3xl\"></i>\r\n                  </div>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                    No payments have been made to this invoice yet.\r\n                  </p>\r\n                </div>\r\n              )}\r\n\r\n              {!loadingPayments && payments.length > 0 && (\r\n                <div className=\"overflow-hidden\">\r\n                  {/* Clean Table Layout */}\r\n                  <div className=\"overflow-x-auto\">\r\n                    <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-600\">\r\n                      <thead className=\"bg-gray-50 dark:bg-gray-800\">\r\n                        <tr>\r\n                          <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Payment Details\r\n                          </th>\r\n                          <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Date & Method\r\n                          </th>\r\n                          <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Status\r\n                          </th>\r\n                          <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                            Amount\r\n                          </th>\r\n                        </tr>\r\n                      </thead>\r\n                      <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\r\n                        {payments.map((payment, index) => (\r\n                          <tr key={payment.payment_id || index} className=\"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\r\n                            <td className=\"px-4 py-4\">\r\n                              <div className=\"flex flex-col\">\r\n                                <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                                  Invoice Number #{payment.invoice_number || `P${index + 1}`}\r\n                                </div>\r\n                                {payment.transaction_reference && (\r\n                                  <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                                    Ref: <span className=\"font-mono bg-gray-100 dark:bg-gray-700 px-1.5 py-0.5 rounded text-xs\">\r\n                                      {payment.transaction_reference}\r\n                                    </span>\r\n                                  </div>\r\n                                )}\r\n                                {payment.notes && (\r\n                                  <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-xs truncate\" title={payment.notes}>\r\n                                    {payment.notes}\r\n                                  </div>\r\n                                )}\r\n                              </div>\r\n                            </td>\r\n                            <td className=\"px-4 py-4\">\r\n                              <div className=\"flex flex-col\">\r\n                                <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                                  {payment.payment_date ? formatDate(payment.payment_date) : 'Not specified'}\r\n                                </div>\r\n                                <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                                  {payment.payment_method || 'Not specified'}\r\n                                </div>\r\n                              </div>\r\n                            </td>\r\n                            <td className=\"px-4 py-4\">\r\n                              {getPaymentStatusBadge(payment.status || 'PENDING')}\r\n                            </td>\r\n                            <td className=\"px-4 py-4 text-right\">\r\n                              <div className=\"text-sm font-semibold text-gray-900 dark:text-gray-100\">\r\n                                ${formatNumber(payment.amount || 0)}\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        ))}\r\n                      </tbody>\r\n                    </table>\r\n                  </div>\r\n\r\n                  {/* Payment Summary */}\r\n                  <div className=\"mt-6 bg-gray-50 dark:bg-gray-800 rounded-lg p-4\">\r\n                    <h5 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">Payment Summary</h5>\r\n                    <div className=\"space-y-2\">\r\n                      <div className=\"flex justify-between items-center\">\r\n                        <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                          Total Payments Made:\r\n                        </span>\r\n                        <span className=\"text-sm font-semibold text-green-600 dark:text-green-400\">\r\n                          ${formatNumber(payments.reduce((sum, payment) => Number(sum) + Number(payment.amount || 0), 0))}\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center\">\r\n                        <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                          Invoice Total:\r\n                        </span>\r\n                        <span className=\"text-sm font-semibold text-gray-900 dark:text-gray-100\">\r\n                          ${formatNumber(invoice.amount || 0)}\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600\">\r\n                        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                          Outstanding Balance:\r\n                        </span>\r\n                        <span className={`text-sm font-bold ${\r\n                          (invoice.amount || 0) - payments.reduce((sum, payment) => Number(sum) + Number(payment.amount || 0), 0) <= 0\r\n                            ? 'text-green-600 dark:text-green-400'\r\n                            : 'text-red-600 dark:text-red-400'\r\n                        }`}>\r\n                          ${formatNumber(Math.max(0, (invoice.amount || 0) - payments.reduce((sum, payment) => Number(sum) + Number(payment.amount || 0), 0)))}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex justify-end space-x-3\">\r\n            <button\r\n              onClick={handleDownload}\r\n              disabled={downloading}\r\n              className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\r\n            >\r\n              {downloading ? (\r\n                <>\r\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                  </svg>\r\n                  Downloading...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\r\n                  </svg>\r\n                  Download Invoice\r\n                </>\r\n              )}\r\n            </button>\r\n\r\n            <button\r\n              onClick={onClose}\r\n              className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors\"\r\n            >\r\n              Close\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InvoiceDetailsModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAaA,MAAM,sBAAsB;QAAC,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAA4B;;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,WAAU,oBAAA,8BAAA,QAAS,UAAU,GAAE;gBACjC;YACF;QACF;wCAAG;QAAC;QAAQ,oBAAA,8BAAA,QAAS,UAAU;KAAC;IAEhC,MAAM,uBAAuB;QAC3B,IAAI,EAAC,oBAAA,8BAAA,QAAS,UAAU,GAAE;QAE1B,mBAAmB;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC,QAAQ,UAAU;YACxE,YAAY,SAAS,IAAI,IAAI,YAAY,EAAE;QAC7C,EAAE,OAAO,OAAO;YACd,YAAY,EAAE;QAChB,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;IAEhC,MAAM,iBAAiB;QACrB,IAAI;YACF,eAAe;gBACgC;YAA/C,MAAM,OAAO,MAAM,gIAAA,CAAA,cAAW,CAAC,eAAe,CAAC,CAAA,sBAAA,QAAQ,UAAU,cAAlB,iCAAA,sBAAsB;YAErE,uBAAuB;YACvB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,AAAC,WAAiC,OAAvB,QAAQ,cAAc,EAAC;YAClD,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,gBAAgB;YACpB,QAAQ;YACR,WAAW;YACX,WAAW;YACX,aAAa;YACb,cAAc;QAChB;QAEA,qBACE,6LAAC;YAAK,WAAW,AAAC,2EAAuJ,OAA7E,aAAa,CAAC,OAAqC,IAAI,cAAc,OAAO;sBACrK;;;;;;IAGP;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAuD;;;;;;sDAGrE,6LAAC;4CAAE,WAAU;;gDAA2C;gDAC5C,QAAQ,cAAc;;;;;;;;;;;;;8CAGpC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,QAAQ,cAAc;;;;;;;;;;;;kEAG3B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAW,AAAC,gDAA8E,OAA/B,eAAe,QAAQ,MAAM;0EAC3F,QAAQ,MAAM;;;;;;;;;;;;kEAGnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAW,AAAC,iDAA+E,OAA/B,eAAe,QAAQ,MAAM,GAAE;;oEAAI;oEACjG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;;kEAGjC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,UAAU;;;;;;;;;;;;kEAGlC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA2C;;;;;;0EAC3D,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQtC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;wBAO3B,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;8DACC,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAGlH,6LAAC;gEAAG,WAAU;0EAAqG;;;;;;0EAGnH,6LAAC;gEAAG,WAAU;0EAAqG;;;;;;0EAGnH,6LAAC;gEAAG,WAAU;0EAAqG;;;;;;;;;;;;;;;;;8DAKvH,6LAAC;oDAAM,WAAU;8DACd,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAW,sBAC7B,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,KAAK,WAAW;;;;;;8EAEnB,6LAAC;oEAAG,WAAU;8EACX,KAAK,QAAQ;;;;;;8EAEhB,6LAAC;oEAAG,WAAU;;wEAAgE;wEAC1E,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,KAAK,UAAU;;;;;;;8EAEhC,6LAAC;oEAAG,WAAU;;wEAA4E;wEACtF,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ,GAAG,KAAK,UAAU;;;;;;;;2DAXzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAuBvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;4CAGpE,iCACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAkC,MAAK;wDAAO,SAAQ;;0EACnE,6LAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,6LAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;;;;;;;;;;;;oCAMX,CAAC,mBAAmB,SAAS,MAAM,KAAK,mBACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;oCAM3D,CAAC,mBAAmB,SAAS,MAAM,GAAG,mBACrC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DAAM,WAAU;sEACf,cAAA,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAoG;;;;;;kFAGlH,6LAAC;wEAAG,WAAU;kFAAoG;;;;;;kFAGlH,6LAAC;wEAAG,WAAU;kFAAoG;;;;;;kFAGlH,6LAAC;wEAAG,WAAU;kFAAqG;;;;;;;;;;;;;;;;;sEAKvH,6LAAC;4DAAM,WAAU;sEACd,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;oEAAqC,WAAU;;sFAC9C,6LAAC;4EAAG,WAAU;sFACZ,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;;4FAAuD;4FACnD,QAAQ,cAAc,IAAI,AAAC,IAAa,OAAV,QAAQ;;;;;;;oFAExD,QAAQ,qBAAqB,kBAC5B,6LAAC;wFAAI,WAAU;;4FAAgD;0GACxD,6LAAC;gGAAK,WAAU;0GAClB,QAAQ,qBAAqB;;;;;;;;;;;;oFAInC,QAAQ,KAAK,kBACZ,6LAAC;wFAAI,WAAU;wFAAkE,OAAO,QAAQ,KAAK;kGAClG,QAAQ,KAAK;;;;;;;;;;;;;;;;;sFAKtB,6LAAC;4EAAG,WAAU;sFACZ,cAAA,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFAAI,WAAU;kGACZ,QAAQ,YAAY,GAAG,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,YAAY,IAAI;;;;;;kGAE7D,6LAAC;wFAAI,WAAU;kGACZ,QAAQ,cAAc,IAAI;;;;;;;;;;;;;;;;;sFAIjC,6LAAC;4EAAG,WAAU;sFACX,sBAAsB,QAAQ,MAAM,IAAI;;;;;;sFAE3C,6LAAC;4EAAG,WAAU;sFACZ,cAAA,6LAAC;gFAAI,WAAU;;oFAAyD;oFACpE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,MAAM,IAAI;;;;;;;;;;;;;mEAnC9B,QAAQ,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;0DA6CvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAC1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAG3D,6LAAC;wEAAK,WAAU;;4EAA2D;4EACvE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,OAAO,OAAO,OAAO,QAAQ,MAAM,IAAI,IAAI;;;;;;;;;;;;;0EAGhG,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA2C;;;;;;kFAG3D,6LAAC;wEAAK,WAAU;;4EAAyD;4EACrE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,MAAM,IAAI;;;;;;;;;;;;;0EAGrC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAuD;;;;;;kFAGvE,6LAAC;wEAAK,WAAW,AAAC,qBAIjB,OAHC,CAAC,QAAQ,MAAM,IAAI,CAAC,IAAI,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,OAAO,OAAO,OAAO,QAAQ,MAAM,IAAI,IAAI,MAAM,IACvG,uCACA;;4EACF;4EACA,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,IAAI,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,OAAO,OAAO,OAAO,QAAQ,MAAM,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWhJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,4BACC;;0DACE,6LAAC;gDAAI,WAAU;gDAAgD,MAAK;gDAAO,SAAQ;;kEACjF,6LAAC;wDAAO,WAAU;wDAAa,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAK,QAAO;wDAAe,aAAY;;;;;;kEACxF,6LAAC;wDAAK,WAAU;wDAAa,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CAC/C;;qEAIR;;0DACE,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;;8CAMZ,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAjYM;KAAA;uCAmYS", "debugId": null}}, {"offset": {"line": 6780, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/payments/UploadPaymentModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { customerApi } from '@/lib/customer-api';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { formatNumber } from '@/utils/formatters';\r\n\r\ninterface UploadPaymentModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  invoice: any;\r\n  onSuccess?: () => void;\r\n}\r\n\r\nconst UploadPaymentModal = ({ isOpen, onClose, invoice, onSuccess }: UploadPaymentModalProps) => {\r\n  const { showSuccess, showError } = useToast();\r\n  const [formData, setFormData] = useState({\r\n    paymentMethod: '',\r\n    transactionReference: '',\r\n    paymentDate: '',\r\n    amount: '',\r\n    notes: '',\r\n  });\r\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState(false);\r\n  const [dragActive, setDragActive] = useState(false);\r\n  const [payments, setPayments] = useState<any[]>([]);\r\n  const [loadingPayments, setLoadingPayments] = useState(false);\r\n  const [totalPaid, setTotalPaid] = useState(0);\r\n\r\n  // Fetch existing payments when modal opens\r\n  useEffect(() => {\r\n    if (isOpen && invoice?.invoice_id) {\r\n      fetchInvoicePayments();\r\n    }\r\n  }, [isOpen, invoice?.invoice_id]);\r\n\r\n  // Calculate balance when payments or invoice changes\r\n  useEffect(() => {\r\n    if (invoice && payments) {\r\n      const totalPaid = payments.reduce((sum, payment) => {\r\n        // Only count paid payments\r\n        if (payment.status === 'approved') {\r\n          return Number(sum) + parseFloat(payment.amount || '0');\r\n        }\r\n        return sum;\r\n      }, 0);\r\n      setTotalPaid(totalPaid)\r\n\r\n\r\n      // Set default amount to remaining balance\r\n      if (invoice.balance > 0 && !formData.amount) {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          amount: invoice.balance.toString()\r\n        }));\r\n      }\r\n    }\r\n  }, [invoice, payments]);\r\n\r\n  const fetchInvoicePayments = async () => {\r\n    if (!invoice?.invoice_id) return;\r\n\r\n    setLoadingPayments(true);\r\n    try {\r\n      console.log('🔍 Fetching payments for invoice:', invoice.invoice_id);\r\n      const response = await customerApi.getInvoicePayments(invoice.invoice_id);\r\n      console.log('💰 Invoice payments received:', response);\r\n      setPayments(response.data || response || []);\r\n    } catch (error) {\r\n      console.error('❌ Error fetching invoice payments:', error);\r\n      setPayments([]);\r\n    } finally {\r\n      setLoadingPayments(false);\r\n    }\r\n  };\r\n\r\n  if (!isOpen || !invoice) return null;\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleFileSelect = (file: File) => {\r\n    // Validate file type\r\n    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];\r\n    if (!allowedTypes.includes(file.type)) {\r\n      setError('Please select a valid file type (JPEG, PNG, or PDF)');\r\n      return;\r\n    }\r\n\r\n    // Validate file size (5MB max)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      setError('File size must be less than 5MB');\r\n      return;\r\n    }\r\n\r\n    setSelectedFile(file);\r\n    setError(null);\r\n  };\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleDrag = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (e.type === 'dragenter' || e.type === 'dragover') {\r\n      setDragActive(true);\r\n    } else if (e.type === 'dragleave') {\r\n      setDragActive(false);\r\n    }\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setDragActive(false);\r\n\r\n    const file = e.dataTransfer.files?.[0];\r\n    if (file) {\r\n      handleFileSelect(file);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Validate required fields\r\n      if (!formData.paymentMethod || !formData.transactionReference || !formData.paymentDate) {\r\n        throw new Error('Please fill in all required fields');\r\n      }\r\n\r\n      if (!selectedFile) {\r\n        throw new Error('Please select a proof of payment file');\r\n      }\r\n\r\n      // Step 1: Upload file to documents controller\r\n      const documentFormData = new FormData();\r\n      documentFormData.append('file', selectedFile);\r\n      documentFormData.append('document_type', 'PROOF_OF_PAYMENT');\r\n      documentFormData.append('entity_type', 'payment');\r\n      documentFormData.append('entity_id', 'temp'); // Will be updated when linking to payment\r\n\r\n      console.log('📤 Step 1: Uploading document...');\r\n      const documentResponse = await customerApi.uploadDocument(documentFormData);\r\n      console.log('✅ Document uploaded:', documentResponse);\r\n\r\n      if (!documentResponse.data?.document_id) {\r\n        throw new Error('Failed to upload document - no document ID returned');\r\n      }\r\n\r\n      // Step 2: Link document to payment\r\n      const paymentData = {\r\n        documentId: documentResponse.data.document_id,\r\n        amount: parseFloat(formData.amount),\r\n        paymentMethod: formData.paymentMethod,\r\n        transactionReference: formData.transactionReference,\r\n        notes: formData.notes\r\n      };\r\n\r\n      console.log('📤 Step 2: Linking document to payment...');\r\n      const response = await customerApi.linkProofOfPayment(\r\n        invoice.invoice_id || invoice.payment_id,\r\n        paymentData\r\n      );\r\n\r\n      console.log('✅ Proof of payment uploaded successfully:', response);\r\n\r\n      // Show success state briefly before closing\r\n      setSuccess(true);\r\n      setError(null); // Clear any previous errors\r\n\r\n      // Show success toast notification\r\n      showSuccess(\r\n        `Proof of payment uploaded successfully! Your payment for invoice ${invoice.invoice_number} is now under review and will be processed shortly.`,\r\n        6000 // Show for 6 seconds\r\n      );\r\n\r\n      // Wait a moment to show success state, then close modal\r\n      setTimeout(() => {\r\n        // Reset form\r\n        setFormData({\r\n          paymentMethod: '',\r\n          transactionReference: '',\r\n          paymentDate: '',\r\n          amount: '',\r\n          notes: '',\r\n        });\r\n        setSelectedFile(null);\r\n        setSuccess(false);\r\n\r\n        // Call success callback and close modal\r\n        onSuccess?.();\r\n        onClose();\r\n      }, 1500); // Show success for 1.5 seconds\r\n    } catch (err: any) {\r\n      console.error('❌ Error uploading proof of payment:', err);\r\n      const errorMessage = err.message || 'Failed to upload proof of payment. Please try again.';\r\n      setError(errorMessage);\r\n      showError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div \r\n          className=\"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75\"\r\n          onClick={onClose}\r\n        ></div>\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-lg\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <div>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                Upload Proof of Payment\r\n              </h3>\r\n              <div className=\"space-y-1\">\r\n                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                  Invoice #{invoice.invoice_number} - ${formatNumber(invoice.amount)}\r\n                </p>\r\n                {loadingPayments ? (\r\n                  <p className=\"text-sm text-blue-600 dark:text-blue-400\">\r\n                    <i className=\"ri-loader-4-line animate-spin mr-1\"></i>\r\n                    Loading balance...\r\n                  </p>\r\n                ) : (\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <p className=\"text-sm text-green-600 dark:text-green-400 font-medium\">\r\n                      Outstanding Balance: ${formatNumber(invoice.balance)}\r\n                    </p>\r\n                    {payments.length > 0 && (\r\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                        ({payments.filter(p => p.status === 'PAID' || p.status === 'paid').length} payment(s) made)\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n            <button\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n            >\r\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Balance Information */}\r\n          {!loadingPayments && (\r\n            <div className=\"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-200\">\r\n                    Payment Information\r\n                  </h4>\r\n                  <div className=\"mt-1 space-y-1\">\r\n                    <p className=\"text-sm text-blue-700 dark:text-blue-300\">\r\n                      Invoice Total: <span className=\"font-semibold\">${formatNumber(invoice.amount)}</span>\r\n                    </p>\r\n                    {payments.length > 0 && (\r\n                      <p className=\"text-sm text-blue-700 dark:text-blue-300\">\r\n                        Amount Paid: <span className=\"font-semibold\">${formatNumber(totalPaid ?? 0)}</span>\r\n                      </p>\r\n                    )}\r\n                    <p className=\"text-sm text-blue-700 dark:text-blue-300\">\r\n                      Outstanding Balance: <span className=\"font-bold text-lg\">${formatNumber(invoice.balance)}</span>\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-right\">\r\n                  {invoice.balance === 0 ? (\r\n                    <div className=\"flex items-center text-green-600 dark:text-green-400\">\r\n                      <i className=\"ri-check-circle-fill mr-1\"></i>\r\n                      <span className=\"text-sm font-medium\">Fully Paid</span>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"flex items-center text-orange-600 dark:text-orange-400\">\r\n                      <i className=\"ri-time-line mr-1\"></i>\r\n                      <span className=\"text-sm font-medium\">Payment Due</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Show message if invoice is fully paid */}\r\n          {invoice.balance === 0 && !loadingPayments && (\r\n            <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\r\n              <div className=\"flex items-center\">\r\n                <i className=\"ri-check-circle-fill text-green-600 dark:text-green-400 mr-2\"></i>\r\n                <div>\r\n                  <p className=\"text-sm font-medium text-green-800 dark:text-green-200\">\r\n                    Invoice Fully Paid\r\n                  </p>\r\n                  <p className=\"text-sm text-green-700 dark:text-green-300\">\r\n                    This invoice has been fully paid. No additional payment is required.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Form */}\r\n          <form onSubmit={handleSubmit} className={`space-y-6 ${invoice.balance === 0 && !loadingPayments ? 'opacity-50 pointer-events-none' : ''}`}>\r\n            {/* Payment Method */}\r\n            <div>\r\n              <label htmlFor=\"paymentMethod\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Payment Method *\r\n              </label>\r\n              <select\r\n                id=\"paymentMethod\"\r\n                name=\"paymentMethod\"\r\n                value={formData.paymentMethod}\r\n                onChange={handleInputChange}\r\n                required\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n              >\r\n                <option value=\"\">Select payment method</option>\r\n                <option value=\"bank_transfer\">Bank Transfer</option>\r\n                <option value=\"mobile_money\">Mobile Money</option>\r\n                <option value=\"cash\">Cash</option>\r\n                <option value=\"online_payment\">Online Payment</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Transaction Reference */}\r\n            <div>\r\n              <label htmlFor=\"transactionReference\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Transaction Reference *\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"transactionReference\"\r\n                name=\"transactionReference\"\r\n                value={formData.transactionReference}\r\n                onChange={handleInputChange}\r\n                required\r\n                placeholder=\"Enter transaction reference number\"\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n              />\r\n            </div>\r\n\r\n            {/* Payment Date */}\r\n            <div>\r\n              <label htmlFor=\"paymentDate\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Payment Date *\r\n              </label>\r\n              <input\r\n                type=\"date\"\r\n                id=\"paymentDate\"\r\n                name=\"paymentDate\"\r\n                value={formData.paymentDate}\r\n                onChange={handleInputChange}\r\n                required\r\n                max={new Date().toISOString().split('T')[0]}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n              />\r\n            </div>\r\n\r\n            {/* Amount */}\r\n            <div>\r\n              <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Amount Paid *\r\n              </label>\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"number\"\r\n                  id=\"amount\"\r\n                  name=\"amount\"\r\n                  value={formData.amount}\r\n                  onChange={handleInputChange}\r\n                  step=\"0.01\"\r\n                  min=\"0\"\r\n                  max={invoice.balance}\r\n                  placeholder=\"Enter amount paid\"\r\n                  required\r\n                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 dark:bg-gray-700 dark:text-gray-100 ${\r\n                    parseFloat(formData.amount) > invoice.balance && formData.amount\r\n                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500'\r\n                      : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500'\r\n                  }`}\r\n                />\r\n                {invoice.balance > 0 && (\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => setFormData(prev => ({ ...prev, amount: invoice.balance.toString() }))}\r\n                    className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium\"\r\n                  >\r\n                    Pay Full Balance\r\n                  </button>\r\n                )}\r\n              </div>\r\n              <div className=\"mt-1 flex justify-between items-center\">\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                  Maximum: ${formatNumber(invoice.balance)}\r\n                </p>\r\n                {parseFloat(formData.amount) > invoice.balance && formData.amount && (\r\n                  <p className=\"text-xs text-red-600 dark:text-red-400\">\r\n                    Amount exceeds outstanding balance\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* File Upload */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Proof of Payment Document *\r\n              </label>\r\n              <div\r\n                className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${\r\n                  dragActive\r\n                    ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'\r\n                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'\r\n                }`}\r\n                onDragEnter={handleDrag}\r\n                onDragLeave={handleDrag}\r\n                onDragOver={handleDrag}\r\n                onDrop={handleDrop}\r\n              >\r\n                <input\r\n                  type=\"file\"\r\n                  onChange={handleFileChange}\r\n                  accept=\".jpg,.jpeg,.png,.pdf\"\r\n                  className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\r\n                />\r\n                <div className=\"text-center\">\r\n                  {selectedFile ? (\r\n                    <div className=\"space-y-2\">\r\n                      <svg className=\"mx-auto h-12 w-12 text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                      </svg>\r\n                      <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                        <span className=\"font-medium text-gray-900 dark:text-gray-100\">{selectedFile.name}</span>\r\n                        <p className=\"text-xs\">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</p>\r\n                      </div>\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => setSelectedFile(null)}\r\n                        className=\"text-sm text-red-600 dark:text-red-400 hover:underline\"\r\n                      >\r\n                        Remove file\r\n                      </button>\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"space-y-2\">\r\n                      <svg className=\"mx-auto h-12 w-12 text-gray-400\" stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 48 48\">\r\n                        <path d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\" strokeWidth={2} strokeLinecap=\"round\" strokeLinejoin=\"round\" />\r\n                      </svg>\r\n                      <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                        <span className=\"font-medium text-blue-600 dark:text-blue-400 hover:underline\">Click to upload</span> or drag and drop\r\n                      </div>\r\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">PNG, JPG, PDF up to 5MB</p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Notes */}\r\n            <div>\r\n              <label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Additional Notes\r\n              </label>\r\n              <textarea\r\n                id=\"notes\"\r\n                name=\"notes\"\r\n                value={formData.notes}\r\n                onChange={handleInputChange}\r\n                rows={3}\r\n                placeholder=\"Any additional information about the payment...\"\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-gray-100\"\r\n              />\r\n            </div>\r\n\r\n            {/* Success Message */}\r\n            {success && (\r\n              <div className=\"p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <svg className=\"w-5 h-5 text-green-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className=\"text-sm font-medium text-green-800 dark:text-green-200\">\r\n                      Upload Successful!\r\n                    </p>\r\n                    <p className=\"text-sm text-green-700 dark:text-green-300 mt-1\">\r\n                      Your proof of payment has been submitted and is now under review.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Error Message */}\r\n            {error && !success && (\r\n              <div className=\"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <svg className=\"w-5 h-5 text-red-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className=\"text-sm font-medium text-red-800 dark:text-red-200\">Upload Failed</p>\r\n                    <p className=\"text-sm text-red-700 dark:text-red-300 mt-1\">{error}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Actions */}\r\n            <div className=\"flex justify-end space-x-3\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading || success || invoice.balance === 0 || parseFloat(formData.amount) > invoice.balance}\r\n                className={`inline-flex items-center px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${\r\n                  success\r\n                    ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500'\r\n                    : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'\r\n                }`}\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    Uploading...\r\n                  </>\r\n                ) : success ? (\r\n                  <>\r\n                    <svg className=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                    Upload Successful!\r\n                  </>\r\n                ) : (\r\n                  'Upload Proof of Payment'\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UploadPaymentModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAcA,MAAM,qBAAqB;QAAC,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAA2B;;IAC1F,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,eAAe;QACf,sBAAsB;QACtB,aAAa;QACb,QAAQ;QACR,OAAO;IACT;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,WAAU,oBAAA,8BAAA,QAAS,UAAU,GAAE;gBACjC;YACF;QACF;uCAAG;QAAC;QAAQ,oBAAA,8BAAA,QAAS,UAAU;KAAC;IAEhC,qDAAqD;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,WAAW,UAAU;gBACvB,MAAM,YAAY,SAAS,MAAM;8DAAC,CAAC,KAAK;wBACtC,2BAA2B;wBAC3B,IAAI,QAAQ,MAAM,KAAK,YAAY;4BACjC,OAAO,OAAO,OAAO,WAAW,QAAQ,MAAM,IAAI;wBACpD;wBACA,OAAO;oBACT;6DAAG;gBACH,aAAa;gBAGb,0CAA0C;gBAC1C,IAAI,QAAQ,OAAO,GAAG,KAAK,CAAC,SAAS,MAAM,EAAE;oBAC3C;wDAAY,CAAA,OAAQ,CAAC;gCACnB,GAAG,IAAI;gCACP,QAAQ,QAAQ,OAAO,CAAC,QAAQ;4BAClC,CAAC;;gBACH;YACF;QACF;uCAAG;QAAC;QAAS;KAAS;IAEtB,MAAM,uBAAuB;QAC3B,IAAI,EAAC,oBAAA,8BAAA,QAAS,UAAU,GAAE;QAE1B,mBAAmB;QACnB,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC,QAAQ,UAAU;YACnE,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC,QAAQ,UAAU;YACxE,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,YAAY,SAAS,IAAI,IAAI,YAAY,EAAE;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,YAAY,EAAE;QAChB,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,IAAI,CAAC,UAAU,CAAC,SAAS,OAAO;IAEhC,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,qBAAqB;QACrB,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAkB;QAChF,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,SAAS;YACT;QACF;QAEA,+BAA+B;QAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAC;YACX;QAAb,MAAM,QAAO,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;YAKL;QAJb,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,MAAM,QAAO,wBAAA,EAAE,YAAY,CAAC,KAAK,cAApB,4CAAA,qBAAsB,CAAC,EAAE;QACtC,IAAI,MAAM;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;gBAqBG;YApBL,2BAA2B;YAC3B,IAAI,CAAC,SAAS,aAAa,IAAI,CAAC,SAAS,oBAAoB,IAAI,CAAC,SAAS,WAAW,EAAE;gBACtF,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,MAAM;YAClB;YAEA,8CAA8C;YAC9C,MAAM,mBAAmB,IAAI;YAC7B,iBAAiB,MAAM,CAAC,QAAQ;YAChC,iBAAiB,MAAM,CAAC,iBAAiB;YACzC,iBAAiB,MAAM,CAAC,eAAe;YACvC,iBAAiB,MAAM,CAAC,aAAa,SAAS,0CAA0C;YAExF,QAAQ,GAAG,CAAC;YACZ,MAAM,mBAAmB,MAAM,gIAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YAC1D,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,IAAI,GAAC,yBAAA,iBAAiB,IAAI,cAArB,6CAAA,uBAAuB,WAAW,GAAE;gBACvC,MAAM,IAAI,MAAM;YAClB;YAEA,mCAAmC;YACnC,MAAM,cAAc;gBAClB,YAAY,iBAAiB,IAAI,CAAC,WAAW;gBAC7C,QAAQ,WAAW,SAAS,MAAM;gBAClC,eAAe,SAAS,aAAa;gBACrC,sBAAsB,SAAS,oBAAoB;gBACnD,OAAO,SAAS,KAAK;YACvB;YAEA,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,gIAAA,CAAA,cAAW,CAAC,kBAAkB,CACnD,QAAQ,UAAU,IAAI,QAAQ,UAAU,EACxC;YAGF,QAAQ,GAAG,CAAC,6CAA6C;YAEzD,4CAA4C;YAC5C,WAAW;YACX,SAAS,OAAO,4BAA4B;YAE5C,kCAAkC;YAClC,YACE,AAAC,oEAA0F,OAAvB,QAAQ,cAAc,EAAC,wDAC3F,KAAK,qBAAqB;;YAG5B,wDAAwD;YACxD,WAAW;gBACT,aAAa;gBACb,YAAY;oBACV,eAAe;oBACf,sBAAsB;oBACtB,aAAa;oBACb,QAAQ;oBACR,OAAO;gBACT;gBACA,gBAAgB;gBAChB,WAAW;gBAEX,wCAAwC;gBACxC,sBAAA,gCAAA;gBACA;YACF,GAAG,OAAO,+BAA+B;QAC3C,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,eAAe,IAAI,OAAO,IAAI;YACpC,SAAS;YACT,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAGA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAuD;;;;;;sDAGrE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDAA2C;wDAC5C,QAAQ,cAAc;wDAAC;wDAAK,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,MAAM;;;;;;;gDAElE,gCACC,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAE,WAAU;;;;;;wDAAyC;;;;;;6GAIxD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;;gEAAyD;gEAC7C,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO;;;;;;;wDAEpD,SAAS,MAAM,GAAG,mBACjB,6LAAC;4DAAE,WAAU;;gEAA2C;gEACpD,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK,QAAQ,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;8CAOtF,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;wBAM1E,CAAC,iCACA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DAGrE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAA2C;0EACvC,6LAAC;gEAAK,WAAU;;oEAAgB;oEAAE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;;oDAE7E,SAAS,MAAM,GAAG,mBACjB,6LAAC;wDAAE,WAAU;;4DAA2C;0EACzC,6LAAC;gEAAK,WAAU;;oEAAgB;oEAAE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,sBAAA,uBAAA,YAAa;;;;;;;;;;;;;kEAG7E,6LAAC;wDAAE,WAAU;;4DAA2C;0EACjC,6LAAC;gEAAK,WAAU;;oEAAoB;oEAAE,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;kDAI7F,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,OAAO,KAAK,kBACnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;;;;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;qGAGxC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;;;;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASjD,QAAQ,OAAO,KAAK,KAAK,CAAC,iCACzB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;;;;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAyD;;;;;;0DAGtE,6LAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;;;;;;;;;;;;sCASlE,6LAAC;4BAAK,UAAU;4BAAc,WAAW,AAAC,aAA8F,OAAlF,QAAQ,OAAO,KAAK,KAAK,CAAC,kBAAkB,mCAAmC;;8CAEnI,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAgB,WAAU;sDAAkE;;;;;;sDAG3G,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,aAAa;4CAC7B,UAAU;4CACV,QAAQ;4CACR,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAgB;;;;;;8DAC9B,6LAAC;oDAAO,OAAM;8DAAe;;;;;;8DAC7B,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAiB;;;;;;;;;;;;;;;;;;8CAKnC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAuB,WAAU;sDAAkE;;;;;;sDAGlH,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,oBAAoB;4CACpC,UAAU;4CACV,QAAQ;4CACR,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAKd,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAkE;;;;;;sDAGzG,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,WAAW;4CAC3B,UAAU;4CACV,QAAQ;4CACR,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;4CAC3C,WAAU;;;;;;;;;;;;8CAKd,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAS,WAAU;sDAAkE;;;;;;sDAGpG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,MAAM;oDACtB,UAAU;oDACV,MAAK;oDACL,KAAI;oDACJ,KAAK,QAAQ,OAAO;oDACpB,aAAY;oDACZ,QAAQ;oDACR,WAAW,AAAC,oHAIX,OAHC,WAAW,SAAS,MAAM,IAAI,QAAQ,OAAO,IAAI,SAAS,MAAM,GAC5D,+EACA;;;;;;gDAGP,QAAQ,OAAO,GAAG,mBACjB,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,QAAQ,QAAQ,OAAO,CAAC,QAAQ;4DAAG,CAAC;oDACnF,WAAU;8DACX;;;;;;;;;;;;sDAKL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDAA2C;wDAC3C,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO;;;;;;;gDAExC,WAAW,SAAS,MAAM,IAAI,QAAQ,OAAO,IAAI,SAAS,MAAM,kBAC/D,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;;;;;;;;;;;;;8CAQ5D,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,WAAW,AAAC,oEAIX,OAHC,aACI,mDACA;4CAEN,aAAa;4CACb,aAAa;4CACb,YAAY;4CACZ,QAAQ;;8DAER,6LAAC;oDACC,MAAK;oDACL,UAAU;oDACV,QAAO;oDACP,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;8DACZ,6BACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAmC,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC1F,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;0EAEvE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgD,aAAa,IAAI;;;;;;kFACjF,6LAAC;wEAAE,WAAU;;4EAAU;4EAAE,CAAC,aAAa,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;0EAExE,6LAAC;gEACC,MAAK;gEACL,SAAS,IAAM,gBAAgB;gEAC/B,WAAU;0EACX;;;;;;;;;;;iHAKH,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAkC,QAAO;gEAAe,MAAK;gEAAO,SAAQ;0EACzF,cAAA,6LAAC;oEAAK,GAAE;oEAAyL,aAAa;oEAAG,eAAc;oEAAQ,gBAAe;;;;;;;;;;;0EAExP,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+D;;;;;;oEAAsB;;;;;;;0EAEvG,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQlE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAkE;;;;;;sDAGnG,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,MAAM;4CACN,aAAY;4CACZ,WAAU;;;;;;;;;;;;gCAKb,yBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAyB,MAAK;oDAAe,SAAQ;8DAClE,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAwI,UAAS;;;;;;;;;;;;;;;;0DAGhL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyD;;;;;;kEAGtE,6LAAC;wDAAE,WAAU;kEAAkD;;;;;;;;;;;;;;;;;;;;;;;gCAStE,SAAS,CAAC,yBACT,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAe,SAAQ;8DAChE,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA0N,UAAS;;;;;;;;;;;;;;;;0DAGlQ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqD;;;;;;kEAClE,6LAAC;wDAAE,WAAU;kEAA+C;;;;;;;;;;;;;;;;;;;;;;;8CAOpE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,UAAU,WAAW,WAAW,QAAQ,OAAO,KAAK,KAAK,WAAW,SAAS,MAAM,IAAI,QAAQ,OAAO;4CACtG,WAAW,AAAC,8MAIX,OAHC,UACI,yDACA;sDAGL,wBACC;;kEACE,6LAAC;wDAAI,WAAU;wDAA6C,MAAK;wDAAO,SAAQ;;0EAC9E,6LAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,6LAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;+DAGN,wBACF;;kEACE,6LAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAe,SAAQ;kEACxD,cAAA,6LAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqH,UAAS;;;;;;;;;;;oDACrJ;;+DAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAtjBM;;QAC+B,mIAAA,CAAA,WAAQ;;;KADvC;uCAwjBS", "debugId": null}}, {"offset": {"line": 8014, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/payments/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport Loader from '@/components/Loader';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport PaymentTabs from '@/components/customer/payments/PaymentTabs';\r\nimport InvoicesTab from '@/components/customer/payments/InvoicesTab';\r\nimport PaymentsTab from '@/components/customer/payments/PaymentsTab';\r\nimport InvoiceDetailsModal from '@/components/customer/payments/InvoiceDetailsModal';\r\nimport UploadPaymentModal from '@/components/customer/payments/UploadPaymentModal';\r\nimport ViewPaymentsModal from '@/components/customer/payments/ViewPaymentsModal';\r\nimport { paymentService } from '@/services/paymentService';\r\nimport { PaymentStatistics } from '@/types/invoice';\r\nimport { formatAmount } from '@/utils/formatters';\r\n\r\nconst CustomerPaymentsPage = () => {\r\n  const { isAuthenticated } = useAuth();\r\n  const router = useRouter();\r\n  const [activeTab, setActiveTab] = useState('invoices');\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [statistics, setStatistics] = useState<PaymentStatistics | null>(null);\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n\r\n  // Modal states\r\n  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);\r\n  const [showInvoiceDetails, setShowInvoiceDetails] = useState(false);\r\n  const [showPaymentTransactions, setShowPaymentTransactions] = useState(false);\r\n  const [showUploadPayment, setShowUploadPayment] = useState(false);\r\n  const [showViewPayments, setShowViewPayments] = useState(false);\r\n\r\n  const loadStatistics = async () => {\r\n    try {\r\n      setStatsLoading(true);\r\n      // Add timeout to prevent infinite loading\r\n      const statsPromise = paymentService.getPaymentStatistics();\r\n      const timeoutPromise = new Promise<never>((_, reject) =>\r\n        setTimeout(() => reject(new Error('Statistics loading timeout')), 20000)\r\n      );\r\n\r\n      const stats = await Promise.race([statsPromise, timeoutPromise]);\r\n      setStatistics(stats);\r\n    } catch (error) {\r\n      setStatistics({\r\n        total: 0,\r\n        pending: 0,\r\n        paid: 0,\r\n        overdue: 0,\r\n        cancelled: 0,\r\n        totalAmount: 0,\r\n        pendingAmount: 0,\r\n        paidAmount: 0,\r\n        overdueAmount: 0,\r\n      });\r\n    } finally {\r\n      setStatsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const initializePage = async () => {\r\n      try {\r\n\r\n        console.log('✅ User authenticated, loading statistics...');\r\n\r\n        // Load statistics with timeout\r\n        const initPromise = loadStatistics();\r\n        const timeoutPromise = new Promise<void>((resolve) =>\r\n          setTimeout(() => {\r\n            console.warn('⚠️ Page initialization timeout, proceeding anyway');\r\n            resolve();\r\n          }, 25000)\r\n        );\r\n\r\n        await Promise.race([initPromise, timeoutPromise]);\r\n        console.log('✅ Payments page initialized successfully');\r\n      } catch (error) {\r\n        console.error('❌ Failed to initialize payments page:', error);\r\n      } finally {\r\n        setIsLoading(false);\r\n        console.log('✅ Page loading completed');\r\n      }\r\n    };\r\n\r\n    initializePage();\r\n  }, [isAuthenticated, router]);\r\n\r\n  const handleTabChange = (tabId: string) => {\r\n    setActiveTab(tabId);\r\n  };\r\n\r\n  const handleViewInvoice = (invoice: any) => {\r\n    setSelectedInvoice(invoice);\r\n    setShowInvoiceDetails(true);\r\n  };\r\n\r\n  const handlePayInvoice = (invoice: any) => {\r\n    // TODO: Implement payment flow - redirect to payment gateway or show payment options\r\n  };\r\n\r\n  const handleViewPaymentsDone = (invoice: any) => {\r\n    console.log('View payments done for invoice:', invoice);\r\n    setSelectedInvoice(invoice);\r\n    setShowViewPayments(true);\r\n  };\r\n\r\n  const handleUploadPayment = (invoice: any) => {\r\n    console.log('Upload payment proof for invoice:', invoice);\r\n    setSelectedInvoice(invoice);\r\n    setShowUploadPayment(true);\r\n  };\r\n\r\n  const handleCloseModals = () => {\r\n    setSelectedInvoice(null);\r\n    setShowInvoiceDetails(false);\r\n    setShowPaymentTransactions(false);\r\n    setShowUploadPayment(false);\r\n    setShowViewPayments(false);\r\n  };\r\n\r\n  const handleUploadSuccess = () => {\r\n    // Refresh statistics after successful upload\r\n    loadStatistics();\r\n  };\r\n\r\n  const handleViewPayment = (payment: any) => {\r\n    console.log('View payment:', payment);\r\n    // TODO: Implement payment view modal or navigation\r\n  };\r\n\r\n  const handleUploadProof = (payment: any) => {\r\n    console.log('Upload proof for payment:', payment);\r\n    // TODO: Implement proof of payment upload modal\r\n  };\r\n\r\n\r\n  // Define tabs for the tab system\r\n  const tabs = [\r\n    {\r\n      id: 'invoices',\r\n      label: 'Invoices',\r\n      icon: 'ri-file-list-3-line',\r\n      content: (\r\n        <InvoicesTab\r\n          onViewInvoice={handleViewInvoice}\r\n          onPayInvoice={handlePayInvoice}\r\n          onViewPaymentsDone={handleViewPaymentsDone}\r\n          onUploadPayment={handleUploadPayment}\r\n        />\r\n      )\r\n    },\r\n    {\r\n      id: 'payments',\r\n      label: 'Payment History',\r\n      icon: 'ri-money-dollar-circle-line',\r\n      content: (\r\n        <PaymentsTab\r\n          onViewPayment={handleViewPayment}\r\n          onUploadProof={handleUploadProof}\r\n        />\r\n      )\r\n    }\r\n  ];\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <Loader message=\"Loading payments...\" />\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  function formatMoney(paidAmount: number): React.ReactNode {\r\n    throw new Error('Function not implemented.');\r\n  }\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Header */}\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\r\n            <div>\r\n              <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Payments & Invoices</h1>\r\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                Manage your invoices and payment history\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Payment Statistics */}\r\n        {!statsLoading && statistics && (\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\r\n              <div className=\"p-5\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\r\n                      <i className=\"ri-file-list-3-line text-white text-lg\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"ml-5 w-0 flex-1\">\r\n                    <dl>\r\n                      <dt className=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\r\n                        Total Invoices\r\n                      </dt>\r\n                      <dd className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                        {statistics.total}\r\n                      </dd>\r\n                    </dl>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\r\n              <div className=\"p-5\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\r\n                      <i className=\"ri-time-line text-white text-lg\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"ml-5 w-0 flex-1\">\r\n                    <dl>\r\n                      <dt className=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\r\n                        Pending\r\n                      </dt>\r\n                      <dd className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                        {statistics.pending}\r\n                      </dd>\r\n                      <dd className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                        {formatMoney(statistics.pendingAmount)}\r\n                      </dd>\r\n                    </dl>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\r\n              <div className=\"p-5\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\r\n                      <i className=\"ri-check-line text-white text-lg\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"ml-5 w-0 flex-1\">\r\n                    <dl>\r\n                      <dt className=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\r\n                        Paid\r\n                      </dt>\r\n                      <dd className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                        {statistics.paid}\r\n                      </dd>\r\n                      <dd className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                        {formatMoney(statistics.paidAmount)}\r\n                      </dd>\r\n                    </dl>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\r\n              <div className=\"p-5\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 bg-red-500 rounded-md flex items-center justify-center\">\r\n                      <i className=\"ri-error-warning-line text-white text-lg\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"ml-5 w-0 flex-1\">\r\n                    <dl>\r\n                      <dt className=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\r\n                        Overdue\r\n                      </dt>\r\n                      <dd className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                        {statistics.overdue}\r\n                      </dd>\r\n                      <dd className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                        {formatAmount(statistics.overdueAmount)}\r\n                      </dd>\r\n                    </dl>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {statsLoading && (\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\">\r\n            {[1, 2, 3, 4].map((i) => (\r\n              <div key={i} className=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\r\n                <div className=\"p-5\">\r\n                  <div className=\"animate-pulse\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-md\"></div>\r\n                      <div className=\"ml-5 flex-1\">\r\n                        <div className=\"h-4 bg-gray-300 dark:bg-gray-600 rounded w-20 mb-2\"></div>\r\n                        <div className=\"h-6 bg-gray-300 dark:bg-gray-600 rounded w-12\"></div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n\r\n        {/* Payment Tabs */}\r\n        <PaymentTabs\r\n          tabs={tabs}\r\n          activeTab={activeTab}\r\n          onTabChange={handleTabChange}\r\n        />\r\n      </div>\r\n\r\n      {/* Modals */}\r\n      <InvoiceDetailsModal\r\n        isOpen={showInvoiceDetails}\r\n        onClose={handleCloseModals}\r\n        invoice={selectedInvoice}\r\n      />\r\n\r\n      <UploadPaymentModal\r\n        isOpen={showUploadPayment}\r\n        onClose={handleCloseModals}\r\n        invoice={selectedInvoice}\r\n        onSuccess={handleUploadSuccess}\r\n      />\r\n\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default CustomerPaymentsPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;;;AAfA;;;;;;;;;;;;;AAiBA,MAAM,uBAAuB;;IAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,eAAe;IACf,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,iBAAiB;QACrB,IAAI;YACF,gBAAgB;YAChB,0CAA0C;YAC1C,MAAM,eAAe,oIAAA,CAAA,iBAAc,CAAC,oBAAoB;YACxD,MAAM,iBAAiB,IAAI,QAAe,CAAC,GAAG,SAC5C,WAAW,IAAM,OAAO,IAAI,MAAM,gCAAgC;YAGpE,MAAM,QAAQ,MAAM,QAAQ,IAAI,CAAC;gBAAC;gBAAc;aAAe;YAC/D,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,cAAc;gBACZ,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,WAAW;gBACX,aAAa;gBACb,eAAe;gBACf,YAAY;gBACZ,eAAe;YACjB;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;iEAAiB;oBACrB,IAAI;wBAEF,QAAQ,GAAG,CAAC;wBAEZ,+BAA+B;wBAC/B,MAAM,cAAc;wBACpB,MAAM,iBAAiB,IAAI;6EAAc,CAAC,UACxC;qFAAW;wCACT,QAAQ,IAAI,CAAC;wCACb;oCACF;oFAAG;;wBAGL,MAAM,QAAQ,IAAI,CAAC;4BAAC;4BAAa;yBAAe;wBAChD,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yCAAyC;oBACzD,SAAU;wBACR,aAAa;wBACb,QAAQ,GAAG,CAAC;oBACd;gBACF;;YAEA;QACF;yCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,sBAAsB;IACxB;IAEA,MAAM,mBAAmB,CAAC;IACxB,qFAAqF;IACvF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,GAAG,CAAC,qCAAqC;QACjD,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,oBAAoB;QACxB,mBAAmB;QACnB,sBAAsB;QACtB,2BAA2B;QAC3B,qBAAqB;QACrB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB;QAC1B,6CAA6C;QAC7C;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,iBAAiB;IAC7B,mDAAmD;IACrD;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,6BAA6B;IACzC,gDAAgD;IAClD;IAGA,iCAAiC;IACjC,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,uBACE,6LAAC,4JAAA,CAAA,UAAW;gBACV,eAAe;gBACf,cAAc;gBACd,oBAAoB;gBACpB,iBAAiB;;;;;;QAGvB;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,uBACE,6LAAC,4JAAA,CAAA,UAAW;gBACV,eAAe;gBACf,eAAe;;;;;;QAGrB;KACD;IAED,IAAI,WAAW;QACb,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;oBAAC,SAAQ;;;;;;;;;;;;;;;;IAIxB;IAEA,SAAS,YAAY,UAAkB;QACrC,MAAM,IAAI,MAAM;IAClB;IAEA,qBACE,6LAAC,mJAAA,CAAA,UAAc;;0BACb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA0D;;;;;;kDACxE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;oBAQ7D,CAAC,gBAAgB,4BAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,6LAAC;4DAAG,WAAU;sEACX,WAAW,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,6LAAC;4DAAG,WAAU;sEACX,WAAW,OAAO;;;;;;sEAErB,6LAAC;4DAAG,WAAU;sEACX,YAAY,WAAW,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQjD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,6LAAC;4DAAG,WAAU;sEACX,WAAW,IAAI;;;;;;sEAElB,6LAAC;4DAAG,WAAU;sEACX,YAAY,WAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ9C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;0DAGjB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgE;;;;;;sEAG9E,6LAAC;4DAAG,WAAU;sEACX,WAAW,OAAO;;;;;;sEAErB,6LAAC;4DAAG,WAAU;sEACX,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE,WAAW,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUrD,8BACC,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6LAAC;gCAAY,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAPf;;;;;;;;;;kCAkBhB,6LAAC,4JAAA,CAAA,UAAW;wBACV,MAAM;wBACN,WAAW;wBACX,aAAa;;;;;;;;;;;;0BAKjB,6LAAC,oKAAA,CAAA,UAAmB;gBAClB,QAAQ;gBACR,SAAS;gBACT,SAAS;;;;;;0BAGX,6LAAC,mKAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,WAAW;;;;;;;;;;;;AAKnB;GAnUM;;QACwB,kIAAA,CAAA,UAAO;QACpB,qIAAA,CAAA,YAAS;;;KAFpB;uCAqUS", "debugId": null}}]}