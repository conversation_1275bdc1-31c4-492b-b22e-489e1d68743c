import { DepartmentService } from './department.service';
import { Department } from '../entities/department.entity';
import { CreateDepartmentDto } from 'src/dto/department/create-department.dto';
import { UpdateDepartmentDto } from 'src/dto/department/update-department.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class DepartmentController {
    private readonly departmentService;
    constructor(departmentService: DepartmentService);
    create(createDto: CreateDepartmentDto): Promise<Department>;
    findAll(query: PaginateQuery): Promise<PaginatedResult<Department>>;
    findOne(id: string): Promise<Department>;
    update(id: string, updateDto: UpdateDepartmentDto): Promise<Department>;
    remove(id: string): Promise<void>;
    restore(id: string): Promise<Department>;
    findDeleted(): Promise<Department[]>;
}
