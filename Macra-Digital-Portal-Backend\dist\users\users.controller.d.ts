import { UsersService } from './users.service';
import { CreateUserDto } from '../dto/user/create-user.dto';
import { UpdateUserDto } from '../dto/user/update-user.dto';
import { UpdateProfileDto } from '../dto/user/update-profile.dto';
import { ChangePasswordDto } from '../dto/user/change-password.dto';
import { User } from '../entities';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    getProfile(req: any): Promise<User | null>;
    updateProfile(req: any, updateProfileDto: UpdateProfileDto): Promise<User>;
    changePassword(req: any, changePasswordDto: ChangePasswordDto): Promise<{
        message: string;
    }>;
    uploadAvatar(req: any, file: Express.Multer.File): Promise<User>;
    removeAvatar(req: any): Promise<User>;
    deactivateAccount(req: any, deactivationData: any): Promise<{
        message: string;
    }>;
    findAll(query: PaginateQuery): Promise<PaginatedResult<User>>;
    getOfficers(query: PaginateQuery): Promise<PaginatedResult<User>>;
    findAllIncludingCustomers(query: PaginateQuery): Promise<PaginatedResult<User>>;
    findOne(id: string): Promise<User | null>;
    create(createUserDto: CreateUserDto): Promise<User>;
    update(id: string, updateUserDto: UpdateUserDto): Promise<User>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
