import { InvoicesService } from './invoices.service';
import { InvoicePdfService } from './invoice-pdf.service';
export declare class CustomerInvoicesController {
    private readonly invoicesService;
    private readonly invoicePdfService;
    constructor(invoicesService: InvoicesService, invoicePdfService: InvoicePdfService);
    getCustomerInvoices(status?: string, page?: number, limit?: number, search?: string, req?: any): Promise<any>;
    getCustomerInvoiceStatistics(req: any): Promise<any>;
    getCustomerInvoice(id: string, req: any): Promise<import("../entities").Invoices>;
    getCustomerApplicationInvoices(applicationId: string, req: any): Promise<import("../entities").Invoices[]>;
    getCustomerApplicationInvoiceStatus(applicationId: string, req: any): Promise<any>;
    downloadCustomerInvoice(id: string, req: any, res: any): Promise<void>;
}
