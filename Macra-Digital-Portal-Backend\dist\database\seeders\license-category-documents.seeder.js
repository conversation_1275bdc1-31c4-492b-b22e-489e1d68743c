"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const licenseTypeStepConfig_1 = require("../../common/utils/licenseTypeStepConfig");
const entities_1 = require("../../entities");
class LicenseCategoryDocumentsSeeder {
    async run(dataSource) {
        const documentRepository = dataSource.getRepository(entities_1.LicenseCategoryDocument);
        const categoryRepository = dataSource.getRepository(entities_1.LicenseCategories);
        const existingCount = await documentRepository.count();
        if (existingCount > 0) {
            return;
        }
        const categories = await categoryRepository.find({
            relations: ['license_type']
        });
        if (categories.length === 0) {
            console.error('No license categories found. Please run license categories seeder first.');
            return;
        }
        const standardDocuments = [
            'Business Plan',
            'Project proposal',
            'Stakeholder CVs',
            'Market analysis and projections',
            'Particulars of financial resources to be applied to project',
            'Tariff proposals',
            'Cash flow projections for 3 years',
            'Experience in the provision of similar services',
            'Business registration or incorporation certificate',
            'Valid tax compliance certificate',
            'Business plan (including service model, financials, and coverage)',
            'Proof of premises (lease/title deed)',
            'Goods in transit insurance',
            'Inventory of fleet/equipment',
            'Customer service policy',
            'IT/tracking system description',
            'Three months of bank statements',
            'Proof of payment (application fee of USD 100)'
        ];
        for (const category of categories) {
            const docs = (0, licenseTypeStepConfig_1.getLicenseTypeStepConfig)(category.license_type.code, category).requirements ?? standardDocuments;
            for (const documentName of docs) {
                const document = documentRepository.create({
                    license_category_id: category.license_category_id,
                    name: documentName,
                    is_required: true,
                });
                await documentRepository.save(document);
            }
        }
    }
}
exports.default = LicenseCategoryDocumentsSeeder;
//# sourceMappingURL=license-category-documents.seeder.js.map