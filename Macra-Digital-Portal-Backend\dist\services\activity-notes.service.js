"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityNotesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const activity_notes_entity_1 = require("../entities/activity-notes.entity");
const notification_helper_service_1 = require("../notifications/notification-helper.service");
const applications_service_1 = require("../applications/applications.service");
let ActivityNotesService = class ActivityNotesService {
    constructor(activityNotesRepository, notificationHelperService, applicationsService) {
        this.activityNotesRepository = activityNotesRepository;
        this.notificationHelperService = notificationHelperService;
        this.applicationsService = applicationsService;
    }
    async create(createDto, userId, skipNotification = false) {
        const activityNote = this.activityNotesRepository.create({
            ...createDto,
            created_by: userId,
        });
        const savedNote = await this.activityNotesRepository.save(activityNote);
        if (!createDto.is_internal && !skipNotification) {
            try {
                if (createDto.entity_type === 'application') {
                    await this.sendActivityNoteNotification(savedNote, userId);
                }
                else if (createDto.entity_type === 'user') {
                    await this.sendGeneralMessageNotification(savedNote, userId);
                }
            }
            catch (error) {
                console.error('Failed to send activity note notification:', error);
            }
        }
        return savedNote;
    }
    async findAll(queryDto = {}) {
        const where = {
            status: activity_notes_entity_1.ActivityNoteStatus.ACTIVE,
        };
        if (queryDto.entity_type)
            where.entity_type = queryDto.entity_type;
        if (queryDto.entity_id)
            where.entity_id = queryDto.entity_id;
        if (queryDto.note_type)
            where.note_type = queryDto.note_type;
        if (queryDto.status)
            where.status = queryDto.status;
        if (queryDto.category)
            where.category = queryDto.category;
        if (queryDto.step)
            where.step = queryDto.step;
        if (queryDto.priority)
            where.priority = queryDto.priority;
        if (queryDto.is_internal !== undefined)
            where.is_internal = queryDto.is_internal;
        if (queryDto.created_by)
            where.created_by = queryDto.created_by;
        const options = {
            where,
            relations: ['creator', 'updater'],
            order: { created_at: 'DESC' },
        };
        return await this.activityNotesRepository.find(options);
    }
    async findByEntity(entityType, entityId) {
        return await this.findAll({
            entity_type: entityType,
            entity_id: entityId,
        });
    }
    async findByEntityAndStep(entityType, entityId, step) {
        return await this.findAll({
            entity_type: entityType,
            entity_id: entityId,
            step: step,
        });
    }
    async findOne(id) {
        const options = {
            where: { id },
            relations: ['creator', 'updater'],
        };
        const activityNote = await this.activityNotesRepository.findOne(options);
        if (!activityNote) {
            throw new common_1.NotFoundException(`Activity note with ID ${id} not found`);
        }
        return activityNote;
    }
    async update(id, updateDto, userId) {
        const activityNote = await this.findOne(id);
        if (activityNote.created_by !== userId) {
            throw new common_1.ForbiddenException('You can only update your own notes');
        }
        Object.assign(activityNote, updateDto);
        activityNote.updated_by = userId;
        return await this.activityNotesRepository.save(activityNote);
    }
    async archive(id, userId) {
        const activityNote = await this.findOne(id);
        if (activityNote.created_by !== userId) {
            throw new common_1.ForbiddenException('You can only archive your own notes');
        }
        activityNote.status = activity_notes_entity_1.ActivityNoteStatus.ARCHIVED;
        activityNote.archived_at = new Date();
        activityNote.updated_by = userId;
        return await this.activityNotesRepository.save(activityNote);
    }
    async softDelete(id, userId) {
        const activityNote = await this.findOne(id);
        if (activityNote.created_by !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own notes');
        }
        activityNote.status = activity_notes_entity_1.ActivityNoteStatus.DELETED;
        activityNote.deleted_at = new Date();
        activityNote.updated_by = userId;
        await this.activityNotesRepository.save(activityNote);
    }
    async hardDelete(id, userId) {
        const activityNote = await this.findOne(id);
        if (activityNote.created_by !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own notes');
        }
        await this.activityNotesRepository.remove(activityNote);
    }
    async createEvaluationComment(applicationId, step, comment, userId, metadata) {
        return await this.create({
            entity_type: 'application',
            entity_id: applicationId,
            note: comment,
            note_type: 'evaluation_comment',
            step: step,
            category: 'evaluation',
            metadata: metadata,
            is_internal: true,
        }, userId);
    }
    async createStatusUpdate(applicationId, statusChange, userId, metadata) {
        return await this.create({
            entity_type: 'application',
            entity_id: applicationId,
            note: statusChange,
            note_type: 'status_update',
            category: 'status',
            metadata: metadata,
            priority: 'high',
        }, userId);
    }
    async sendGeneralMessageNotification(activityNote, userId) {
        try {
            const createdBy = activityNote.creator ?
                `${activityNote.creator.first_name} ${activityNote.creator.last_name}` :
                'MACRA Team';
            const additionalEmails = activityNote.metadata?.additional_emails || [];
            if (additionalEmails.length === 0) {
                console.warn('No additional emails provided for general message');
                return;
            }
            console.log(`📧 Sending general message to ${additionalEmails.length} recipient(s): ${additionalEmails.join(', ')}`);
            for (const email of additionalEmails) {
                if (email && email.trim()) {
                    try {
                        await this.notificationHelperService.sendEmailNotification({
                            recipientEmail: email.trim(),
                            recipientName: 'Valued User',
                            subject: `Message from MACRA - ${createdBy}`,
                            message: activityNote.note,
                            htmlContent: this.generateGeneralMessageHtml(activityNote.note, createdBy),
                            entityType: 'user',
                            entityId: activityNote.entity_id,
                            recipientType: 'STAFF',
                            createdBy: userId,
                            sendEmail: true,
                            createInApp: false,
                        });
                        console.log(`✅ General message sent to: ${email}`);
                    }
                    catch (emailError) {
                        console.error(`❌ Failed to send general message to ${email}:`, emailError);
                    }
                }
            }
        }
        catch (error) {
            console.error('Error in sendGeneralMessageNotification:', error);
            throw error;
        }
    }
    generateGeneralMessageHtml(message, senderName) {
        return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <img src="cid:logo@macra" alt="MACRA Logo" style="height: 60px;">
            <h1 style="color: #dc2626; margin: 20px 0 10px 0; font-size: 24px;">MACRA Digital Portal</h1>
            <p style="color: #666; margin: 0; font-size: 16px;">Message from ${senderName}</p>
          </div>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
            <p style="color: #333; line-height: 1.6; margin: 0; white-space: pre-wrap;">${message}</p>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
            <p style="color: #666; font-size: 14px; margin: 0;">
              This message was sent from the MACRA Digital Portal by ${senderName}.
            </p>
            <p style="color: #666; font-size: 12px; margin: 10px 0 0 0;">
              © ${new Date().getFullYear()} Malawi Communications Regulatory Authority (MACRA)
            </p>
          </div>
        </div>
      </div>
    `;
    }
    async sendActivityNoteNotification(activityNote, userId) {
        try {
            const application = await this.applicationsService.findOne(activityNote.entity_id);
            if (!application || !application.applicant) {
                console.warn(`Application or applicant not found for activity note ${activityNote.id}`);
                return;
            }
            const noteType = activityNote.note_type || 'general';
            const category = activityNote.category || 'general';
            const step = activityNote.step;
            const createdBy = activityNote.creator ?
                `${activityNote.creator.first_name} ${activityNote.creator.last_name}` :
                'MACRA Team';
            const additionalEmails = activityNote.metadata?.additional_emails || [];
            await this.notificationHelperService.notifyActivityNote(activityNote.entity_id, application.applicant.applicant_id, application.applicant.email, application.applicant.name, application.application_number, application.license_category?.license_type?.name || 'License', activityNote.note, noteType, category, step, createdBy, activityNote.created_at.toLocaleDateString(), userId);
            if (additionalEmails.length > 0) {
                console.log(`📧 Sending activity note notifications to ${additionalEmails.length} additional email(s): ${additionalEmails.join(', ')}`);
                for (const email of additionalEmails) {
                    if (email && email.trim()) {
                        try {
                            await this.notificationHelperService.notifyActivityNote(activityNote.entity_id, null, email.trim(), 'Additional Recipient', application.application_number, application.license_category?.license_type?.name || 'License', activityNote.note, noteType, category, step, createdBy, activityNote.created_at.toLocaleDateString(), userId, true);
                            console.log(`✅ Additional notification sent to: ${email}`);
                        }
                        catch (emailError) {
                            console.error(`❌ Failed to send notification to additional email ${email}:`, emailError);
                        }
                    }
                }
            }
            console.log(`✅ Activity note notification sent for application ${application.application_number}`);
        }
        catch (error) {
            console.error('❌ Failed to send activity note notification:', error);
            throw error;
        }
    }
};
exports.ActivityNotesService = ActivityNotesService;
exports.ActivityNotesService = ActivityNotesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(activity_notes_entity_1.ActivityNote)),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => notification_helper_service_1.NotificationHelperService))),
    __param(2, (0, common_1.Inject)((0, common_1.forwardRef)(() => applications_service_1.ApplicationsService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        notification_helper_service_1.NotificationHelperService,
        applications_service_1.ApplicationsService])
], ActivityNotesService);
//# sourceMappingURL=activity-notes.service.js.map