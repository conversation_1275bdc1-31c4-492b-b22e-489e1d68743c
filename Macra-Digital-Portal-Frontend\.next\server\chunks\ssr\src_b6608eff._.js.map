{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { License, PaginatedResponse } from '@/types';\r\n\r\n\r\nexport const licenseService = {\r\n  // Get all licenses with pagination\r\n  async getLicenses(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    status?: string;\r\n    licenseType?: string;\r\n    dateRange?: string;\r\n  }): Promise<PaginatedResponse<License> > {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    if (params?.licenseType) queryParams.append('filter.licenseType', params.licenseType);\r\n    if (params?.dateRange) queryParams.append('filter.dateRange', params.dateRange);\r\n\r\n    const response = await apiClient.get(`/licenses?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single license by ID\r\n  async getLicense(id: string): Promise<License> {\r\n    const response = await apiClient.get(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license by application ID\r\n  async getLicenseByApplication(applicationId: string): Promise<License | null> {\r\n    try {\r\n      // Since there's no direct endpoint, we'll get all licenses and filter by application_id\r\n      // In a real implementation, you'd want to add this endpoint to the backend\r\n      const response = await apiClient.get(`/licenses?filter.application_id=${applicationId}`);\r\n      const result = processApiResponse(response);\r\n      \r\n      if (result.data && result.data.length > 0) {\r\n        return result.data[0]; // Return the first (and should be only) license for this application\r\n      }\r\n      \r\n      return null;\r\n    } catch (error) {\r\n      console.error('Error getting license by application:', error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  // Get licenses by applicant\r\n  async getLicensesByApplicant(applicantId: string): Promise<License[]> {\r\n    const response = await apiClient.get(`/licenses/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license by license number\r\n  async getLicenseByNumber(licenseNumber: string): Promise<License> {\r\n    const response = await apiClient.get(`/licenses/by-number/${licenseNumber}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Download license PDF\r\n  async downloadLicensePDF(licenseId: string): Promise<Blob> {\r\n    try {\r\n      const response = await apiClient.get(`/licenses/${licenseId}/pdf`, {\r\n        responseType: 'blob',\r\n        headers: {\r\n          'Accept': 'application/pdf',\r\n        },\r\n      });\r\n      \r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error downloading license PDF:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get license statistics\r\n  async getLicenseStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/licenses/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get licenses expiring soon\r\n  async getExpiringSoon(days: number = 30): Promise<License[]> {\r\n    const response = await apiClient.get(`/licenses/expiring-soon?days=${days}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new license (admin only)\r\n  async createLicense(data: {\r\n    license_number: string;\r\n    application_id: string;\r\n    applicant_id: string;\r\n    license_type_id: string;\r\n    status?: string;\r\n    issue_date: string;\r\n    expiry_date: string;\r\n    issued_by: string;\r\n    conditions?: string;\r\n  }): Promise<License> {\r\n    const response = await apiClient.post('/licenses', data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update license (admin only)\r\n  async updateLicense(id: string, data: Partial<License>): Promise<License> {\r\n    const response = await apiClient.put(`/licenses/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license (admin only)\r\n  async deleteLicense(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIO,MAAM,iBAAiB;IAC5B,mCAAmC;IACnC,MAAM,aAAY,MASjB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QACrE,IAAI,QAAQ,aAAa,YAAY,MAAM,CAAC,sBAAsB,OAAO,WAAW;QACpF,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,oBAAoB,OAAO,SAAS;QAE9E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,YAAY,QAAQ,IAAI;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2BAA2B;IAC3B,MAAM,YAAW,EAAU;QACzB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,yBAAwB,aAAqB;QACjD,IAAI;YACF,wFAAwF;YACxF,2EAA2E;YAC3E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,gCAAgC,EAAE,eAAe;YACvF,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAElC,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;gBACzC,OAAO,OAAO,IAAI,CAAC,EAAE,EAAE,qEAAqE;YAC9F;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,wBAAuB,WAAmB;QAC9C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,aAAa;QAC5E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,oBAAmB,aAAqB;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,eAAe;QAC3E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,oBAAmB,SAAiB;QACxC,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,IAAI,CAAC,EAAE;gBACjE,cAAc;gBACd,SAAS;oBACP,UAAU;gBACZ;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM,iBAAgB,OAAe,EAAE;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,MAAM;QAC3E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kCAAkC;IAClC,MAAM,eAAc,IAUnB;QACC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,aAAa;QACnD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU,EAAE,IAAsB;QACpD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;QACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU;QAC5B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;QACzD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/cacheService.ts"], "sourcesContent": ["// Cache service for API responses to reduce rate limiting\r\n\r\ninterface CacheItem<T> {\r\n  data: T;\r\n  timestamp: number;\r\n  expiresAt: number;\r\n}\r\n\r\nclass CacheService {\r\n  private cache = new Map<string, CacheItem<any>>();\r\n  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL\r\n\r\n  /**\r\n   * Set cache item with TTL\r\n   */\r\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\r\n    const now = Date.now();\r\n    const item: CacheItem<T> = {\r\n      data,\r\n      timestamp: now,\r\n      expiresAt: now + ttl\r\n    };\r\n    \r\n    this.cache.set(key, item);\r\n    console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);\r\n  }\r\n\r\n  /**\r\n   * Get cache item if not expired\r\n   */\r\n  get<T>(key: string): T | null {\r\n    const item = this.cache.get(key);\r\n    \r\n    if (!item) {\r\n      console.log(`Cache MISS: ${key}`);\r\n      return null;\r\n    }\r\n\r\n    const now = Date.now();\r\n    if (now > item.expiresAt) {\r\n      console.log(`Cache EXPIRED: ${key}`);\r\n      this.cache.delete(key);\r\n      return null;\r\n    }\r\n\r\n    console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);\r\n    return item.data as T;\r\n  }\r\n\r\n  /**\r\n   * Check if cache has valid item\r\n   */\r\n  has(key: string): boolean {\r\n    return this.get(key) !== null;\r\n  }\r\n\r\n  /**\r\n   * Delete cache item\r\n   */\r\n  delete(key: string): boolean {\r\n    console.log(`Cache DELETE: ${key}`);\r\n    return this.cache.delete(key);\r\n  }\r\n\r\n  /**\r\n   * Clear all cache\r\n   */\r\n  clear(): void {\r\n    console.log('Cache CLEAR: All items');\r\n    this.cache.clear();\r\n  }\r\n\r\n  /**\r\n   * Get cache stats\r\n   */\r\n  getStats(): { size: number; keys: string[] } {\r\n    return {\r\n      size: this.cache.size,\r\n      keys: Array.from(this.cache.keys())\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clean expired items\r\n   */\r\n  cleanup(): void {\r\n    const now = Date.now();\r\n    let cleaned = 0;\r\n\r\n    for (const [key, item] of this.cache.entries()) {\r\n      if (now > item.expiresAt) {\r\n        this.cache.delete(key);\r\n        cleaned++;\r\n      }\r\n    }\r\n\r\n    if (cleaned > 0) {\r\n      console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get or set pattern - fetch data if not cached\r\n   */\r\n  async getOrSet<T>(\r\n    key: string,\r\n    fetcher: () => Promise<T>,\r\n    ttl: number = this.defaultTTL\r\n  ): Promise<T> {\r\n    // Try to get from cache first\r\n    const cached = this.get<T>(key);\r\n    if (cached !== null) {\r\n      return cached;\r\n    }\r\n\r\n    // Fetch fresh data\r\n    console.log(`Cache FETCH: ${key}`);\r\n    const data = await fetcher();\r\n    \r\n    // Store in cache\r\n    this.set(key, data, ttl);\r\n    \r\n    return data;\r\n  }\r\n\r\n  /**\r\n   * Invalidate cache by pattern\r\n   */\r\n  invalidatePattern(pattern: string): void {\r\n    const regex = new RegExp(pattern);\r\n    let invalidated = 0;\r\n\r\n    for (const key of this.cache.keys()) {\r\n      if (regex.test(key)) {\r\n        this.cache.delete(key);\r\n        invalidated++;\r\n      }\r\n    }\r\n\r\n    if (invalidated > 0) {\r\n      console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);\r\n    }\r\n  }\r\n}\r\n\r\n// Create singleton instance\r\nexport const cacheService = new CacheService();\r\n\r\n// Cache keys constants\r\nexport const CACHE_KEYS = {\r\n  LICENSE_TYPES: 'license-types',\r\n  LICENSE_CATEGORIES: 'license-categories',\r\n  LICENSE_CATEGORIES_BY_TYPE: (typeId: string) => `license-categories-type-${typeId}`,\r\n  USER_APPLICATIONS: 'user-applications',\r\n  APPLICATION: (id: string) => `application-${id}`,\r\n} as const;\r\n\r\n// Cache TTL constants (in milliseconds)\r\nexport const CACHE_TTL = {\r\n  SHORT: 2 * 60 * 1000,      // 2 minutes\r\n  MEDIUM: 5 * 60 * 1000,     // 5 minutes\r\n  LONG: 15 * 60 * 1000,      // 15 minutes\r\n  VERY_LONG: 60 * 60 * 1000, // 1 hour\r\n} as const;\r\n\r\n// Auto cleanup every 5 minutes\r\nsetInterval(() => {\r\n  cacheService.cleanup();\r\n}, 5 * 60 * 1000);\r\n\r\nexport default cacheService;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;AAQ1D,MAAM;IACI,QAAQ,IAAI,MAA8B;IAC1C,aAAa,IAAI,KAAK,KAAK;IAEnC;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAqB;YACzB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;QACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa,EAAE,IAAI,GAAG,CAAC;IACvD;IAEA;;GAEC,GACD,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE5B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK;YAChC,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,GAAG,CAAC;QAChE,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,IAAI,GAAW,EAAW;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;IAC3B;IAEA;;GAEC,GACD,OAAO,GAAW,EAAW;QAC3B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA;;GAEC,GACD,QAAc;QACZ,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA;;GAEC,GACD,WAA6C;QAC3C,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,UAAU;QAEd,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC9C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,UAAU,GAAG;YACf,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,cAAc,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,MAAc,IAAI,CAAC,UAAU,EACjB;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK;QACjC,MAAM,OAAO,MAAM;QAEnB,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;QAEpB,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QACzB,IAAI,cAAc;QAElB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,cAAc,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,yBAAyB,EAAE,SAAS;QAC3F;IACF;AACF;AAGO,MAAM,eAAe,IAAI;AAGzB,MAAM,aAAa;IACxB,eAAe;IACf,oBAAoB;IACpB,4BAA4B,CAAC,SAAmB,CAAC,wBAAwB,EAAE,QAAQ;IACnF,mBAAmB;IACnB,aAAa,CAAC,KAAe,CAAC,YAAY,EAAE,IAAI;AAClD;AAGO,MAAM,YAAY;IACvB,OAAO,IAAI,KAAK;IAChB,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAEA,+BAA+B;AAC/B,YAAY;IACV,aAAa,OAAO;AACtB,GAAG,IAAI,KAAK;uCAEG", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseTypeService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\r\nimport { CreateLicenseTypeDto, LicenseType, PaginateQuery , PaginatedResponse, UpdateLicenseTypeDto } from \"@/types\";\r\n\r\nexport const licenseTypeService = {\r\n  // Get all license types with pagination\r\n  async getLicenseTypes(query: PaginateQuery = {}): Promise<PaginatedResponse<LicenseType>> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/license-types?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license type by ID\r\n  async getLicenseType(id: string): Promise<LicenseType> {\r\n    const response = await apiClient.get(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  \r\n  // Get license type by ID\r\n  async getLicenseTypeByCode(code: string): Promise<LicenseType> {\r\n    const response = await apiClient.get(`/license-types/by-code/${code}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n\r\n  // Create new license type\r\n  async createLicenseType(licenseTypeData: CreateLicenseTypeDto): Promise<LicenseType> {\r\n    const response = await apiClient.post('/license-types', licenseTypeData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update license type\r\n  async updateLicenseType(id: string, licenseTypeData: UpdateLicenseTypeDto): Promise<LicenseType> {\r\n    const response = await apiClient.put(`/license-types/${id}`, licenseTypeData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license type\r\n  async deleteLicenseType(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/license-types/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get all license types (simple list for dropdowns) with caching\r\n  async getAllLicenseTypes(): Promise<any> {\r\n    return cacheService.getOrSet(\r\n      CACHE_KEYS.LICENSE_TYPES,\r\n      async () => {\r\n        console.log('Fetching license types from API...');\r\n        // Reduce limit to avoid rate limiting\r\n        const response = await this.getLicenseTypes({ limit: 100 });\r\n        return processApiResponse(response);\r\n      },\r\n      CACHE_TTL.LONG // Cache for 15 minutes\r\n    );\r\n  },\r\n\r\n  // Get license types for sidebar navigation\r\n  async getNavigationItems(): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get('/license-types/navigation/sidebar');\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('LicenseTypeService.getNavigationItems error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,MAAM,qBAAqB;IAChC,wCAAwC;IACxC,MAAM,iBAAgB,QAAuB,CAAC,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC3D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAGA,yBAAyB;IACzB,MAAM,sBAAqB,IAAY;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM;QACrE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAGA,0BAA0B;IAC1B,MAAM,mBAAkB,eAAqC;QAC3D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,kBAAkB;QACxD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU,EAAE,eAAqC;QACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;QAC7D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;QAC9D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iEAAiE;IACjE,MAAM;QACJ,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,+HAAA,CAAA,aAAU,CAAC,aAAa,EACxB;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;gBAAE,OAAO;YAAI;YACzD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,GACA,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,2CAA2C;IAC3C,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;AAGF", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Pagination.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\n// Import the pagination response type from the existing structure\r\ninterface PaginationMeta {\r\n  itemsPerPage: number;\r\n  totalItems: number;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  sortBy: [string, string][];\r\n  searchBy: string[];\r\n  search: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\ninterface PaginationProps {\r\n  meta: PaginationMeta;\r\n  onPageChange: (page: number) => void;\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  showFirstLast?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showInfo?: boolean;\r\n  maxVisiblePages?: number;\r\n  pageSizeOptions?: number[];\r\n  className?: string;\r\n}\r\n\r\nconst Pagination: React.FC<PaginationProps> = ({\r\n  meta,\r\n  onPageChange,\r\n  onPageSizeChange,\r\n  showFirstLast = true,\r\n  showPageSizeSelector = true,\r\n  showInfo = true,\r\n  maxVisiblePages = 7,\r\n  pageSizeOptions = [10, 25, 50, 100],\r\n  className = ''\r\n}) => {\r\n  const { currentPage, totalPages, totalItems, itemsPerPage } = meta;\r\n\r\n  // Don't render if there's only one page or no pages and no additional features\r\n  if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate current items range\r\n  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;\r\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\r\n\r\n  // Calculate which pages to show\r\n  const getVisiblePages = (): (number | string)[] => {\r\n    const pages: (number | string)[] = [];\r\n    \r\n    // If total pages is less than or equal to maxVisiblePages, show all\r\n    if (totalPages <= maxVisiblePages) {\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        pages.push(i);\r\n      }\r\n      return pages;\r\n    }\r\n\r\n    // Always show first page\r\n    pages.push(1);\r\n\r\n    // Calculate start and end of the visible range around current page\r\n    const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current\r\n    let startPage = Math.max(2, currentPage - sidePages);\r\n    let endPage = Math.min(totalPages - 1, currentPage + sidePages);\r\n\r\n    // Adjust if we're near the beginning\r\n    if (currentPage <= sidePages + 2) {\r\n      startPage = 2;\r\n      endPage = Math.min(totalPages - 1, maxVisiblePages - 1);\r\n    }\r\n\r\n    // Adjust if we're near the end\r\n    if (currentPage >= totalPages - sidePages - 1) {\r\n      startPage = Math.max(2, totalPages - maxVisiblePages + 2);\r\n      endPage = totalPages - 1;\r\n    }\r\n\r\n    // Add ellipsis after first page if needed\r\n    if (startPage > 2) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Add pages in the visible range\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    // Add ellipsis before last page if needed\r\n    if (endPage < totalPages - 1) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Always show last page (if it's not already included)\r\n    if (totalPages > 1) {\r\n      pages.push(totalPages);\r\n    }\r\n\r\n    return pages;\r\n  };\r\n\r\n  const visiblePages = getVisiblePages();\r\n\r\n  const handlePageClick = (page: number | string) => {\r\n    if (typeof page === 'number' && page !== currentPage) {\r\n      onPageChange(page);\r\n    }\r\n  };\r\n\r\n  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newPageSize = parseInt(event.target.value);\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(newPageSize);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentPage > 1) {\r\n      onPageChange(currentPage - 1);\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentPage < totalPages) {\r\n      onPageChange(currentPage + 1);\r\n    }\r\n  };\r\n\r\n  const handleFirst = () => {\r\n    if (currentPage !== 1) {\r\n      onPageChange(1);\r\n    }\r\n  };\r\n\r\n  const handleLast = () => {\r\n    if (currentPage !== totalPages) {\r\n      onPageChange(totalPages);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>\r\n      {/* Left side - Info and page size selector */}\r\n      <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\r\n        {/* Items info */}\r\n        {showInfo && totalItems > 0 && (\r\n          <div className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n            Showing <span className=\"font-medium\">{startItem}</span> to{' '}\r\n            <span className=\"font-medium\">{endItem}</span> of{' '}\r\n            <span className=\"font-medium\">{totalItems}</span> results\r\n          </div>\r\n        )}\r\n\r\n        {/* Page size selector */}\r\n        {showPageSizeSelector && onPageSizeChange && (\r\n          <div className=\"flex items-center space-x-2\">\r\n            <select\r\n              value={itemsPerPage}\r\n              onChange={handlePageSizeChange}\r\n              className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\r\n            >\r\n              {pageSizeOptions.map((size) => (\r\n                <option key={size} value={size}>\r\n                  {size} per page\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side - Pagination controls */}\r\n      {totalPages > 1 && (\r\n        <nav className=\"flex items-center space-x-1\" aria-label=\"Pagination\">\r\n          {/* First page button */}\r\n          {showFirstLast && currentPage > 1 && (\r\n            <button\r\n              onClick={handleFirst}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to first page\"\r\n            >\r\n              <i className=\"ri-skip-back-line\"></i>\r\n            </button>\r\n          )}\r\n\r\n          {/* Previous button */}\r\n          <button\r\n            onClick={handlePrevious}\r\n            disabled={currentPage === 1}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === 1\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`}\r\n            aria-label=\"Go to previous page\"\r\n          >\r\n            <i className=\"ri-arrow-left-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Page numbers */}\r\n          {visiblePages.map((page, index) => (\r\n            <React.Fragment key={index}>\r\n              {page === '...' ? (\r\n                <span className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400\">\r\n                  ...\r\n                </span>\r\n              ) : (\r\n                <button\r\n                  onClick={() => handlePageClick(page)}\r\n                  className={`inline-flex items-center px-4 py-2 text-sm font-medium border ${\r\n                    page === currentPage\r\n                      ? 'text-white bg-red-600 border-red-600 hover:bg-red-700'\r\n                      : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n                  }`}\r\n                  aria-label={`Go to page ${page}`}\r\n                  aria-current={page === currentPage ? 'page' : undefined}\r\n                >\r\n                  {page}\r\n                </button>\r\n              )}\r\n            </React.Fragment>\r\n          ))}\r\n\r\n          {/* Next button */}\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={currentPage === totalPages}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === totalPages\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`}\r\n            aria-label=\"Go to next page\"\r\n          >\r\n            <i className=\"ri-arrow-right-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Last page button */}\r\n          {showFirstLast && currentPage < totalPages && (\r\n            <button\r\n              onClick={handleLast}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to last page\"\r\n            >\r\n              <i className=\"ri-skip-forward-line\"></i>\r\n            </button>\r\n          )}\r\n        </nav>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Pagination;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA4BA,MAAM,aAAwC,CAAC,EAC7C,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,WAAW,IAAI,EACf,kBAAkB,CAAC,EACnB,kBAAkB;IAAC;IAAI;IAAI;IAAI;CAAI,EACnC,YAAY,EAAE,EACf;IACC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;IAE9D,+EAA+E;IAC/E,IAAI,cAAc,KAAK,CAAC,wBAAwB,CAAC,UAAU;QACzD,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,YAAY,aAAa,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,IAAI;IAC1E,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,oEAAoE;QACpE,IAAI,cAAc,iBAAiB;YACjC,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;YACA,OAAO;QACT;QAEA,yBAAyB;QACzB,MAAM,IAAI,CAAC;QAEX,mEAAmE;QACnE,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,IAAI,kCAAkC;QAC3F,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC1C,IAAI,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;QAErD,qCAAqC;QACrC,IAAI,eAAe,YAAY,GAAG;YAChC,YAAY;YACZ,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,kBAAkB;QACvD;QAEA,+BAA+B;QAC/B,IAAI,eAAe,aAAa,YAAY,GAAG;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,kBAAkB;YACvD,UAAU,aAAa;QACzB;QAEA,0CAA0C;QAC1C,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,CAAC;QACb;QAEA,iCAAiC;QACjC,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,0CAA0C;QAC1C,IAAI,UAAU,aAAa,GAAG;YAC5B,MAAM,IAAI,CAAC;QACb;QAEA,uDAAuD;QACvD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,YAAY,SAAS,aAAa;YACpD,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,SAAS,MAAM,MAAM,CAAC,KAAK;QAC/C,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB,GAAG;YACrB,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB,YAAY;YAC9B,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gKAAgK,EAAE,WAAW;;0BAE5L,8OAAC;gBAAI,WAAU;;oBAEZ,YAAY,aAAa,mBACxB,8OAAC;wBAAI,WAAU;;4BAA2C;0CAChD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAiB;4BAAI;0CAC5D,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAe;4BAAI;0CAClD,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAkB;;;;;;;oBAKpD,wBAAwB,kCACvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAO;4BACP,UAAU;4BACV,WAAU;sCAET,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;oCAAkB,OAAO;;wCACvB;wCAAK;;mCADK;;;;;;;;;;;;;;;;;;;;;YAUtB,aAAa,mBACZ,8OAAC;gBAAI,WAAU;gBAA8B,cAAW;;oBAErD,iBAAiB,cAAc,mBAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;kCAKjB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,IACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,IAAI,KAAK,gBAAgB;wBAC5D,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;sCACZ,SAAS,sBACR,8OAAC;gCAAK,WAAU;0CAAgK;;;;;yFAIhL,8OAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,CAAC,8DAA8D,EACxE,SAAS,cACL,0DACA,wLACJ;gCACF,cAAY,CAAC,WAAW,EAAE,MAAM;gCAChC,gBAAc,SAAS,cAAc,SAAS;0CAE7C;;;;;;2BAhBc;;;;;kCAuBvB,8OAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,CAAC,8DAA8D,EACxE,gBAAgB,aACZ,0HACA,uLACL,CAAC,EAAE,iBAAiB,cAAc,aAAa,KAAK,gBAAgB;wBACrE,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,iBAAiB,cAAc,4BAC9B,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAO3B;uCAEe", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/DataTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { PaginateQuery } from '../../types';\r\nimport Pagination from './Pagination';\r\nimport '../../styles/DataTable.css';\r\n\r\n// Generic paginated response interface to handle different response types\r\ninterface GenericPaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select?: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\ninterface Column<T> {\r\n  key: keyof T | string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  searchable?: boolean;\r\n  render?: (value: unknown, item: T) => React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface DataTableProps<T> {\r\n  columns: Column<T>[];\r\n  data: GenericPaginatedResponse<T> | null;\r\n  loading?: boolean;\r\n  onQueryChange: (query: PaginateQuery) => void;\r\n  searchPlaceholder?: string;\r\n  className?: string;\r\n  emptyStateIcon?: string;\r\n  emptyStateMessage?: string;\r\n}\r\n\r\nexport default function DataTable<T extends Record<string, unknown>>({\r\n  columns,\r\n  data,\r\n  loading = false,\r\n  onQueryChange,\r\n  searchPlaceholder = \"Search...\",\r\n  className = \"\",\r\n  emptyStateIcon = \"ri-inbox-line\",\r\n  emptyStateMessage = \"No data found\",\r\n}: DataTableProps<T>) {\r\n  const [query, setQuery] = useState<PaginateQuery>({\r\n    page: 1,\r\n    limit: 10,\r\n    search: '',\r\n    sortBy: [],\r\n  });\r\n  const [searchInput, setSearchInput] = useState('');\r\n\r\n  const handleSearch = useCallback((search: string) => {\r\n    try {\r\n      const newQuery = { ...query, search, page: 1 };\r\n      setQuery(newQuery);\r\n      onQueryChange(newQuery);\r\n    } catch (error) {\r\n      console.error('Error handling search:', error);\r\n    }\r\n  }, [query, onQueryChange]);\r\n\r\n  // Debounced search effect\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (searchInput !== query.search) {\r\n        handleSearch(searchInput);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchInput, query.search, handleSearch]);\r\n\r\n  const handleSort = (columnKey: string) => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    let newSortBy: string[] = [];\r\n\r\n    if (!currentSort) {\r\n      newSortBy = [`${columnKey}:ASC`];\r\n    } else if (currentSort.endsWith(':ASC')) {\r\n      newSortBy = [`${columnKey}:DESC`];\r\n    } else {\r\n      newSortBy = [];\r\n    }\r\n\r\n    const newQuery = { ...query, sortBy: newSortBy, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    const newQuery = { ...query, page };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const getSortDirection = (columnKey: string): 'asc' | 'desc' | null => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    if (!currentSort) return null;\r\n    return currentSort.endsWith(':ASC') ? 'asc' : 'desc';\r\n  };\r\n\r\n  // Handle null data case early\r\n  if (!data) {\r\n    return (\r\n      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n        <div className=\"p-6 text-center text-gray-500 dark:text-gray-400\">\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n            <span className=\"ml-2\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    const newQuery = { ...query, limit: newPageSize, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n      {/* Search Bar */}\r\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <i className=\"ri-search-line text-gray-400 dark:text-gray-500\"></i>\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder={searchPlaceholder}\r\n            value={searchInput}\r\n            onChange={(e) => setSearchInput(e.target.value)}\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className=\"overflow-x-auto data-table-container\">\r\n        <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n            <tr>\r\n              {columns.map((column) => (\r\n                <th\r\n                  key={String(column.key)}\r\n                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${\r\n                    column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''\r\n                  } ${column.className || ''}`}\r\n                  onClick={() => column.sortable && handleSort(String(column.key))}\r\n                >\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>{column.label}</span>\r\n                    {column.sortable && (\r\n                      <div className=\"flex flex-col\">\r\n                        <i className={`ri-arrow-up-s-line text-xs ${\r\n                          getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${\r\n                          getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n            {loading ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n                    <span className=\"ml-2\">Loading...</span>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : !data?.data || data.data.length === 0 ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex flex-col items-center justify-center py-8\">\r\n                    <i className={`${emptyStateIcon} text-4xl mb-2`}></i>\r\n                    <p>{emptyStateMessage}</p>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : (\r\n              data.data.map((item, index) => (\r\n                <tr key={index} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  {columns.map((column) => (\r\n                    <td key={String(column.key)} className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100\">\r\n                      {column.render\r\n                        ? column.render(item[column.key as keyof T], item)\r\n                        : String(item[column.key as keyof T] || '')\r\n                      }\r\n                    </td>\r\n                  ))}\r\n                </tr>\r\n              ))\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {data?.meta && data.meta.totalItems !== undefined && data.meta.currentPage !== undefined && data.meta.totalPages !== undefined && (\r\n        <Pagination\r\n          meta={{\r\n            ...data.meta,\r\n            totalItems: data.meta.totalItems,\r\n            currentPage: data.meta.currentPage,\r\n            totalPages: data.meta.totalPages,\r\n          }}\r\n          onPageChange={handlePageChange}\r\n          onPageSizeChange={handlePageSizeChange}\r\n          showFirstLast={true}\r\n          showPageSizeSelector={true}\r\n          showInfo={true}\r\n          maxVisiblePages={7}\r\n          pageSizeOptions={[10, 25, 50, 100]}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;;AAkDe,SAAS,UAA6C,EACnE,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,aAAa,EACb,oBAAoB,WAAW,EAC/B,YAAY,EAAE,EACd,iBAAiB,eAAe,EAChC,oBAAoB,eAAe,EACjB;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI;YACF,MAAM,WAAW;gBAAE,GAAG,KAAK;gBAAE;gBAAQ,MAAM;YAAE;YAC7C,SAAS;YACT,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF,GAAG;QAAC;QAAO;KAAc;IAEzB,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,gBAAgB,MAAM,MAAM,EAAE;gBAChC,aAAa;YACf;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAa,MAAM,MAAM;QAAE;KAAa;IAE5C,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,YAAsB,EAAE;QAE5B,IAAI,CAAC,aAAa;YAChB,YAAY;gBAAC,GAAG,UAAU,IAAI,CAAC;aAAC;QAClC,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;YACvC,YAAY;gBAAC,GAAG,UAAU,KAAK,CAAC;aAAC;QACnC,OAAO;YACL,YAAY,EAAE;QAChB;QAEA,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,QAAQ;YAAW,MAAM;QAAE;QACxD,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAK;QAClC,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,cAAc,MAAM,MAAM,EAAE,KAAK,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,YAAY,QAAQ,CAAC,UAAU,QAAQ;IAChD;IAEA,8BAA8B;IAC9B,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;sBACxF,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,OAAO;YAAa,MAAM;QAAE;QACzD,SAAS;QACT,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,4DAA4D,EAAE,WAAW;;0BAExF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BACC,MAAK;4BACL,aAAa;4BACb,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;0CACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wCAEC,WAAW,CAAC,kGAAkG,EAC5G,OAAO,QAAQ,GAAG,4DAA4D,GAC/E,CAAC,EAAE,OAAO,SAAS,IAAI,IAAI;wCAC5B,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,OAAO,GAAG;kDAE9D,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAM,OAAO,KAAK;;;;;;gDAClB,OAAO,QAAQ,kBACd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAW,CAAC,2BAA2B,EACxC,iBAAiB,OAAO,OAAO,GAAG,OAAO,QAAQ,iBAAiB,iBAClE;;;;;;sEACF,8OAAC;4DAAE,WAAW,CAAC,mCAAmC,EAChD,iBAAiB,OAAO,OAAO,GAAG,OAAO,SAAS,iBAAiB,iBACnE;;;;;;;;;;;;;;;;;;uCAfH,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;sCAuB9B,8OAAC;4BAAM,WAAU;sCACd,wBACC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;uCAI3B,CAAC,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK,kBACtC,8OAAC;0CACC,cAAA,8OAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAW,GAAG,eAAe,cAAc,CAAC;;;;;;0DAC/C,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;uCAKV,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC;oCAAe,WAAU;8CACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;4CAA4B,WAAU;sDACpC,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAY,EAAE,QAC3C,OAAO,IAAI,CAAC,OAAO,GAAG,CAAY,IAAI;2CAHnC,OAAO,OAAO,GAAG;;;;;mCAFrB;;;;;;;;;;;;;;;;;;;;;YAiBlB,MAAM,QAAQ,KAAK,IAAI,CAAC,UAAU,KAAK,aAAa,KAAK,IAAI,CAAC,WAAW,KAAK,aAAa,KAAK,IAAI,CAAC,UAAU,KAAK,2BACnH,8OAAC,0IAAA,CAAA,UAAU;gBACT,MAAM;oBACJ,GAAG,KAAK,IAAI;oBACZ,YAAY,KAAK,IAAI,CAAC,UAAU;oBAChC,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC,YAAY,KAAK,IAAI,CAAC,UAAU;gBAClC;gBACA,cAAc;gBACd,kBAAkB;gBAClB,eAAe;gBACf,sBAAsB;gBACtB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;oBAAC;oBAAI;oBAAI;oBAAI;iBAAI;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 993, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\r\n  label?: string;\r\n  error?: string;\r\n  helperText?: string;\r\n  required?: boolean;\r\n  options: SelectOption[];\r\n  placeholder?: string;\r\n  className?: string;\r\n  containerClassName?: string;\r\n  onChange?: (value: string) => void;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  required = false,\r\n  options = [],\r\n  placeholder = 'Select an option...',\r\n  className = '',\r\n  containerClassName = '',\r\n  onChange,\r\n  id,\r\n  value,\r\n  ...props\r\n}, ref) => {\r\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\r\n  \r\n  const baseSelectClasses = `\r\n    w-full px-3 py-2 border rounded-md shadow-sm \r\n    focus:outline-none focus:ring-2 focus:ring-offset-2 \r\n    disabled:opacity-50 disabled:cursor-not-allowed\r\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\r\n    transition-colors duration-200\r\n    appearance-none bg-white\r\n    bg-no-repeat bg-right bg-[length:16px_16px]\r\n    pr-10\r\n  `;\r\n  \r\n  const selectClasses = error\r\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\r\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    if (onChange) {\r\n      onChange(e.target.value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-1 ${containerClassName}`}>\r\n      {label && (\r\n        <label \r\n          htmlFor={selectId}\r\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\r\n        >\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <div className=\"relative\">\r\n        <select\r\n          ref={ref}\r\n          id={selectId}\r\n          value={value || ''}\r\n          onChange={handleChange}\r\n          className={`${selectClasses} ${className}`}\r\n          {...props}\r\n        >\r\n          {placeholder && (\r\n            <option value=\"\" disabled>\r\n              {placeholder}\r\n            </option>\r\n          )}\r\n          \r\n          {options.map((option) => (\r\n            <option \r\n              key={option.value} \r\n              value={option.value}\r\n              disabled={option.disabled}\r\n            >\r\n              {option.label}\r\n            </option>\r\n          ))}\r\n        </select>\r\n        \r\n        {/* Custom dropdown arrow */}\r\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\r\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\r\n        </div>\r\n      </div>\r\n      \r\n      {error && (\r\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,oBAAoB,CAAC;;;;;;;;;EAS3B,CAAC;IAED,MAAM,gBAAgB,QAClB,GAAG,kBAAkB,2EAA2E,CAAC,GACjG,GAAG,kBAAkB,0GAA0G,CAAC;IAEpI,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW;wBACzC,GAAG,KAAK;;4BAER,6BACC,8OAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/certificates/PostalServicesCertificate.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef } from 'react';\r\nimport { License } from '@/types';\r\n\r\ninterface PostalServicesCertificateProps {\r\n  license: License;\r\n}\r\n\r\nexport default function PostalServicesCertificate({ license }: PostalServicesCertificateProps) {\r\n  const qrCodeRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    // Generate QR code\r\n    if (qrCodeRef.current && typeof window !== 'undefined') {\r\n      import('qrcode').then((QRCode) => {\r\n        const qrData = `MACRA Postal License Verification\r\nRef: ${license.license_number}\r\nLicense No: ${license.license_number}\r\nLicensee: ${license.application.applicant?.name || 'N/A'}\r\nValid: ${license.issue_date} - ${license.expiry_date}\r\nVerify at: https://macra.mw/verify/${license.license_number}`;\r\n\r\n        QRCode.toCanvas(qrCodeRef.current!, qrData, {\r\n          width: 96,\r\n          margin: 1,\r\n          color: {\r\n            dark: '#000000',\r\n            light: '#FFFFFF'\r\n          }\r\n        }).catch((error) => {\r\n          console.error('QR Code generation failed:', error);\r\n          if (qrCodeRef.current) {\r\n            qrCodeRef.current.innerHTML = 'QR Code<br>Error';\r\n          }\r\n        });\r\n      });\r\n    }\r\n  }, [license]);\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const getValidityPeriod = () => {\r\n    const issueDate = new Date(license.issue_date);\r\n    const expiryDate = new Date(license.expiry_date);\r\n    const years = expiryDate.getFullYear() - issueDate.getFullYear();\r\n    return `${years} Years (${formatDate(license.issue_date)} - ${formatDate(license.expiry_date)})`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"postal-certificate\">\r\n      <style jsx>{`\r\n        .postal-certificate {\r\n          font-family: 'Times New Roman', serif;\r\n          background: white;\r\n          border: 4px solid #dc2626;\r\n          border-radius: 8px;\r\n          padding: 40px;\r\n          min-height: 842px;\r\n          position: relative;\r\n          overflow: hidden;\r\n        }\r\n        \r\n        .inner-border {\r\n          border: 2px solid #16a34a;\r\n          padding: 30px;\r\n          min-height: 100%;\r\n          position: relative;\r\n        }\r\n        \r\n        .watermark {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%) rotate(-15deg);\r\n          opacity: 0.05;\r\n          pointer-events: none;\r\n          z-index: 0;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          text-align: center;\r\n        }\r\n        \r\n        .watermark-text {\r\n          font-size: 36px;\r\n          font-weight: bold;\r\n          color: #dc2626;\r\n          letter-spacing: 3px;\r\n          line-height: 1.2;\r\n        }\r\n        \r\n        .content-wrapper {\r\n          position: relative;\r\n          z-index: 1;\r\n        }\r\n        \r\n        .header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n        }\r\n        \r\n        .logo-section {\r\n          margin-bottom: 20px;\r\n        }\r\n        \r\n        .authority-name {\r\n          border: 2px solid #dc2626;\r\n          display: inline-block;\r\n          padding: 12px 30px;\r\n          margin-bottom: 20px;\r\n        }\r\n        \r\n        .authority-title {\r\n          color: #dc2626;\r\n          font-weight: bold;\r\n          font-size: 16px;\r\n          letter-spacing: 1px;\r\n        }\r\n        \r\n        .reference-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          margin-bottom: 15px;\r\n          font-size: 14px;\r\n          color: #2563eb;\r\n        }\r\n        \r\n        .certificate-type {\r\n          background-color: #16a34a;\r\n          color: white;\r\n          padding: 10px 20px;\r\n          display: inline-block;\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n        \r\n        .certification-text {\r\n          font-size: 14px;\r\n          color: #374151;\r\n          margin-bottom: 25px;\r\n        }\r\n        \r\n        .license-details {\r\n          margin-bottom: 25px;\r\n          line-height: 1.8;\r\n        }\r\n        \r\n        .detail-row {\r\n          display: flex;\r\n          margin-bottom: 12px;\r\n          align-items: flex-start;\r\n        }\r\n        \r\n        .detail-label {\r\n          font-weight: 600;\r\n          min-width: 200px;\r\n          color: #374151;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .detail-colon {\r\n          margin: 0 10px;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        .detail-value {\r\n          font-weight: bold;\r\n          color: #111827;\r\n          flex: 1;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .services-section {\r\n          margin-bottom: 20px;\r\n        }\r\n        \r\n        .services-title {\r\n          font-weight: bold;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .services-list {\r\n          list-style: none;\r\n          padding-left: 20px;\r\n        }\r\n        \r\n        .services-list li {\r\n          margin-bottom: 4px;\r\n          font-size: 13px;\r\n          position: relative;\r\n        }\r\n        \r\n        .services-list li:before {\r\n          content: \"•\";\r\n          color: #16a34a;\r\n          font-weight: bold;\r\n          position: absolute;\r\n          left: -15px;\r\n        }\r\n        \r\n        .compliance-section {\r\n          text-align: justify;\r\n          line-height: 1.6;\r\n          margin-bottom: 30px;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .footer-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: flex-end;\r\n          margin-top: 40px;\r\n        }\r\n        \r\n        .signature-area {\r\n          flex: 1;\r\n        }\r\n        \r\n        .issue-location {\r\n          font-size: 14px;\r\n          margin-bottom: 60px;\r\n        }\r\n        \r\n        .signature-line {\r\n          text-align: center;\r\n          border-top: 1px solid black;\r\n          padding-top: 8px;\r\n          max-width: 200px;\r\n        }\r\n        \r\n        .dg-name {\r\n          font-weight: bold;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .dg-title {\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .qr-section {\r\n          flex-shrink: 0;\r\n          margin-left: 40px;\r\n          text-align: center;\r\n        }\r\n        \r\n        .qr-code {\r\n          width: 100px;\r\n          height: 100px;\r\n          border: 2px solid #9ca3af;\r\n          background: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 10px;\r\n          color: #6b7280;\r\n          margin-bottom: 8px;\r\n        }\r\n        \r\n        .qr-text {\r\n          font-size: 10px;\r\n          color: #4b5563;\r\n          max-width: 100px;\r\n        }\r\n        \r\n        .security-footer {\r\n          background-color: #06b6d4;\r\n          color: white;\r\n          text-align: center;\r\n          padding: 8px;\r\n          font-size: 12px;\r\n          font-weight: 600;\r\n          margin-top: 20px;\r\n          margin: 20px -30px -30px -30px;\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"inner-border\">\r\n        {/* Watermark */}\r\n        <div className=\"watermark\">\r\n          <div className=\"watermark-text\">\r\n            MALAWI COMMUNICATIONS<br />\r\n            REGULATORY AUTHORITY\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"content-wrapper\">\r\n          {/* Header */}\r\n          <div className=\"header\">\r\n            {/* Authority Name */}\r\n            <div className=\"authority-name\">\r\n              <h1 className=\"authority-title\">\r\n                MALAWI COMMUNICATIONS REGULATORY AUTHORITY\r\n              </h1>\r\n            </div>\r\n\r\n            {/* Reference and License Numbers */}\r\n            <div className=\"reference-section\">\r\n              <span>Ref: PS - {new Date().getFullYear()}</span>\r\n              <span>License No. {license.license_number}</span>\r\n            </div>\r\n\r\n            {/* Certificate Type */}\r\n            <div className=\"certificate-type\">\r\n              POSTAL SERVICES LICENSE\r\n            </div>\r\n            <p className=\"certification-text\">\r\n              This license authorizes the holder to operate postal services in Malawi\r\n            </p>\r\n          </div>\r\n\r\n          {/* License Details */}\r\n          <div className=\"license-details\">\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSEE NAME</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">BUSINESS REGISTRATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.business_registration_number || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">TAX IDENTIFICATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.tpin || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">REGISTERED ADDRESS</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <div className=\"detail-value\">\r\n                <div>{license.application.applicant?.name || 'N/A'}</div>\r\n                {/* <div>{license.application.applicant?.city || ''}, {license.application.applicant?.country || 'Malawi'}</div> */}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">SERVICE COVERAGE AREA</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">National</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSE VALIDITY PERIOD</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{getValidityPeriod()}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Authorized Services */}\r\n          <div className=\"services-section\">\r\n            <div className=\"services-title\">AUTHORIZED POSTAL SERVICES:</div>\r\n            <ul className=\"services-list\">\r\n              <li>Domestic mail collection, processing, and delivery</li>\r\n              <li>International mail services (inbound and outbound)</li>\r\n              <li>Express and courier services</li>\r\n              <li>Parcel and package delivery services</li>\r\n              <li>Postal financial services (money orders, postal banking)</li>\r\n              <li>Philatelic services</li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Compliance Statement */}\r\n          <div className=\"compliance-section\">\r\n            <p>\r\n              {license.conditions || \r\n                `This license is issued under the Communications Act, 2016, and authorizes the licensee \r\n                to provide postal services in Malawi subject to compliance with all applicable laws, \r\n                regulations, and license conditions. The licensee shall maintain adequate infrastructure, \r\n                qualified personnel, and service standards as prescribed by the Malawi Communications \r\n                Regulatory Authority. This license is non-transferable and must be renewed before expiration.`\r\n              }\r\n            </p>\r\n          </div>\r\n\r\n          {/* Footer with Signature and QR Code */}\r\n          <div className=\"footer-section\">\r\n            {/* Signature Section */}\r\n            <div className=\"signature-area\">\r\n              <div className=\"issue-location\">\r\n                <p>\r\n                  Issued at Lilongwe, this {formatDate(license.issue_date)}\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"signature-line\">\r\n                <p className=\"dg-name\">Daud Suleman</p>\r\n                <p className=\"dg-title\">Director General</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* QR Code Section */}\r\n            <div className=\"qr-section\">\r\n              <div className=\"qr-code\">\r\n                <div ref={qrCodeRef}></div>\r\n              </div>\r\n              <p className=\"qr-text\">\r\n                Scan for verification\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Security Footer */}\r\n          <div className=\"security-footer\">\r\n            This licence is issued without any alterations and remains the property of MACRA\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AASe,SAAS,0BAA0B,EAAE,OAAO,EAAkC;IAC3F,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;QACnB;;IAwBF,GAAG;QAAC;KAAQ;IAEZ,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,YAAY,IAAI,KAAK,QAAQ,UAAU;QAC7C,MAAM,aAAa,IAAI,KAAK,QAAQ,WAAW;QAC/C,MAAM,QAAQ,WAAW,WAAW,KAAK,UAAU,WAAW;QAC9D,OAAO,GAAG,MAAM,QAAQ,EAAE,WAAW,QAAQ,UAAU,EAAE,GAAG,EAAE,WAAW,QAAQ,WAAW,EAAE,CAAC,CAAC;IAClG;IAEA,qBACE,8OAAC;iDAAc;;;;;;0BAsOb,8OAAC;yDAAc;;kCAEb,8OAAC;iEAAc;kCACb,cAAA,8OAAC;qEAAc;;gCAAiB;8CACT,8OAAC;;;;;;;gCAAK;;;;;;;;;;;;kCAK/B,8OAAC;iEAAc;;0CAEb,8OAAC;yEAAc;;kDAEb,8OAAC;iFAAc;kDACb,cAAA,8OAAC;qFAAa;sDAAkB;;;;;;;;;;;kDAMlC,8OAAC;iFAAc;;0DACb,8OAAC;;;oDAAK;oDAAW,IAAI,OAAO,WAAW;;;;;;;0DACvC,8OAAC;;;oDAAK;oDAAa,QAAQ,cAAc;;;;;;;;;;;;;kDAI3C,8OAAC;iFAAc;kDAAmB;;;;;;kDAGlC,8OAAC;iFAAY;kDAAqB;;;;;;;;;;;;0CAMpC,8OAAC;yEAAc;;kDACb,8OAAC;iFAAc;;0DACb,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAgB,QAAQ,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;;;;;;;kDAGzE,8OAAC;iFAAc;;0DACb,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAgB,QAAQ,WAAW,CAAC,SAAS,EAAE,gCAAgC;;;;;;;;;;;;kDAGjG,8OAAC;iFAAc;;0DACb,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAgB,QAAQ,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;;;;;;;kDAGzE,8OAAC;iFAAc;;0DACb,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAc;0DACb,cAAA,8OAAC;;8DAAK,QAAQ,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;;;;;;;;;;;;kDAKjD,8OAAC;iFAAc;;0DACb,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAe;;;;;;;;;;;;kDAGjC,8OAAC;iFAAc;;0DACb,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAe;;;;;;0DAC/B,8OAAC;yFAAe;0DAAgB;;;;;;;;;;;;;;;;;;0CAKpC,8OAAC;yEAAc;;kDACb,8OAAC;iFAAc;kDAAiB;;;;;;kDAChC,8OAAC;iFAAa;;0DACZ,8OAAC;;0DAAG;;;;;;0DACJ,8OAAC;;0DAAG;;;;;;0DACJ,8OAAC;;0DAAG;;;;;;0DACJ,8OAAC;;0DAAG;;;;;;0DACJ,8OAAC;;0DAAG;;;;;;0DACJ,8OAAC;;0DAAG;;;;;;;;;;;;;;;;;;0CAKR,8OAAC;yEAAc;0CACb,cAAA,8OAAC;;8CACE,QAAQ,UAAU,IACjB,CAAC;;;;6GAI4F,CAAC;;;;;;;;;;;0CAMpG,8OAAC;yEAAc;;kDAEb,8OAAC;iFAAc;;0DACb,8OAAC;yFAAc;0DACb,cAAA,8OAAC;;;wDAAE;wDACyB,WAAW,QAAQ,UAAU;;;;;;;;;;;;0DAI3D,8OAAC;yFAAc;;kEACb,8OAAC;iGAAY;kEAAU;;;;;;kEACvB,8OAAC;iGAAY;kEAAW;;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;iFAAc;;0DACb,8OAAC;yFAAc;0DACb,cAAA,8OAAC;oDAAI,KAAK;;;;;;;;;;;;0DAEZ,8OAAC;yFAAY;0DAAU;;;;;;;;;;;;;;;;;;0CAO3B,8OAAC;yEAAc;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}, {"offset": {"line": 1706, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/certificates/TypeApprovalCertificate.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef } from 'react';\r\nimport { License } from '@/types';\r\n\r\ninterface TypeApprovalCertificateProps {\r\n  license: License;\r\n}\r\n\r\nexport default function TypeApprovalCertificate({ license }: TypeApprovalCertificateProps) {\r\n  const qrCodeRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    // Generate QR code\r\n    if (qrCodeRef.current && typeof window !== 'undefined') {\r\n      import('qrcode').then((QRCode) => {\r\n        const qrData = `MACRA Type Approval Certificate Verification\r\nRef: ${license.license_number}\r\nCertificate No: ${license.license_number}\r\nApplicant: ${license.application.applicant?.name || 'N/A'}\r\nValid: ${license.issue_date} - ${license.expiry_date}\r\nVerify at: https://macra.mw/verify/${license.license_number}`;\r\n\r\n        QRCode.toCanvas(qrCodeRef.current!, qrData, {\r\n          width: 96,\r\n          margin: 1,\r\n          color: {\r\n            dark: '#000000',\r\n            light: '#FFFFFF'\r\n          }\r\n        }).catch((error) => {\r\n          console.error('QR Code generation failed:', error);\r\n          if (qrCodeRef.current) {\r\n            qrCodeRef.current.innerHTML = 'QR Code<br>Error';\r\n          }\r\n        });\r\n      });\r\n    }\r\n  }, [license]);\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const getValidityPeriod = () => {\r\n    const issueDate = new Date(license.issue_date);\r\n    const expiryDate = new Date(license.expiry_date);\r\n    const years = expiryDate.getFullYear() - issueDate.getFullYear();\r\n    return `${years} Years (${formatDate(license.issue_date)} - ${formatDate(license.expiry_date)})`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"type-approval-certificate\">\r\n      <style jsx>{`\r\n        .type-approval-certificate {\r\n          font-family: 'Times New Roman', serif;\r\n          background: white;\r\n          border: 4px solid #dc2626;\r\n          border-radius: 8px;\r\n          padding: 40px;\r\n          min-height: 842px;\r\n          position: relative;\r\n          overflow: hidden;\r\n        }\r\n        \r\n        .inner-border {\r\n          border: 2px solid #16a34a;\r\n          padding: 30px;\r\n          min-height: 100%;\r\n          position: relative;\r\n        }\r\n        \r\n        .watermark {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%) rotate(-15deg);\r\n          opacity: 0.05;\r\n          pointer-events: none;\r\n          z-index: 0;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          text-align: center;\r\n        }\r\n        \r\n        .watermark-text {\r\n          font-size: 36px;\r\n          font-weight: bold;\r\n          color: #dc2626;\r\n          letter-spacing: 3px;\r\n          line-height: 1.2;\r\n        }\r\n        \r\n        .content-wrapper {\r\n          position: relative;\r\n          z-index: 1;\r\n        }\r\n        \r\n        .header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n        }\r\n        \r\n        .authority-name {\r\n          border: 2px solid #dc2626;\r\n          display: inline-block;\r\n          padding: 12px 30px;\r\n          margin-bottom: 20px;\r\n        }\r\n        \r\n        .authority-title {\r\n          color: #dc2626;\r\n          font-weight: bold;\r\n          font-size: 16px;\r\n          letter-spacing: 1px;\r\n        }\r\n        \r\n        .reference-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          margin-bottom: 15px;\r\n          font-size: 14px;\r\n          color: #2563eb;\r\n        }\r\n        \r\n        .certificate-type {\r\n          background-color: #7c3aed;\r\n          color: white;\r\n          padding: 10px 20px;\r\n          display: inline-block;\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n        \r\n        .certification-text {\r\n          font-size: 14px;\r\n          color: #374151;\r\n          margin-bottom: 25px;\r\n        }\r\n        \r\n        .certificate-details {\r\n          margin-bottom: 25px;\r\n          line-height: 1.8;\r\n        }\r\n        \r\n        .detail-row {\r\n          display: flex;\r\n          margin-bottom: 12px;\r\n          align-items: flex-start;\r\n        }\r\n        \r\n        .detail-label {\r\n          font-weight: 600;\r\n          min-width: 200px;\r\n          color: #374151;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .detail-colon {\r\n          margin: 0 10px;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        .detail-value {\r\n          font-weight: bold;\r\n          color: #111827;\r\n          flex: 1;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .equipment-section {\r\n          margin-bottom: 20px;\r\n          background-color: #f9fafb;\r\n          padding: 15px;\r\n          border-radius: 6px;\r\n          border-left: 4px solid #7c3aed;\r\n        }\r\n        \r\n        .equipment-title {\r\n          font-weight: bold;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .compliance-section {\r\n          text-align: justify;\r\n          line-height: 1.6;\r\n          margin-bottom: 30px;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .footer-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: flex-end;\r\n          margin-top: 40px;\r\n        }\r\n        \r\n        .signature-area {\r\n          flex: 1;\r\n        }\r\n        \r\n        .issue-location {\r\n          font-size: 14px;\r\n          margin-bottom: 60px;\r\n        }\r\n        \r\n        .signature-line {\r\n          text-align: center;\r\n          border-top: 1px solid black;\r\n          padding-top: 8px;\r\n          max-width: 200px;\r\n        }\r\n        \r\n        .dg-name {\r\n          font-weight: bold;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .dg-title {\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .qr-section {\r\n          flex-shrink: 0;\r\n          margin-left: 40px;\r\n          text-align: center;\r\n        }\r\n        \r\n        .qr-code {\r\n          width: 100px;\r\n          height: 100px;\r\n          border: 2px solid #9ca3af;\r\n          background: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 10px;\r\n          color: #6b7280;\r\n          margin-bottom: 8px;\r\n        }\r\n        \r\n        .qr-text {\r\n          font-size: 10px;\r\n          color: #4b5563;\r\n          max-width: 100px;\r\n        }\r\n        \r\n        .security-footer {\r\n          background-color: #06b6d4;\r\n          color: white;\r\n          text-align: center;\r\n          padding: 8px;\r\n          font-size: 12px;\r\n          font-weight: 600;\r\n          margin-top: 20px;\r\n          margin: 20px -30px -30px -30px;\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"inner-border\">\r\n        {/* Watermark */}\r\n        <div className=\"watermark\">\r\n          <div className=\"watermark-text\">\r\n            MALAWI COMMUNICATIONS<br />\r\n            REGULATORY AUTHORITY\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"content-wrapper\">\r\n          {/* Header */}\r\n          <div className=\"header\">\r\n            {/* Authority Name */}\r\n            <div className=\"authority-name\">\r\n              <h1 className=\"authority-title\">\r\n                MALAWI COMMUNICATIONS REGULATORY AUTHORITY\r\n              </h1>\r\n            </div>\r\n\r\n            {/* Reference and Certificate Numbers */}\r\n            <div className=\"reference-section\">\r\n              <span>Ref: TA - {new Date().getFullYear()}</span>\r\n              <span>Certificate No. {license.license_number}</span>\r\n            </div>\r\n\r\n            {/* Certificate Type */}\r\n            <div className=\"certificate-type\">\r\n              TYPE APPROVAL CERTIFICATE\r\n            </div>\r\n            <p className=\"certification-text\">\r\n              This certificate confirms that the equipment described below has been type approved for use in Malawi\r\n            </p>\r\n          </div>\r\n\r\n          {/* Certificate Details */}\r\n          <div className=\"certificate-details\">\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">APPLICANT NAME</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">BUSINESS REGISTRATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.business_registration_number || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">TAX IDENTIFICATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.tpin || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">REGISTERED ADDRESS</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <div className=\"detail-value\">\r\n                <div>{license.application.applicant?.name || 'N/A'}</div>\r\n                {/* <div>{license.application.applicant?.address || 'N/A'}</div> */}\r\n                {/* <div>{license.application.applicant?.city || ''}, {license.application.applicant?.country || 'Malawi'}</div> */}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">CERTIFICATE VALIDITY PERIOD</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{getValidityPeriod()}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Equipment Details */}\r\n          <div className=\"equipment-section\">\r\n            <div className=\"equipment-title\">APPROVED EQUIPMENT/SERVICE DETAILS:</div>\r\n            <div className=\"certificate-details\">\r\n              <div className=\"detail-row\">\r\n                <span className=\"detail-label\">EQUIPMENT TYPE</span>\r\n                <span className=\"detail-colon\">:</span>\r\n                <span className=\"detail-value\">{license.application.license_category?.name || 'Standards Compliance Equipment'}</span>\r\n              </div>\r\n              <div className=\"detail-row\">\r\n                <span className=\"detail-label\">STANDARDS COMPLIANCE</span>\r\n                <span className=\"detail-colon\">:</span>\r\n                <span className=\"detail-value\">ITU-R, ITU-T, and MACRA Technical Standards</span>\r\n              </div>\r\n              <div className=\"detail-row\">\r\n                <span className=\"detail-label\">AUTHORIZATION</span>\r\n                <span className=\"detail-colon\">:</span>\r\n                <span className=\"detail-value\">{license.application.license_category?.authorizes || 'Use of approved equipment in Malawi telecommunications networks'}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Compliance Statement */}\r\n          <div className=\"compliance-section\">\r\n            <p>\r\n              {license.conditions || \r\n                `This certificate is issued under the Communications Act, 2016, and confirms that the equipment \r\n                described above meets the technical standards and requirements for use in Malawi. The equipment \r\n                must be used in accordance with the approved specifications and applicable regulations. Any \r\n                modifications to the equipment will void this certificate. This certificate is non-transferable \r\n                and must be renewed before expiration.`\r\n              }\r\n            </p>\r\n          </div>\r\n\r\n          {/* Footer with Signature and QR Code */}\r\n          <div className=\"footer-section\">\r\n            {/* Signature Section */}\r\n            <div className=\"signature-area\">\r\n              <div className=\"issue-location\">\r\n                <p>\r\n                  Issued at Lilongwe, this {formatDate(license.issue_date)}\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"signature-line\">\r\n                <p className=\"dg-name\">Daud Suleman</p>\r\n                <p className=\"dg-title\">Director General</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* QR Code Section */}\r\n            <div className=\"qr-section\">\r\n              <div className=\"qr-code\">\r\n                <div ref={qrCodeRef}></div>\r\n              </div>\r\n              <p className=\"qr-text\">\r\n                Scan for verification\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Security Footer */}\r\n          <div className=\"security-footer\">\r\n            This certificate is issued without any alterations and remains the property of MACRA\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AASe,SAAS,wBAAwB,EAAE,OAAO,EAAgC;IACvF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;QACnB;;IAwBF,GAAG;QAAC;KAAQ;IAEZ,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,YAAY,IAAI,KAAK,QAAQ,UAAU;QAC7C,MAAM,aAAa,IAAI,KAAK,QAAQ,WAAW;QAC/C,MAAM,QAAQ,WAAW,WAAW,KAAK,UAAU,WAAW;QAC9D,OAAO,GAAG,MAAM,QAAQ,EAAE,WAAW,QAAQ,UAAU,EAAE,GAAG,EAAE,WAAW,QAAQ,WAAW,EAAE,CAAC,CAAC;IAClG;IAEA,qBACE,8OAAC;kDAAc;;;;;;0BAmNb,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;kCACb,cAAA,8OAAC;sEAAc;;gCAAiB;8CACT,8OAAC;;;;;;;gCAAK;;;;;;;;;;;;kCAK/B,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;;kDAEb,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAa;sDAAkB;;;;;;;;;;;kDAMlC,8OAAC;kFAAc;;0DACb,8OAAC;;;oDAAK;oDAAW,IAAI,OAAO,WAAW;;;;;;;0DACvC,8OAAC;;;oDAAK;oDAAiB,QAAQ,cAAc;;;;;;;;;;;;;kDAI/C,8OAAC;kFAAc;kDAAmB;;;;;;kDAGlC,8OAAC;kFAAY;kDAAqB;;;;;;;;;;;;0CAMpC,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAgB,QAAQ,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;;;;;;;kDAGzE,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAgB,QAAQ,WAAW,CAAC,SAAS,EAAE,gCAAgC;;;;;;;;;;;;kDAGjG,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAgB,QAAQ,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;;;;;;;kDAGzE,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAc;0DACb,cAAA,8OAAC;;8DAAK,QAAQ,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;;;;;;;;;;;;kDAMjD,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAgB;;;;;;;;;;;;;;;;;;0CAKpC,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;kDAAkB;;;;;;kDACjC,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;;kEACb,8OAAC;kGAAe;kEAAe;;;;;;kEAC/B,8OAAC;kGAAe;kEAAe;;;;;;kEAC/B,8OAAC;kGAAe;kEAAgB,QAAQ,WAAW,CAAC,gBAAgB,EAAE,QAAQ;;;;;;;;;;;;0DAEhF,8OAAC;0FAAc;;kEACb,8OAAC;kGAAe;kEAAe;;;;;;kEAC/B,8OAAC;kGAAe;kEAAe;;;;;;kEAC/B,8OAAC;kGAAe;kEAAe;;;;;;;;;;;;0DAEjC,8OAAC;0FAAc;;kEACb,8OAAC;kGAAe;kEAAe;;;;;;kEAC/B,8OAAC;kGAAe;kEAAe;;;;;;kEAC/B,8OAAC;kGAAe;kEAAgB,QAAQ,WAAW,CAAC,gBAAgB,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0CAM1F,8OAAC;0EAAc;0CACb,cAAA,8OAAC;;8CACE,QAAQ,UAAU,IACjB,CAAC;;;;sDAIqC,CAAC;;;;;;;;;;;0CAM7C,8OAAC;0EAAc;;kDAEb,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;0DACb,cAAA,8OAAC;;;wDAAE;wDACyB,WAAW,QAAQ,UAAU;;;;;;;;;;;;0DAI3D,8OAAC;0FAAc;;kEACb,8OAAC;kGAAY;kEAAU;;;;;;kEACvB,8OAAC;kGAAY;kEAAW;;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;0DACb,cAAA,8OAAC;oDAAI,KAAK;;;;;;;;;;;;0DAEZ,8OAAC;0FAAY;0DAAU;;;;;;;;;;;;;;;;;;0CAO3B,8OAAC;0EAAc;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}, {"offset": {"line": 2296, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/certificates/StandardsCertificate.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef } from 'react';\r\nimport { License } from '@/types';\r\n\r\ninterface StandardsCertificateProps {\r\n  license: License;\r\n}\r\n\r\nexport default function StandardsCertificate({ license }: StandardsCertificateProps) {\r\n  const qrCodeRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    // Generate QR code\r\n    if (qrCodeRef.current && typeof window !== 'undefined') {\r\n      import('qrcode').then((QRCode) => {\r\n        const qrData = `MACRA License Verification\r\nRef: ${license.license_number}\r\nLicense No: ${license.license_number}\r\nLicensee: ${license.application.applicant?.name || 'N/A'}\r\nType: ${license.application.license_category?.name || 'N/A'}\r\nValid: ${license.issue_date} - ${license.expiry_date}\r\nVerify at: https://macra.mw/verify/${license.license_number}`;\r\n\r\n        QRCode.toCanvas(qrCodeRef.current!, qrData, {\r\n          width: 96,\r\n          margin: 1,\r\n          color: {\r\n            dark: '#000000',\r\n            light: '#FFFFFF'\r\n          }\r\n        }).catch((error) => {\r\n          console.error('QR Code generation failed:', error);\r\n          if (qrCodeRef.current) {\r\n            qrCodeRef.current.innerHTML = 'QR Code<br>Error';\r\n          }\r\n        });\r\n      });\r\n    }\r\n  }, [license]);\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const getValidityPeriod = () => {\r\n    const issueDate = new Date(license.issue_date);\r\n    const expiryDate = new Date(license.expiry_date);\r\n    const years = expiryDate.getFullYear() - issueDate.getFullYear();\r\n    return `${years} Years (${formatDate(license.issue_date)} - ${formatDate(license.expiry_date)})`;\r\n  };\r\n\r\n  const getLicenseTypeTitle = () => {\r\n    switch (license.code) {\r\n      case 'telecommunications':\r\n        return 'TELECOMMUNICATIONS LICENSE';\r\n      case 'broadcasting':\r\n        return 'BROADCASTING LICENSE';\r\n      case 'spectrum_management':\r\n        return 'SPECTRUM MANAGEMENT LICENSE';\r\n      default:\r\n        return 'COMMUNICATIONS LICENSE';\r\n    }\r\n  };\r\n\r\n  const getLicenseDescription = () => {\r\n    switch (license.code) {\r\n      case 'telecommunications':\r\n        return 'This license authorizes the holder to provide telecommunications services in Malawi';\r\n      case 'broadcasting':\r\n        return 'This license authorizes the holder to operate broadcasting services in Malawi';\r\n      case 'spectrum_management':\r\n        return 'This license authorizes the holder to manage radio frequency spectrum in Malawi';\r\n      default:\r\n        return 'This license authorizes the holder to operate communications services in Malawi';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"standards-certificate\">\r\n      <style jsx>{`\r\n        .standards-certificate {\r\n          font-family: 'Times New Roman', serif;\r\n          background: white;\r\n          border: 4px solid #dc2626;\r\n          border-radius: 8px;\r\n          padding: 40px;\r\n          min-height: 842px;\r\n          position: relative;\r\n          overflow: hidden;\r\n        }\r\n        \r\n        .inner-border {\r\n          border: 2px solid #16a34a;\r\n          padding: 30px;\r\n          min-height: 100%;\r\n          position: relative;\r\n        }\r\n        \r\n        .watermark {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%) rotate(-15deg);\r\n          opacity: 0.05;\r\n          pointer-events: none;\r\n          z-index: 0;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          text-align: center;\r\n        }\r\n        \r\n        .watermark-text {\r\n          font-size: 36px;\r\n          font-weight: bold;\r\n          color: #dc2626;\r\n          letter-spacing: 3px;\r\n          line-height: 1.2;\r\n        }\r\n        \r\n        .content-wrapper {\r\n          position: relative;\r\n          z-index: 1;\r\n        }\r\n        \r\n        .header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n        }\r\n        \r\n        .authority-name {\r\n          border: 2px solid #dc2626;\r\n          display: inline-block;\r\n          padding: 12px 30px;\r\n          margin-bottom: 20px;\r\n        }\r\n        \r\n        .authority-title {\r\n          color: #dc2626;\r\n          font-weight: bold;\r\n          font-size: 16px;\r\n          letter-spacing: 1px;\r\n        }\r\n        \r\n        .reference-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          margin-bottom: 15px;\r\n          font-size: 14px;\r\n          color: #2563eb;\r\n        }\r\n        \r\n        .certificate-type {\r\n          background-color: #2563eb;\r\n          color: white;\r\n          padding: 10px 20px;\r\n          display: inline-block;\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n        \r\n        .certification-text {\r\n          font-size: 14px;\r\n          color: #374151;\r\n          margin-bottom: 25px;\r\n        }\r\n        \r\n        .license-details {\r\n          margin-bottom: 25px;\r\n          line-height: 1.8;\r\n        }\r\n        \r\n        .detail-row {\r\n          display: flex;\r\n          margin-bottom: 12px;\r\n          align-items: flex-start;\r\n        }\r\n        \r\n        .detail-label {\r\n          font-weight: 600;\r\n          min-width: 200px;\r\n          color: #374151;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .detail-colon {\r\n          margin: 0 10px;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        .detail-value {\r\n          font-weight: bold;\r\n          color: #111827;\r\n          flex: 1;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .authorization-section {\r\n          margin-bottom: 20px;\r\n          background-color: #f9fafb;\r\n          padding: 15px;\r\n          border-radius: 6px;\r\n          border-left: 4px solid #2563eb;\r\n        }\r\n        \r\n        .authorization-title {\r\n          font-weight: bold;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .compliance-section {\r\n          text-align: justify;\r\n          line-height: 1.6;\r\n          margin-bottom: 30px;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .footer-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: flex-end;\r\n          margin-top: 40px;\r\n        }\r\n        \r\n        .signature-area {\r\n          flex: 1;\r\n        }\r\n        \r\n        .issue-location {\r\n          font-size: 14px;\r\n          margin-bottom: 60px;\r\n        }\r\n        \r\n        .signature-line {\r\n          text-align: center;\r\n          border-top: 1px solid black;\r\n          padding-top: 8px;\r\n          max-width: 200px;\r\n        }\r\n        \r\n        .dg-name {\r\n          font-weight: bold;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .dg-title {\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .qr-section {\r\n          flex-shrink: 0;\r\n          margin-left: 40px;\r\n          text-align: center;\r\n        }\r\n        \r\n        .qr-code {\r\n          width: 100px;\r\n          height: 100px;\r\n          border: 2px solid #9ca3af;\r\n          background: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 10px;\r\n          color: #6b7280;\r\n          margin-bottom: 8px;\r\n        }\r\n        \r\n        .qr-text {\r\n          font-size: 10px;\r\n          color: #4b5563;\r\n          max-width: 100px;\r\n        }\r\n        \r\n        .security-footer {\r\n          background-color: #06b6d4;\r\n          color: white;\r\n          text-align: center;\r\n          padding: 8px;\r\n          font-size: 12px;\r\n          font-weight: 600;\r\n          margin-top: 20px;\r\n          margin: 20px -30px -30px -30px;\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"inner-border\">\r\n        {/* Watermark */}\r\n        <div className=\"watermark\">\r\n          <div className=\"watermark-text\">\r\n            MALAWI COMMUNICATIONS<br />\r\n            REGULATORY AUTHORITY\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"content-wrapper\">\r\n          {/* Header */}\r\n          <div className=\"header\">\r\n            {/* Authority Name */}\r\n            <div className=\"authority-name\">\r\n              <h1 className=\"authority-title\">\r\n                MALAWI COMMUNICATIONS REGULATORY AUTHORITY\r\n              </h1>\r\n            </div>\r\n\r\n            {/* Reference and License Numbers */}\r\n            <div className=\"reference-section\">\r\n              <span>Ref: {license.code?.toUpperCase().substring(0, 2) || 'GEN'} - {new Date().getFullYear()}</span>\r\n              <span>License No. {license.license_number}</span>\r\n            </div>\r\n\r\n            {/* Certificate Type */}\r\n            <div className=\"certificate-type\">\r\n              {getLicenseTypeTitle()}\r\n            </div>\r\n            <p className=\"certification-text\">\r\n              {getLicenseDescription()}\r\n            </p>\r\n          </div>\r\n\r\n          {/* License Details */}\r\n          <div className=\"license-details\">\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSEE NAME</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">BUSINESS REGISTRATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.business_registration_number || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">TAX IDENTIFICATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.tpin || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">REGISTERED ADDRESS</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <div className=\"detail-value\">\r\n                <div>{license.application.applicant?.name || 'N/A'}</div>\r\n                {/* <div>{license.application.applicant?.address || 'N/A'}</div> */}\r\n                {/* <div>{license.application.applicant?.city || ''}, {license.application.applicant?.country || 'Malawi'}</div> */}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSE CATEGORY</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.license_category?.name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSE VALIDITY PERIOD</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{getValidityPeriod()}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Authorization Details */}\r\n          <div className=\"authorization-section\">\r\n            <div className=\"authorization-title\">AUTHORIZATION:</div>\r\n            <p>{license.application.license_category?.authorizes || 'This license authorizes the holder to operate communications services as specified in the license conditions and applicable regulations.'}</p>\r\n          </div>\r\n\r\n          {/* Compliance Statement */}\r\n          <div className=\"compliance-section\">\r\n            <p>\r\n              {license.conditions || \r\n                `This license is issued under the Communications Act, 2016, and authorizes the licensee \r\n                to provide the specified services in Malawi subject to compliance with all applicable laws, \r\n                regulations, and license conditions. The licensee shall maintain adequate infrastructure, \r\n                qualified personnel, and service standards as prescribed by the Malawi Communications \r\n                Regulatory Authority. This license is non-transferable and must be renewed before expiration.`\r\n              }\r\n            </p>\r\n          </div>\r\n\r\n          {/* Footer with Signature and QR Code */}\r\n          <div className=\"footer-section\">\r\n            {/* Signature Section */}\r\n            <div className=\"signature-area\">\r\n              <div className=\"issue-location\">\r\n                <p>\r\n                  Issued at Lilongwe, this {formatDate(license.issue_date)}\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"signature-line\">\r\n                <p className=\"dg-name\">Daud Suleman</p>\r\n                <p className=\"dg-title\">Director General</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* QR Code Section */}\r\n            <div className=\"qr-section\">\r\n              <div className=\"qr-code\">\r\n                <div ref={qrCodeRef}></div>\r\n              </div>\r\n              <p className=\"qr-text\">\r\n                Scan for verification\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Security Footer */}\r\n          <div className=\"security-footer\">\r\n            This licence is issued without any alterations and remains the property of MACRA\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AASe,SAAS,qBAAqB,EAAE,OAAO,EAA6B;IACjF,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB;QACnB;;IAyBF,GAAG;QAAC;KAAQ;IAEZ,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,YAAY,IAAI,KAAK,QAAQ,UAAU;QAC7C,MAAM,aAAa,IAAI,KAAK,QAAQ,WAAW;QAC/C,MAAM,QAAQ,WAAW,WAAW,KAAK,UAAU,WAAW;QAC9D,OAAO,GAAG,MAAM,QAAQ,EAAE,WAAW,QAAQ,UAAU,EAAE,GAAG,EAAE,WAAW,QAAQ,WAAW,EAAE,CAAC,CAAC;IAClG;IAEA,MAAM,sBAAsB;QAC1B,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB;QAC5B,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;kDAAc;;;;;;0BAmNb,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;kCACb,cAAA,8OAAC;sEAAc;;gCAAiB;8CACT,8OAAC;;;;;;;gCAAK;;;;;;;;;;;;kCAK/B,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;;kDAEb,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAa;sDAAkB;;;;;;;;;;;kDAMlC,8OAAC;kFAAc;;0DACb,8OAAC;;;oDAAK;oDAAM,QAAQ,IAAI,EAAE,cAAc,UAAU,GAAG,MAAM;oDAAM;oDAAI,IAAI,OAAO,WAAW;;;;;;;0DAC3F,8OAAC;;;oDAAK;oDAAa,QAAQ,cAAc;;;;;;;;;;;;;kDAI3C,8OAAC;kFAAc;kDACZ;;;;;;kDAEH,8OAAC;kFAAY;kDACV;;;;;;;;;;;;0CAKL,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAgB,QAAQ,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;;;;;;;kDAGzE,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAgB,QAAQ,WAAW,CAAC,SAAS,EAAE,gCAAgC;;;;;;;;;;;;kDAGjG,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAgB,QAAQ,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;;;;;;;kDAGzE,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAc;0DACb,cAAA,8OAAC;;8DAAK,QAAQ,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;;;;;;;;;;;;kDAMjD,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAgB,QAAQ,WAAW,CAAC,gBAAgB,EAAE,QAAQ;;;;;;;;;;;;kDAGhF,8OAAC;kFAAc;;0DACb,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAe;;;;;;0DAC/B,8OAAC;0FAAe;0DAAgB;;;;;;;;;;;;;;;;;;0CAKpC,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;kDAAsB;;;;;;kDACrC,8OAAC;;kDAAG,QAAQ,WAAW,CAAC,gBAAgB,EAAE,cAAc;;;;;;;;;;;;0CAI1D,8OAAC;0EAAc;0CACb,cAAA,8OAAC;;8CACE,QAAQ,UAAU,IACjB,CAAC;;;;6GAI4F,CAAC;;;;;;;;;;;0CAMpG,8OAAC;0EAAc;;kDAEb,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;0DACb,cAAA,8OAAC;;;wDAAE;wDACyB,WAAW,QAAQ,UAAU;;;;;;;;;;;;0DAI3D,8OAAC;0FAAc;;kEACb,8OAAC;kGAAY;kEAAU;;;;;;kEACvB,8OAAC;kGAAY;kEAAW;;;;;;;;;;;;;;;;;;kDAK5B,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;0DACb,cAAA,8OAAC;oDAAI,KAAK;;;;;;;;;;;;0DAEZ,8OAAC;0FAAY;0DAAU;;;;;;;;;;;;;;;;;;0CAO3B,8OAAC;0EAAc;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}, {"offset": {"line": 2845, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/certificates/CertificateModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { License } from '@/types';\r\nimport { licenseService } from '@/services/licenseService';\r\nimport PostalServicesCertificate from './PostalServicesCertificate';\r\nimport TypeApprovalCertificate from './TypeApprovalCertificate';\r\nimport StandardsCertificate from './StandardsCertificate';\r\nimport toast from 'react-hot-toast';\r\n\r\ninterface CertificateModalProps {\r\n  license: License;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport default function CertificateModal({ license, isOpen, onClose }: CertificateModalProps) {\r\n  const [isDownloading, setIsDownloading] = useState(false);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const renderCertificate = () => {\r\n    // Determine which certificate to show based on license code\r\n    switch (license.code) {\r\n      case 'postal_services':\r\n        return <PostalServicesCertificate license={license} />;\r\n      case 'standards_compliance':\r\n        return <TypeApprovalCertificate license={license} />;\r\n      case 'telecommunications':\r\n      case 'broadcasting':\r\n      case 'spectrum_management':\r\n      default:\r\n        // Fallback to standards certificate for other types\r\n        return <StandardsCertificate license={license} />;\r\n    }\r\n  };\r\n\r\n  const handlePrint = () => {\r\n    window.print();\r\n  };\r\n\r\n  const handleDownloadPDF = async () => {\r\n    try {\r\n      setIsDownloading(true);\r\n      const blob = await licenseService.downloadLicensePDF(license.license_id);\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `${license.license_number}_certificate.pdf`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n      toast.success('Certificate PDF downloaded successfully');\r\n    } catch (error) {\r\n      console.error('Error downloading certificate PDF:', error);\r\n      toast.error('Failed to download certificate PDF');\r\n    } finally {\r\n      setIsDownloading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div \r\n          className=\"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75\"\r\n          onClick={onClose}\r\n        />\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg\">\r\n          {/* Modal header */}\r\n          <div className=\"flex items-center justify-between mb-4 print:hidden\">\r\n            <h3 className=\"text-lg font-medium text-gray-900\">\r\n              License Certificate - {license.license_number}\r\n            </h3>\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleDownloadPDF}\r\n                disabled={isDownloading}\r\n                className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {isDownloading ? (\r\n                  <>\r\n                    <i className=\"ri-loader-4-line mr-2 animate-spin\"></i>\r\n                    Downloading...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <i className=\"ri-download-line mr-2\"></i>\r\n                    Download PDF\r\n                  </>\r\n                )}\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              >\r\n                <i className=\"ri-close-line mr-2\"></i>\r\n                Close\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Certificate content */}\r\n          <div className=\"certificate-container\">\r\n            {renderCertificate()}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Print styles */}\r\n      <style jsx global>{`\r\n        @media print {\r\n          body * {\r\n            visibility: hidden;\r\n          }\r\n          .certificate-container,\r\n          .certificate-container * {\r\n            visibility: visible;\r\n          }\r\n          .certificate-container {\r\n            position: absolute;\r\n            left: 0;\r\n            top: 0;\r\n            width: 100%;\r\n          }\r\n          .print\\\\:hidden {\r\n            display: none !important;\r\n          }\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAgBe,SAAS,iBAAiB,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAyB;IAC1F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,oBAAoB;QACxB,4DAA4D;QAC5D,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,qBAAO,8OAAC,+JAAA,CAAA,UAAyB;oBAAC,SAAS;;;;;;YAC7C,KAAK;gBACH,qBAAO,8OAAC,6JAAA,CAAA,UAAuB;oBAAC,SAAS;;;;;;YAC3C,KAAK;YACL,KAAK;YACL,KAAK;YACL;gBACE,oDAAoD;gBACpD,qBAAO,8OAAC,0JAAA,CAAA,UAAoB;oBAAC,SAAS;;;;;;QAC1C;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,KAAK;IACd;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,iBAAiB;YACjB,MAAM,OAAO,MAAM,iIAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,QAAQ,UAAU;YACvE,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,GAAG,QAAQ,cAAc,CAAC,gBAAgB,CAAC;YAC3D,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC;kDAAc;;0BACb,8OAAC;0DAAc;;kCAEb,8OAAC;wBAEC,SAAS;kEADC;;;;;;kCAKZ,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;;4CAAoC;4CACzB,QAAQ,cAAc;;;;;;;kDAE/C,8OAAC;kFAAc;;0DACb,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU;0FACA;0DAET,8BACC;;sEACE,8OAAC;sGAAY;;;;;;wDAAyC;;iFAIxD;;sEACE,8OAAC;sGAAY;;;;;;wDAA4B;;;;;;;;0DAK/C,8OAAC;gDACC,MAAK;gDACL,SAAS;0FACC;;kEAEV,8OAAC;kGAAY;;;;;;oDAAyB;;;;;;;;;;;;;;;;;;;0CAO5C,8OAAC;0EAAc;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4Bb", "debugId": null}}, {"offset": {"line": 3057, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/licenses/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { licenseService } from '@/services/licenseService';\r\nimport { License, LicenseStatus, LicenseType } from '@/types';\r\nimport { licenseTypeService } from '@/services/licenseTypeService';\r\nimport DataTable from '@/components/common/DataTable';\r\nimport Select from '@/components/common/Select';\r\nimport CertificateModal from '@/components/certificates/CertificateModal';\r\nimport toast from 'react-hot-toast';\r\nimport { PaginatedResponse, PaginateQuery } from '@/types';\r\n\r\nexport default function LicensesPage() {\r\n  const [licensesData, setLicensesData] = useState<PaginatedResponse<License> | null>(null);\r\n  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);\r\n\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [statusFilter, setStatusFilter] = useState<string>('');\r\n  const [licenseTypeFilter, setLicenseTypeFilter] = useState<string>('');\r\n  const [dateRangeFilter, setDateRangeFilter] = useState<string>('');\r\n\r\n  // Certificate modal state\r\n  const [selectedLicense, setSelectedLicense] = useState<License | null>(null);\r\n  const [isCertificateModalOpen, setIsCertificateModalOpen] = useState(false);\r\n\r\n  // Load licenses function for DataTable\r\n  const loadLicenses = useCallback(async (query: PaginateQuery) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const params = {\r\n        page: query.page,\r\n        limit: query.limit,\r\n        search: query.search || undefined,\r\n        sortBy: Array.isArray(query.sortBy) ? query.sortBy[0] : query.sortBy,\r\n        sortOrder: Array.isArray(query.sortBy) && query.sortBy.length > 1 ? query.sortBy[1] as 'ASC' | 'DESC' : undefined,\r\n        status: statusFilter || undefined,\r\n        licenseType: licenseTypeFilter || undefined,\r\n        dateRange: dateRangeFilter || undefined,\r\n      };\r\n\r\n      const response = await licenseService.getLicenses(params);\r\n      setLicensesData(response);\r\n    } catch (err: unknown) {\r\n      setError('Failed to load licenses');\r\n      console.error('Error loading licenses:', err);\r\n      toast.error('Failed to load licenses');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [statusFilter, licenseTypeFilter, dateRangeFilter]);\r\n\r\n  // Load license types on component mount\r\n  useEffect(() => {\r\n    const fetchInitialData = async () => {\r\n      try {\r\n        // Fetch license types\r\n        const typesResponse = await licenseTypeService.getAllLicenseTypes();\r\n        const types = Array.isArray(typesResponse) ? typesResponse : (typesResponse?.data || []);\r\n        setLicenseTypes(types);\r\n      } catch (error) {\r\n        console.error('Error fetching initial data:', error);\r\n      }\r\n    };\r\n\r\n    fetchInitialData();\r\n  }, []);\r\n\r\n  // Load licenses on component mount and when filters change\r\n  useEffect(() => {\r\n    loadLicenses({ page: 1, limit: 10 });\r\n  }, [loadLicenses]);\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    const statusClasses: Record<string, string> = {\r\n      [LicenseStatus.ACTIVE]: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',\r\n      [LicenseStatus.EXPIRED]: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',\r\n      [LicenseStatus.SUSPENDED]: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',\r\n      [LicenseStatus.REVOKED]: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',\r\n      [LicenseStatus.UNDER_REVIEW]: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',\r\n    };\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status] || statusClasses[LicenseStatus.ACTIVE]}`}>\r\n        {status.replace('_', ' ').toUpperCase()}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  const handleViewCertificate = (license: License) => {\r\n    setSelectedLicense(license);\r\n    setIsCertificateModalOpen(true);\r\n  };\r\n\r\n  const handleCloseCertificateModal = () => {\r\n    setIsCertificateModalOpen(false);\r\n    setSelectedLicense(null);\r\n  };\r\n\r\n  const handleFilterChange = (filterType: string, value: string) => {\r\n    switch (filterType) {\r\n      case 'status':\r\n        setStatusFilter(value);\r\n        break;\r\n      case 'licenseType':\r\n        setLicenseTypeFilter(value);\r\n        break;\r\n      case 'dateRange':\r\n        setDateRangeFilter(value);\r\n        break;\r\n    }\r\n    // Reload licenses with new filters\r\n    loadLicenses({ page: 1, limit: 10 });\r\n  };\r\n\r\n  // Define table columns\r\n  const licenseColumns = [\r\n    {\r\n      key: 'license_number',\r\n      label: 'License Number',\r\n      sortable: true,\r\n      render: (value: unknown, item: License) => (\r\n        <div className=\"flex items-center\">\r\n          <div className=\"flex-shrink-0 h-10 w-10\">\r\n            <div className=\"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center\">\r\n              <i className=\"ri-award-line text-blue-600 dark:text-blue-400\"></i>\r\n            </div>\r\n          </div>\r\n          <div className=\"ml-4\">\r\n            <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n              {item.license_number}\r\n            </div>\r\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n              {item.code || 'N/A'}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'applicant',\r\n      label: 'Applicant',\r\n      render: (value: unknown, item: License) => (\r\n        <div>\r\n          <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n            {item.application.applicant?.name || 'N/A'}\r\n          </div>\r\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            {item.application.applicant?.email || 'N/A'}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'license_type',\r\n      label: 'License Type',\r\n      render: (value: unknown, item: License) => (\r\n        <div>\r\n          <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n            {item?.description || 'N/A'}\r\n          </div>\r\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            {item?.code || 'N/A'}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      sortable: true,\r\n      render: (value: unknown, item: License) => getStatusBadge(item.status),\r\n    },\r\n    {\r\n      key: 'issue_date',\r\n      label: 'Issue Date',\r\n      sortable: true,\r\n      render: (value: unknown, item: License) => (\r\n        <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {new Date(item.issue_date).toLocaleDateString()}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'expiry_date',\r\n      label: 'Expiry Date',\r\n      sortable: true,\r\n      render: (value: unknown, item: License) => {\r\n        const expiryDate = new Date(item.expiry_date);\r\n        const isExpired = expiryDate < new Date();\r\n        const isExpiringSoon = expiryDate < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\r\n        \r\n        return (\r\n          <span className={`text-sm ${isExpired ? 'text-red-600 dark:text-red-400' : isExpiringSoon ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-500 dark:text-gray-400'}`}>\r\n            {expiryDate.toLocaleDateString()}\r\n          </span>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (value: unknown, item: License) => (\r\n        <div className=\"flex items-center justify-end space-x-2\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => handleViewCertificate(item)}\r\n            className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors\"\r\n          >\r\n            <i className=\"ri-eye-line mr-1\"></i>\r\n            View Certificate\r\n          </button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const statusOptions = [\r\n    { value: '', label: 'All Statuses' },\r\n    { value: LicenseStatus.ACTIVE, label: 'Active' },\r\n    { value: LicenseStatus.EXPIRED, label: 'Expired' },\r\n    { value: LicenseStatus.SUSPENDED, label: 'Suspended' },\r\n    { value: LicenseStatus.REVOKED, label: 'Revoked' },\r\n    { value: LicenseStatus.UNDER_REVIEW, label: 'Under Review' },\r\n  ];\r\n\r\n  const licenseTypeOptions = [\r\n    { value: '', label: 'All License Types' },\r\n    ...licenseTypes.map(type => ({\r\n      value: type.license_type_id,\r\n      label: type.name\r\n    }))\r\n  ];\r\n\r\n  const dateRangeOptions = [\r\n    { value: '', label: 'All Time' },\r\n    { value: 'today', label: 'Today' },\r\n    { value: 'week', label: 'This Week' },\r\n    { value: 'month', label: 'This Month' },\r\n    { value: 'quarter', label: 'This Quarter' },\r\n    { value: 'year', label: 'This Year' },\r\n    { value: 'expiring_30', label: 'Expiring in 30 Days' },\r\n    { value: 'expiring_90', label: 'Expiring in 90 Days' },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Page header */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">Licenses</h1>\r\n              <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\r\n                Manage and view all issued licenses in the system\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Filters */}\r\n        <div className=\"mb-6 bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Status\r\n              </label>\r\n              <Select\r\n                value={statusFilter}\r\n                onChange={(value) => handleFilterChange('status', value)}\r\n                options={statusOptions}\r\n                placeholder=\"Filter by status\"\r\n              />\r\n            </div>\r\n\r\n            {/* <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                License Type\r\n              </label>\r\n              <Select\r\n                value={licenseTypeFilter}\r\n                onChange={(value) => handleFilterChange('licenseType', value)}\r\n                options={licenseTypeOptions}\r\n                placeholder=\"Filter by license type\"\r\n              />\r\n            </div> */}\r\n\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Date Range\r\n              </label>\r\n              <Select\r\n                value={dateRangeFilter}\r\n                onChange={(value) => handleFilterChange('dateRange', value)}\r\n                options={dateRangeOptions}\r\n                placeholder=\"Filter by date range\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Error message */}\r\n        {error && (\r\n          <div className=\"mb-6\">\r\n            <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4\">\r\n              <div className=\"flex\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <i className=\"ri-error-warning-line text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\r\n                    Error loading licenses\r\n                  </h3>\r\n                  <div className=\"mt-2 text-sm text-red-700 dark:text-red-300\">\r\n                    <p>{error}</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"ml-auto pl-3\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => setError(null)}\r\n                    className=\"inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800\"\r\n                  >\r\n                    <span className=\"sr-only\">Dismiss</span>\r\n                    <i className=\"ri-close-line text-sm\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Licenses Table */}\r\n        <DataTable<License>\r\n          columns={licenseColumns}\r\n          data={licensesData}\r\n          loading={loading}\r\n          onQueryChange={loadLicenses}\r\n          searchPlaceholder=\"Search licenses by number, applicant, or type...\"\r\n          emptyStateIcon=\"ri-award-line\"\r\n          emptyStateMessage=\"No licenses found.\"\r\n        />\r\n      </div>\r\n\r\n      {/* Certificate Modal */}\r\n      {selectedLicense && (\r\n        <CertificateModal\r\n          license={selectedLicense}\r\n          isOpen={isCertificateModalOpen}\r\n          onClose={handleCloseCertificateModal}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IACpF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAElE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE/D,0BAA0B;IAC1B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,uCAAuC;IACvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,SAAS;gBACb,MAAM,MAAM,IAAI;gBAChB,OAAO,MAAM,KAAK;gBAClB,QAAQ,MAAM,MAAM,IAAI;gBACxB,QAAQ,MAAM,OAAO,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,EAAE,GAAG,MAAM,MAAM;gBACpE,WAAW,MAAM,OAAO,CAAC,MAAM,MAAM,KAAK,MAAM,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM,MAAM,CAAC,EAAE,GAAqB;gBACxG,QAAQ,gBAAgB;gBACxB,aAAa,qBAAqB;gBAClC,WAAW,mBAAmB;YAChC;YAEA,MAAM,WAAW,MAAM,iIAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;YAClD,gBAAgB;QAClB,EAAE,OAAO,KAAc;YACrB,SAAS;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAc;QAAmB;KAAgB;IAErD,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,sBAAsB;gBACtB,MAAM,gBAAgB,MAAM,qIAAA,CAAA,qBAAkB,CAAC,kBAAkB;gBACjE,MAAM,QAAQ,MAAM,OAAO,CAAC,iBAAiB,gBAAiB,eAAe,QAAQ,EAAE;gBACvF,gBAAgB;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;YAAE,MAAM;YAAG,OAAO;QAAG;IACpC,GAAG;QAAC;KAAa;IAEjB,MAAM,iBAAiB,CAAC;QACtB,MAAM,gBAAwC;YAC5C,CAAC,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,EAAE;YACxB,CAAC,uHAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,EAAE;YACzB,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,EAAE;YAC3B,CAAC,uHAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,EAAE;YACzB,CAAC,uHAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,EAAE;QAChC;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,EAAE;sBACvJ,OAAO,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;IAG3C;IAEA,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB;QACnB,0BAA0B;IAC5B;IAEA,MAAM,8BAA8B;QAClC,0BAA0B;QAC1B,mBAAmB;IACrB;IAEA,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,OAAQ;YACN,KAAK;gBACH,gBAAgB;gBAChB;YACF,KAAK;gBACH,qBAAqB;gBACrB;YACF,KAAK;gBACH,mBAAmB;gBACnB;QACJ;QACA,mCAAmC;QACnC,aAAa;YAAE,MAAM;YAAG,OAAO;QAAG;IACpC;IAEA,uBAAuB;IACvB,MAAM,iBAAiB;QACrB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,KAAK,cAAc;;;;;;8CAEtB,8OAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;;QAKxB;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACZ,KAAK,WAAW,CAAC,SAAS,EAAE,QAAQ;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;sCACZ,KAAK,WAAW,CAAC,SAAS,EAAE,SAAS;;;;;;;;;;;;QAI9C;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACZ,MAAM,eAAe;;;;;;sCAExB,8OAAC;4BAAI,WAAU;sCACZ,MAAM,QAAQ;;;;;;;;;;;;QAIvB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,OAAkB,eAAe,KAAK,MAAM;QACvE;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAK,WAAU;8BACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;QAGnD;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,OAAgB;gBACvB,MAAM,aAAa,IAAI,KAAK,KAAK,WAAW;gBAC5C,MAAM,YAAY,aAAa,IAAI;gBACnC,MAAM,iBAAiB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;gBAE9E,qBACE,8OAAC;oBAAK,WAAW,CAAC,QAAQ,EAAE,YAAY,mCAAmC,iBAAiB,yCAAyC,oCAAoC;8BACtK,WAAW,kBAAkB;;;;;;YAGpC;QACF;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAgB,qBACvB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,sBAAsB;wBACrC,WAAU;;0CAEV,8OAAC;gCAAE,WAAU;;;;;;4BAAuB;;;;;;;;;;;;QAK5C;KACD;IAED,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAI,OAAO;QAAe;QACnC;YAAE,OAAO,uHAAA,CAAA,gBAAa,CAAC,MAAM;YAAE,OAAO;QAAS;QAC/C;YAAE,OAAO,uHAAA,CAAA,gBAAa,CAAC,OAAO;YAAE,OAAO;QAAU;QACjD;YAAE,OAAO,uHAAA,CAAA,gBAAa,CAAC,SAAS;YAAE,OAAO;QAAY;QACrD;YAAE,OAAO,uHAAA,CAAA,gBAAa,CAAC,OAAO;YAAE,OAAO;QAAU;QACjD;YAAE,OAAO,uHAAA,CAAA,gBAAa,CAAC,YAAY;YAAE,OAAO;QAAe;KAC5D;IAED,MAAM,qBAAqB;QACzB;YAAE,OAAO;YAAI,OAAO;QAAoB;WACrC,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC3B,OAAO,KAAK,eAAe;gBAC3B,OAAO,KAAK,IAAI;YAClB,CAAC;KACF;IAED,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAI,OAAO;QAAW;QAC/B;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAQ,OAAO;QAAY;QACpC;YAAE,OAAO;YAAS,OAAO;QAAa;QACtC;YAAE,OAAO;YAAW,OAAO;QAAe;QAC1C;YAAE,OAAO;YAAQ,OAAO;QAAY;QACpC;YAAE,OAAO;YAAe,OAAO;QAAsB;QACrD;YAAE,OAAO;YAAe,OAAO;QAAsB;KACtD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAsD;;;;;;kDACpE,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;kCAUnE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC,sIAAA,CAAA,UAAM;4CACL,OAAO;4CACP,UAAU,CAAC,QAAU,mBAAmB,UAAU;4CAClD,SAAS;4CACT,aAAY;;;;;;;;;;;;8CAgBhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,8OAAC,sIAAA,CAAA,UAAM;4CACL,OAAO;4CACP,UAAU,CAAC,QAAU,mBAAmB,aAAa;4CACrD,SAAS;4CACT,aAAY;;;;;;;;;;;;;;;;;;;;;;;oBAOnB,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DAGnE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;8DAAG;;;;;;;;;;;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,SAAS;4CACxB,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzB,8OAAC,yIAAA,CAAA,UAAS;wBACR,SAAS;wBACT,MAAM;wBACN,SAAS;wBACT,eAAe;wBACf,mBAAkB;wBAClB,gBAAe;wBACf,mBAAkB;;;;;;;;;;;;YAKrB,iCACC,8OAAC,sJAAA,CAAA,UAAgB;gBACf,SAAS;gBACT,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAKnB", "debugId": null}}]}