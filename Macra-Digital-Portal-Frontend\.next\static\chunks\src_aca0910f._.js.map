{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,KAOjB;QAPiB,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU,GAPiB;;IAQnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,AAAC,GAAgB,OAAd,YAAW,KAAyB,OAAtB,oBAAmB,KAAgD,OAA7C,YAAY,SAAS,kBAAkB,IAAG,KAAa,OAAV;IAEzG,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,CAAA,iBAAA,2BAAA,KAAM,UAAU,IAAG,AAAC,KAAoB,OAAhB,KAAK,UAAU,IAAK;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["import { LicenseCategory } from \".\";\r\nimport { User } from \"./user\";\r\n\r\nexport interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  validity?: number;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  categories?: LicenseCategory[];\r\n  iconColor?: string;\r\n  icon?: string;\r\n  iconBg?: string;\r\n}\r\n\r\nexport interface StatusAction {\r\n  status: ApplicationStatus;\r\n  label: string;\r\n  icon: string;\r\n  color: string;\r\n  hoverColor: string;\r\n  roles?: string[],\r\n  description: string;\r\n  confirmMessage?: string;\r\n}\r\n\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application extends Record<string, unknown> {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: string;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: Applicant;\r\n  license_category?:LicenseCategory;\r\n  assignee?:User;\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  PENDING = 'pending',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  PENDING_PAYMENT = 'pending_payment',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n  PASS_EVALUATION = 'pass_evaluation',\r\n  WAITING_FOR_APPROVAL = 'waiting_for_approval',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface License extends Record<string, unknown> {\r\n  license_id: string;\r\n  license_number: string;\r\n  description?: string;\r\n  application_id: string;\r\n  applicant_id: string;\r\n  license_type_id: string;\r\n  status: LicenseStatus;\r\n  issue_date: string;\r\n  expiry_date: string;\r\n  issued_by: string;\r\n  code?: string;\r\n  conditions?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  application: Application;\r\n  issuer?:User;\r\n}\r\n\r\nexport enum LicenseStatus {\r\n  ACTIVE = 'active',\r\n  EXPIRED = 'expired',\r\n  SUSPENDED = 'suspended',\r\n  REVOKED = 'revoked',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAkEO,IAAA,AAAK,2CAAA;;;;;;;;;;;;WAAA;;AAyCL,IAAA,AAAK,uCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\nimport { AppNotification, NotificationSummary } from '@/types/notification';\r\n\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications/my-notifications?${queryParams.toString()}`;\r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response).data;\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.length || 0,\r\n        unread_count: data.length || 0,\r\n        notifications: Array.isArray(data) ? data : []\r\n      };\r\n    } catch (error: any) {\r\n      // Convert params to string format for URLSearchParams\r\n      const stringParams: Record<string, string> = {};\r\n      if (params) {\r\n        if (params.page) stringParams.page = params.page.toString();\r\n        if (params.limit) stringParams.limit = params.limit.toString();\r\n        if (params.type) stringParams.type = params.type;\r\n        if (params.status) stringParams.status = params.status;\r\n      }\r\n\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications/my-notifications?${new URLSearchParams(stringParams).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/mark-read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read (implemented client-side by marking each notification individually)\r\n  async markAllAsRead(): Promise<void> {\r\n    // This is handled by the useNotifications hook by calling markAsRead for each unread notification\r\n    // We don't need a separate backend endpoint for this\r\n    throw new Error('markAllAsRead should be handled by useNotifications hook');\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Send activity note notification (creates notification which auto-creates activity note)\r\n  async sendActivityNoteNotification(data: {\r\n    applicationId: string;\r\n    message: string;\r\n    noteType?: string;\r\n    category?: string;\r\n    priority?: 'low' | 'normal' | 'high';\r\n    attachmentsCount?: number;\r\n  }): Promise<any> {\r\n    const response = await apiClient.post('/notifications/activity-note', {\r\n      application_id: data.applicationId,\r\n      message: data.message,\r\n      note_type: data.noteType || 'evaluation_comment',\r\n      category: data.category || 'communication',\r\n      priority: data.priority || 'normal',\r\n      attachments_count: data.attachmentsCount || 0,\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: string,\r\n  newStatus: string,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.DRAFT]: {\r\n      title: 'Application Draft Saved',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been saved as a draft. You can continue editing and submit it when ready.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING]: {\r\n      title: 'Application Pending',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is pending initial review. We'll notify you once the review begins.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING_PAYMENT]: {\r\n      title: 'Payment Required',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been approved for payment. Please complete the payment to proceed with license issuance.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus as keyof typeof messages] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAIO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,AAAC,mCAAyD,OAAvB,YAAY,QAAQ;YACxE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAE9C,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,MAAM,IAAI;gBAC5B,cAAc,KAAK,MAAM,IAAI;gBAC7B,eAAe,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;YAChD;QACF,EAAE,OAAO,OAAY;gBAYP,iBACF;YAZV,sDAAsD;YACtD,MAAM,eAAuC,CAAC;YAC9C,IAAI,QAAQ;gBACV,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ;gBACzD,IAAI,OAAO,KAAK,EAAE,aAAa,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ;gBAC5D,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI;gBAChD,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,GAAG,OAAO,MAAM;YACxD;YAEA,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,IAAI,KAAI;gBAClC,QAAQ,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,KAAI;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,AAAC,mCAA+E,OAA7C,IAAI,gBAAgB,cAAc,QAAQ;YACzF;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,AAAC,kBAAgC,OAAf,gBAAe;QACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qGAAqG;IACrG,MAAM;QACJ,kGAAkG;QAClG,qDAAqD;QACrD,MAAM,IAAI,MAAM;IAClB;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,kBAAgC,OAAf;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0FAA0F;IAC1F,MAAM,8BAA6B,IAOlC;QACC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB,KAAK,aAAa;YAClC,SAAS,KAAK,OAAO;YACrB,WAAW,KAAK,QAAQ,IAAI;YAC5B,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ,IAAI;YAC3B,mBAAmB,KAAK,gBAAgB,IAAI;QAC9C;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,AAAC,KAAuB,OAAnB,oBAAmB,iBAAe;IACjF,MAAM,WAAW,OAAO,AAAC,WAAe,OAAL,QAAS;IAE5C,MAAM,WAAW;QACf,CAAC,0HAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YACzB,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAqE,OAArD,mBAAkB,qCAAkD,OAAf,cAAwB,OAAT,UAAS;YAClI,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkE,OAAlD,mBAAkB,kCAA+C,OAAf,cAAwB,OAAT,UAAS;YAC/H,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,EAAE;YACnC,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,AAAC,yBAA4D,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACxF,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAiE,OAAjD,mBAAkB,iCAA2D,OAA5B,UAAU,OAAO,CAAC,KAAK,MAAK;QAClI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAmC,IAAI;AACzD;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,AAAC,QAAkB,OAAX,KAAK,GAAG;IAEvC,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,AAAC,OAAwB,OAAlB;QACvB,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,0HAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,0HAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,0HAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode, useEffect } from 'react';\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: ReactNode;\r\n  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';\r\n  showCloseButton?: boolean;\r\n  closeOnOverlayClick?: boolean;\r\n}\r\n\r\nexport default function Modal({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  showCloseButton = true,\r\n  closeOnOverlayClick = true,\r\n}: ModalProps) {\r\n  // Handle escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape' && isOpen) {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent body scroll when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'sm:max-w-sm';\r\n      case 'md':\r\n        return 'sm:max-w-md';\r\n      case 'lg':\r\n        return 'sm:max-w-lg';\r\n      case 'xl':\r\n        return 'sm:max-w-xl';\r\n      case '2xl':\r\n        return 'sm:max-w-2xl';\r\n      default:\r\n        return 'sm:max-w-md';\r\n    }\r\n  };\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {\r\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"fixed inset-0\"\r\n        onClick={handleOverlayClick}\r\n        aria-hidden=\"true\"\r\n      />\r\n      \r\n      <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full ${getSizeClasses()} mx-4 transform transition-all max-h-[90vh] flex flex-col`}>\r\n        {/* Header - Fixed */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            {title}\r\n          </h3>\r\n          {showCloseButton && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Scrollable Content */}\r\n        <div className=\"flex-1 overflow-hidden\">\r\n          <div className=\"overflow-y-auto h-full\">\r\n            <div className=\"p-6\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAce,SAAS,MAAM,KAQjB;QARiB,EAC5B,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EACf,GARiB;;IAS5B,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;wBACpC;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,yCAAyC;gBACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YACvD;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS;gBACT,eAAY;;;;;;0BAGd,6LAAC;gBAAI,WAAW,AAAC,kEAAkF,OAAjB,kBAAiB;;kCAEjG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,iCACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GA3FwB;KAAA", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Notification } from '@/hooks/useNotifications';\r\n\r\ninterface NotificationItemProps {\r\n  notification: Notification;\r\n  onMarkAsRead: (id: string) => void;\r\n  onNotificationClick?: (notification: Notification) => void;\r\n}\r\n\r\nconst NotificationItem: React.FC<NotificationItemProps> = ({\r\n  notification,\r\n  onMarkAsRead,\r\n  onNotificationClick,\r\n}) => {\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) {\r\n      return 'Just now';\r\n    } else if (diffInSeconds < 3600) {\r\n      const minutes = Math.floor(diffInSeconds / 60);\r\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 86400) {\r\n      const hours = Math.floor(diffInSeconds / 3600);\r\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 604800) {\r\n      const days = Math.floor(diffInSeconds / 86400);\r\n      return `${days} day${days > 1 ? 's' : ''} ago`;\r\n    } else {\r\n      return date.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const getNotificationIcon = (type: string) => {\r\n    switch (type.toLowerCase()) {\r\n      case 'email':\r\n        return 'ri-mail-line';\r\n      case 'sms':\r\n        return 'ri-message-3-line';\r\n      case 'in_app':\r\n        return 'ri-notification-3-line';\r\n      case 'application_status':\r\n        return 'ri-file-list-3-line';\r\n      case 'evaluation_assigned':\r\n        return 'ri-user-settings-line';\r\n      case 'payment_due':\r\n        return 'ri-money-dollar-circle-line';\r\n      case 'license_expiry':\r\n        return 'ri-calendar-event-line';\r\n      case 'system_alert':\r\n        return 'ri-alert-line';\r\n      default:\r\n        return 'ri-notification-3-line';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority.toLowerCase()) {\r\n      case 'urgent':\r\n        return 'text-red-600 dark:text-red-400';\r\n      case 'high':\r\n        return 'text-orange-600 dark:text-orange-400';\r\n      case 'medium':\r\n        return 'text-yellow-600 dark:text-yellow-400';\r\n      case 'low':\r\n        return 'text-green-600 dark:text-green-400';\r\n      default:\r\n        return 'text-gray-600 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const handleItemClick = () => {\r\n    if (!notification.is_read) {\r\n      onMarkAsRead(notification.notification_id);\r\n    }\r\n    if (onNotificationClick) {\r\n      onNotificationClick(notification);\r\n    }\r\n  };\r\n\r\n  const handleMarkAsReadClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onMarkAsRead(notification.notification_id);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200 ${\r\n        !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''\r\n      }`}\r\n      onClick={handleItemClick}\r\n    >\r\n      <div className=\"flex items-start space-x-3\">\r\n        {/* Notification Icon */}\r\n        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\r\n          !notification.is_read \r\n            ? 'bg-blue-100 dark:bg-blue-900/50' \r\n            : 'bg-gray-100 dark:bg-gray-700'\r\n        }`}>\r\n          <i className={`${getNotificationIcon(notification.type)} text-sm ${\r\n            !notification.is_read \r\n              ? 'text-blue-600 dark:text-blue-400' \r\n              : 'text-gray-600 dark:text-gray-400'\r\n          }`}></i>\r\n        </div>\r\n\r\n        {/* Notification Content */}\r\n        <div className=\"flex-1 min-w-0\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex-1\">\r\n              <h4 className={`text-sm font-medium ${\r\n                !notification.is_read \r\n                  ? 'text-gray-900 dark:text-gray-100' \r\n                  : 'text-gray-700 dark:text-gray-300'\r\n              }`}>\r\n                {notification.subject}\r\n              </h4>\r\n              <p className={`mt-1 text-sm ${\r\n                !notification.is_read \r\n                  ? 'text-gray-700 dark:text-gray-300' \r\n                  : 'text-gray-500 dark:text-gray-400'\r\n              }`}>\r\n                {notification.message}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Priority Indicator */}\r\n            {notification.priority && notification.priority !== 'medium' && (\r\n              <div className={`flex-shrink-0 ml-2 ${getPriorityColor(notification.priority)}`}>\r\n                <i className=\"ri-flag-line text-xs\"></i>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Metadata */}\r\n          <div className=\"mt-2 flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\r\n              <span>{formatTimeAgo(notification.created_at)}</span>\r\n              {notification.entity_type && (\r\n                <span className=\"capitalize\">{notification.entity_type}</span>\r\n              )}\r\n            </div>\r\n\r\n            {/* Actions */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              {!notification.is_read && (\r\n                <button\r\n                  onClick={handleMarkAsReadClick}\r\n                  className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 hover:text-blue-800 dark:hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200\"\r\n                  title=\"Mark as read\"\r\n                >\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Mark as Read\r\n                </button>\r\n              )}\r\n              {notification.is_read && (\r\n                <span className=\"text-xs text-green-600 dark:text-green-400 flex items-center\">\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Read\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Unread Indicator */}\r\n        {!notification.is_read && (\r\n          <div className=\"flex-shrink-0 w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full\"></div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationItem;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,MAAM,mBAAoD;QAAC,EACzD,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACpB;IACC,MAAM,gBAAgB,CAAC;QACrB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,gBAAgB,MAAM;YAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;YAC3C,OAAO,AAAC,GAAmB,OAAjB,SAAQ,WAAgC,OAAvB,UAAU,IAAI,MAAM,IAAG;QACpD,OAAO,IAAI,gBAAgB,OAAO;YAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,AAAC,GAAe,OAAb,OAAM,SAA4B,OAArB,QAAQ,IAAI,MAAM,IAAG;QAC9C,OAAO,IAAI,gBAAgB,QAAQ;YACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;YACxC,OAAO,AAAC,GAAa,OAAX,MAAK,QAA0B,OAApB,OAAO,IAAI,MAAM,IAAG;QAC3C,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,aAAa,aAAa,eAAe;QAC3C;QACA,IAAI,qBAAqB;YACvB,oBAAoB;QACtB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,EAAE,eAAe;QACjB,aAAa,aAAa,eAAe;IAC3C;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,2IAEX,OADC,CAAC,aAAa,OAAO,GAAG,mCAAmC;QAE7D,SAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAW,AAAC,uEAIhB,OAHC,CAAC,aAAa,OAAO,GACjB,oCACA;8BAEJ,cAAA,6LAAC;wBAAE,WAAW,AAAC,GACb,OADe,oBAAoB,aAAa,IAAI,GAAE,aAIvD,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;;;;;;;;;;;8BAKR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,AAAC,uBAIf,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;sDAEH,aAAa,OAAO;;;;;;sDAEvB,6LAAC;4CAAE,WAAW,AAAC,gBAId,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;sDAEH,aAAa,OAAO;;;;;;;;;;;;gCAKxB,aAAa,QAAQ,IAAI,aAAa,QAAQ,KAAK,0BAClD,6LAAC;oCAAI,WAAW,AAAC,sBAA6D,OAAxC,iBAAiB,aAAa,QAAQ;8CAC1E,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAM,cAAc,aAAa,UAAU;;;;;;wCAC3C,aAAa,WAAW,kBACvB,6LAAC;4CAAK,WAAU;sDAAc,aAAa,WAAW;;;;;;;;;;;;8CAK1D,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,aAAa,OAAO,kBACpB,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAM;;8DAEN,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;wCAIzC,aAAa,OAAO,kBACnB,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;gBAS/C,CAAC,aAAa,OAAO,kBACpB,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;KArKM;uCAuKS", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useNotifications.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\nexport interface NotificationCounts {\r\n  total: number;\r\n  unread: number;\r\n}\r\n\r\nexport interface UseNotificationsReturn {\r\n  notifications: AppNotification[];\r\n  unreadCount: number;\r\n  totalCount: number;\r\n  loading: boolean;\r\n  error: string | null;\r\n  fetchNotifications: () => Promise<void>;\r\n  markAsRead: (id: string) => Promise<void>;\r\n  markAllAsRead: () => Promise<void>;\r\n  refreshNotifications: () => Promise<void>;\r\n  getNotificationCounts: () => Promise<void>;\r\n}\r\n\r\nexport const useNotifications = (): UseNotificationsReturn => {\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [totalCount, setTotalCount] = useState(0);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { user, isAuthenticated } = useAuth();\r\n  const { showError } = useToast();\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      console.log('🔍 Fetching notifications for user:', {\r\n        userId: user.user_id,\r\n        email: user.email\r\n      });\r\n\r\n      const data = await notificationService.getUserNotifications({\r\n        page: 1,\r\n        limit: 50 // Get more notifications for better UX\r\n      });\r\n\r\n      if (data && data.notifications) {\r\n        setNotifications(data.notifications);\r\n        setUnreadCount(data.unread_count);\r\n        setTotalCount(data.total_count);\r\n      } else {\r\n        setNotifications([]);\r\n        setUnreadCount(0);\r\n        setTotalCount(0);\r\n      }\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';\r\n      setError(errorMessage);\r\n      console.error('Error fetching notifications:', err);\r\n      // Don't show toast error for initial load failures\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [isAuthenticated, user]);\r\n\r\n  const markAsRead = useCallback(async (id: string) => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await notificationService.markAsRead(id);\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notification =>\r\n          notification.notification_id === id\r\n            ? { ...notification, status: 'read', read_at: new Date().toISOString() }\r\n            : notification\r\n        )\r\n      );\r\n\r\n      // Update unread count\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark notification as read');\r\n      console.error('Error marking notification as read:', err);\r\n    }\r\n  }, [isAuthenticated, showError]);\r\n\r\n  const markAllAsRead = useCallback(async () => {\r\n    if (!isAuthenticated || notifications.length === 0) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Mark all unread notifications as read\r\n      const unreadNotifications = notifications.filter(n => n.status === 'unread');\r\n\r\n      for (const notification of unreadNotifications) {\r\n        await markAsRead(notification.notification_id);\r\n      }\r\n\r\n      setUnreadCount(0);\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark all notifications as read');\r\n      console.error('Error marking all notifications as read:', err);\r\n    }\r\n  }, [isAuthenticated, notifications, markAsRead, showError]);\r\n\r\n  const getNotificationCounts = useCallback(async () => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const counts = await notificationService.getNotificationCount();\r\n      setUnreadCount(counts.unread);\r\n      setTotalCount(counts.total);\r\n    } catch (err) {\r\n      console.error('Error getting notification counts:', err);\r\n    }\r\n  }, [isAuthenticated]);\r\n\r\n  const refreshNotifications = useCallback(async () => {\r\n    await fetchNotifications();\r\n  }, [fetchNotifications]);\r\n\r\n  // Initial fetch when component mounts and user is authenticated\r\n  useEffect(() => {\r\n    if (isAuthenticated && user) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  // Set up polling for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    const interval = setInterval(() => {\r\n      fetchNotifications();\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  return {\r\n    notifications,\r\n    unreadCount,\r\n    totalCount,\r\n    loading,\r\n    error,\r\n    fetchNotifications,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n    getNotificationCounts,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;;AALA;;;;;AA0BO,MAAM,mBAAmB;;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAE7B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACF,QAAQ,GAAG,CAAC,uCAAuC;oBACjD,QAAQ,KAAK,OAAO;oBACpB,OAAO,KAAK,KAAK;gBACnB;gBAEA,MAAM,OAAO,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;oBAC1D,MAAM;oBACN,OAAO,GAAG,uCAAuC;gBACnD;gBAEA,IAAI,QAAQ,KAAK,aAAa,EAAE;oBAC9B,iBAAiB,KAAK,aAAa;oBACnC,eAAe,KAAK,YAAY;oBAChC,cAAc,KAAK,WAAW;gBAChC,OAAO;oBACL,iBAAiB,EAAE;oBACnB,eAAe;oBACf,cAAc;gBAChB;YACF,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mDAAmD;YACrD,SAAU;gBACR,WAAW;YACb;QACF;2DAAG;QAAC;QAAiB;KAAK;IAE1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACpC,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,yIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;gBAErC,qBAAqB;gBACrB;gEAAiB,CAAA,OACf,KAAK,GAAG;wEAAC,CAAA,eACP,aAAa,eAAe,KAAK,KAC7B;oCAAE,GAAG,YAAY;oCAAE,QAAQ;oCAAQ,SAAS,IAAI,OAAO,WAAW;gCAAG,IACrE;;;gBAIR,sBAAsB;gBACtB;gEAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;;YAC5C,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;mDAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAChC,IAAI,CAAC,mBAAmB,cAAc,MAAM,KAAK,GAAG;gBAClD;YACF;YAEA,IAAI;gBACF,wCAAwC;gBACxC,MAAM,sBAAsB,cAAc,MAAM;uFAAC,CAAA,IAAK,EAAE,MAAM,KAAK;;gBAEnE,KAAK,MAAM,gBAAgB,oBAAqB;oBAC9C,MAAM,WAAW,aAAa,eAAe;gBAC/C;gBAEA,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,4CAA4C;YAC5D;QACF;sDAAG;QAAC;QAAiB;QAAe;QAAY;KAAU;IAE1D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YACxC,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,SAAS,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB;gBAC7D,eAAe,OAAO,MAAM;gBAC5B,cAAc,OAAO,KAAK;YAC5B,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;8DAAG;QAAC;KAAgB;IAEpB,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACvC,MAAM;QACR;6DAAG;QAAC;KAAmB;IAEvB,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,mBAAmB,MAAM;gBAC3B;YACF;QACF;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,MAAM,WAAW;uDAAY;oBAC3B;gBACF;sDAAG,QAAQ,aAAa;YAExB;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlJa;;QAMuB,kIAAA,CAAA,UAAO;QACnB,mIAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Modal from '@/components/common/Modal';\r\nimport NotificationItem from './NotificationItem';\r\nimport Loader from '@/components/Loader';\r\nimport { useNotifications } from '@/hooks/useNotifications';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n}) => {\r\n  const {\r\n    notifications,\r\n    unreadCount,\r\n    loading,\r\n    error,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n  } = useNotifications();\r\n  \r\n  const { showSuccess, showError } = useToast();\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('unread');\r\n  const [localLoading, setLocalLoading] = useState(false);\r\n\r\n  // Filter notifications based on selected filter\r\n  const filteredNotifications = notifications.filter(notification => {\r\n    if (filter === 'unread') {\r\n      return !notification.read_at;\r\n    }\r\n    return true;\r\n  });\r\n\r\n\r\n  // Refresh notifications when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      refreshNotifications();\r\n    }\r\n  }, [isOpen, refreshNotifications]);\r\n\r\n  const handleMarkAsRead = async (id: string) => {\r\n    try {\r\n      await markAsRead(id);\r\n      showSuccess('Notification marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    if (unreadCount === 0) {\r\n      showError('No unread notifications to mark');\r\n      return;\r\n    }\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await markAllAsRead();\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleNotificationClick = (notification: AppNotification) => {\r\n    // Handle notification click - could navigate to related page\r\n    if (notification.entity_type === 'application' && notification.entity_id) {\r\n      // Navigate to application details\r\n      window.location.href = `/customer/my-licenses?application_id=${notification.entity_id}`;\r\n    }\r\n  };\r\n\r\n  const handleRefresh = async () => {\r\n    setLocalLoading(true);\r\n    try {\r\n      await refreshNotifications();\r\n    } catch (error) {\r\n      console.error('Error refreshing notifications:', error);\r\n      showError('Failed to refresh notifications');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title=\"Notifications\"\r\n      size=\"lg\"\r\n    >\r\n      <div className=\"flex flex-col h-96\">\r\n        {/* Header with filters and actions */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Filter buttons */}\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'all'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                All ({notifications.length})\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'unread'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                Unread ({unreadCount})\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action buttons */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <button\r\n              onClick={handleRefresh}\r\n              disabled={loading || localLoading}\r\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50\"\r\n              title=\"Refresh notifications\"\r\n            >\r\n              <i className={`ri-refresh-line ${(loading || localLoading) ? 'animate-spin' : ''}`}></i>\r\n            </button>\r\n            \r\n            {unreadCount > 0 && (\r\n              <button\r\n                onClick={handleMarkAllAsRead}\r\n                disabled={loading || localLoading}\r\n                className=\"px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Notifications list */}\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          {loading && notifications.length === 0 ? (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <Loader message=\"Loading notifications...\" />\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-error-warning-line text-4xl text-red-500 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                Error Loading Notifications\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n                {error}\r\n              </p>\r\n              <button\r\n                onClick={handleRefresh}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          ) : filteredNotifications.length === 0 ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                {filter === 'unread' ? 'No Unread Notifications' : 'No Notifications'}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {filter === 'unread' \r\n                  ? 'All caught up! You have no unread notifications.'\r\n                  : 'You have no notifications at this time.'\r\n                }\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\r\n              {filteredNotifications.map((notification) => (\r\n                <NotificationItem\r\n                  key={notification.notification_id}\r\n                  notification={notification}\r\n                  onMarkAsRead={handleMarkAsRead}\r\n                  onNotificationClick={handleNotificationClick}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        {filteredNotifications.length > 0 && (\r\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700 text-center\">\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Showing {filteredNotifications.length} of {notifications.length} notifications\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAeA,MAAM,oBAAsD;QAAC,EAC3D,MAAM,EACN,OAAO,EACR;;IACC,MAAM,EACJ,aAAa,EACb,WAAW,EACX,OAAO,EACP,KAAK,EACL,UAAU,EACV,aAAa,EACb,oBAAoB,EACrB,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gDAAgD;IAChD,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW,UAAU;YACvB,OAAO,CAAC,aAAa,OAAO;QAC9B;QACA,OAAO;IACT;IAGA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;sCAAG;QAAC;QAAQ;KAAqB;IAEjC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW;YACjB,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,GAAG;YACrB,UAAU;YACV;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,6DAA6D;QAC7D,IAAI,aAAa,WAAW,KAAK,iBAAiB,aAAa,SAAS,EAAE;YACxE,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,AAAC,wCAA8D,OAAvB,aAAa,SAAS;QACvF;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,wIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,+DAIX,OAHC,WAAW,QACP,qEACA;;4CAEP;4CACO,cAAc,MAAM;4CAAC;;;;;;;kDAE7B,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,+DAIX,OAHC,WAAW,WACP,qEACA;;4CAEP;4CACU;4CAAY;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCAAE,WAAW,AAAC,mBAAkE,OAAhD,AAAC,WAAW,eAAgB,iBAAiB;;;;;;;;;;;gCAG/E,cAAc,mBACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;8BACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;mEAEhB,sBACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,6LAAC;gCAAE,WAAU;0CACV;;;;;;0CAEH,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;mEAID,sBAAsB,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CACX,WAAW,WAAW,4BAA4B;;;;;;0CAErD,6LAAC;gCAAE,WAAU;0CACV,WAAW,WACR,qDACA;;;;;;;;;;;iFAKR,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC,0JAAA,CAAA,UAAgB;gCAEf,cAAc;gCACd,cAAc;gCACd,qBAAqB;+BAHhB,aAAa,eAAe;;;;;;;;;;;;;;;gBAW1C,sBAAsB,MAAM,GAAG,mBAC9B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAA2C;4BAC7C,sBAAsB,MAAM;4BAAC;4BAAK,cAAc,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAO9E;GAzMM;;QAYA,mIAAA,CAAA,mBAAgB;QAEe,mIAAA,CAAA,WAAQ;;;KAdvC;uCA2MS", "debugId": null}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport NotificationModal from '../notifications/NotificationModal';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n}\r\n\r\nconst NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\r\n  const { user } = useAuth();\r\n  const { showError } = useToast();\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  // Fetch notification count\r\n  const fetchNotificationCount = async () => {\r\n    if (!user) return;\r\n    \r\n    try {\r\n      const data = await notificationService.getNotificationCount();\r\n      setUnreadCount(data.unread);\r\n    } catch (error) {\r\n      console.error('Error fetching notification count:', error);\r\n      showError('Failed to load notification count');\r\n    }\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchNotificationCount();\r\n  }, [user]);\r\n\r\n  // Poll for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    const interval = setInterval(fetchNotificationCount, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Handle modal close and refresh count\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    fetchNotificationCount(); // Refresh count when modal closes\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className={`relative ${className}`}>\r\n        {/* Notification Bell */}\r\n        <button\r\n          onClick={() => setIsModalOpen(true)}\r\n          className=\"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n          title=\"Notifications\"\r\n        >\r\n          <i className=\"ri-notification-line text-xl\"></i>\r\n          {unreadCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1\">\r\n              {unreadCount > 99 ? '99+' : unreadCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYA,MAAM,mBAAoD;QAAC,EAAE,YAAY,EAAE,EAAE;;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC3D,eAAe,KAAK,MAAM;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW,YAAY,wBAAwB;YACrD;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,eAAe;QACf,0BAA0B,kCAAkC;IAC9D;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,6LAAC;gBAAI,WAAW,AAAC,YAAqB,OAAV;0BAE1B,cAAA,6LAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;oBACV,OAAM;;sCAEN,6LAAC;4BAAE,WAAU;;;;;;wBACZ,cAAc,mBACb,6LAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOpC,6LAAC,2JAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;GA/DM;;QACa,kIAAA,CAAA,UAAO;QACF,mIAAA,CAAA,WAAQ;;;KAF1B;uCAiES", "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\nimport NotificationBell from '../common/NotificationBell';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'My Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Invoices & Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <NotificationBell className=\"mr-4\" />\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAqBA,MAAM,iBAAgD;QAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM;gBACpC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;aACD;kDAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM;gBACjC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;+CAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,MAAM,gBAAgB;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,cAAc,OAAO;kEAAC,CAAA;4BACpB,OAAO,QAAQ,CAAC;wBAClB;;gBACF;;YAEA,4DAA4D;YAC5D,MAAM,QAAQ,WAAW,eAAe;YACxC;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,uBAAuB,CAAC;QAC1B;0DAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAChD,MAAM,eAA0C;gBAC9C,aAAa;gBACb,yBAAyB;gBACzB,0BAA0B;gBAC1B,iCAAiC;gBACjC,sBAAsB;gBACtB,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,6BAA6B;gBAC7B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;YAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,AAAC,WAAe,OAAL,MAAK;YACtD,WAAW;YACX,uBAAuB;QACzB;qDAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,2CAA2C;YAC3C,OAAO,QAAQ,CAAC;QAClB;qDAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,sBAAsB,CAAC;QACzB;yDAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,qCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,6LAAC;gBAAM,WAAW,AAAC,6KAE8D,OAA7E,sBAAsB,kBAAkB,sCAAqC;0BAE/E,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,AAAC,0JAKT,OAHC,KAAK,OAAO,GACV,8GACA,yHACH;;8DAGH,6LAAC;oDAAI,WAAW,AAAC,iDAAqG,OAArD,KAAK,OAAO,GAAG,mCAAmC;8DACjH,cAAA,6LAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAqB5B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,6LAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;iHAGb,6LAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mJAAA,CAAA,UAAgB;4CAAC,WAAU;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,gIAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,CAAA,iBAAA,2BAAA,KAAM,aAAa,KAAI;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,6LAAC,qIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlTM;;QAGa,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACP,kIAAA,CAAA,UAAO;QACD,qIAAA,CAAA,aAAU;;;KAN7B;uCAoTS", "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/TextInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  ...props\r\n}, ref) => {\r\n  // Base input styling with proper text visibility for all modes - force text color to ensure visibility\r\n  const baseInputClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <input\r\n        ref={ref}\r\n        className={inputClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextInput.displayName = 'TextInput';\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAoC,QAU5D;QAV6D,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,uGAAuG;IACvG,MAAM,iBAAiB,AAAC,+OAEpB,OADF,YAAY,WAAW,IACxB,KAAmD,OAAhD,YAAY,UAAU,mBAAmB;IAE7C,4BAA4B;IAC5B,MAAM,aAAa,AAAC,GAClB,OADoB,gBAAe,KAKnC,OAJA,QACI,+EACA,wCACL,KAIG,OAHF,WACI,8DACA,IACL,KAAa,OAAV;IAEJ,MAAM,aAAa,AAAC,2DAEnB,OADC,YAAY,UAAU,kDAAkD;IAG1E,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAGV,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 2252, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/TextArea.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  rows = 3,\r\n  ...props\r\n}, ref) => {\r\n  // Base textarea styling with proper text visibility for all modes - force text color to ensure visibility\r\n  const baseTextAreaClass = `px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 resize-y min-h-[120px] ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-2 text-sm min-h-[80px]' : 'text-base'}`;\r\n\r\n  // Error and disabled states\r\n  const textAreaClass = `${baseTextAreaClass} ${\r\n    error\r\n      ? \"border-red-300 dark:border-red-500 focus:ring-red-500 focus:border-red-500 bg-red-50 dark:bg-red-900/10\"\r\n      : \"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500\"\r\n  } ${\r\n    disabled\r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\"\r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-semibold text-gray-800 dark:text-gray-200 mb-3 ${\r\n    variant === 'small' ? 'text-sm text-gray-700 dark:text-gray-300 mb-2' : 'text-base'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <textarea\r\n        ref={ref}\r\n        className={textAreaClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        rows={rows}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-2 text-sm text-red-600 dark:text-red-400 flex items-center\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n\r\n      {helperText && !error && (\r\n        <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400 flex items-center\">\r\n          <i className=\"ri-information-line mr-1\"></i>\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextArea.displayName = 'TextArea';\r\n\r\nexport default TextArea;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAsC,QAW7D;QAX8D,EAC/D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,EACR,GAAG,OACJ;IACC,0GAA0G;IAC1G,MAAM,oBAAoB,AAAC,8QAEvB,OADF,YAAY,WAAW,IACxB,KAAmE,OAAhE,YAAY,UAAU,8BAA8B;IAExD,4BAA4B;IAC5B,MAAM,gBAAgB,AAAC,GACrB,OADuB,mBAAkB,KAKzC,OAJA,QACI,4GACA,yFACL,KAIG,OAHF,WACI,8DACA,IACL,KAAa,OAAV;IAEJ,MAAM,aAAa,AAAC,6DAEnB,OADC,YAAY,UAAU,kDAAkD;IAG1E,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,MAAM;gBACL,GAAG,KAAK;;;;;;YAGV,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;;;;;;;AAKX;;AAEA,SAAS,WAAW,GAAG;uCAER", "debugId": null}}, {"offset": {"line": 2356, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n  options?: SelectOption[];\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  options,\r\n  children,\r\n  ...props\r\n}, ref) => {\r\n  // Base select styling with proper text visibility for all modes\r\n  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const selectClass = `${baseSelectClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <select\r\n        ref={ref}\r\n        className={selectClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      >\r\n        {options ? (\r\n          options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))\r\n        ) : (\r\n          children\r\n        )}\r\n      </select>\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAkC,QAYvD;QAZwD,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,GAAG,OACJ;IACC,gEAAgE;IAChE,MAAM,kBAAkB,AAAC,gMAErB,OADF,YAAY,WAAW,IACxB,KAAmD,OAAhD,YAAY,UAAU,mBAAmB;IAE7C,4BAA4B;IAC5B,MAAM,cAAc,AAAC,GACnB,OADqB,iBAAgB,KAKrC,OAJA,QACI,+EACA,wCACL,KAIG,OAHF,WACI,8DACA,IACL,KAAa,OAAV;IAEJ,MAAM,aAAa,AAAC,2DAEnB,OADC,YAAY,UAAU,kDAAkD;IAG1E,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;0BAER,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,6LAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;oEAK3B;;;;;;YAIH,uBACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 2449, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/consumerAffairsService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { PaginatedResponse, PaginateQuery, BaseEntity, UserReference } from '@/types';\r\n\r\n// Types following backend entity structure\r\nexport interface ConsumerAffairsComplaint extends BaseEntity {\r\n  complaint_id: string;\r\n  complaint_number: string;\r\n  complainant_id: string;\r\n  title: string;\r\n  description: string;\r\n  category: ComplaintCategory;\r\n  status: ComplaintStatus;\r\n  priority: ComplaintPriority;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n  deleted_at?: string;\r\n\r\n  // Related data\r\n  complainant?: UserReference;\r\n  assignee?: UserReference;\r\n\r\n  attachments?: ConsumerAffairsComplaintAttachment[];\r\n  status_history?: ConsumerAffairsComplaintStatusHistory[];\r\n}\r\n\r\nexport interface ConsumerAffairsComplaintAttachment {\r\n  attachment_id: string;\r\n  complaint_id: string;\r\n  file_name: string;\r\n  file_type: string;\r\n  file_size: number;\r\n  file_path: string;\r\n  uploaded_at: string;\r\n  uploaded_by: string;\r\n}\r\n\r\nexport interface ConsumerAffairsComplaintStatusHistory {\r\n  history_id: string;\r\n  complaint_id: string;\r\n  status: ComplaintStatus;\r\n  comment?: string;\r\n  created_at: string;\r\n  created_by: string;\r\n}\r\n\r\n// Enums matching backend\r\nexport enum ComplaintCategory {\r\n  BILLING_CHARGES = 'Billing & Charges',\r\n  SERVICE_QUALITY = 'Service Quality',\r\n  NETWORK_ISSUES = 'Network Issues',\r\n  CUSTOMER_SERVICE = 'Customer Service',\r\n  CONTRACT_DISPUTES = 'Contract Disputes',\r\n  ACCESSIBILITY = 'Accessibility',\r\n  FRAUD_SCAMS = 'Fraud & Scams',\r\n  OTHER = 'Other',\r\n}\r\n\r\nexport enum ComplaintStatus {\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  INVESTIGATING = 'investigating',\r\n  RESOLVED = 'resolved',\r\n  CLOSED = 'closed',\r\n}\r\n\r\nexport enum ComplaintPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent',\r\n}\r\n\r\nexport interface CreateConsumerAffairsComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: ComplaintCategory;\r\n  priority?: ComplaintPriority;\r\n  attachments?: File[];\r\n}\r\n\r\nexport interface UpdateConsumerAffairsComplaintData {\r\n  title?: string;\r\n  description?: string;\r\n  category?: ComplaintCategory;\r\n  status?: ComplaintStatus;\r\n  priority?: ComplaintPriority;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n}\r\n\r\n// Use centralized pagination types from @/types\r\n\r\nexport type ConsumerAffairsComplaintsResponse = PaginatedResponse<ConsumerAffairsComplaint>;\r\n\r\nexport const consumerAffairsService = {\r\n\r\n  // Create new complaint\r\n  async createComplaint(data: CreateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {\r\n    try {\r\n      console.log('🔄 Creating consumer affairs complaint:', {\r\n        title: data.title,\r\n        category: data.category,\r\n        hasAttachments: data.attachments && data.attachments.length > 0\r\n      });\r\n\r\n      const formData = new FormData();\r\n      formData.append('title', data.title);\r\n      formData.append('description', data.description);\r\n      formData.append('category', data.category);\r\n\r\n      if (data.priority) {\r\n        formData.append('priority', data.priority);\r\n      }\r\n\r\n      // Add attachments if provided\r\n      if (data.attachments && data.attachments.length > 0) {\r\n        data.attachments.forEach((file) => {\r\n          formData.append('attachments', file);\r\n        });\r\n      }\r\n\r\n      const response = await apiClient.post('/consumer-affairs-complaints', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get all complaints with pagination\r\n  async getComplaints(query: PaginateQuery = {}): Promise<ConsumerAffairsComplaintsResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/consumer-affairs-complaints?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get complaint by ID\r\n  async getComplaint(id: string): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.get(`/consumer-affairs-complaints/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get complaint by ID (alias for consistency)\r\n  async getComplaintById(id: string): Promise<ConsumerAffairsComplaint> {\r\n    return this.getComplaint(id);\r\n  },\r\n\r\n  // Update complaint\r\n  async updateComplaint(id: string, data: UpdateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.put(`/consumer-affairs-complaints/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete complaint\r\n  async deleteComplaint(id: string): Promise<void> {\r\n    await apiClient.delete(`/consumer-affairs-complaints/${id}`);\r\n  },\r\n\r\n  // Update complaint status (for staff)\r\n  async updateComplaintStatus(id: string, status: ComplaintStatus, comment?: string): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.put(`/consumer-affairs-complaints/${id}/status`, {\r\n      status,\r\n      comment\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Add attachment to complaint\r\n  async addAttachment(id: string, file: File): Promise<ConsumerAffairsComplaintAttachment> {\r\n    const formData = new FormData();\r\n    formData.append('files', file);\r\n\r\n    const response = await apiClient.post(`/consumer-affairs-complaints/${id}/attachments`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Remove attachment from complaint\r\n  async removeAttachment(complaintId: string, attachmentId: string): Promise<void> {\r\n    await apiClient.delete(`/consumer-affairs-complaints/${complaintId}/attachments/${attachmentId}`);\r\n  },\r\n\r\n\r\n\r\n  // Helper methods\r\n  getStatusColor(status: string): string {\r\n    switch (status?.toLowerCase()) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getPriorityColor(priority: string): string {\r\n    switch (priority?.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getStatusOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'submitted', label: 'Submitted' },\r\n      { value: 'under_review', label: 'Under Review' },\r\n      { value: 'investigating', label: 'Investigating' },\r\n      { value: 'resolved', label: 'Resolved' },\r\n      { value: 'closed', label: 'Closed' }\r\n    ];\r\n  },\r\n\r\n  getCategoryOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'Billing & Charges', label: 'Billing & Charges' },\r\n      { value: 'Service Quality', label: 'Service Quality' },\r\n      { value: 'Network Issues', label: 'Network Issues' },\r\n      { value: 'Customer Service', label: 'Customer Service' },\r\n      { value: 'Contract Disputes', label: 'Contract Disputes' },\r\n      { value: 'Accessibility', label: 'Accessibility' },\r\n      { value: 'Fraud & Scams', label: 'Fraud & Scams' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  },\r\n\r\n  getPriorityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'urgent', label: 'Urgent' }\r\n    ];\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAgDO,IAAA,AAAK,2CAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,yCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,2CAAA;;;;;WAAA;;AA+BL,MAAM,yBAAyB;IAEpC,uBAAuB;IACvB,MAAM,iBAAgB,IAAwC;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C;gBACrD,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG;YAChE;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;YACnC,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAEzC,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAC3C;YAEA,8BAA8B;YAC9B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC;oBACxB,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC,UAAU;gBAC9E,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,MAAM;YAAc,QAAA,iEAAuB,CAAC;QAC1C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC;oBAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,AAAC,UAAa,OAAJ,MAAO;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,AAAC,UAAa,OAAJ,MAAO;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,gCAAiD,OAAlB,OAAO,QAAQ;QACpF,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,gCAAkC,OAAH;QACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8CAA8C;IAC9C,MAAM,kBAAiB,EAAU;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAwC;QACxE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,gCAAkC,OAAH,KAAM;QAC3E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,gCAAkC,OAAH;IACzD;IAEA,sCAAsC;IACtC,MAAM,uBAAsB,EAAU,EAAE,MAAuB,EAAE,OAAgB;QAC/E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,gCAAkC,OAAH,IAAG,YAAU;YAChF;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU,EAAE,IAAU;QACxC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,gCAAkC,OAAH,IAAG,iBAAe,UAAU;YAChG,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,kBAAiB,WAAmB,EAAE,YAAoB;QAC9D,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,gCAA0D,OAA3B,aAAY,iBAA4B,OAAb;IACpF;IAIA,iBAAiB;IACjB,gBAAe,MAAc;QAC3B,OAAQ,mBAAA,6BAAA,OAAQ,WAAW;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,kBAAiB,QAAgB;QAC/B,OAAQ,qBAAA,+BAAA,SAAU,WAAW;YAC3B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAmB,OAAO;YAAkB;YACrD;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAoB,OAAO;YAAmB;YACvD;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;AACF", "debugId": null}}, {"offset": {"line": 2705, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/index.ts"], "sourcesContent": ["// Consumer Affairs Services\r\nexport * from './consumerAffairsService';\r\nexport { consumerAffairsService } from './consumerAffairsService';\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;AAC5B", "debugId": null}}, {"offset": {"line": 2726, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/ConsumerAffairsModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport TextInput from '@/components/forms/TextInput';\r\nimport TextArea from '@/components/forms/TextArea';\r\nimport Select from '@/components/forms/Select';\r\nimport { consumerAffairsService, CreateConsumerAffairsComplaintData } from '@/services/consumer-affairs';\r\nimport { useToast } from '@/contexts/ToastContext';\r\n\r\ninterface ConsumerAffairsModalProps {\r\n  onClose: () => void;\r\n  onSubmit: (data: any) => void;\r\n}\r\n\r\nconst complaintCategories = [\r\n  'Billing & Charges',\r\n  'Service Quality',\r\n  'Network Issues',\r\n  'Customer Service',\r\n  'Contract Disputes',\r\n  'Accessibility',\r\n  'Fraud & Scams',\r\n  'Other'\r\n];\r\n\r\nconst ConsumerAffairsModal: React.FC<ConsumerAffairsModalProps> = ({ onClose, onSubmit }) => {\r\n  const { showSuccess, showError } = useToast();\r\n  const [formData, setFormData] = useState({\r\n    title: '',\r\n    description: '',\r\n    category: ''\r\n  });\r\n  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [attachments, setAttachments] = useState<File[]>([]);\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n    \r\n    // Clear error when user starts typing\r\n    if (formErrors[name]) {\r\n      setFormErrors(prev => ({\r\n        ...prev,\r\n        [name]: ''\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files) {\r\n      const newFiles = Array.from(e.target.files);\r\n      setAttachments(prev => [...prev, ...newFiles]);\r\n    }\r\n  };\r\n\r\n  const removeAttachment = (index: number) => {\r\n    setAttachments(prev => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const errors: {[key: string]: string} = {};\r\n\r\n    if (!formData.title.trim()) {\r\n      errors.title = 'Title is required';\r\n    }\r\n\r\n    if (!formData.description.trim()) {\r\n      errors.description = 'Description is required';\r\n    } else if (formData.description.trim().length < 20) {\r\n      errors.description = 'Description must be at least 20 characters';\r\n    }\r\n\r\n    if (!formData.category) {\r\n      errors.category = 'Category is required';\r\n    }\r\n\r\n    setFormErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    try {\r\n      const complaintData: CreateConsumerAffairsComplaintData = {\r\n        title: formData.title,\r\n        description: formData.description,\r\n        category: formData.category,\r\n        attachments: attachments\r\n      };\r\n\r\n      const response = await consumerAffairsService.createComplaint(complaintData);\r\n\r\n      // Show success message\r\n      showSuccess(\r\n        `Your complaint has been submitted successfully! Reference ID: ${response.complaint_id || 'N/A'}`,\r\n        6000\r\n      );\r\n\r\n      onSubmit(response);\r\n\r\n      // Reset form\r\n      setFormData({\r\n        title: '',\r\n        description: '',\r\n        category: ''\r\n      });\r\n      setAttachments([]);\r\n\r\n    } catch (error) {\r\n      console.error('Error submitting complaint:', error);\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to submit complaint. Please try again.';\r\n      showError(errorMessage);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto\">\r\n        <div className=\"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            Lodge Consumer Affairs Complaint\r\n          </h3>\r\n          <button\r\n            type=\"button\"\r\n            onClick={onClose}\r\n            aria-label=\"Close modal\"\r\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n          >\r\n            <i className=\"ri-close-line text-xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"p-6\">\r\n          <div className=\"mb-6\">\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Submit your complaint about telecommunications services, billing issues, or other consumer concerns.\r\n              Our team will investigate and work to resolve your issue.\r\n            </p>\r\n          </div>\r\n\r\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n            {/* Title */}\r\n            <TextInput\r\n              label=\"Complaint Title *\"\r\n              id=\"complaint-title\"\r\n              name=\"title\"\r\n              value={formData.title}\r\n              onChange={handleInputChange}\r\n              placeholder=\"Brief summary of your complaint\"\r\n              error={formErrors.title}\r\n              required\r\n            />\r\n\r\n            {/* Category */}\r\n            <Select\r\n              label=\"Category\"\r\n              name=\"category\"\r\n              value={formData.category}\r\n              onChange={handleInputChange}\r\n              error={formErrors.category}\r\n              required\r\n            >\r\n              <option value=\"\">Select a category</option>\r\n              {complaintCategories.map(category => (\r\n                <option key={category} value={category}>{category}</option>\r\n              ))}\r\n            </Select>\r\n\r\n            {/* Description */}\r\n            <div>\r\n              <TextArea\r\n                label=\"Detailed Description *\"\r\n                id=\"complaint-description\"\r\n                name=\"description\"\r\n                value={formData.description}\r\n                onChange={handleInputChange}\r\n                rows={6}\r\n                placeholder=\"Please provide a detailed description of your complaint, including dates, times, and any relevant information...\"\r\n                error={formErrors.description}\r\n                helperText=\"Minimum 20 characters required\"\r\n                required\r\n              />\r\n            </div>\r\n\r\n            {/* File Attachments */}\r\n            <div>\r\n              <label htmlFor=\"complaint-attachments\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Supporting Documents (Optional)\r\n              </label>\r\n              <input\r\n                id=\"complaint-attachments\"\r\n                type=\"file\"\r\n                multiple\r\n                accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\"\r\n                onChange={handleFileChange}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n              />\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 5MB per file)\r\n              </p>\r\n\r\n              {/* Show selected files */}\r\n              {attachments.length > 0 && (\r\n                <div className=\"mt-3 space-y-2\">\r\n                  {attachments.map((file, index) => (\r\n                    <div key={index} className=\"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded\">\r\n                      <div className=\"flex items-center\">\r\n                        <i className=\"ri-file-line text-gray-400 mr-2\"></i>\r\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">{file.name}</span>\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400 ml-2\">\r\n                          ({formatFileSize(file.size)})\r\n                        </span>\r\n                      </div>\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => removeAttachment(index)}\r\n                        className=\"text-red-500 hover:text-red-700\"\r\n                        aria-label={`Remove ${file.name}`}\r\n                      >\r\n                        <i className=\"ri-close-line\"></i>\r\n                      </button>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Submit Buttons */}\r\n            <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                disabled={isSubmitting}\r\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300\"\r\n              >\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                    Submitting...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <i className=\"ri-send-plane-line mr-2\"></i>\r\n                    Submit Complaint\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ConsumerAffairsModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAcA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,uBAA4D;QAAC,EAAE,OAAO,EAAE,QAAQ,EAAE;;IACtF,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAEzD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,sCAAsC;QACtC,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;YAC1C,eAAe,CAAA,OAAQ;uBAAI;uBAAS;iBAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACrD;IAEA,MAAM,eAAe;QACnB,MAAM,SAAkC,CAAC;QAEzC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,OAAO,KAAK,GAAG;QACjB;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,OAAO,WAAW,GAAG;QACvB,OAAO,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;YAClD,OAAO,WAAW,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,OAAO,QAAQ,GAAG;QACpB;QAEA,cAAc;QACd,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,gBAAoD;gBACxD,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,QAAQ;gBAC3B,aAAa;YACf;YAEA,MAAM,WAAW,MAAM,mKAAA,CAAA,yBAAsB,CAAC,eAAe,CAAC;YAE9D,uBAAuB;YACvB,YACE,AAAC,iEAA+F,OAA/B,SAAS,YAAY,IAAI,QAC1F;YAGF,SAAS;YAET,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,UAAU;YACZ;YACA,eAAe,EAAE;QAEnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,cAAW;4BACX,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;sCAM1D,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC,2IAAA,CAAA,UAAS;oCACR,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,aAAY;oCACZ,OAAO,WAAW,KAAK;oCACvB,QAAQ;;;;;;8CAIV,6LAAC,wIAAA,CAAA,UAAM;oCACL,OAAM;oCACN,MAAK;oCACL,OAAO,SAAS,QAAQ;oCACxB,UAAU;oCACV,OAAO,WAAW,QAAQ;oCAC1B,QAAQ;;sDAER,6LAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,oBAAoB,GAAG,CAAC,CAAA,yBACvB,6LAAC;gDAAsB,OAAO;0DAAW;+CAA5B;;;;;;;;;;;8CAKjB,6LAAC;8CACC,cAAA,6LAAC,0IAAA,CAAA,UAAQ;wCACP,OAAM;wCACN,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU;wCACV,MAAM;wCACN,aAAY;wCACZ,OAAO,WAAW,WAAW;wCAC7B,YAAW;wCACX,QAAQ;;;;;;;;;;;8CAKZ,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAwB,WAAU;sDAAkE;;;;;;sDAGnH,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,QAAO;4CACP,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;wCAK5D,YAAY,MAAM,GAAG,mBACpB,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;;;;;8EACb,6LAAC;oEAAK,WAAU;8EAA4C,KAAK,IAAI;;;;;;8EACrE,6LAAC;oEAAK,WAAU;;wEAAgD;wEAC5D,eAAe,KAAK,IAAI;wEAAE;;;;;;;;;;;;;sEAGhC,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,iBAAiB;4DAChC,WAAU;4DACV,cAAY,AAAC,UAAmB,OAAV,KAAK,IAAI;sEAE/B,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;mDAdP;;;;;;;;;;;;;;;;8CAuBlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,6BACC;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAAyC;;6EAIxD;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW/D;GA9PM;;QAC+B,mIAAA,CAAA,WAAQ;;;KADvC;uCAgQS", "debugId": null}}, {"offset": {"line": 3180, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/data-breach/dataBreachService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { PaginatedResponse, PaginateQuery, BaseEntity, UserReference } from '@/types';\r\n\r\n// Types following backend entity structure\r\nexport interface DataBreachReport {\r\n  report_id: string;\r\n  report_number: string;\r\n  reporter_id: string;\r\n  title: string;\r\n  description: string;\r\n  category: DataBreachCategory;\r\n  severity: DataBreachSeverity;\r\n  status: string;\r\n  priority: DataBreachPriority;\r\n  incident_date: string;\r\n  organization_involved: string;\r\n  affected_data_types?: string;\r\n  contact_attempts?: string;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string;\r\n  created_by?: string;\r\n  updated_by?: string;\r\n\r\n  // Related data\r\n  reporter?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  assignee?: {\r\n    user_id: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    email: string;\r\n  };\r\n\r\n  attachments?: DataBreachReportAttachment[];\r\n  status_history?: DataBreachReportStatusHistory[];\r\n}\r\n\r\nexport interface DataBreachReportAttachment {\r\n  attachment_id: string;\r\n  report_id: string;\r\n  file_name: string;\r\n  file_type: string;\r\n  file_size: number;\r\n  file_path: string;\r\n  uploaded_at: string;\r\n  uploaded_by: string;\r\n}\r\n\r\nexport interface DataBreachReportStatusHistory {\r\n  history_id: string;\r\n  report_id: string;\r\n  status: DataBreachStatus;\r\n  comment?: string;\r\n  created_at: string;\r\n  created_by: string;\r\n}\r\n\r\n// Enums matching backend\r\nexport enum DataBreachCategory {\r\n  PERSONAL_DATA = 'Personal Data',\r\n  FINANCIAL_DATA = 'Financial Data',\r\n  HEALTH_DATA = 'Health Data',\r\n  TECHNICAL_DATA = 'Technical Data',\r\n  COMMUNICATION_DATA = 'Communication Data',\r\n  OTHER = 'Other',\r\n}\r\n\r\nexport enum DataBreachSeverity {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  CRITICAL = 'critical',\r\n}\r\n\r\nexport enum DataBreachStatus {\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  INVESTIGATING = 'investigating',\r\n  RESOLVED = 'resolved',\r\n  CLOSED = 'closed',\r\n}\r\n\r\nexport enum DataBreachPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent',\r\n}\r\n\r\nexport interface CreateDataBreachReportData {\r\n  title: string;\r\n  description: string;\r\n  category: DataBreachCategory;\r\n  severity: DataBreachSeverity;\r\n  priority?: DataBreachPriority;\r\n  incident_date: string;\r\n  organization_involved: string;\r\n  affected_data_types?: string;\r\n  contact_attempts?: string;\r\n  attachments?: File[];\r\n}\r\n\r\nexport interface UpdateDataBreachReportData {\r\n  title?: string;\r\n  description?: string;\r\n  category?: DataBreachCategory;\r\n  severity?: DataBreachSeverity;\r\n  status?: DataBreachStatus;\r\n  priority?: DataBreachPriority;\r\n  incident_date?: string;\r\n  organization_involved?: string;\r\n  affected_data_types?: string;\r\n  contact_attempts?: string;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n}\r\n\r\nexport type DataBreachReportsResponse = PaginatedResponse<DataBreachReport>;\r\n\r\nexport const dataBreachService = {\r\n  // Create new report\r\n  async createReport(data: CreateDataBreachReportData): Promise<DataBreachReport> {\r\n    try {\r\n      console.log('🔄 Creating data breach report:', {\r\n        title: data.title,\r\n        category: data.category,\r\n        severity: data.severity,\r\n        hasAttachments: data.attachments && data.attachments.length > 0\r\n      });\r\n\r\n      const formData = new FormData();\r\n      formData.append('title', data.title);\r\n      formData.append('description', data.description);\r\n      formData.append('category', data.category);\r\n      formData.append('severity', data.severity);\r\n      formData.append('incident_date', data.incident_date);\r\n      formData.append('organization_involved', data.organization_involved);\r\n\r\n      if (data.priority) {\r\n        formData.append('priority', data.priority);\r\n      }\r\n\r\n      if (data.affected_data_types) {\r\n        formData.append('affected_data_types', data.affected_data_types);\r\n      }\r\n\r\n      if (data.contact_attempts) {\r\n        formData.append('contact_attempts', data.contact_attempts);\r\n      }\r\n\r\n      // Add attachments if provided\r\n      if (data.attachments && data.attachments.length > 0) {\r\n        data.attachments.forEach((file) => {\r\n          formData.append('attachments', file);\r\n        });\r\n      }\r\n\r\n      const response = await apiClient.post('/data-breach-reports', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      console.log('✅ Data breach report created successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Error creating data breach report:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get report by ID\r\n  async getReportById(reportId: string): Promise<DataBreachReport> {\r\n    try {\r\n      console.log('🔄 Fetching data breach report by ID:', reportId);\r\n\r\n      const response = await apiClient.get(`/data-breach-reports/${reportId}`);\r\n\r\n      console.log('✅ Data breach report fetched successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Error fetching data breach report:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update report status\r\n  async updateStatus(reportId: string, status: string, comment?: string): Promise<DataBreachReport> {\r\n    try {\r\n      console.log('🔄 Updating data breach report status:', { reportId, status, comment });\r\n\r\n      const response = await apiClient.put(`/data-breach-reports/${reportId}/status`, {\r\n        status,\r\n        comment\r\n      });\r\n\r\n      console.log('✅ Data breach report status updated successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Error updating data breach report status:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Assign report to officer\r\n  async assignReport(reportId: string, assignedTo: string): Promise<DataBreachReport> {\r\n    try {\r\n      console.log('🔄 Assigning data breach report:', { reportId, assignedTo });\r\n\r\n      const response = await apiClient.put(`/data-breach-reports/${reportId}/assign`, {\r\n        assigned_to: assignedTo\r\n      });\r\n\r\n      console.log('✅ Data breach report assigned successfully:', response.data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('❌ Error assigning data breach report:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get all reports with pagination\r\n  async getReports(query: PaginateQuery = {}): Promise<any> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/data-breach-reports?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get report by ID\r\n  async getReport(id: string): Promise<DataBreachReport> {\r\n    const response = await apiClient.get(`/data-breach-reports/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update report\r\n  async updateReport(id: string, data: UpdateDataBreachReportData): Promise<DataBreachReport> {\r\n    const response = await apiClient.put(`/data-breach-reports/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete report\r\n  async deleteReport(id: string): Promise<void> {\r\n    await apiClient.delete(`/data-breach-reports/${id}`);\r\n  },\r\n\r\n  // Update report status (for staff)\r\n  async updateReportStatus(id: string, status: DataBreachStatus, comment?: string): Promise<DataBreachReport> {\r\n    const response = await apiClient.put(`/data-breach-reports/${id}/status`, {\r\n      status,\r\n      comment\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Add attachment to report\r\n  async addAttachment(id: string, file: File): Promise<DataBreachReportAttachment> {\r\n    const formData = new FormData();\r\n    formData.append('files', file);\r\n\r\n    const response = await apiClient.post(`/data-breach-reports/${id}/attachments`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Remove attachment from report\r\n  async removeAttachment(reportId: string, attachmentId: string): Promise<void> {\r\n    await apiClient.delete(`/data-breach-reports/${reportId}/attachments/${attachmentId}`);\r\n  },\r\n\r\n  // Helper methods\r\n  getStatusColor(status: string): string {\r\n    switch (status?.toLowerCase()) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getSeverityColor(severity: string): string {\r\n    switch (severity?.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getStatusOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'submitted', label: 'Submitted' },\r\n      { value: 'under_review', label: 'Under Review' },\r\n      { value: 'investigating', label: 'Investigating' },\r\n      { value: 'resolved', label: 'Resolved' },\r\n      { value: 'closed', label: 'Closed' }\r\n    ];\r\n  },\r\n\r\n  getCategoryOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'Personal Data', label: 'Personal Data' },\r\n      { value: 'Financial Data', label: 'Financial Data' },\r\n      { value: 'Health Data', label: 'Health Data' },\r\n      { value: 'Technical Data', label: 'Technical Data' },\r\n      { value: 'Communication Data', label: 'Communication Data' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  },\r\n\r\n  getSeverityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'critical', label: 'Critical' }\r\n    ];\r\n  },\r\n\r\n  getPriorityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'urgent', label: 'Urgent' }\r\n    ];\r\n  },\r\n};\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAoEO,IAAA,AAAK,4CAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,4CAAA;;;;;WAAA;;AAOL,IAAA,AAAK,0CAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,4CAAA;;;;;WAAA;;AAuCL,MAAM,oBAAoB;IAC/B,oBAAoB;IACpB,MAAM,cAAa,IAAgC;QACjD,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;gBAC7C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG;YAChE;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;YACnC,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YACzC,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YACzC,SAAS,MAAM,CAAC,iBAAiB,KAAK,aAAa;YACnD,SAAS,MAAM,CAAC,yBAAyB,KAAK,qBAAqB;YAEnE,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAC3C;YAEA,IAAI,KAAK,mBAAmB,EAAE;gBAC5B,SAAS,MAAM,CAAC,uBAAuB,KAAK,mBAAmB;YACjE;YAEA,IAAI,KAAK,gBAAgB,EAAE;gBACzB,SAAS,MAAM,CAAC,oBAAoB,KAAK,gBAAgB;YAC3D;YAEA,8BAA8B;YAC9B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC;oBACxB,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,wBAAwB,UAAU;gBACtE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,8CAA8C,SAAS,IAAI;YACvE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,eAAc,QAAgB;QAClC,IAAI;YACF,QAAQ,GAAG,CAAC,yCAAyC;YAErD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wBAAgC,OAAT;YAE7D,QAAQ,GAAG,CAAC,8CAA8C,SAAS,IAAI;YACvE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAa,QAAgB,EAAE,MAAc,EAAE,OAAgB;QACnE,IAAI;YACF,QAAQ,GAAG,CAAC,0CAA0C;gBAAE;gBAAU;gBAAQ;YAAQ;YAElF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wBAAgC,OAAT,UAAS,YAAU;gBAC9E;gBACA;YACF;YAEA,QAAQ,GAAG,CAAC,qDAAqD,SAAS,IAAI;YAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAa,QAAgB,EAAE,UAAkB;QACrD,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;gBAAE;gBAAU;YAAW;YAEvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wBAAgC,OAAT,UAAS,YAAU;gBAC9E,aAAa;YACf;YAEA,QAAQ,GAAG,CAAC,+CAA+C,SAAS,IAAI;YACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,MAAM;YAAW,QAAA,iEAAuB,CAAC;QACvC,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC;oBAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,AAAC,UAAa,OAAJ,MAAO;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,AAAC,UAAa,OAAJ,MAAO;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wBAAyC,OAAlB,OAAO,QAAQ;QAC5E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mBAAmB;IACnB,MAAM,WAAU,EAAU;QACxB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wBAA0B,OAAH;QAC7D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,cAAa,EAAU,EAAE,IAAgC;QAC7D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wBAA0B,OAAH,KAAM;QACnE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,cAAa,EAAU;QAC3B,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,wBAA0B,OAAH;IACjD;IAEA,mCAAmC;IACnC,MAAM,oBAAmB,EAAU,EAAE,MAAwB,EAAE,OAAgB;QAC7E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wBAA0B,OAAH,IAAG,YAAU;YACxE;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2BAA2B;IAC3B,MAAM,eAAc,EAAU,EAAE,IAAU;QACxC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,AAAC,wBAA0B,OAAH,IAAG,iBAAe,UAAU;YACxF,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,kBAAiB,QAAgB,EAAE,YAAoB;QAC3D,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,wBAA+C,OAAxB,UAAS,iBAA4B,OAAb;IACzE;IAEA,iBAAiB;IACjB,gBAAe,MAAc;QAC3B,OAAQ,mBAAA,6BAAA,OAAQ,WAAW;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,kBAAiB,QAAgB;QAC/B,OAAQ,qBAAA,+BAAA,SAAU,WAAW;YAC3B,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAe,OAAO;YAAc;YAC7C;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAsB,OAAO;YAAqB;YAC3D;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAY,OAAO;YAAW;SACxC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;AACF", "debugId": null}}, {"offset": {"line": 3510, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/data-breach/index.ts"], "sourcesContent": ["export * from './dataBreachService';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 3529, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/DataBreachModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport TextInput from '@/components/forms/TextInput';\r\nimport TextArea from '@/components/forms/TextArea';\r\nimport Select from '@/components/forms/Select';\r\nimport { dataBreachService, CreateDataBreachReportData } from '@/services/data-breach';\r\nimport { useToast } from '@/contexts/ToastContext';\r\n\r\ninterface DataBreachModalProps {\r\n  onClose: () => void;\r\n  onSubmit: (data: any) => void;\r\n}\r\n\r\nconst breachCategories = [\r\n  'Unauthorized Data Access',\r\n  'Data Misuse or Sharing',\r\n  'Privacy Violations',\r\n  'Identity Theft',\r\n  'Phishing Attempts',\r\n  'Data Loss or Theft',\r\n  'Consent Violations',\r\n  'Other'\r\n];\r\n\r\nconst severityLevels = [\r\n  { value: 'low', label: 'Low - Minor privacy concern' },\r\n  { value: 'medium', label: 'Medium - Moderate data exposure' },\r\n  { value: 'high', label: 'High - Significant data breach' },\r\n  { value: 'critical', label: 'Critical - Severe security incident' }\r\n];\r\n\r\nconst DataBreachModal: React.FC<DataBreachModalProps> = ({ onClose, onSubmit }) => {\r\n  const { showSuccess, showError } = useToast();\r\n  const [formData, setFormData] = useState({\r\n    title: '',\r\n    description: '',\r\n    category: '',\r\n    severity: '',\r\n    incidentDate: '',\r\n    affectedData: '',\r\n    organization: '',\r\n    contactAttempts: ''\r\n  });\r\n  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [attachments, setAttachments] = useState<File[]>([]);\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n    \r\n    // Clear error when user starts typing\r\n    if (formErrors[name]) {\r\n      setFormErrors(prev => ({\r\n        ...prev,\r\n        [name]: ''\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files) {\r\n      const newFiles = Array.from(e.target.files);\r\n      setAttachments(prev => [...prev, ...newFiles]);\r\n    }\r\n  };\r\n\r\n  const removeAttachment = (index: number) => {\r\n    setAttachments(prev => prev.filter((_, i) => i !== index));\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const errors: {[key: string]: string} = {};\r\n\r\n    if (!formData.title.trim()) {\r\n      errors.title = 'Title is required';\r\n    }\r\n\r\n    if (!formData.description.trim()) {\r\n      errors.description = 'Description is required';\r\n    } else if (formData.description.trim().length < 20) {\r\n      errors.description = 'Description must be at least 20 characters';\r\n    }\r\n\r\n    if (!formData.category) {\r\n      errors.category = 'Category is required';\r\n    }\r\n\r\n    if (!formData.severity) {\r\n      errors.severity = 'Severity level is required';\r\n    }\r\n\r\n    if (!formData.incidentDate) {\r\n      errors.incidentDate = 'Incident date is required';\r\n    }\r\n\r\n    if (!formData.organization.trim()) {\r\n      errors.organization = 'Organization involved is required';\r\n    }\r\n\r\n    setFormErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n    try {\r\n      const reportData: CreateDataBreachReportData = {\r\n        title: formData.title,\r\n        description: formData.description,\r\n        category: formData.category,\r\n        severity: formData.severity,\r\n        incident_date: formData.incidentDate,\r\n        organization_involved: formData.organization,\r\n        affected_data_types: formData.affectedData,\r\n        contact_attempts: formData.contactAttempts,\r\n        attachments: attachments\r\n      };\r\n\r\n      const response = await dataBreachService.createReport(reportData);\r\n\r\n      // Show success message\r\n      showSuccess(\r\n        `Your data breach report has been submitted successfully! Reference ID: ${response.report_id || 'N/A'}`,\r\n        6000\r\n      );\r\n\r\n      onSubmit(response);\r\n\r\n      // Reset form\r\n      setFormData({\r\n        title: '',\r\n        description: '',\r\n        category: '',\r\n        severity: '',\r\n        incidentDate: '',\r\n        affectedData: '',\r\n        organization: '',\r\n        contactAttempts: ''\r\n      });\r\n      setAttachments([]);\r\n\r\n    } catch (error) {\r\n      console.error('Error submitting data breach report:', error);\r\n      const errorMessage = error instanceof Error ? error.message : 'Failed to submit data breach report. Please try again.';\r\n      showError(errorMessage);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto\">\r\n        <div className=\"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            Report Data Breach\r\n          </h3>\r\n          <button\r\n            type=\"button\"\r\n            onClick={onClose}\r\n            aria-label=\"Close modal\"\r\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n          >\r\n            <i className=\"ri-close-line text-xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"p-6\">\r\n          <div className=\"mb-6\">\r\n            <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\r\n              <div className=\"flex\">\r\n                <i className=\"ri-shield-keyhole-line text-red-600 text-lg mr-3 mt-0.5\"></i>\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-red-800 dark:text-red-300 mb-1\">\r\n                    Data Breach Reporting\r\n                  </h4>\r\n                  <p className=\"text-sm text-red-700 dark:text-red-400\">\r\n                    Report unauthorized access, misuse, or breach of your personal data. This information will be treated confidentially and investigated promptly.\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n            {/* Title */}\r\n            <TextInput\r\n              label=\"Incident Title *\"\r\n              id=\"breach-title\"\r\n              name=\"title\"\r\n              value={formData.title}\r\n              onChange={handleInputChange}\r\n              placeholder=\"Brief summary of the data breach incident\"\r\n              error={formErrors.title}\r\n              required\r\n            />\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              {/* Category */}\r\n              <Select\r\n                label=\"Breach Category *\"\r\n                name=\"category\"\r\n                value={formData.category}\r\n                onChange={handleInputChange}\r\n                error={formErrors.category}\r\n                required\r\n              >\r\n                <option value=\"\">Select a category</option>\r\n                {breachCategories.map(category => (\r\n                  <option key={category} value={category}>{category}</option>\r\n                ))}\r\n              </Select>\r\n\r\n              {/* Severity */}\r\n              <Select\r\n                label=\"Severity Level *\"\r\n                name=\"severity\"\r\n                value={formData.severity}\r\n                onChange={handleInputChange}\r\n                error={formErrors.severity}\r\n                required\r\n              >\r\n                <option value=\"\">Select severity level</option>\r\n                {severityLevels.map(level => (\r\n                  <option key={level.value} value={level.value}>{level.label}</option>\r\n                ))}\r\n              </Select>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              {/* Incident Date */}\r\n              <TextInput\r\n                label=\"Incident Date *\"\r\n                id=\"incident-date\"\r\n                name=\"incidentDate\"\r\n                type=\"date\"\r\n                value={formData.incidentDate}\r\n                onChange={handleInputChange}\r\n                error={formErrors.incidentDate}\r\n                required\r\n              />\r\n\r\n              {/* Organization */}\r\n              <TextInput\r\n                label=\"Organization Involved *\"\r\n                id=\"organization\"\r\n                name=\"organization\"\r\n                value={formData.organization}\r\n                onChange={handleInputChange}\r\n                placeholder=\"Name of the organization responsible\"\r\n                error={formErrors.organization}\r\n                required\r\n              />\r\n            </div>\r\n\r\n            {/* Affected Data */}\r\n            <TextArea\r\n              label=\"Affected Data Types\"\r\n              id=\"affected-data\"\r\n              name=\"affectedData\"\r\n              value={formData.affectedData}\r\n              onChange={handleInputChange}\r\n              rows={3}\r\n              placeholder=\"Describe what type of personal data was affected (e.g., names, phone numbers, addresses, financial information)\"\r\n              error={formErrors.affectedData}\r\n            />\r\n\r\n            {/* Description */}\r\n            <TextArea\r\n              label=\"Detailed Description *\"\r\n              id=\"breach-description\"\r\n              name=\"description\"\r\n              value={formData.description}\r\n              onChange={handleInputChange}\r\n              rows={6}\r\n              placeholder=\"Please provide a detailed description of the incident, including how you discovered it, what happened, and any impact on you...\"\r\n              error={formErrors.description}\r\n              helperText=\"Minimum 20 characters required\"\r\n              required\r\n            />\r\n\r\n            {/* Contact Attempts */}\r\n            <TextArea\r\n              label=\"Previous Contact Attempts\"\r\n              id=\"contact-attempts\"\r\n              name=\"contactAttempts\"\r\n              value={formData.contactAttempts}\r\n              onChange={handleInputChange}\r\n              rows={3}\r\n              placeholder=\"Describe any attempts you made to contact the organization about this incident\"\r\n              error={formErrors.contactAttempts}\r\n            />\r\n\r\n            {/* File Attachments */}\r\n            <div>\r\n              <label htmlFor=\"breach-attachments\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n                Supporting Evidence (Optional)\r\n              </label>\r\n              <input\r\n                id=\"breach-attachments\"\r\n                type=\"file\"\r\n                multiple\r\n                accept=\".pdf,.doc,.docx,.jpg,.jpeg,.png\"\r\n                onChange={handleFileChange}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n              />\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                Screenshots, emails, documents, or other evidence (Max 5MB per file)\r\n              </p>\r\n\r\n              {/* Show selected files */}\r\n              {attachments.length > 0 && (\r\n                <div className=\"mt-3 space-y-2\">\r\n                  {attachments.map((file, index) => (\r\n                    <div key={index} className=\"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded\">\r\n                      <div className=\"flex items-center\">\r\n                        <i className=\"ri-file-line text-gray-400 mr-2\"></i>\r\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">{file.name}</span>\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400 ml-2\">\r\n                          ({formatFileSize(file.size)})\r\n                        </span>\r\n                      </div>\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => removeAttachment(index)}\r\n                        className=\"text-red-500 hover:text-red-700\"\r\n                        aria-label={`Remove ${file.name}`}\r\n                      >\r\n                        <i className=\"ri-close-line\"></i>\r\n                      </button>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Submit Buttons */}\r\n            <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n              >\r\n                Cancel\r\n              </button>\r\n              <button\r\n                type=\"submit\"\r\n                disabled={isSubmitting}\r\n                className=\"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300\"\r\n              >\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                    Submitting...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <i className=\"ri-shield-keyhole-line mr-2\"></i>\r\n                    Submit Report\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DataBreachModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAcA,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB;QAAE,OAAO;QAAO,OAAO;IAA8B;IACrD;QAAE,OAAO;QAAU,OAAO;IAAkC;IAC5D;QAAE,OAAO;QAAQ,OAAO;IAAiC;IACzD;QAAE,OAAO;QAAY,OAAO;IAAsC;CACnE;AAED,MAAM,kBAAkD;QAAC,EAAE,OAAO,EAAE,QAAQ,EAAE;;IAC5E,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;QACV,cAAc;QACd,cAAc;QACd,cAAc;QACd,iBAAiB;IACnB;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAEzD,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QAED,sCAAsC;QACtC,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;YAC1C,eAAe,CAAA,OAAQ;uBAAI;uBAAS;iBAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACrD;IAEA,MAAM,eAAe;QACnB,MAAM,SAAkC,CAAC;QAEzC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,OAAO,KAAK,GAAG;QACjB;QAEA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YAChC,OAAO,WAAW,GAAG;QACvB,OAAO,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;YAClD,OAAO,WAAW,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,cAAc;QACd,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,aAAyC;gBAC7C,OAAO,SAAS,KAAK;gBACrB,aAAa,SAAS,WAAW;gBACjC,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,QAAQ;gBAC3B,eAAe,SAAS,YAAY;gBACpC,uBAAuB,SAAS,YAAY;gBAC5C,qBAAqB,SAAS,YAAY;gBAC1C,kBAAkB,SAAS,eAAe;gBAC1C,aAAa;YACf;YAEA,MAAM,WAAW,MAAM,yJAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC;YAEtD,uBAAuB;YACvB,YACE,AAAC,0EAAqG,OAA5B,SAAS,SAAS,IAAI,QAChG;YAGF,SAAS;YAET,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,iBAAiB;YACnB;YACA,eAAe,EAAE;QAEnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,cAAW;4BACX,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;;;;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA0D;;;;;;8DAGxE,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9D,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC,2IAAA,CAAA,UAAS;oCACR,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU;oCACV,aAAY;oCACZ,OAAO,WAAW,KAAK;oCACvB,QAAQ;;;;;;8CAGV,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,wIAAA,CAAA,UAAM;4CACL,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,OAAO,WAAW,QAAQ;4CAC1B,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,iBAAiB,GAAG,CAAC,CAAA,yBACpB,6LAAC;wDAAsB,OAAO;kEAAW;uDAA5B;;;;;;;;;;;sDAKjB,6LAAC,wIAAA,CAAA,UAAM;4CACL,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,OAAO,WAAW,QAAQ;4CAC1B,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,eAAe,GAAG,CAAC,CAAA,sBAClB,6LAAC;wDAAyB,OAAO,MAAM,KAAK;kEAAG,MAAM,KAAK;uDAA7C,MAAM,KAAK;;;;;;;;;;;;;;;;;8CAK9B,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,2IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,YAAY;4CAC5B,UAAU;4CACV,OAAO,WAAW,YAAY;4CAC9B,QAAQ;;;;;;sDAIV,6LAAC,2IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,YAAY;4CAC5B,UAAU;4CACV,aAAY;4CACZ,OAAO,WAAW,YAAY;4CAC9B,QAAQ;;;;;;;;;;;;8CAKZ,6LAAC,0IAAA,CAAA,UAAQ;oCACP,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,YAAY;oCAC5B,UAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,OAAO,WAAW,YAAY;;;;;;8CAIhC,6LAAC,0IAAA,CAAA,UAAQ;oCACP,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,WAAW;oCAC3B,UAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,OAAO,WAAW,WAAW;oCAC7B,YAAW;oCACX,QAAQ;;;;;;8CAIV,6LAAC,0IAAA,CAAA,UAAQ;oCACP,OAAM;oCACN,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,eAAe;oCAC/B,UAAU;oCACV,MAAM;oCACN,aAAY;oCACZ,OAAO,WAAW,eAAe;;;;;;8CAInC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAqB,WAAU;sDAAkE;;;;;;sDAGhH,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,QAAO;4CACP,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;wCAK5D,YAAY,MAAM,GAAG,mBACpB,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;;;;;8EACb,6LAAC;oEAAK,WAAU;8EAA4C,KAAK,IAAI;;;;;;8EACrE,6LAAC;oEAAK,WAAU;;wEAAgD;wEAC5D,eAAe,KAAK,IAAI;wEAAE;;;;;;;;;;;;;sEAGhC,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,iBAAiB;4DAChC,WAAU;4DACV,cAAY,AAAC,UAAmB,OAAV,KAAK,IAAI;sEAE/B,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;mDAdP;;;;;;;;;;;;;;;;8CAuBlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,6BACC;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAAyC;;6EAIxD;;kEACE,6LAAC;wDAAE,WAAU;;;;;;oDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnE;GAnWM;;QAC+B,mIAAA,CAAA,WAAQ;;;KADvC;uCAqWS", "debugId": null}}, {"offset": {"line": 4162, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/ComplaintStatusBar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface ComplaintStage {\r\n  id: string;\r\n  name: string;\r\n  description?: string;\r\n  icon?: string;\r\n}\r\n\r\ninterface ComplaintStatusBarProps {\r\n  currentStage: number; // 0-based index\r\n  stages: ComplaintStage[];\r\n  status?: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';\r\n  progressPercentage?: number;\r\n  showPercentage?: boolean;\r\n  showStageNames?: boolean;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  variant?: 'horizontal' | 'vertical';\r\n  className?: string;\r\n}\r\n\r\nconst ComplaintStatusBar: React.FC<ComplaintStatusBarProps> = ({\r\n  currentStage,\r\n  stages,\r\n  status = 'submitted',\r\n  progressPercentage,\r\n  showPercentage = true,\r\n  showStageNames = true,\r\n  size = 'md',\r\n  variant = 'horizontal',\r\n  className = ''\r\n}) => {\r\n  // Calculate progress percentage if not provided\r\n  const calculatedProgress = progressPercentage ?? Math.round(((currentStage + 1) / stages.length) * 100);\r\n  \r\n  // Get status color\r\n  const getStatusColor = () => {\r\n    switch (status) {\r\n      case 'submitted': return 'bg-blue-500';\r\n      case 'under_review': return 'bg-yellow-500';\r\n      case 'investigating': return 'bg-orange-500';\r\n      case 'resolved': return 'bg-green-500';\r\n      case 'closed': return 'bg-gray-500';\r\n      default: return 'bg-gray-400';\r\n    }\r\n  };\r\n\r\n  // Get status text color\r\n  const getStatusTextColor = () => {\r\n    switch (status) {\r\n      case 'submitted': return 'text-blue-600';\r\n      case 'under_review': return 'text-yellow-600';\r\n      case 'investigating': return 'text-orange-600';\r\n      case 'resolved': return 'text-green-600';\r\n      case 'closed': return 'text-gray-600';\r\n      default: return 'text-gray-600';\r\n    }\r\n  };\r\n\r\n  // Size configurations\r\n  const sizeClasses = {\r\n    sm: {\r\n      stage: 'w-6 h-6 text-xs',\r\n      bar: 'h-1',\r\n      text: 'text-xs'\r\n    },\r\n    md: {\r\n      stage: 'w-8 h-8 text-sm',\r\n      bar: 'h-2',\r\n      text: 'text-sm'\r\n    },\r\n    lg: {\r\n      stage: 'w-10 h-10 text-base',\r\n      bar: 'h-3',\r\n      text: 'text-base'\r\n    }\r\n  };\r\n\r\n  // Vertical variant\r\n  if (variant === 'vertical') {\r\n    return (\r\n      <div className={`space-y-4 ${className}`}>\r\n        {stages.map((stage, index) => {\r\n          const isCompleted = index < currentStage;\r\n          const isCurrent = index === currentStage;\r\n\r\n          return (\r\n            <div key={stage.id} className=\"flex items-center space-x-3\">\r\n              {/* Stage Circle */}\r\n              <div className={`\r\n                ${sizeClasses[size].stage} rounded-full flex items-center justify-center font-medium\r\n                ${isCompleted ? `${getStatusColor()} text-white` : \r\n                  isCurrent ? `border-2 border-current ${getStatusTextColor()} bg-white` :\r\n                  'bg-gray-200 text-gray-400'}\r\n              `}>\r\n                {isCompleted ? (\r\n                  <i className=\"ri-check-line\"></i>\r\n                ) : (\r\n                  <span>{index + 1}</span>\r\n                )}\r\n              </div>\r\n\r\n              {/* Stage Info */}\r\n              <div className=\"flex-1\">\r\n                <div className={`font-medium ${isCurrent ? getStatusTextColor() : isCompleted ? 'text-gray-900' : 'text-gray-400'}`}>\r\n                  {stage.name}\r\n                </div>\r\n                {stage.description && (\r\n                  <div className={`${sizeClasses[size].text} ${isCurrent ? 'text-gray-600' : 'text-gray-400'}`}>\r\n                    {stage.description}\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Progress Indicator */}\r\n              {isCurrent && showPercentage && (\r\n                <div className={`${sizeClasses[size].text} font-medium ${getStatusTextColor()}`}>\r\n                  {calculatedProgress}%\r\n                </div>\r\n              )}\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Horizontal variant\r\n  return (\r\n    <div className={`w-full ${className}`}>\r\n      {/* Progress Bar */}\r\n      <div className=\"relative\">\r\n        <div className={`w-full ${sizeClasses[size].bar} bg-gray-200 rounded-full overflow-hidden`}>\r\n          <div \r\n            className={`${sizeClasses[size].bar} ${getStatusColor()} transition-all duration-500 ease-out rounded-full`}\r\n            style={{ width: `${calculatedProgress}%` }}\r\n          />\r\n        </div>\r\n\r\n        {/* Stage Markers */}\r\n        <div className=\"absolute top-0 left-0 w-full flex justify-between items-center\" style={{ transform: 'translateY(-50%)' }}>\r\n          {stages.map((stage, index) => {\r\n            const isCompleted = index < currentStage;\r\n            const isCurrent = index === currentStage;\r\n            const position = (index / (stages.length - 1)) * 100;\r\n\r\n            return (\r\n              <div \r\n                key={stage.id}\r\n                className=\"flex flex-col items-center\"\r\n                style={{ position: 'absolute', left: `${position}%`, transform: 'translateX(-50%)' }}\r\n              >\r\n                {/* Stage Circle */}\r\n                <div className={`\r\n                  ${sizeClasses[size].stage} rounded-full flex items-center justify-center font-medium border-2 bg-white\r\n                  ${isCompleted ? `${getStatusColor().replace('bg-', 'border-')} ${getStatusColor()} text-white` : \r\n                    isCurrent ? `border-current ${getStatusTextColor()}` :\r\n                    'border-gray-300 text-gray-400'}\r\n                `}>\r\n                  {isCompleted ? (\r\n                    <i className=\"ri-check-line\"></i>\r\n                  ) : stage.icon ? (\r\n                    <i className={stage.icon}></i>\r\n                  ) : (\r\n                    <span>{index + 1}</span>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Stage Name */}\r\n                {showStageNames && (\r\n                  <div className={`mt-2 ${sizeClasses[size].text} font-medium text-center max-w-20 ${\r\n                    isCurrent ? getStatusTextColor() : \r\n                    isCompleted ? 'text-gray-900' : \r\n                    'text-gray-400'\r\n                  }`}>\r\n                    {stage.name}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Progress Percentage */}\r\n      {showPercentage && (\r\n        <div className=\"flex justify-between items-center mt-8\">\r\n          <div className={`${sizeClasses[size].text} ${getStatusTextColor()} font-medium`}>\r\n            Progress: {calculatedProgress}%\r\n          </div>\r\n          <div className={`${sizeClasses[size].text} text-gray-500 capitalize`}>\r\n            Status: {status.replace('_', ' ')}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Current Stage Description */}\r\n      {showStageNames && stages[currentStage]?.description && (\r\n        <div className=\"mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\r\n          <div className={`${sizeClasses[size].text} text-gray-600 dark:text-gray-400`}>\r\n            <strong>Current Stage:</strong> {stages[currentStage].description}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ComplaintStatusBar;\r\n\r\n// Predefined stage configurations for different complaint types\r\nexport const COMPLAINT_STAGES = {\r\n  CONSUMER_AFFAIRS: [\r\n    { id: 'submitted', name: 'Submitted', description: 'Complaint has been received and logged', icon: 'ri-file-text-line' },\r\n    { id: 'under_review', name: 'Under Review', description: 'Initial review and assessment in progress', icon: 'ri-search-line' },\r\n    { id: 'investigating', name: 'Investigating', description: 'Detailed investigation and fact-finding', icon: 'ri-spy-line' },\r\n    { id: 'resolved', name: 'Resolved', description: 'Issue has been resolved and action taken', icon: 'ri-check-double-line' }\r\n  ],\r\n  DATA_BREACH: [\r\n    { id: 'submitted', name: 'Reported', description: 'Data breach report has been received', icon: 'ri-shield-line' },\r\n    { id: 'under_review', name: 'Assessment', description: 'Assessing severity and impact', icon: 'ri-search-line' },\r\n    { id: 'investigating', name: 'Investigation', description: 'Investigating the breach and gathering evidence', icon: 'ri-spy-line' },\r\n    { id: 'resolved', name: 'Resolved', description: 'Breach contained and remediation completed', icon: 'ri-shield-check-line' }\r\n  ]\r\n};\r\n\r\n// Helper function to get current stage index from status\r\nexport const getStageIndexFromStatus = (status: string): number => {\r\n  const statusMap: { [key: string]: number } = {\r\n    'submitted': 0,\r\n    'under_review': 1,\r\n    'investigating': 2,\r\n    'resolved': 3,\r\n    'closed': 3\r\n  };\r\n  return statusMap[status] || 0;\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAuBA,MAAM,qBAAwD;QAAC,EAC7D,YAAY,EACZ,MAAM,EACN,SAAS,WAAW,EACpB,kBAAkB,EAClB,iBAAiB,IAAI,EACrB,iBAAiB,IAAI,EACrB,OAAO,IAAI,EACX,UAAU,YAAY,EACtB,YAAY,EAAE,EACf;QAsKwB;IArKvB,gDAAgD;IAChD,MAAM,qBAAqB,+BAAA,gCAAA,qBAAsB,KAAK,KAAK,CAAC,AAAC,CAAC,eAAe,CAAC,IAAI,OAAO,MAAM,GAAI;IAEnG,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,wBAAwB;IACxB,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAc;QAClB,IAAI;YACF,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,IAAI;YACF,OAAO;YACP,KAAK;YACL,MAAM;QACR;QACA,IAAI;YACF,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,IAAI,YAAY,YAAY;QAC1B,qBACE,6LAAC;YAAI,WAAW,AAAC,aAAsB,OAAV;sBAC1B,OAAO,GAAG,CAAC,CAAC,OAAO;gBAClB,MAAM,cAAc,QAAQ;gBAC5B,MAAM,YAAY,UAAU;gBAE5B,qBACE,6LAAC;oBAAmB,WAAU;;sCAE5B,6LAAC;4BAAI,WAAW,AAAC,qBAEb,OADA,WAAW,CAAC,KAAK,CAAC,KAAK,EAAC,gFAGI,OAF5B,cAAc,AAAC,GAAmB,OAAjB,kBAAiB,iBAClC,YAAY,AAAC,2BAA+C,OAArB,sBAAqB,eAC5D,6BAA4B;sCAE7B,4BACC,6LAAC;gCAAE,WAAU;;;;;yFAEb,6LAAC;0CAAM,QAAQ;;;;;;;;;;;sCAKnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,AAAC,eAAiG,OAAnF,YAAY,uBAAuB,cAAc,kBAAkB;8CAC/F,MAAM,IAAI;;;;;;gCAEZ,MAAM,WAAW,kBAChB,6LAAC;oCAAI,WAAW,AAAC,GAA4B,OAA1B,WAAW,CAAC,KAAK,CAAC,IAAI,EAAC,KAAiD,OAA9C,YAAY,kBAAkB;8CACxE,MAAM,WAAW;;;;;;;;;;;;wBAMvB,aAAa,gCACZ,6LAAC;4BAAI,WAAW,AAAC,GAAwC,OAAtC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAC,iBAAoC,OAArB;;gCACtD;gCAAmB;;;;;;;;mBA9BhB,MAAM,EAAE;;;;;YAmCtB;;;;;;IAGN;IAEA,qBAAqB;IACrB,qBACE,6LAAC;QAAI,WAAW,AAAC,UAAmB,OAAV;;0BAExB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,UAA+B,OAAtB,WAAW,CAAC,KAAK,CAAC,GAAG,EAAC;kCAC9C,cAAA,6LAAC;4BACC,WAAW,AAAC,GAA2B,OAAzB,WAAW,CAAC,KAAK,CAAC,GAAG,EAAC,KAAoB,OAAjB,kBAAiB;4BACxD,OAAO;gCAAE,OAAO,AAAC,GAAqB,OAAnB,oBAAmB;4BAAG;;;;;;;;;;;kCAK7C,6LAAC;wBAAI,WAAU;wBAAiE,OAAO;4BAAE,WAAW;wBAAmB;kCACpH,OAAO,GAAG,CAAC,CAAC,OAAO;4BAClB,MAAM,cAAc,QAAQ;4BAC5B,MAAM,YAAY,UAAU;4BAC5B,MAAM,WAAW,AAAC,QAAQ,CAAC,OAAO,MAAM,GAAG,CAAC,IAAK;4BAEjD,qBACE,6LAAC;gCAEC,WAAU;gCACV,OAAO;oCAAE,UAAU;oCAAY,MAAM,AAAC,GAAW,OAAT,UAAS;oCAAI,WAAW;gCAAmB;;kDAGnF,6LAAC;wCAAI,WAAW,AAAC,uBAEb,OADA,WAAW,CAAC,KAAK,CAAC,KAAK,EAAC,oGAGQ,OAFhC,cAAc,AAAC,GAAgD,OAA9C,iBAAiB,OAAO,CAAC,OAAO,YAAW,KAAoB,OAAjB,kBAAiB,iBAChF,YAAY,AAAC,kBAAsC,OAArB,wBAC9B,iCAAgC;kDAEjC,4BACC,6LAAC;4CAAE,WAAU;;;;;uFACX,MAAM,IAAI,iBACZ,6LAAC;4CAAE,WAAW,MAAM,IAAI;;;;;qGAExB,6LAAC;sDAAM,QAAQ;;;;;;;;;;;oCAKlB,gCACC,6LAAC;wCAAI,WAAW,AAAC,QACf,OADsB,WAAW,CAAC,KAAK,CAAC,IAAI,EAAC,sCAI9C,OAHC,YAAY,uBACZ,cAAc,kBACd;kDAEC,MAAM,IAAI;;;;;;;+BA3BV,MAAM,EAAE;;;;;wBAgCnB;;;;;;;;;;;;YAKH,gCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,AAAC,GAA4B,OAA1B,WAAW,CAAC,KAAK,CAAC,IAAI,EAAC,KAAwB,OAArB,sBAAqB;;4BAAe;4BACpE;4BAAmB;;;;;;;kCAEhC,6LAAC;wBAAI,WAAW,AAAC,GAAyB,OAAvB,WAAW,CAAC,KAAK,CAAC,IAAI,EAAC;;4BAA4B;4BAC3D,OAAO,OAAO,CAAC,KAAK;;;;;;;;;;;;;YAMlC,oBAAkB,uBAAA,MAAM,CAAC,aAAa,cAApB,2CAAA,qBAAsB,WAAW,mBAClD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,AAAC,GAAyB,OAAvB,WAAW,CAAC,KAAK,CAAC,IAAI,EAAC;;sCACxC,6LAAC;sCAAO;;;;;;wBAAuB;wBAAE,MAAM,CAAC,aAAa,CAAC,WAAW;;;;;;;;;;;;;;;;;;AAM7E;KAzLM;uCA2LS;AAGR,MAAM,mBAAmB;IAC9B,kBAAkB;QAChB;YAAE,IAAI;YAAa,MAAM;YAAa,aAAa;YAA0C,MAAM;QAAoB;QACvH;YAAE,IAAI;YAAgB,MAAM;YAAgB,aAAa;YAA6C,MAAM;QAAiB;QAC7H;YAAE,IAAI;YAAiB,MAAM;YAAiB,aAAa;YAA2C,MAAM;QAAc;QAC1H;YAAE,IAAI;YAAY,MAAM;YAAY,aAAa;YAA4C,MAAM;QAAuB;KAC3H;IACD,aAAa;QACX;YAAE,IAAI;YAAa,MAAM;YAAY,aAAa;YAAwC,MAAM;QAAiB;QACjH;YAAE,IAAI;YAAgB,MAAM;YAAc,aAAa;YAAiC,MAAM;QAAiB;QAC/G;YAAE,IAAI;YAAiB,MAAM;YAAiB,aAAa;YAAmD,MAAM;QAAc;QAClI;YAAE,IAAI;YAAY,MAAM;YAAY,aAAa;YAA8C,MAAM;QAAuB;KAC7H;AACH;AAGO,MAAM,0BAA0B,CAAC;IACtC,MAAM,YAAuC;QAC3C,aAAa;QACb,gBAAgB;QAChB,iBAAiB;QACjB,YAAY;QACZ,UAAU;IACZ;IACA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B", "debugId": null}}, {"offset": {"line": 4540, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/data-protection/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport Loader from '@/components/Loader';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport ConsumerAffairsModal from '@/components/customer/ConsumerAffairsModal';\r\nimport DataBreachModal from '@/components/customer/DataBreachModal';\r\nimport ComplaintStatusBar, { COMPLAINT_STAGES, getStageIndexFromStatus } from '@/components/customer/ComplaintStatusBar';\r\nimport { consumerAffairsService, ConsumerAffairsComplaint } from '@/services/consumer-affairs';\r\nimport { dataBreachService, DataBreachReport } from '@/services/data-breach';\r\n\r\ninterface CombinedComplaint {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  type: 'consumer_affairs' | 'data_breach';\r\n  priority: 'low' | 'medium' | 'high' | 'urgent';\r\n  status: 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed';\r\n  submittedAt: string;\r\n  updatedAt: string;\r\n  assignedTo?: string;\r\n  resolution?: string;\r\n  number?: string;\r\n}\r\n\r\nconst DataProtectionPage = () => {\r\n  const { isAuthenticated, loading: authLoading } = useAuth();\r\n  const router = useRouter();\r\n\r\n  const [complaints, setComplaints] = useState<CombinedComplaint[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [activeTab, setActiveTab] = useState<'overview' | 'track'>('overview');\r\n  const [showConsumerAffairsModal, setShowConsumerAffairsModal] = useState(false);\r\n  const [showDataBreachModal, setShowDataBreachModal] = useState(false);\r\n\r\n  // Redirect to customer login if not authenticated\r\n  useEffect(() => {\r\n    if (!authLoading && !isAuthenticated) {\r\n      router.push('/customer/auth/login');\r\n    }\r\n  }, [isAuthenticated, authLoading, router]);\r\n\r\n  // Fetch data function\r\n  const fetchData = useCallback(async () => {\r\n    if (!isAuthenticated) {\r\n      console.log('❌ User not authenticated, skipping data fetch');\r\n      return;\r\n    }\r\n\r\n    console.log('✅ User authenticated, fetching data...');\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      setError('');\r\n\r\n      // Fetch both consumer affairs complaints and data breach reports\r\n      const [consumerAffairsResponse, dataBreachResponse] = await Promise.all([\r\n        consumerAffairsService.getComplaints({ limit: 100 }),\r\n        dataBreachService.getReports({ limit: 100 })\r\n      ]);\r\n\r\n        console.log('🔍 Consumer Affairs Response:', consumerAffairsResponse);\r\n        console.log('🔍 Consumer Affairs Response.data type:', typeof consumerAffairsResponse.data);\r\n        console.log('🔍 Consumer Affairs Response.data:', consumerAffairsResponse.data);\r\n        console.log('🔍 Data Breach Response:', dataBreachResponse);\r\n        console.log('🔍 Data Breach Response.data type:', typeof dataBreachResponse.data);\r\n        console.log('🔍 Data Breach Response.data:', dataBreachResponse.data);\r\n\r\n        // Ensure data is an array (services return data directly)\r\n        const consumerAffairsData = Array.isArray(consumerAffairsResponse.data)\r\n          ? consumerAffairsResponse.data\r\n          : [];\r\n        const dataBreachData = Array.isArray(dataBreachResponse.data)\r\n          ? dataBreachResponse.data\r\n          : [];\r\n\r\n        console.log('🔍 Consumer Affairs Data Array:', consumerAffairsData);\r\n        console.log('🔍 Data Breach Data Array:', dataBreachData);\r\n\r\n        // Combine and transform the data\r\n        const combinedComplaints: CombinedComplaint[] = [\r\n          ...consumerAffairsData.map((complaint: ConsumerAffairsComplaint) => ({\r\n            id: complaint.complaint_id,\r\n            title: complaint.title,\r\n            description: complaint.description,\r\n            category: complaint.category,\r\n            type: 'consumer_affairs' as const,\r\n            priority: complaint.priority,\r\n            status: complaint.status,\r\n            submittedAt: complaint.created_at,\r\n            updatedAt: complaint.updated_at,\r\n            assignedTo: complaint.assignee?.first_name && complaint.assignee?.last_name\r\n              ? `${complaint.assignee.first_name} ${complaint.assignee.last_name}`\r\n              : undefined,\r\n            resolution: complaint.resolution,\r\n            number: complaint.complaint_number\r\n          })),\r\n          ...dataBreachData.map((report: DataBreachReport) => ({\r\n            id: report.report_id,\r\n            title: report.title,\r\n            description: report.description,\r\n            category: report.category,\r\n            type: 'data_breach' as const,\r\n            priority: report.priority,\r\n            status: report.status,\r\n            submittedAt: report.created_at,\r\n            updatedAt: report.updated_at,\r\n            assignedTo: report.assignee?.first_name && report.assignee?.last_name\r\n              ? `${report.assignee.first_name} ${report.assignee.last_name}`\r\n              : undefined,\r\n            resolution: report.resolution,\r\n            number: report.report_number\r\n          }))\r\n        ];\r\n\r\n        // Sort by creation date (newest first)\r\n        combinedComplaints.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime());\r\n\r\n        setComplaints(combinedComplaints);\r\n\r\n      } catch (err: unknown) {\r\n        console.error('Error fetching complaints:', err);\r\n\r\n        const errorMessage = err instanceof Error ? err.message : 'Unknown error';\r\n        const isAxiosError = err && typeof err === 'object' && 'response' in err;\r\n        const axiosError = isAxiosError ? err as { response?: { status?: number; data?: unknown } } : null;\r\n        const status = axiosError?.response?.status;\r\n\r\n        console.error('Error details:', {\r\n          message: errorMessage,\r\n          response: axiosError?.response?.data,\r\n          status: status\r\n        });\r\n\r\n        if (status === 401) {\r\n          setError('Authentication required. Please log in again.');\r\n        } else if (status === 404) {\r\n          setError('API endpoints not found. Please check if the backend is running.');\r\n        } else {\r\n          setError(`Failed to load complaints: ${errorMessage}`);\r\n        }\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    }, [isAuthenticated]);\r\n\r\n  // Fetch data on mount and when authentication changes\r\n  useEffect(() => {\r\n    fetchData();\r\n  }, [isAuthenticated, fetchData]);\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800';\r\n      case 'resolved': return 'bg-green-100 text-green-800';\r\n      case 'closed': return 'bg-gray-100 text-gray-800';\r\n      default: return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority) {\r\n      case 'low': return 'bg-gray-100 text-gray-800';\r\n      case 'medium': return 'bg-blue-100 text-blue-800';\r\n      case 'high': return 'bg-orange-100 text-orange-800';\r\n      case 'urgent': return 'bg-red-100 text-red-800';\r\n      default: return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit'\r\n    });\r\n  };\r\n\r\n  const consumerAffairsComplaints = complaints.filter(c => c.type === 'consumer_affairs');\r\n  const dataBreachComplaints = complaints.filter(c => c.type === 'data_breach');\r\n\r\n  if (authLoading || isLoading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <Loader message=\"Loading Data Protection...\" />\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\">\r\n          <p>{error}</p>\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => window.location.reload()}\r\n            className=\"mt-2 text-sm underline hover:no-underline\"\r\n          >\r\n            Try again\r\n          </button>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Header */}\r\n        <div className=\"mb-8\">\r\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\r\n            Data Protection\r\n          </h1>\r\n          <p className=\"text-gray-600 dark:text-gray-400\">\r\n            Submit and track consumer affairs complaints and data breach reports\r\n          </p>\r\n        </div>\r\n\r\n        {/* Tabs */}\r\n        <div className=\"border-b border-gray-200 dark:border-gray-700 mb-6\">\r\n          <nav className=\"-mb-px flex space-x-8\">\r\n            {[\r\n              { key: 'overview', label: 'Overview', icon: 'ri-dashboard-line' },\r\n              { key: 'track', label: 'Track Complaints', icon: 'ri-search-eye-line', count: complaints.length }\r\n            ].map((tab) => (\r\n              <button\r\n                type=\"button\"\r\n                key={tab.key}\r\n                onClick={() => setActiveTab(tab.key as 'overview' | 'track')}\r\n                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ${\r\n                  activeTab === tab.key\r\n                    ? 'border-primary text-primary'\r\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\r\n                }`}\r\n              >\r\n                <i className={`${tab.icon} mr-2`}></i>\r\n                {tab.label}\r\n                {tab.count !== undefined && (\r\n                  <span className=\"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\">\r\n                    {tab.count}\r\n                  </span>\r\n                )}\r\n              </button>\r\n            ))}\r\n          </nav>\r\n        </div>\r\n\r\n        {/* Overview Tab */}\r\n        {activeTab === 'overview' && (\r\n          <div className=\"space-y-8\">\r\n            {/* Statistics Cards */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className=\"ri-shield-user-line text-2xl text-blue-600\"></i>\r\n                  </div>\r\n                  <div className=\"ml-4\">\r\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Consumer Affairs</p>\r\n                    <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{consumerAffairsComplaints.length}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className=\"ri-shield-keyhole-line text-2xl text-red-600\"></i>\r\n                  </div>\r\n                  <div className=\"ml-4\">\r\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Data Breaches</p>\r\n                    <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{dataBreachComplaints.length}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className=\"ri-file-list-3-line text-2xl text-green-600\"></i>\r\n                  </div>\r\n                  <div className=\"ml-4\">\r\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Complaints</p>\r\n                    <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{complaints.length}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Action Cards */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              {/* Consumer Affairs Card */}\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n                <div className=\"flex items-center mb-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className=\"ri-shield-user-line text-3xl text-blue-600\"></i>\r\n                  </div>\r\n                  <div className=\"ml-4\">\r\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Consumer Affairs</h3>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                      Report issues with telecommunications services, billing, or customer service\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"space-y-3\">\r\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    <p>• Billing disputes and overcharges</p>\r\n                    <p>• Service quality issues</p>\r\n                    <p>• Network connectivity problems</p>\r\n                    <p>• Customer service complaints</p>\r\n                  </div>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => setShowConsumerAffairsModal(true)}\r\n                    className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-300\"\r\n                  >\r\n                    <i className=\"ri-file-add-line mr-2\"></i>\r\n                    Lodge Consumer Affairs Complaint\r\n                  </button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Data Breach Card */}\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n                <div className=\"flex items-center mb-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className=\"ri-shield-keyhole-line text-3xl text-red-600\"></i>\r\n                  </div>\r\n                  <div className=\"ml-4\">\r\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Data Breach Report</h3>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                      Report unauthorized access, misuse, or breach of your personal data\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"space-y-3\">\r\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    <p>• Unauthorized data access</p>\r\n                    <p>• Data misuse or sharing</p>\r\n                    <p>• Privacy violations</p>\r\n                    <p>• Identity theft concerns</p>\r\n                  </div>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => setShowDataBreachModal(true)}\r\n                    className=\"w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300\"\r\n                  >\r\n                    <i className=\"ri-shield-keyhole-line mr-2\"></i>\r\n                    Report Data Breach\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Track Complaints Tab */}\r\n        {activeTab === 'track' && (\r\n          <div>\r\n            {complaints.length === 0 ? (\r\n              <div className=\"text-center py-12\">\r\n                <i className=\"ri-file-search-line text-4xl text-gray-400 mb-4\"></i>\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No complaints found</h3>\r\n                <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\r\n                  You haven&apos;t submitted any complaints yet.\r\n                </p>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setActiveTab('overview')}\r\n                  className=\"px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300\"\r\n                >\r\n                  Submit Your First Complaint\r\n                </button>\r\n              </div>\r\n            ) : (\r\n              <div className=\"space-y-6\">\r\n                {complaints.map((complaint) => (\r\n                  <div key={complaint.id} className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n                    <div className=\"flex justify-between items-start mb-4\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"flex items-center mb-2\">\r\n                          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mr-3\">\r\n                            {complaint.title}\r\n                          </h3>\r\n                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(complaint.status)}`}>\r\n                            {complaint.status.replace('_', ' ').toUpperCase()}\r\n                          </span>\r\n                          <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(complaint.priority)}`}>\r\n                            {complaint.priority.toUpperCase()}\r\n                          </span>\r\n                          <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${\r\n                            complaint.type === 'consumer_affairs' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'\r\n                          }`}>\r\n                            {complaint.type === 'consumer_affairs' ? 'CONSUMER AFFAIRS' : 'DATA BREACH'}\r\n                          </span>\r\n                        </div>\r\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\r\n                          ID: {complaint.id} | Category: {complaint.category}\r\n                        </p>\r\n                        <p className=\"text-sm text-gray-700 dark:text-gray-300 mb-3\">\r\n                          {complaint.description.length > 150\r\n                            ? `${complaint.description.substring(0, 150)}...`\r\n                            : complaint.description}\r\n                        </p>\r\n\r\n                        {/* Complaint Status Bar */}\r\n                        <div className=\"mb-4\">\r\n                          <ComplaintStatusBar\r\n                            currentStage={getStageIndexFromStatus(complaint.status)}\r\n                            stages={complaint.type === 'consumer_affairs' ? COMPLAINT_STAGES.CONSUMER_AFFAIRS : COMPLAINT_STAGES.DATA_BREACH}\r\n                            status={complaint.status as 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed'}\r\n                            size=\"sm\"\r\n                            variant=\"horizontal\"\r\n                            showPercentage={false}\r\n                            showStageNames={true}\r\n                            className=\"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\"\r\n                          />\r\n                        </div>\r\n\r\n                        <div className=\"flex items-center text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span>Submitted: {formatDate(complaint.submittedAt)}</span>\r\n                          <span className=\"mx-2\">•</span>\r\n                          <span>Updated: {formatDate(complaint.updatedAt)}</span>\r\n                          {complaint.assignedTo && (\r\n                            <>\r\n                              <span className=\"mx-2\">•</span>\r\n                              <span>Assigned to: {complaint.assignedTo}</span>\r\n                            </>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <button\r\n                        type=\"button\"\r\n                        className=\"ml-4 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600\"\r\n                      >\r\n                        View Details\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Modals */}\r\n        {showConsumerAffairsModal && (\r\n          <ConsumerAffairsModal\r\n            onClose={() => setShowConsumerAffairsModal(false)}\r\n            onSubmit={(data) => {\r\n              console.log('Consumer Affairs complaint submitted:', data);\r\n              setShowConsumerAffairsModal(false);\r\n              // Refresh complaints list without full page reload\r\n              fetchData();\r\n            }}\r\n          />\r\n        )}\r\n\r\n        {showDataBreachModal && (\r\n          <DataBreachModal\r\n            onClose={() => setShowDataBreachModal(false)}\r\n            onSubmit={(data) => {\r\n              console.log('Data breach report submitted:', data);\r\n              setShowDataBreachModal(false);\r\n              // Refresh complaints list without full page reload\r\n              fetchData();\r\n            }}\r\n          />\r\n        )}\r\n      </div>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default DataProtectionPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;;;AAXA;;;;;;;;;;;AA4BA,MAAM,qBAAqB;;IACzB,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,OAAO,IAAI,CAAC;YACd;QACF;uCAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,sBAAsB;IACtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC5B,IAAI,CAAC,iBAAiB;gBACpB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC;YAEZ,IAAI;gBACF,aAAa;gBACb,SAAS;gBAET,iEAAiE;gBACjE,MAAM,CAAC,yBAAyB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACtE,mKAAA,CAAA,yBAAsB,CAAC,aAAa,CAAC;wBAAE,OAAO;oBAAI;oBAClD,yJAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC;wBAAE,OAAO;oBAAI;iBAC3C;gBAEC,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,QAAQ,GAAG,CAAC,2CAA2C,OAAO,wBAAwB,IAAI;gBAC1F,QAAQ,GAAG,CAAC,sCAAsC,wBAAwB,IAAI;gBAC9E,QAAQ,GAAG,CAAC,4BAA4B;gBACxC,QAAQ,GAAG,CAAC,sCAAsC,OAAO,mBAAmB,IAAI;gBAChF,QAAQ,GAAG,CAAC,iCAAiC,mBAAmB,IAAI;gBAEpE,0DAA0D;gBAC1D,MAAM,sBAAsB,MAAM,OAAO,CAAC,wBAAwB,IAAI,IAClE,wBAAwB,IAAI,GAC5B,EAAE;gBACN,MAAM,iBAAiB,MAAM,OAAO,CAAC,mBAAmB,IAAI,IACxD,mBAAmB,IAAI,GACvB,EAAE;gBAEN,QAAQ,GAAG,CAAC,mCAAmC;gBAC/C,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,iCAAiC;gBACjC,MAAM,qBAA0C;uBAC3C,oBAAoB,GAAG;qEAAC,CAAC;gCAUd,qBAAkC;mCAVqB;gCACnE,IAAI,UAAU,YAAY;gCAC1B,OAAO,UAAU,KAAK;gCACtB,aAAa,UAAU,WAAW;gCAClC,UAAU,UAAU,QAAQ;gCAC5B,MAAM;gCACN,UAAU,UAAU,QAAQ;gCAC5B,QAAQ,UAAU,MAAM;gCACxB,aAAa,UAAU,UAAU;gCACjC,WAAW,UAAU,UAAU;gCAC/B,YAAY,EAAA,sBAAA,UAAU,QAAQ,cAAlB,0CAAA,oBAAoB,UAAU,OAAI,uBAAA,UAAU,QAAQ,cAAlB,2CAAA,qBAAoB,SAAS,IACvE,AAAC,GAAmC,OAAjC,UAAU,QAAQ,CAAC,UAAU,EAAC,KAAgC,OAA7B,UAAU,QAAQ,CAAC,SAAS,IAChE;gCACJ,YAAY,UAAU,UAAU;gCAChC,QAAQ,UAAU,gBAAgB;4BACpC;;;uBACG,eAAe,GAAG;qEAAC,CAAC;gCAUT,kBAA+B;mCAVQ;gCACnD,IAAI,OAAO,SAAS;gCACpB,OAAO,OAAO,KAAK;gCACnB,aAAa,OAAO,WAAW;gCAC/B,UAAU,OAAO,QAAQ;gCACzB,MAAM;gCACN,UAAU,OAAO,QAAQ;gCACzB,QAAQ,OAAO,MAAM;gCACrB,aAAa,OAAO,UAAU;gCAC9B,WAAW,OAAO,UAAU;gCAC5B,YAAY,EAAA,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,UAAU,OAAI,oBAAA,OAAO,QAAQ,cAAf,wCAAA,kBAAiB,SAAS,IACjE,AAAC,GAAgC,OAA9B,OAAO,QAAQ,CAAC,UAAU,EAAC,KAA6B,OAA1B,OAAO,QAAQ,CAAC,SAAS,IAC1D;gCACJ,YAAY,OAAO,UAAU;gCAC7B,QAAQ,OAAO,aAAa;4BAC9B;;;iBACD;gBAED,uCAAuC;gBACvC,mBAAmB,IAAI;iEAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;;gBAErG,cAAc;YAEhB,EAAE,OAAO,KAAc;oBAMN,sBAIH;gBATZ,QAAQ,KAAK,CAAC,8BAA8B;gBAE5C,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,MAAM,eAAe,OAAO,OAAO,QAAQ,YAAY,cAAc;gBACrE,MAAM,aAAa,eAAe,MAA4D;gBAC9F,MAAM,SAAS,uBAAA,kCAAA,uBAAA,WAAY,QAAQ,cAApB,2CAAA,qBAAsB,MAAM;gBAE3C,QAAQ,KAAK,CAAC,kBAAkB;oBAC9B,SAAS;oBACT,QAAQ,EAAE,uBAAA,kCAAA,wBAAA,WAAY,QAAQ,cAApB,4CAAA,sBAAsB,IAAI;oBACpC,QAAQ;gBACV;gBAEA,IAAI,WAAW,KAAK;oBAClB,SAAS;gBACX,OAAO,IAAI,WAAW,KAAK;oBACzB,SAAS;gBACX,OAAO;oBACL,SAAS,AAAC,8BAA0C,OAAb;gBACzC;YACF,SAAU;gBACR,aAAa;YACf;QACF;oDAAG;QAAC;KAAgB;IAEtB,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,4BAA4B,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IACpE,MAAM,uBAAuB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IAE/D,IAAI,eAAe,WAAW;QAC5B,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;oBAAC,SAAQ;;;;;;;;;;;;;;;;IAIxB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAG;;;;;;kCACJ,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC,mJAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,KAAK;gCAAY,OAAO;gCAAY,MAAM;4BAAoB;4BAChE;gCAAE,KAAK;gCAAS,OAAO;gCAAoB,MAAM;gCAAsB,OAAO,WAAW,MAAM;4BAAC;yBACjG,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;gCACC,MAAK;gCAEL,SAAS,IAAM,aAAa,IAAI,GAAG;gCACnC,WAAW,AAAC,gFAIX,OAHC,cAAc,IAAI,GAAG,GACjB,gCACA;;kDAGN,6LAAC;wCAAE,WAAW,AAAC,GAAW,OAAT,IAAI,IAAI,EAAC;;;;;;oCACzB,IAAI,KAAK;oCACT,IAAI,KAAK,KAAK,2BACb,6LAAC;wCAAK,WAAU;kDACb,IAAI,KAAK;;;;;;;+BAZT,IAAI,GAAG;;;;;;;;;;;;;;;gBAqBnB,cAAc,4BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2D,0BAA0B,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAK9G,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2D,qBAAqB,MAAM;;;;;;;;;;;;;;;;;;;;;;;8CAKzG,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA2D,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjG,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAuD;;;;;;sEACrE,6LAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAK5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,4BAA4B;oDAC3C,WAAU;;sEAEV,6LAAC;4DAAE,WAAU;;;;;;wDAA4B;;;;;;;;;;;;;;;;;;;8CAO/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAuD;;;;;;sEACrE,6LAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAK5D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,uBAAuB;oDACtC,WAAU;;sEAEV,6LAAC;4DAAE,WAAU;;;;;;wDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAU1D,cAAc,yBACb,6LAAC;8BACE,WAAW,MAAM,KAAK,kBACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAGrD,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CACX;;;;;;;;;;;iFAKH,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,0BACf,6LAAC;gCAAuB,WAAU;0CAChC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,UAAU,KAAK;;;;;;sEAElB,6LAAC;4DAAK,WAAW,AAAC,8CAA8E,OAAjC,eAAe,UAAU,MAAM;sEAC3F,UAAU,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;sEAEjD,6LAAC;4DAAK,WAAW,AAAC,mDAAuF,OAArC,iBAAiB,UAAU,QAAQ;sEACpG,UAAU,QAAQ,CAAC,WAAW;;;;;;sEAEjC,6LAAC;4DAAK,WAAW,AAAC,mDAEjB,OADC,UAAU,IAAI,KAAK,qBAAqB,8BAA8B;sEAErE,UAAU,IAAI,KAAK,qBAAqB,qBAAqB;;;;;;;;;;;;8DAGlE,6LAAC;oDAAE,WAAU;;wDAAgD;wDACtD,UAAU,EAAE;wDAAC;wDAAc,UAAU,QAAQ;;;;;;;8DAEpD,6LAAC;oDAAE,WAAU;8DACV,UAAU,WAAW,CAAC,MAAM,GAAG,MAC5B,AAAC,GAA0C,OAAxC,UAAU,WAAW,CAAC,SAAS,CAAC,GAAG,MAAK,SAC3C,UAAU,WAAW;;;;;;8DAI3B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uJAAA,CAAA,UAAkB;wDACjB,cAAc,CAAA,GAAA,uJAAA,CAAA,0BAAuB,AAAD,EAAE,UAAU,MAAM;wDACtD,QAAQ,UAAU,IAAI,KAAK,qBAAqB,uJAAA,CAAA,mBAAgB,CAAC,gBAAgB,GAAG,uJAAA,CAAA,mBAAgB,CAAC,WAAW;wDAChH,QAAQ,UAAU,MAAM;wDACxB,MAAK;wDACL,SAAQ;wDACR,gBAAgB;wDAChB,gBAAgB;wDAChB,WAAU;;;;;;;;;;;8DAId,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAK;gEAAY,WAAW,UAAU,WAAW;;;;;;;sEAClD,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;;gEAAK;gEAAU,WAAW,UAAU,SAAS;;;;;;;wDAC7C,UAAU,UAAU,kBACnB;;8EACE,6LAAC;oEAAK,WAAU;8EAAO;;;;;;8EACvB,6LAAC;;wEAAK;wEAAc,UAAU,UAAU;;;;;;;;;;;;;;;;;;;;;sDAKhD,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;+BAzDK,UAAU,EAAE;;;;;;;;;;;;;;;gBAqE/B,0CACC,6LAAC,yJAAA,CAAA,UAAoB;oBACnB,SAAS,IAAM,4BAA4B;oBAC3C,UAAU,CAAC;wBACT,QAAQ,GAAG,CAAC,yCAAyC;wBACrD,4BAA4B;wBAC5B,mDAAmD;wBACnD;oBACF;;;;;;gBAIH,qCACC,6LAAC,oJAAA,CAAA,UAAe;oBACd,SAAS,IAAM,uBAAuB;oBACtC,UAAU,CAAC;wBACT,QAAQ,GAAG,CAAC,iCAAiC;wBAC7C,uBAAuB;wBACvB,mDAAmD;wBACnD;oBACF;;;;;;;;;;;;;;;;;AAMZ;GAvcM;;QAC8C,kIAAA,CAAA,UAAO;QAC1C,qIAAA,CAAA,YAAS;;;KAFpB;uCAycS", "debugId": null}}]}