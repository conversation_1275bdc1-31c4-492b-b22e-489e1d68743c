{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["import { LicenseCategory } from \".\";\r\nimport { User } from \"./user\";\r\n\r\nexport interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  validity?: number;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  categories?: LicenseCategory[];\r\n  iconColor?: string;\r\n  icon?: string;\r\n  iconBg?: string;\r\n}\r\n\r\nexport interface StatusAction {\r\n  status: ApplicationStatus;\r\n  label: string;\r\n  icon: string;\r\n  color: string;\r\n  hoverColor: string;\r\n  roles?: string[],\r\n  description: string;\r\n  confirmMessage?: string;\r\n}\r\n\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application extends Record<string, unknown> {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: string;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: Applicant;\r\n  license_category?:LicenseCategory;\r\n  assignee?:User;\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  PENDING = 'pending',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  PENDING_PAYMENT = 'pending_payment',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n  PASS_EVALUATION = 'pass_evaluation',\r\n  WAITING_FOR_APPROVAL = 'waiting_for_approval',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface License extends Record<string, unknown> {\r\n  license_id: string;\r\n  license_number: string;\r\n  description?: string;\r\n  application_id: string;\r\n  applicant_id: string;\r\n  license_type_id: string;\r\n  status: LicenseStatus;\r\n  issue_date: string;\r\n  expiry_date: string;\r\n  issued_by: string;\r\n  code?: string;\r\n  conditions?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  application: Application;\r\n  issuer?:User;\r\n}\r\n\r\nexport enum LicenseStatus {\r\n  ACTIVE = 'active',\r\n  EXPIRED = 'expired',\r\n  SUSPENDED = 'suspended',\r\n  REVOKED = 'revoked',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAkEO,IAAA,AAAK,2CAAA;;;;;;;;;;;;WAAA;;AAyCL,IAAA,AAAK,uCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\nimport { AppNotification, NotificationSummary } from '@/types/notification';\r\n\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications/my-notifications?${queryParams.toString()}`;\r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response).data;\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.length || 0,\r\n        unread_count: data.length || 0,\r\n        notifications: Array.isArray(data) ? data : []\r\n      };\r\n    } catch (error: any) {\r\n      // Convert params to string format for URLSearchParams\r\n      const stringParams: Record<string, string> = {};\r\n      if (params) {\r\n        if (params.page) stringParams.page = params.page.toString();\r\n        if (params.limit) stringParams.limit = params.limit.toString();\r\n        if (params.type) stringParams.type = params.type;\r\n        if (params.status) stringParams.status = params.status;\r\n      }\r\n\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications/my-notifications?${new URLSearchParams(stringParams).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/mark-read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read (implemented client-side by marking each notification individually)\r\n  async markAllAsRead(): Promise<void> {\r\n    // This is handled by the useNotifications hook by calling markAsRead for each unread notification\r\n    // We don't need a separate backend endpoint for this\r\n    throw new Error('markAllAsRead should be handled by useNotifications hook');\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Send activity note notification (creates notification which auto-creates activity note)\r\n  async sendActivityNoteNotification(data: {\r\n    applicationId: string;\r\n    message: string;\r\n    noteType?: string;\r\n    category?: string;\r\n    priority?: 'low' | 'normal' | 'high';\r\n    attachmentsCount?: number;\r\n  }): Promise<any> {\r\n    const response = await apiClient.post('/notifications/activity-note', {\r\n      application_id: data.applicationId,\r\n      message: data.message,\r\n      note_type: data.noteType || 'evaluation_comment',\r\n      category: data.category || 'communication',\r\n      priority: data.priority || 'normal',\r\n      attachments_count: data.attachmentsCount || 0,\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: string,\r\n  newStatus: string,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.DRAFT]: {\r\n      title: 'Application Draft Saved',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been saved as a draft. You can continue editing and submit it when ready.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING]: {\r\n      title: 'Application Pending',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is pending initial review. We'll notify you once the review begins.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING_PAYMENT]: {\r\n      title: 'Payment Required',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been approved for payment. Please complete the payment to proceed with license issuance.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus as keyof typeof messages] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAIO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,CAAC,gCAAgC,EAAE,YAAY,QAAQ,IAAI;YAC5E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAE9C,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,MAAM,IAAI;gBAC5B,cAAc,KAAK,MAAM,IAAI;gBAC7B,eAAe,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;YAChD;QACF,EAAE,OAAO,OAAY;YACnB,sDAAsD;YACtD,MAAM,eAAuC,CAAC;YAC9C,IAAI,QAAQ;gBACV,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ;gBACzD,IAAI,OAAO,KAAK,EAAE,aAAa,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ;gBAC5D,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI;gBAChD,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,GAAG,OAAO,MAAM;YACxD;YAEA,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,MAAM,QAAQ,EAAE,QAAQ;gBAClC,QAAQ,MAAM,QAAQ,EAAE,UAAU;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,CAAC,gCAAgC,EAAE,IAAI,gBAAgB,cAAc,QAAQ,IAAI;YAC7F;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,eAAe,UAAU,CAAC;QACnF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qGAAqG;IACrG,MAAM;QACJ,kGAAkG;QAClG,qDAAqD;QACrD,MAAM,IAAI,MAAM;IAClB;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,gBAAgB;QAC1E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0FAA0F;IAC1F,MAAM,8BAA6B,IAOlC;QACC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB,KAAK,aAAa;YAClC,SAAS,KAAK,OAAO;YACrB,WAAW,KAAK,QAAQ,IAAI;YAC5B,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ,IAAI;YAC3B,mBAAmB,KAAK,gBAAgB,IAAI;QAC9C;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,CAAC,EAAE,EAAE,mBAAmB,WAAW,CAAC,GAAG;IACjF,MAAM,WAAW,OAAO,CAAC,QAAQ,EAAE,MAAM,GAAG;IAE5C,MAAM,WAAW;QACf,CAAC,uHAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YACzB,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,+EAA+E,CAAC;YACvJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,qEAAqE,CAAC;YAC7I,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6DAA6D,CAAC;YACrI,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,iCAAiC,EAAE,eAAe,SAAS,kCAAkC,CAAC;YACrK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,8BAA8B,EAAE,eAAe,SAAS,sCAAsC,CAAC;YACtK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,EAAE;YACnC,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,8FAA8F,CAAC;YACtK,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,uDAAuD,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,wEAAwE,CAAC;YAChJ,MAAM;QACR;QACA,CAAC,uHAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,kCAAkC,CAAC;YAC1G,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,CAAC,KAAK,EAAE,oBAAoB,cAAc,EAAE,kBAAkB,6BAA6B,EAAE,UAAU,OAAO,CAAC,KAAK,KAAK,CAAC,CAAC;QACpI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAmC,IAAI;AACzD;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;IAE3C,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,CAAC,IAAI,EAAE,mBAAmB;QAC1C,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,uHAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode, useEffect } from 'react';\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: ReactNode;\r\n  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';\r\n  showCloseButton?: boolean;\r\n  closeOnOverlayClick?: boolean;\r\n}\r\n\r\nexport default function Modal({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  showCloseButton = true,\r\n  closeOnOverlayClick = true,\r\n}: ModalProps) {\r\n  // Handle escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape' && isOpen) {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent body scroll when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'sm:max-w-sm';\r\n      case 'md':\r\n        return 'sm:max-w-md';\r\n      case 'lg':\r\n        return 'sm:max-w-lg';\r\n      case 'xl':\r\n        return 'sm:max-w-xl';\r\n      case '2xl':\r\n        return 'sm:max-w-2xl';\r\n      default:\r\n        return 'sm:max-w-md';\r\n    }\r\n  };\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {\r\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"fixed inset-0\"\r\n        onClick={handleOverlayClick}\r\n        aria-hidden=\"true\"\r\n      />\r\n      \r\n      <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full ${getSizeClasses()} mx-4 transform transition-all max-h-[90vh] flex flex-col`}>\r\n        {/* Header - Fixed */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            {title}\r\n          </h3>\r\n          {showCloseButton && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Scrollable Content */}\r\n        <div className=\"flex-1 overflow-hidden\">\r\n          <div className=\"overflow-y-auto h-full\">\r\n            <div className=\"p-6\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAce,SAAS,MAAM,EAC5B,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EACf;IACX,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;gBACpC;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;YACrC,yCAAyC;YACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YACvD;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,SAAS;gBACT,eAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAW,CAAC,+DAA+D,EAAE,iBAAiB,yDAAyD,CAAC;;kCAE3J,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,iCACC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Notification } from '@/hooks/useNotifications';\r\n\r\ninterface NotificationItemProps {\r\n  notification: Notification;\r\n  onMarkAsRead: (id: string) => void;\r\n  onNotificationClick?: (notification: Notification) => void;\r\n}\r\n\r\nconst NotificationItem: React.FC<NotificationItemProps> = ({\r\n  notification,\r\n  onMarkAsRead,\r\n  onNotificationClick,\r\n}) => {\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) {\r\n      return 'Just now';\r\n    } else if (diffInSeconds < 3600) {\r\n      const minutes = Math.floor(diffInSeconds / 60);\r\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 86400) {\r\n      const hours = Math.floor(diffInSeconds / 3600);\r\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 604800) {\r\n      const days = Math.floor(diffInSeconds / 86400);\r\n      return `${days} day${days > 1 ? 's' : ''} ago`;\r\n    } else {\r\n      return date.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const getNotificationIcon = (type: string) => {\r\n    switch (type.toLowerCase()) {\r\n      case 'email':\r\n        return 'ri-mail-line';\r\n      case 'sms':\r\n        return 'ri-message-3-line';\r\n      case 'in_app':\r\n        return 'ri-notification-3-line';\r\n      case 'application_status':\r\n        return 'ri-file-list-3-line';\r\n      case 'evaluation_assigned':\r\n        return 'ri-user-settings-line';\r\n      case 'payment_due':\r\n        return 'ri-money-dollar-circle-line';\r\n      case 'license_expiry':\r\n        return 'ri-calendar-event-line';\r\n      case 'system_alert':\r\n        return 'ri-alert-line';\r\n      default:\r\n        return 'ri-notification-3-line';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority.toLowerCase()) {\r\n      case 'urgent':\r\n        return 'text-red-600 dark:text-red-400';\r\n      case 'high':\r\n        return 'text-orange-600 dark:text-orange-400';\r\n      case 'medium':\r\n        return 'text-yellow-600 dark:text-yellow-400';\r\n      case 'low':\r\n        return 'text-green-600 dark:text-green-400';\r\n      default:\r\n        return 'text-gray-600 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const handleItemClick = () => {\r\n    if (!notification.is_read) {\r\n      onMarkAsRead(notification.notification_id);\r\n    }\r\n    if (onNotificationClick) {\r\n      onNotificationClick(notification);\r\n    }\r\n  };\r\n\r\n  const handleMarkAsReadClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onMarkAsRead(notification.notification_id);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200 ${\r\n        !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''\r\n      }`}\r\n      onClick={handleItemClick}\r\n    >\r\n      <div className=\"flex items-start space-x-3\">\r\n        {/* Notification Icon */}\r\n        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\r\n          !notification.is_read \r\n            ? 'bg-blue-100 dark:bg-blue-900/50' \r\n            : 'bg-gray-100 dark:bg-gray-700'\r\n        }`}>\r\n          <i className={`${getNotificationIcon(notification.type)} text-sm ${\r\n            !notification.is_read \r\n              ? 'text-blue-600 dark:text-blue-400' \r\n              : 'text-gray-600 dark:text-gray-400'\r\n          }`}></i>\r\n        </div>\r\n\r\n        {/* Notification Content */}\r\n        <div className=\"flex-1 min-w-0\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex-1\">\r\n              <h4 className={`text-sm font-medium ${\r\n                !notification.is_read \r\n                  ? 'text-gray-900 dark:text-gray-100' \r\n                  : 'text-gray-700 dark:text-gray-300'\r\n              }`}>\r\n                {notification.subject}\r\n              </h4>\r\n              <p className={`mt-1 text-sm ${\r\n                !notification.is_read \r\n                  ? 'text-gray-700 dark:text-gray-300' \r\n                  : 'text-gray-500 dark:text-gray-400'\r\n              }`}>\r\n                {notification.message}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Priority Indicator */}\r\n            {notification.priority && notification.priority !== 'medium' && (\r\n              <div className={`flex-shrink-0 ml-2 ${getPriorityColor(notification.priority)}`}>\r\n                <i className=\"ri-flag-line text-xs\"></i>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Metadata */}\r\n          <div className=\"mt-2 flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\r\n              <span>{formatTimeAgo(notification.created_at)}</span>\r\n              {notification.entity_type && (\r\n                <span className=\"capitalize\">{notification.entity_type}</span>\r\n              )}\r\n            </div>\r\n\r\n            {/* Actions */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              {!notification.is_read && (\r\n                <button\r\n                  onClick={handleMarkAsReadClick}\r\n                  className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 hover:text-blue-800 dark:hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200\"\r\n                  title=\"Mark as read\"\r\n                >\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Mark as Read\r\n                </button>\r\n              )}\r\n              {notification.is_read && (\r\n                <span className=\"text-xs text-green-600 dark:text-green-400 flex items-center\">\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Read\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Unread Indicator */}\r\n        {!notification.is_read && (\r\n          <div className=\"flex-shrink-0 w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full\"></div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationItem;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,MAAM,mBAAoD,CAAC,EACzD,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACpB;IACC,MAAM,gBAAgB,CAAC;QACrB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,gBAAgB,MAAM;YAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;YAC3C,OAAO,GAAG,QAAQ,OAAO,EAAE,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;QACzD,OAAO,IAAI,gBAAgB,OAAO;YAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,GAAG,MAAM,KAAK,EAAE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC;QACnD,OAAO,IAAI,gBAAgB,QAAQ;YACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;YACxC,OAAO,GAAG,KAAK,IAAI,EAAE,OAAO,IAAI,MAAM,GAAG,IAAI,CAAC;QAChD,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,aAAa,aAAa,eAAe;QAC3C;QACA,IAAI,qBAAqB;YACvB,oBAAoB;QACtB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,EAAE,eAAe;QACjB,aAAa,aAAa,eAAe;IAC3C;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,wIAAwI,EAClJ,CAAC,aAAa,OAAO,GAAG,mCAAmC,IAC3D;QACF,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAW,CAAC,oEAAoE,EACnF,CAAC,aAAa,OAAO,GACjB,oCACA,gCACJ;8BACA,cAAA,8OAAC;wBAAE,WAAW,GAAG,oBAAoB,aAAa,IAAI,EAAE,SAAS,EAC/D,CAAC,aAAa,OAAO,GACjB,qCACA,oCACJ;;;;;;;;;;;8BAIJ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAW,CAAC,oBAAoB,EAClC,CAAC,aAAa,OAAO,GACjB,qCACA,oCACJ;sDACC,aAAa,OAAO;;;;;;sDAEvB,8OAAC;4CAAE,WAAW,CAAC,aAAa,EAC1B,CAAC,aAAa,OAAO,GACjB,qCACA,oCACJ;sDACC,aAAa,OAAO;;;;;;;;;;;;gCAKxB,aAAa,QAAQ,IAAI,aAAa,QAAQ,KAAK,0BAClD,8OAAC;oCAAI,WAAW,CAAC,mBAAmB,EAAE,iBAAiB,aAAa,QAAQ,GAAG;8CAC7E,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;sCAMnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAM,cAAc,aAAa,UAAU;;;;;;wCAC3C,aAAa,WAAW,kBACvB,8OAAC;4CAAK,WAAU;sDAAc,aAAa,WAAW;;;;;;;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;wCACZ,CAAC,aAAa,OAAO,kBACpB,8OAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAM;;8DAEN,8OAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;wCAIzC,aAAa,OAAO,kBACnB,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;gBAS/C,CAAC,aAAa,OAAO,kBACpB,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useNotifications.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\nexport interface NotificationCounts {\r\n  total: number;\r\n  unread: number;\r\n}\r\n\r\nexport interface UseNotificationsReturn {\r\n  notifications: AppNotification[];\r\n  unreadCount: number;\r\n  totalCount: number;\r\n  loading: boolean;\r\n  error: string | null;\r\n  fetchNotifications: () => Promise<void>;\r\n  markAsRead: (id: string) => Promise<void>;\r\n  markAllAsRead: () => Promise<void>;\r\n  refreshNotifications: () => Promise<void>;\r\n  getNotificationCounts: () => Promise<void>;\r\n}\r\n\r\nexport const useNotifications = (): UseNotificationsReturn => {\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [totalCount, setTotalCount] = useState(0);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { user, isAuthenticated } = useAuth();\r\n  const { showError } = useToast();\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      console.log('🔍 Fetching notifications for user:', {\r\n        userId: user.user_id,\r\n        email: user.email\r\n      });\r\n\r\n      const data = await notificationService.getUserNotifications({\r\n        page: 1,\r\n        limit: 50 // Get more notifications for better UX\r\n      });\r\n\r\n      if (data && data.notifications) {\r\n        setNotifications(data.notifications);\r\n        setUnreadCount(data.unread_count);\r\n        setTotalCount(data.total_count);\r\n      } else {\r\n        setNotifications([]);\r\n        setUnreadCount(0);\r\n        setTotalCount(0);\r\n      }\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';\r\n      setError(errorMessage);\r\n      console.error('Error fetching notifications:', err);\r\n      // Don't show toast error for initial load failures\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [isAuthenticated, user]);\r\n\r\n  const markAsRead = useCallback(async (id: string) => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await notificationService.markAsRead(id);\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notification =>\r\n          notification.notification_id === id\r\n            ? { ...notification, status: 'read', read_at: new Date().toISOString() }\r\n            : notification\r\n        )\r\n      );\r\n\r\n      // Update unread count\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark notification as read');\r\n      console.error('Error marking notification as read:', err);\r\n    }\r\n  }, [isAuthenticated, showError]);\r\n\r\n  const markAllAsRead = useCallback(async () => {\r\n    if (!isAuthenticated || notifications.length === 0) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Mark all unread notifications as read\r\n      const unreadNotifications = notifications.filter(n => n.status === 'unread');\r\n\r\n      for (const notification of unreadNotifications) {\r\n        await markAsRead(notification.notification_id);\r\n      }\r\n\r\n      setUnreadCount(0);\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark all notifications as read');\r\n      console.error('Error marking all notifications as read:', err);\r\n    }\r\n  }, [isAuthenticated, notifications, markAsRead, showError]);\r\n\r\n  const getNotificationCounts = useCallback(async () => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const counts = await notificationService.getNotificationCount();\r\n      setUnreadCount(counts.unread);\r\n      setTotalCount(counts.total);\r\n    } catch (err) {\r\n      console.error('Error getting notification counts:', err);\r\n    }\r\n  }, [isAuthenticated]);\r\n\r\n  const refreshNotifications = useCallback(async () => {\r\n    await fetchNotifications();\r\n  }, [fetchNotifications]);\r\n\r\n  // Initial fetch when component mounts and user is authenticated\r\n  useEffect(() => {\r\n    if (isAuthenticated && user) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  // Set up polling for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    const interval = setInterval(() => {\r\n      fetchNotifications();\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  return {\r\n    notifications,\r\n    unreadCount,\r\n    totalCount,\r\n    loading,\r\n    error,\r\n    fetchNotifications,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n    getNotificationCounts,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;AA0BO,MAAM,mBAAmB;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAE7B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC7B;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC;gBACjD,QAAQ,KAAK,OAAO;gBACpB,OAAO,KAAK,KAAK;YACnB;YAEA,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;gBAC1D,MAAM;gBACN,OAAO,GAAG,uCAAuC;YACnD;YAEA,IAAI,QAAQ,KAAK,aAAa,EAAE;gBAC9B,iBAAiB,KAAK,aAAa;gBACnC,eAAe,KAAK,YAAY;gBAChC,cAAc,KAAK,WAAW;YAChC,OAAO;gBACL,iBAAiB,EAAE;gBACnB,eAAe;gBACf,cAAc;YAChB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,mDAAmD;QACrD,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAiB;KAAK;IAE1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI,CAAC,iBAAiB;YACpB;QACF;QAEA,IAAI;YACF,MAAM,sIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;YAErC,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,eAAe,KAAK,KAC7B;wBAAE,GAAG,YAAY;wBAAE,QAAQ;wBAAQ,SAAS,IAAI,OAAO,WAAW;oBAAG,IACrE;YAIR,sBAAsB;YACtB,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;QAC5C,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,UAAU;YACV,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,mBAAmB,cAAc,MAAM,KAAK,GAAG;YAClD;QACF;QAEA,IAAI;YACF,wCAAwC;YACxC,MAAM,sBAAsB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAEnE,KAAK,MAAM,gBAAgB,oBAAqB;gBAC9C,MAAM,WAAW,aAAa,eAAe;YAC/C;YAEA,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,UAAU;YACV,QAAQ,KAAK,CAAC,4CAA4C;QAC5D;IACF,GAAG;QAAC;QAAiB;QAAe;QAAY;KAAU;IAE1D,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,IAAI,CAAC,iBAAiB;YACpB;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC7D,eAAe,OAAO,MAAM;YAC5B,cAAc,OAAO,KAAK;QAC5B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,MAAM;IACR,GAAG;QAAC;KAAmB;IAEvB,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,MAAM;YAC3B;QACF;IACF,GAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC7B;QACF;QAEA,MAAM,WAAW,YAAY;YAC3B;QACF,GAAG,QAAQ,aAAa;QAExB,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Modal from '@/components/common/Modal';\r\nimport NotificationItem from './NotificationItem';\r\nimport Loader from '@/components/Loader';\r\nimport { useNotifications } from '@/hooks/useNotifications';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n}) => {\r\n  const {\r\n    notifications,\r\n    unreadCount,\r\n    loading,\r\n    error,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n  } = useNotifications();\r\n  \r\n  const { showSuccess, showError } = useToast();\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('unread');\r\n  const [localLoading, setLocalLoading] = useState(false);\r\n\r\n  // Filter notifications based on selected filter\r\n  const filteredNotifications = notifications.filter(notification => {\r\n    if (filter === 'unread') {\r\n      return !notification.read_at;\r\n    }\r\n    return true;\r\n  });\r\n\r\n\r\n  // Refresh notifications when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      refreshNotifications();\r\n    }\r\n  }, [isOpen, refreshNotifications]);\r\n\r\n  const handleMarkAsRead = async (id: string) => {\r\n    try {\r\n      await markAsRead(id);\r\n      showSuccess('Notification marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    if (unreadCount === 0) {\r\n      showError('No unread notifications to mark');\r\n      return;\r\n    }\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await markAllAsRead();\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleNotificationClick = (notification: AppNotification) => {\r\n    // Handle notification click - could navigate to related page\r\n    if (notification.entity_type === 'application' && notification.entity_id) {\r\n      // Navigate to application details\r\n      window.location.href = `/customer/my-licenses?application_id=${notification.entity_id}`;\r\n    }\r\n  };\r\n\r\n  const handleRefresh = async () => {\r\n    setLocalLoading(true);\r\n    try {\r\n      await refreshNotifications();\r\n    } catch (error) {\r\n      console.error('Error refreshing notifications:', error);\r\n      showError('Failed to refresh notifications');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title=\"Notifications\"\r\n      size=\"lg\"\r\n    >\r\n      <div className=\"flex flex-col h-96\">\r\n        {/* Header with filters and actions */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Filter buttons */}\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'all'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                All ({notifications.length})\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'unread'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                Unread ({unreadCount})\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action buttons */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <button\r\n              onClick={handleRefresh}\r\n              disabled={loading || localLoading}\r\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50\"\r\n              title=\"Refresh notifications\"\r\n            >\r\n              <i className={`ri-refresh-line ${(loading || localLoading) ? 'animate-spin' : ''}`}></i>\r\n            </button>\r\n            \r\n            {unreadCount > 0 && (\r\n              <button\r\n                onClick={handleMarkAllAsRead}\r\n                disabled={loading || localLoading}\r\n                className=\"px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Notifications list */}\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          {loading && notifications.length === 0 ? (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <Loader message=\"Loading notifications...\" />\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-error-warning-line text-4xl text-red-500 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                Error Loading Notifications\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n                {error}\r\n              </p>\r\n              <button\r\n                onClick={handleRefresh}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          ) : filteredNotifications.length === 0 ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                {filter === 'unread' ? 'No Unread Notifications' : 'No Notifications'}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {filter === 'unread' \r\n                  ? 'All caught up! You have no unread notifications.'\r\n                  : 'You have no notifications at this time.'\r\n                }\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\r\n              {filteredNotifications.map((notification) => (\r\n                <NotificationItem\r\n                  key={notification.notification_id}\r\n                  notification={notification}\r\n                  onMarkAsRead={handleMarkAsRead}\r\n                  onNotificationClick={handleNotificationClick}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        {filteredNotifications.length > 0 && (\r\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700 text-center\">\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Showing {filteredNotifications.length} of {notifications.length} notifications\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAeA,MAAM,oBAAsD,CAAC,EAC3D,MAAM,EACN,OAAO,EACR;IACC,MAAM,EACJ,aAAa,EACb,WAAW,EACX,OAAO,EACP,KAAK,EACL,UAAU,EACV,aAAa,EACb,oBAAoB,EACrB,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gDAAgD;IAChD,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW,UAAU;YACvB,OAAO,CAAC,aAAa,OAAO;QAC9B;QACA,OAAO;IACT;IAGA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;KAAqB;IAEjC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW;YACjB,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,GAAG;YACrB,UAAU;YACV;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,6DAA6D;QAC7D,IAAI,aAAa,WAAW,KAAK,iBAAiB,aAAa,SAAS,EAAE;YACxE,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,qCAAqC,EAAE,aAAa,SAAS,EAAE;QACzF;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,qIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,4DAA4D,EACtE,WAAW,QACP,qEACA,iFACJ;;4CACH;4CACO,cAAc,MAAM;4CAAC;;;;;;;kDAE7B,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,4DAA4D,EACtE,WAAW,WACP,qEACA,iFACJ;;4CACH;4CACU;4CAAY;;;;;;;;;;;;;;;;;;sCAM3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC;wCAAE,WAAW,CAAC,gBAAgB,EAAE,AAAC,WAAW,eAAgB,iBAAiB,IAAI;;;;;;;;;;;gCAGnF,cAAc,mBACb,8OAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;8BACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;mEAEhB,sBACF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAEH,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;mEAID,sBAAsB,MAAM,KAAK,kBACnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAG,WAAU;0CACX,WAAW,WAAW,4BAA4B;;;;;;0CAErD,8OAAC;gCAAE,WAAU;0CACV,WAAW,WACR,qDACA;;;;;;;;;;;iFAKR,8OAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,8OAAC,uJAAA,CAAA,UAAgB;gCAEf,cAAc;gCACd,cAAc;gCACd,qBAAqB;+BAHhB,aAAa,eAAe;;;;;;;;;;;;;;;gBAW1C,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAA2C;4BAC7C,sBAAsB,MAAM;4BAAC;4BAAK,cAAc,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAO9E;uCAEe", "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport NotificationModal from '../notifications/NotificationModal';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n}\r\n\r\nconst NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\r\n  const { user } = useAuth();\r\n  const { showError } = useToast();\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  // Fetch notification count\r\n  const fetchNotificationCount = async () => {\r\n    if (!user) return;\r\n    \r\n    try {\r\n      const data = await notificationService.getNotificationCount();\r\n      setUnreadCount(data.unread);\r\n    } catch (error) {\r\n      console.error('Error fetching notification count:', error);\r\n      showError('Failed to load notification count');\r\n    }\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchNotificationCount();\r\n  }, [user]);\r\n\r\n  // Poll for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    const interval = setInterval(fetchNotificationCount, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Handle modal close and refresh count\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    fetchNotificationCount(); // Refresh count when modal closes\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className={`relative ${className}`}>\r\n        {/* Notification Bell */}\r\n        <button\r\n          onClick={() => setIsModalOpen(true)}\r\n          className=\"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n          title=\"Notifications\"\r\n        >\r\n          <i className=\"ri-notification-line text-xl\"></i>\r\n          {unreadCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1\">\r\n              {unreadCount > 99 ? '99+' : unreadCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYA,MAAM,mBAAoD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,sIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC3D,eAAe,KAAK,MAAM;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY,wBAAwB;QACrD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,eAAe;QACf,0BAA0B,kCAAkC;IAC9D;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,WAAW;0BAErC,cAAA,8OAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;oBACV,OAAM;;sCAEN,8OAAC;4BAAE,WAAU;;;;;;wBACZ,cAAc,mBACb,8OAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOpC,8OAAC,wJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;uCAEe", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\nimport NotificationBell from '../common/NotificationBell';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'My Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Invoices & Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <NotificationBell className=\"mr-4\" />\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,yBAAyB;YACzB,0BAA0B;YAC1B,iCAAiC;YACjC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAqB5B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;iHAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,UAAgB;4CAAC,WAAU;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 1995, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/TextInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  ...props\r\n}, ref) => {\r\n  // Base input styling with proper text visibility for all modes - force text color to ensure visibility\r\n  const baseInputClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <input\r\n        ref={ref}\r\n        className={inputClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextInput.displayName = 'TextInput';\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,uGAAuG;IACvG,MAAM,iBAAiB,CAAC,4OAA4O,EAClQ,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 2070, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n  options?: SelectOption[];\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  options,\r\n  children,\r\n  ...props\r\n}, ref) => {\r\n  // Base select styling with proper text visibility for all modes\r\n  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const selectClass = `${baseSelectClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <select\r\n        ref={ref}\r\n        className={selectClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      >\r\n        {options ? (\r\n          options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))\r\n        ) : (\r\n          children\r\n        )}\r\n      </select>\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,gEAAgE;IAChE,MAAM,kBAAkB,CAAC,6LAA6L,EACpN,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,cAAc,GAAG,gBAAgB,CAAC,EACtC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;0BAER,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;oEAK3B;;;;;;YAIH,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formatters.ts"], "sourcesContent": ["/**\r\n * Formats a number as currency with commas for better readability\r\n * For numbers over 5 digits, adds a comma after the first 2 figures\r\n * \r\n * @param amount - The amount to format\r\n * @param currency - The currency code (e.g., 'MWK', 'USD')\r\n * @param minimumFractionDigits - Minimum number of decimal places (default: 0)\r\n * @returns Formatted currency string\r\n */\r\nexport const formatCurrency = (\r\n  amount: number | string,\r\n  currency: string = 'USD',\r\n  minimumFractionDigits: number = 0\r\n): string => {\r\n  // Convert string to number if needed\r\n  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n\r\n  if(currency == '$') currency = 'USD';\r\n  \r\n  // Get the currency symbol\r\n  const formatter = new Intl.NumberFormat('en-MW', {\r\n    style: 'currency',\r\n    currency: currency,\r\n    minimumFractionDigits: minimumFractionDigits,\r\n    useGrouping: false, // We'll handle grouping manually\r\n  });\r\n  \r\n  // Format without grouping to get the base string\r\n  const formatted = formatter.format(numericAmount);\r\n  \r\n  // Extract the numeric part (remove currency symbol and any spaces)\r\n  const parts = formatted.match(/([^\\d]*)(\\d+(?:\\.\\d+)?)(.*)/);\r\n  if (!parts) return formatted;\r\n  \r\n  const [, prefix, numericPart, suffix] = parts;\r\n  \r\n  // Format the number with custom grouping\r\n  let formattedNumber = numericPart;\r\n  \r\n  // For numbers with 5 or more digits, we want to ensure there's a comma after the first 2 figures\r\n  if (numericPart.replace(/\\D/g, '').length >= 5) {\r\n    // Split the integer and decimal parts\r\n    const [integerPart, decimalPart] = numericPart.split('.');\r\n    \r\n    // Format the integer part with commas\r\n    // First, add a comma after the first 2 digits\r\n    let formattedInteger = integerPart.slice(0, 2) + ',' + integerPart.slice(2);\r\n    \r\n    // Then add commas for the rest of the number every 3 digits\r\n    formattedInteger = formattedInteger.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    \r\n    // Combine the parts back\r\n    formattedNumber = formattedInteger + (decimalPart ? '.' + decimalPart : '');\r\n  } else {\r\n    // For smaller numbers, use standard grouping (every 3 digits)\r\n    formattedNumber = numericPart.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n  }\r\n  \r\n  // Combine everything back\r\n  return prefix + formattedNumber + suffix;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED AMOUNT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format amount with currency (alternative to formatCurrency for consistency)\r\n * @param amount - The amount to format\r\n * @param currency - Currency code (default: '$')\r\n * @param locale - Locale for formatting (default: 'en-US')\r\n */\r\nexport const formatAmount = (\r\n  amount: number | string,\r\n  currency: string = 'USD',\r\n  locale: string = 'en-US'\r\n): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return `$ 0.00`;\r\n\r\n  return `${currency} ${numAmount.toLocaleString(locale, {\r\n    minimumFractionDigits: 2,\r\n    maximumFractionDigits: 2\r\n  })}`;\r\n};\r\n\r\n/**\r\n * Format amount without currency symbol\r\n * @param amount - The amount to format\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatNumber = (amount: number | string, decimals: number = 2): string => {\r\n  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  if (isNaN(numAmount)) return '0.00';\r\n\r\n  return numAmount.toLocaleString('en-US', {\r\n    minimumFractionDigits: decimals,\r\n    maximumFractionDigits: decimals\r\n  });\r\n};\r\n\r\n/**\r\n * Format license category fee with special handling for \"Short Code Allocation\"\r\n * @param fee - The fee amount (string or number)\r\n * @param categoryName - The name of the license category\r\n * @param currency - Currency code (default: 'MWK')\r\n */\r\nexport const formatLicenseCategoryFee = (\r\n  fee: string | number,\r\n  categoryName: string,\r\n): string => {\r\n  // Check if fee is 0 or empty\r\n  if (!fee || fee === '0' || parseFloat(fee.toString()) === 0) {\r\n    // Show \"Free\" for categories with 0 fee\r\n    return \"Free\";\r\n  }\r\n  // Format as currency for non-zero fees\r\n  return formatCurrency(fee);\r\n};\r\n\r\n/**\r\n * Format percentage\r\n * @param value - The value to format as percentage\r\n * @param decimals - Number of decimal places (default: 1)\r\n */\r\nexport const formatPercentage = (value: number | string, decimals: number = 1): string => {\r\n  const numValue = typeof value === 'string' ? parseFloat(value) : value;\r\n  if (isNaN(numValue)) return '0%';\r\n\r\n  return `${numValue.toFixed(decimals)}%`;\r\n};\r\n\r\n// ============================================================================\r\n// ENHANCED DATE & TIME FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Formats a date string to a readable format\r\n *\r\n * @param dateString - The date string to format\r\n * @param options - Intl.DateTimeFormatOptions\r\n * @returns Formatted date string\r\n */\r\nexport const formatDate = (\r\n  dateString: string | Date,\r\n  options: Intl.DateTimeFormatOptions = {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric'\r\n  }\r\n): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return new Intl.DateTimeFormat('en-MW', options).format(date);\r\n};\r\n\r\n/**\r\n * Format date in long format (e.g., \"January 15, 2024\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateLong = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  return date.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'long',\r\n    day: 'numeric'\r\n  });\r\n};\r\n\r\n/**\r\n * Format time (e.g., \"2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid time';\r\n\r\n  return date.toLocaleTimeString('en-US', {\r\n    hour: 'numeric',\r\n    minute: '2-digit',\r\n    hour12: true\r\n  });\r\n};\r\n\r\n/**\r\n * Format datetime (e.g., \"Jan 15, 2024 at 2:30 PM\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid datetime';\r\n\r\n  return `${formatDate(date)} at ${formatTime(date)}`;\r\n};\r\n\r\n/**\r\n * Format relative time (e.g., \"2 hours ago\", \"in 3 days\")\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatRelativeTime = (dateString: string | Date): string => {\r\n  if (!dateString) return 'Not specified';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return 'Invalid date';\r\n\r\n  const now = new Date();\r\n  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n  if (diffInSeconds < 60) return 'Just now';\r\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\r\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\r\n  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;\r\n  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;\r\n\r\n  return `${Math.floor(diffInSeconds / 31536000)} years ago`;\r\n};\r\n\r\n/**\r\n * Format date for input fields (YYYY-MM-DD)\r\n * @param dateString - Date string or Date object\r\n */\r\nexport const formatDateForInput = (dateString: string | Date): string => {\r\n  if (!dateString) return '';\r\n\r\n  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;\r\n  if (isNaN(date.getTime())) return '';\r\n\r\n  return date.toISOString().split('T')[0];\r\n};\r\n\r\n// ============================================================================\r\n// STRING CASE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Convert string to camelCase\r\n * @param str - String to convert\r\n */\r\nexport const toCamelCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => {\r\n      return index === 0 ? word.toLowerCase() : word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to PascalCase\r\n * @param str - String to convert\r\n */\r\nexport const toPascalCase = (str: string): string => {\r\n  return str\r\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word) => {\r\n      return word.toUpperCase();\r\n    })\r\n    .replace(/\\s+/g, '');\r\n};\r\n\r\n/**\r\n * Convert string to kebab-case\r\n * @param str - String to convert\r\n */\r\nexport const toKebabCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1-$2')\r\n    .replace(/[\\s_]+/g, '-')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to snake_case\r\n * @param str - String to convert\r\n */\r\nexport const toSnakeCase = (str: string): string => {\r\n  return str\r\n    .replace(/([a-z])([A-Z])/g, '$1_$2')\r\n    .replace(/[\\s-]+/g, '_')\r\n    .toLowerCase();\r\n};\r\n\r\n/**\r\n * Convert string to Title Case\r\n * @param str - String to convert\r\n */\r\nexport const toTitleCase = (str: string): string => {\r\n  return str.replace(/\\w\\S*/g, (txt) => {\r\n    return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();\r\n  });\r\n};\r\n\r\n/**\r\n * Convert string to Sentence case\r\n * @param str - String to convert\r\n */\r\nexport const toSentenceCase = (str: string): string => {\r\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\r\n};\r\n\r\n// ============================================================================\r\n// TEXT FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Truncate text with ellipsis\r\n * @param text - Text to truncate\r\n * @param maxLength - Maximum length before truncation\r\n * @param suffix - Suffix to add (default: '...')\r\n */\r\nexport const truncateText = (text: string, maxLength: number, suffix: string = '...'): string => {\r\n  if (!text || text.length <= maxLength) return text || '';\r\n  return text.substring(0, maxLength - suffix.length) + suffix;\r\n};\r\n\r\n/**\r\n * Capitalize first letter of each word\r\n * @param str - String to capitalize\r\n */\r\nexport const capitalizeWords = (str: string): string => {\r\n  return str.replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Remove extra whitespace and normalize spacing\r\n * @param str - String to normalize\r\n */\r\nexport const normalizeWhitespace = (str: string): string => {\r\n  return str.replace(/\\s+/g, ' ').trim();\r\n};\r\n\r\n/**\r\n * Extract initials from a name\r\n * @param name - Full name\r\n * @param maxInitials - Maximum number of initials (default: 2)\r\n */\r\nexport const getInitials = (name: string, maxInitials: number = 2): string => {\r\n  if (!name) return '';\r\n\r\n  return name\r\n    .split(' ')\r\n    .filter(word => word.length > 0)\r\n    .slice(0, maxInitials)\r\n    .map(word => word.charAt(0).toUpperCase())\r\n    .join('');\r\n};\r\n\r\n/**\r\n * Convert text to slug format (URL-friendly)\r\n * @param text - Text to convert\r\n */\r\nexport const toSlug = (text: string): string => {\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/[^\\w\\s-]/g, '') // Remove special characters\r\n    .replace(/[\\s_-]+/g, '-') // Replace spaces and underscores with hyphens\r\n    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens\r\n};\r\n\r\n/**\r\n * Highlight search terms in text\r\n * @param text - Text to highlight\r\n * @param searchTerm - Term to highlight\r\n * @param className - CSS class for highlighting (default: 'highlight')\r\n */\r\nexport const highlightText = (text: string, searchTerm: string, className: string = 'highlight'): string => {\r\n  if (!searchTerm) return text;\r\n\r\n  const regex = new RegExp(`(${searchTerm})`, 'gi');\r\n  return text.replace(regex, `<span class=\"${className}\">$1</span>`);\r\n};\r\n\r\n// ============================================================================\r\n// PHONE & EMAIL FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format phone number\r\n * @param phone - Phone number to format\r\n * @param format - Format type ('international' | 'national' | 'minimal')\r\n */\r\nexport const formatPhone = (phone: string, format: 'international' | 'national' | 'minimal' = 'national'): string => {\r\n  if (!phone) return '';\r\n\r\n  // Remove all non-digit characters\r\n  const digits = phone.replace(/\\D/g, '');\r\n\r\n  if (digits.length < 10) return phone; // Return original if too short\r\n\r\n  switch (format) {\r\n    case 'international':\r\n      return `+${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)} ${digits.slice(9)}`;\r\n    case 'national':\r\n      return `(${digits.slice(-10, -7)}) ${digits.slice(-7, -4)}-${digits.slice(-4)}`;\r\n    case 'minimal':\r\n      return `${digits.slice(-10, -7)}.${digits.slice(-7, -4)}.${digits.slice(-4)}`;\r\n    default:\r\n      return phone;\r\n  }\r\n};\r\n\r\n/**\r\n * Mask email for privacy (e.g., \"j***@example.com\")\r\n * @param email - Email to mask\r\n */\r\nexport const maskEmail = (email: string): string => {\r\n  if (!email || !email.includes('@')) return email;\r\n\r\n  const [username, domain] = email.split('@');\r\n  if (username.length <= 2) return email;\r\n\r\n  const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);\r\n  return `${maskedUsername}@${domain}`;\r\n};\r\n\r\n/**\r\n * Validate email format\r\n * @param email - Email to validate\r\n */\r\nexport const isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\n// ============================================================================\r\n// ID & REFERENCE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format application number with prefix\r\n * @param number - Application number\r\n * @param prefix - Prefix to add (default: 'APP')\r\n */\r\nexport const formatApplicationNumber = (number: string | number, prefix: string = 'APP'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format invoice number with prefix\r\n * @param number - Invoice number\r\n * @param prefix - Prefix to add (default: 'INV')\r\n */\r\nexport const formatInvoiceNumber = (number: string | number, prefix: string = 'INV'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Format task number with prefix\r\n * @param number - Task number\r\n * @param prefix - Prefix to add (default: 'TASK')\r\n */\r\nexport const formatTaskNumber = (number: string | number, prefix: string = 'TASK'): string => {\r\n  if (!number) return '';\r\n  return `${prefix}-${String(number).padStart(6, '0')}`;\r\n};\r\n\r\n/**\r\n * Generate a random reference ID\r\n * @param length - Length of the ID (default: 8)\r\n * @param prefix - Optional prefix\r\n */\r\nexport const generateReferenceId = (length: number = 8, prefix?: string): string => {\r\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n  let result = '';\r\n\r\n  for (let i = 0; i < length; i++) {\r\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\r\n  }\r\n\r\n  return prefix ? `${prefix}-${result}` : result;\r\n};\r\n\r\n/**\r\n * Convert UUID to user-friendly reference ID for customers\r\n * @param uuid - The UUID to convert\r\n * @param prefix - Optional prefix (default: 'REF')\r\n */\r\nexport const formatCustomerReferenceId = (uuid: string, prefix: string = 'REF'): string => {\r\n  if (!uuid) return '';\r\n\r\n  // Take first 8 characters of UUID (without hyphens) and convert to uppercase\r\n  const cleanUuid = uuid.replace(/-/g, '').toUpperCase();\r\n  const shortId = cleanUuid.substring(0, 8);\r\n\r\n  return `${prefix}-${shortId}`;\r\n};\r\n\r\n/**\r\n * Mask sensitive ID (show only first and last 2 characters)\r\n * @param id - ID to mask\r\n */\r\nexport const maskId = (id: string): string => {\r\n  if (!id || id.length <= 4) return id;\r\n\r\n  const start = id.slice(0, 2);\r\n  const end = id.slice(-2);\r\n  const middle = '*'.repeat(id.length - 4);\r\n\r\n  return `${start}${middle}${end}`;\r\n};\r\n\r\n// ============================================================================\r\n// STATUS & BADGE FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format status text for display\r\n * @param status - Status to format\r\n */\r\nexport const formatStatus = (status: string): string => {\r\n  if (!status) return '';\r\n\r\n  return status\r\n    .replace(/_/g, ' ')\r\n    .replace(/\\b\\w/g, char => char.toUpperCase());\r\n};\r\n\r\n/**\r\n * Get status color class\r\n * @param status - Status to get color for\r\n */\r\n\r\n\r\nexport const getStatusColor = (status: any): string => {\r\n  const statusLower = status.toLowerCase();\r\n\r\n  switch (statusLower) {\r\n    case 'active':\r\n    case 'approved':\r\n    case 'completed':\r\n    case 'paid':\r\n    case 'success':\r\n      return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';\r\n\r\n    case 'pending':\r\n    case 'in_progress':\r\n    case 'processing':\r\n    case 'review':\r\n      return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';\r\n\r\n    case 'rejected':\r\n    case 'failed':\r\n    case 'error':\r\n    case 'overdue':\r\n    case 'cancelled':\r\n      return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';\r\n\r\n    case 'draft':\r\n    case 'inactive':\r\n    case 'disabled':\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n\r\n    case 'warning':\r\n    case 'attention':\r\n      return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/20';\r\n\r\n    default:\r\n      return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';\r\n  }\r\n};\r\n\r\n// ============================================================================\r\n// FILE SIZE & VALIDATION FORMATTERS\r\n// ============================================================================\r\n\r\n/**\r\n * Format file size in human readable format\r\n * @param bytes - File size in bytes\r\n * @param decimals - Number of decimal places (default: 2)\r\n */\r\nexport const formatFileSize = (bytes: number, decimals: number = 2): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Get file extension from filename\r\n * @param filename - Filename to extract extension from\r\n */\r\nexport const getFileExtension = (filename: string): string => {\r\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);\r\n};\r\n\r\n/**\r\n * Get file type icon class based on extension\r\n * @param filename - Filename to get icon for\r\n */\r\nexport const getFileTypeIcon = (filename: string): string => {\r\n  const extension = getFileExtension(filename).toLowerCase();\r\n\r\n  switch (extension) {\r\n    case 'pdf':\r\n      return 'ri-file-pdf-line text-red-500';\r\n    case 'doc':\r\n    case 'docx':\r\n      return 'ri-file-word-line text-blue-500';\r\n    case 'xls':\r\n    case 'xlsx':\r\n      return 'ri-file-excel-line text-green-500';\r\n    case 'ppt':\r\n    case 'pptx':\r\n      return 'ri-file-ppt-line text-orange-500';\r\n    case 'jpg':\r\n    case 'jpeg':\r\n    case 'png':\r\n    case 'gif':\r\n    case 'bmp':\r\n      return 'ri-image-line text-purple-500';\r\n    case 'zip':\r\n    case 'rar':\r\n    case '7z':\r\n      return 'ri-file-zip-line text-yellow-500';\r\n    case 'txt':\r\n      return 'ri-file-text-line text-gray-500';\r\n    default:\r\n      return 'ri-file-line text-gray-500';\r\n  }\r\n};\r\n\r\nexport const formatHumanReadable = (text : string, caseType = 'first') => {\r\n  if (!text || typeof text !== 'string') return '';\r\n  \r\n  // Clean and normalize the text\r\n  let formatted = text\r\n    .trim()\r\n    .replace(/[-_]+/g, ' ') // Replace hyphens and underscores with spaces\r\n    .replace(/\\s+/g, ' ')  // Collapse multiple spaces\r\n    .toLowerCase();\r\n  \r\n  // Split into words\r\n  const words = formatted.split(' ');\r\n  \r\n  // Format based on caseType\r\n  switch (caseType.toLowerCase()) {\r\n    case 'lower':\r\n      return formatted;\r\n      \r\n    case 'upper':\r\n      return words\r\n        .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n        .join(' ');\r\n        \r\n    case 'first':\r\n    default:\r\n      return words\r\n        .map((word, index) => \r\n          index === 0 \r\n            ? word.charAt(0).toUpperCase() + word.slice(1)\r\n            : word\r\n        )\r\n        .join(' ');\r\n  }\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,iBAAiB,CAC5B,QACA,WAAmB,KAAK,EACxB,wBAAgC,CAAC;IAEjC,qCAAqC;IACrC,MAAM,gBAAgB,OAAO,WAAW,WAAW,WAAW,UAAU;IAExE,IAAG,YAAY,KAAK,WAAW;IAE/B,0BAA0B;IAC1B,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,aAAa;IACf;IAEA,iDAAiD;IACjD,MAAM,YAAY,UAAU,MAAM,CAAC;IAEnC,mEAAmE;IACnE,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,GAAG,QAAQ,aAAa,OAAO,GAAG;IAExC,yCAAyC;IACzC,IAAI,kBAAkB;IAEtB,iGAAiG;IACjG,IAAI,YAAY,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG;QAC9C,sCAAsC;QACtC,MAAM,CAAC,aAAa,YAAY,GAAG,YAAY,KAAK,CAAC;QAErD,sCAAsC;QACtC,8CAA8C;QAC9C,IAAI,mBAAmB,YAAY,KAAK,CAAC,GAAG,KAAK,MAAM,YAAY,KAAK,CAAC;QAEzE,4DAA4D;QAC5D,mBAAmB,iBAAiB,OAAO,CAAC,yBAAyB;QAErE,yBAAyB;QACzB,kBAAkB,mBAAmB,CAAC,cAAc,MAAM,cAAc,EAAE;IAC5E,OAAO;QACL,8DAA8D;QAC9D,kBAAkB,YAAY,OAAO,CAAC,yBAAyB;IACjE;IAEA,0BAA0B;IAC1B,OAAO,SAAS,kBAAkB;AACpC;AAYO,MAAM,eAAe,CAC1B,QACA,WAAmB,KAAK,EACxB,SAAiB,OAAO;IAExB,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAO,CAAC,MAAM,CAAC;IAErC,OAAO,GAAG,SAAS,CAAC,EAAE,UAAU,cAAc,CAAC,QAAQ;QACrD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAOO,MAAM,eAAe,CAAC,QAAyB,WAAmB,CAAC;IACxE,MAAM,YAAY,OAAO,WAAW,WAAW,WAAW,UAAU;IACpE,IAAI,MAAM,YAAY,OAAO;IAE7B,OAAO,UAAU,cAAc,CAAC,SAAS;QACvC,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAQO,MAAM,2BAA2B,CACtC,KACA;IAEA,6BAA6B;IAC7B,IAAI,CAAC,OAAO,QAAQ,OAAO,WAAW,IAAI,QAAQ,QAAQ,GAAG;QAC3D,wCAAwC;QACxC,OAAO;IACT;IACA,uCAAuC;IACvC,OAAO,eAAe;AACxB;AAOO,MAAM,mBAAmB,CAAC,OAAwB,WAAmB,CAAC;IAC3E,MAAM,WAAW,OAAO,UAAU,WAAW,WAAW,SAAS;IACjE,IAAI,MAAM,WAAW,OAAO;IAE5B,OAAO,GAAG,SAAS,OAAO,CAAC,UAAU,CAAC,CAAC;AACzC;AAaO,MAAM,aAAa,CACxB,YACA,UAAsC;IACpC,MAAM;IACN,OAAO;IACP,KAAK;AACP,CAAC;IAED,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS,SAAS,MAAM,CAAC;AAC1D;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAMO,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,GAAG,WAAW,MAAM,IAAI,EAAE,WAAW,OAAO;AACrD;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;IAEpE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,YAAY,CAAC;IAChF,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,UAAU,CAAC;IACjF,IAAI,gBAAgB,SAAS,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,SAAS,CAAC;IACnF,IAAI,gBAAgB,UAAU,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,SAAS,WAAW,CAAC;IAExF,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,UAAU,UAAU,CAAC;AAC5D;AAMO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,OAAO,eAAe,WAAW,IAAI,KAAK,cAAc;IACrE,IAAI,MAAM,KAAK,OAAO,KAAK,OAAO;IAElC,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AACzC;AAUO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC,MAAM;QACrC,OAAO,UAAU,IAAI,KAAK,WAAW,KAAK,KAAK,WAAW;IAC5D,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,eAAe,CAAC;IAC3B,OAAO,IACJ,OAAO,CAAC,uBAAuB,CAAC;QAC/B,OAAO,KAAK,WAAW;IACzB,GACC,OAAO,CAAC,QAAQ;AACrB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IACJ,OAAO,CAAC,mBAAmB,SAC3B,OAAO,CAAC,WAAW,KACnB,WAAW;AAChB;AAMO,MAAM,cAAc,CAAC;IAC1B,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC;QAC5B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;IAC/D;AACF;AAMO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;AAC/D;AAYO,MAAM,eAAe,CAAC,MAAc,WAAmB,SAAiB,KAAK;IAClF,IAAI,CAAC,QAAQ,KAAK,MAAM,IAAI,WAAW,OAAO,QAAQ;IACtD,OAAO,KAAK,SAAS,CAAC,GAAG,YAAY,OAAO,MAAM,IAAI;AACxD;AAMO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AACxD;AAMO,MAAM,sBAAsB,CAAC;IAClC,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI;AACtC;AAOO,MAAM,cAAc,CAAC,MAAc,cAAsB,CAAC;IAC/D,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,KAAK,CAAC,KACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAC7B,KAAK,CAAC,GAAG,aACT,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC;AACV;AAMO,MAAM,SAAS,CAAC;IACrB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IAAI,4BAA4B;KACrD,OAAO,CAAC,YAAY,KAAK,8CAA8C;KACvE,OAAO,CAAC,YAAY,KAAK,kCAAkC;AAChE;AAQO,MAAM,gBAAgB,CAAC,MAAc,YAAoB,YAAoB,WAAW;IAC7F,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE;IAC5C,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,WAAW,CAAC;AACnE;AAWO,MAAM,cAAc,CAAC,OAAe,SAAmD,UAAU;IACtG,IAAI,CAAC,OAAO,OAAO;IAEnB,kCAAkC;IAClC,MAAM,SAAS,MAAM,OAAO,CAAC,OAAO;IAEpC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO,OAAO,+BAA+B;IAErE,OAAQ;QACN,KAAK;YACH,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,IAAI;QAChG,KAAK;YACH,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI;QACjF,KAAK;YACH,OAAO,GAAG,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI;QAC/E;YACE,OAAO;IACX;AACF;AAMO,MAAM,YAAY,CAAC;IACxB,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM,OAAO;IAE3C,MAAM,CAAC,UAAU,OAAO,GAAG,MAAM,KAAK,CAAC;IACvC,IAAI,SAAS,MAAM,IAAI,GAAG,OAAO;IAEjC,MAAM,iBAAiB,SAAS,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC,SAAS,MAAM,GAAG;IAChH,OAAO,GAAG,eAAe,CAAC,EAAE,QAAQ;AACtC;AAMO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAWO,MAAM,0BAA0B,CAAC,QAAyB,SAAiB,KAAK;IACrF,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,MAAM;AACvD;AAOO,MAAM,sBAAsB,CAAC,QAAyB,SAAiB,KAAK;IACjF,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,MAAM;AACvD;AAOO,MAAM,mBAAmB,CAAC,QAAyB,SAAiB,MAAM;IAC/E,IAAI,CAAC,QAAQ,OAAO;IACpB,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,QAAQ,QAAQ,CAAC,GAAG,MAAM;AACvD;AAOO,MAAM,sBAAsB,CAAC,SAAiB,CAAC,EAAE;IACtD,MAAM,QAAQ;IACd,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IAEA,OAAO,SAAS,GAAG,OAAO,CAAC,EAAE,QAAQ,GAAG;AAC1C;AAOO,MAAM,4BAA4B,CAAC,MAAc,SAAiB,KAAK;IAC5E,IAAI,CAAC,MAAM,OAAO;IAElB,6EAA6E;IAC7E,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM,IAAI,WAAW;IACpD,MAAM,UAAU,UAAU,SAAS,CAAC,GAAG;IAEvC,OAAO,GAAG,OAAO,CAAC,EAAE,SAAS;AAC/B;AAMO,MAAM,SAAS,CAAC;IACrB,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,GAAG,OAAO;IAElC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG;IAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC;IACtB,MAAM,SAAS,IAAI,MAAM,CAAC,GAAG,MAAM,GAAG;IAEtC,OAAO,GAAG,QAAQ,SAAS,KAAK;AAClC;AAUO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OACJ,OAAO,CAAC,MAAM,KACd,OAAO,CAAC,SAAS,CAAA,OAAQ,KAAK,WAAW;AAC9C;AAQO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,cAAc,OAAO,WAAW;IAEtC,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QAET,KAAK;QACL,KAAK;YACH,OAAO;QAET;YACE,OAAO;IACX;AACF;AAWO,MAAM,iBAAiB,CAAC,OAAe,WAAmB,CAAC;IAChE,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAE/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAMO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAMO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,iBAAiB,UAAU,WAAW;IAExD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,sBAAsB,CAAC,MAAe,WAAW,OAAO;IACnE,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU,OAAO;IAE9C,+BAA+B;IAC/B,IAAI,YAAY,KACb,IAAI,GACJ,OAAO,CAAC,UAAU,KAAK,8CAA8C;KACrE,OAAO,CAAC,QAAQ,KAAM,2BAA2B;KACjD,WAAW;IAEd,mBAAmB;IACnB,MAAM,QAAQ,UAAU,KAAK,CAAC;IAE9B,2BAA2B;IAC3B,OAAQ,SAAS,WAAW;QAC1B,KAAK;YACH,OAAO;QAET,KAAK;YACH,OAAO,MACJ,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;QAEV,KAAK;QACL;YACE,OAAO,MACJ,GAAG,CAAC,CAAC,MAAM,QACV,UAAU,IACN,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,KAC1C,MAEL,IAAI,CAAC;IACZ;AACF", "debugId": null}}, {"offset": {"line": 2537, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/procurement/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport Loader from '@/components/Loader';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport TextInput from '@/components/forms/TextInput';\r\nimport Select from '@/components/forms/Select';\r\nimport { formatCurrency, formatDate } from '@/utils/formatters';\r\n\r\ninterface Tender {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  publishDate: string;\r\n  closingDate: string;\r\n  status: 'open' | 'closed' | 'awarded' | 'cancelled';\r\n  estimatedValue: number;\r\n  currency: string;\r\n  requirements: string[];\r\n  documents: TenderDocument[];\r\n  hasAccess: boolean;\r\n  paymentRequired: boolean;\r\n  accessFee: number;\r\n}\r\n\r\ninterface TenderDocument {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  size: number;\r\n  downloadUrl: string;\r\n}\r\n\r\ninterface Bid {\r\n  id: string;\r\n  tenderId: string;\r\n  status: 'draft' | 'submitted' | 'under_review' | 'accepted' | 'rejected';\r\n  submittedAt: string;\r\n  amount: number;\r\n  currency: string;\r\n  documents: BidDocument[];\r\n}\r\n\r\ninterface BidDocument {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n  size: number;\r\n  uploadedAt: string;\r\n}\r\n\r\n\r\n\r\ninterface Invoice {\r\n  invoiceNumber: string;\r\n  issueDate: string;\r\n  dueDate: string;\r\n  tenderId: string;\r\n  tenderTitle: string;\r\n  amount: number;\r\n  currency: string;\r\n  description: string;\r\n  paymentInstructions: {\r\n    bankName: string;\r\n    accountName: string;\r\n    accountNumber: string;\r\n    swiftCode: string;\r\n    reference: string;\r\n    generalInstructions?: string;\r\n  };\r\n}\r\n\r\n// Sample data - replace with actual API calls\r\nconst sampleTenders: Tender[] = [\r\n  {\r\n    id: 'TEN-2024-001',\r\n    title: 'IT Equipment and Software Procurement',\r\n    description: 'Procurement of office computers, servers, networking equipment, and software licenses for MACRA operations.',\r\n    category: 'Information Technology',\r\n    publishDate: '2024-01-01',\r\n    closingDate: '2024-02-15',\r\n    status: 'open',\r\n    estimatedValue: 2500000,\r\n    currency: 'MWK',\r\n    requirements: [\r\n      'Valid business registration certificate',\r\n      'Tax clearance certificate',\r\n      'Technical specifications compliance',\r\n      'Minimum 3 years experience in IT procurement',\r\n      'Financial capacity demonstration'\r\n    ],\r\n    documents: [\r\n      {\r\n        id: 'doc-1',\r\n        name: 'Technical Specifications.pdf',\r\n        type: 'application/pdf',\r\n        size: 1024000,\r\n        downloadUrl: '/api/documents/download/doc-1'\r\n      },\r\n      {\r\n        id: 'doc-2',\r\n        name: 'Bid Submission Template.pdf',\r\n        type: 'application/pdf',\r\n        size: 512000,\r\n        downloadUrl: '/api/documents/download/doc-2'\r\n      }\r\n    ],\r\n    hasAccess: false,\r\n    paymentRequired: true,\r\n    accessFee: 50000\r\n  },\r\n  {\r\n    id: 'TEN-2024-002',\r\n    title: 'Vehicle Fleet Management Services',\r\n    description: 'Comprehensive vehicle fleet management and maintenance services for MACRA vehicle fleet.',\r\n    category: 'Transportation',\r\n    publishDate: '2024-01-10',\r\n    closingDate: '2024-02-20',\r\n    status: 'open',\r\n    estimatedValue: 1800000,\r\n    currency: 'MWK',\r\n    requirements: [\r\n      'Valid business registration certificate',\r\n      'Insurance coverage proof',\r\n      'Fleet management experience',\r\n      'Qualified technical staff',\r\n      'Service level agreement compliance'\r\n    ],\r\n    documents: [\r\n      {\r\n        id: 'doc-3',\r\n        name: 'Service Requirements.pdf',\r\n        type: 'application/pdf',\r\n        size: 800000,\r\n        downloadUrl: '/api/documents/download/doc-3'\r\n      }\r\n    ],\r\n    hasAccess: true,\r\n    paymentRequired: false,\r\n    accessFee: 0\r\n  }\r\n];\r\n\r\nconst sampleBids: Bid[] = [\r\n  {\r\n    id: 'bid-1',\r\n    tenderId: 'TEN-2024-002',\r\n    status: 'submitted',\r\n    submittedAt: '2024-01-15T10:30:00Z',\r\n    amount: 1750000,\r\n    currency: 'MWK',\r\n    documents: [\r\n      {\r\n        id: 'bid-doc-1',\r\n        name: 'Technical Proposal.pdf',\r\n        type: 'application/pdf',\r\n        size: 2048000,\r\n        uploadedAt: '2024-01-15T10:25:00Z'\r\n      },\r\n      {\r\n        id: 'bid-doc-2',\r\n        name: 'Financial Proposal.pdf',\r\n        type: 'application/pdf',\r\n        size: 1024000,\r\n        uploadedAt: '2024-01-15T10:28:00Z'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n\r\n\r\nconst CustomerProcurementPage = () => {\r\n  const { isAuthenticated, loading  : authLoading } = useAuth();\r\n  const router = useRouter();\r\n\r\n  const [tenders, setTenders] = useState<Tender[]>([]);\r\n  const [myBids, setMyBids] = useState<Bid[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [activeTab, setActiveTab] = useState<'available' | 'my-bids'>('available');\r\n  const [selectedTender, setSelectedTender] = useState<Tender | null>(null);\r\n  const [showInvoiceModal, setShowInvoiceModal] = useState(false);\r\n  const [showBidModal, setShowBidModal] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filterCategory, setFilterCategory] = useState('all');\r\n  const [filterStatus, setFilterStatus] = useState('all');\r\n\r\n  // Redirect to customer login if not authenticated\r\n  useEffect(() => {\r\n    if (!authLoading && !isAuthenticated) {\r\n      router.push('/customer/auth/login');\r\n    }\r\n  }, [isAuthenticated, authLoading, router]);\r\n\r\n  // Fetch data\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      if (!isAuthenticated) return;\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        setError('');\r\n\r\n        // In a real implementation, these would be API calls\r\n        // const [tendersRes, bidsRes] = await Promise.allSettled([\r\n        //   customerApi.getTenders(),\r\n        //   customerApi.getMyBids()\r\n        // ]);\r\n\r\n        // For now, use sample data\r\n        setTenders(sampleTenders);\r\n        setMyBids(sampleBids);\r\n\r\n      } catch (err) {\r\n        console.error('Error fetching procurement data:', err);\r\n        setError('Failed to load procurement data. Please try refreshing the page.');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [isAuthenticated]);\r\n\r\n  // Filter tenders\r\n  const filteredTenders = tenders.filter(tender => {\r\n    const matchesSearch = tender.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                         tender.description.toLowerCase().includes(searchTerm.toLowerCase());\r\n    const matchesCategory = filterCategory === 'all' || tender.category === filterCategory;\r\n    const matchesStatus = filterStatus === 'all' || tender.status === filterStatus;\r\n    \r\n    return matchesSearch && matchesCategory && matchesStatus;\r\n  });\r\n\r\n  // Get unique categories\r\n  const categories = Array.from(new Set(tenders.map(t => t.category)));\r\n\r\n  const handlePayForAccess = async (tender: Tender) => {\r\n    setSelectedTender(tender);\r\n    setShowInvoiceModal(true);\r\n  };\r\n\r\n  const handleSubmitBid = (tender: Tender) => {\r\n    setSelectedTender(tender);\r\n    setShowBidModal(true);\r\n  };\r\n\r\n  const handleDownloadDocument = async (document: TenderDocument) => {\r\n    try {\r\n      // In a real implementation, this would download the file\r\n      console.log('Downloading document:', document.name);\r\n      // const blob = await customerApi.downloadTenderDocument(document.id);\r\n      // const url = window.URL.createObjectURL(blob);\r\n      // const a = document.createElement('a');\r\n      // a.href = url;\r\n      // a.download = document.name;\r\n      // a.click();\r\n    } catch (error) {\r\n      console.error('Error downloading document:', error);\r\n    }\r\n  };\r\n\r\n  const handleViewBidDocument = async (document: BidDocument) => {\r\n    try {\r\n      // In a real implementation, this would open the document in a new tab or modal\r\n      console.log('Viewing bid document:', document.name);\r\n      // const blob = await customerApi.viewBidDocument(document.id);\r\n      // const url = window.URL.createObjectURL(blob);\r\n      // window.open(url, '_blank');\r\n      \r\n      // For demo purposes, show an alert\r\n      alert(`Opening document: ${document.name}\\n\\nIn a real implementation, this would open the document in a new tab or viewer.`);\r\n    } catch (error) {\r\n      console.error('Error viewing document:', error);\r\n    }\r\n  };\r\n\r\n  // Using the imported formatCurrency and formatDate functions from utils\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'open': return 'bg-green-100 text-green-800';\r\n      case 'closed': return 'bg-gray-100 text-gray-800';\r\n      case 'awarded': return 'bg-blue-100 text-blue-800';\r\n      case 'cancelled': return 'bg-red-100 text-red-800';\r\n      case 'submitted': return 'bg-blue-100 text-blue-800';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800';\r\n      case 'accepted': return 'bg-green-100 text-green-800';\r\n      case 'rejected': return 'bg-red-100 text-red-800';\r\n      case 'draft': return 'bg-gray-100 text-gray-800';\r\n      default: return 'bg-gray-100 text-gray-800';\r\n    }\r\n  };\r\n\r\n  if (authLoading || isLoading) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <Loader message=\"Loading procurement data...\" />\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout>\r\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\">\r\n          <p>{error}</p>\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => window.location.reload()}\r\n            className=\"mt-2 text-sm underline hover:no-underline\"\r\n          >\r\n            Try again\r\n          </button>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Tabs */}\r\n        <div className=\"border-b border-gray-200 dark:border-gray-700 mb-6\">\r\n          <nav className=\"-mb-px flex space-x-8\">\r\n            {[\r\n              { key: 'available', label: 'Available Tenders', count: filteredTenders.length },\r\n              { key: 'my-bids', label: 'My Bids', count: myBids.length }\r\n            ].map((tab) => (\r\n              <button\r\n                key={tab.key}\r\n                onClick={() => setActiveTab(tab.key as 'available' | 'my-bids')}\r\n                className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${\r\n                  activeTab === tab.key\r\n                    ? 'border-primary text-primary'\r\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\r\n                }`}\r\n              >\r\n                {tab.label}\r\n                <span className=\"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\">\r\n                  {tab.count}\r\n                </span>\r\n              </button>\r\n            ))}\r\n          </nav>\r\n        </div>\r\n\r\n        {/* Available Tenders Tab */}\r\n        {activeTab === 'available' && (\r\n          <div>\r\n            {/* Filters */}\r\n            <div className=\"bg-white dark:bg-gray-800 p-4 rounded-lg shadow mb-6\">\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                <TextInput\r\n                  label=\"Search\"\r\n                  type=\"text\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  placeholder=\"Search tenders...\"\r\n                />\r\n                <Select\r\n                  label=\"Category\"\r\n                  value={filterCategory}\r\n                  onChange={(e) => setFilterCategory(e.target.value)}\r\n                >\r\n                  <option value=\"all\">All Categories</option>\r\n                  {categories.map(category => (\r\n                    <option key={category} value={category}>{category}</option>\r\n                  ))}\r\n                </Select>\r\n                <Select\r\n                  label=\"Status\"\r\n                  value={filterStatus}\r\n                  onChange={(e) => setFilterStatus(e.target.value)}\r\n                >\r\n                  <option value=\"all\">All Status</option>\r\n                  <option value=\"open\">Open</option>\r\n                  <option value=\"closed\">Closed</option>\r\n                  <option value=\"awarded\">Awarded</option>\r\n                  <option value=\"cancelled\">Cancelled</option>\r\n                </Select>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Tenders List */}\r\n            <div className=\"space-y-6\">\r\n              {filteredTenders.map((tender) => (\r\n                <div key={tender.id} className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n                  <div className=\"flex justify-between items-start mb-4\">\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex items-center gap-3 mb-2\">\r\n                        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                          {tender.title}\r\n                        </h3>\r\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(tender.status)}`}>\r\n                          {tender.status.charAt(0).toUpperCase() + tender.status.slice(1)}\r\n                        </span>\r\n                      </div>\r\n                      <p className=\"text-gray-600 dark:text-gray-400 mb-2\">{tender.description}</p>\r\n                      <div className=\"flex flex-wrap gap-4 text-sm text-gray-500 dark:text-gray-400\">\r\n                        <span>Category: {tender.category}</span>\r\n                        <span>Estimated Value: {formatCurrency(tender.estimatedValue, tender.currency)}</span>\r\n                        <span>Published: {formatDate(tender.publishDate)}</span>\r\n                        <span>Closes: {formatDate(tender.closingDate)}</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Requirements */}\r\n                  <div className=\"mb-4\">\r\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">Requirements:</h4>\r\n                    <ul className=\"list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1\">\r\n                      {tender.requirements.map((req, index) => (\r\n                        <li key={index}>{req}</li>\r\n                      ))}\r\n                    </ul>\r\n                  </div>\r\n\r\n                  {/* Documents */}\r\n                  {tender.hasAccess ? (\r\n                    <div className=\"mb-4\">\r\n                      <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">Documents:</h4>\r\n                      <div className=\"space-y-2\">\r\n                        {tender.documents.map((doc) => (\r\n                          <div key={doc.id} className=\"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded\">\r\n                            <div className=\"flex items-center\">\r\n                              <i className=\"ri-file-pdf-line text-red-500 mr-2\"></i>\r\n                              <span className=\"text-sm text-gray-700 dark:text-gray-300\">{doc.name}</span>\r\n                              <span className=\"text-xs text-gray-500 ml-2\">\r\n                                ({(doc.size / 1024).toFixed(0)} KB)\r\n                              </span>\r\n                            </div>\r\n                            <button\r\n                              onClick={() => handleDownloadDocument(doc)}\r\n                              className=\"text-primary hover:text-red-700 text-sm font-medium\"\r\n                            >\r\n                              Download\r\n                            </button>\r\n                          </div>\r\n                        ))}\r\n                      </div>\r\n                    </div>\r\n                  ) : tender.paymentRequired && (\r\n                    <div className=\"mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded\">\r\n                      <div className=\"flex items-center\">\r\n                        <i className=\"ri-lock-line text-yellow-600 mr-2\"></i>\r\n                        <span className=\"text-sm text-yellow-800 dark:text-yellow-200\">\r\n                          Payment of {formatCurrency(tender.accessFee, tender.currency)} required to access tender documents\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Actions */}\r\n                  <div className=\"flex gap-3\">\r\n                    {!tender.hasAccess && tender.paymentRequired ? (\r\n                      <button\r\n                        onClick={() => handlePayForAccess(tender)}\r\n                        className=\"px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 text-sm font-medium transition-colors duration-300\"\r\n                      >\r\n                        Pay for Access ({formatCurrency(tender.accessFee, tender.currency)})\r\n                      </button>\r\n                    ) : tender.hasAccess && tender.status === 'open' ? (\r\n                      <button\r\n                        onClick={() => handleSubmitBid(tender)}\r\n                        className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-sm font-medium\"\r\n                      >\r\n                        Submit Bid\r\n                      </button>\r\n                    ) : null}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n\r\n              {filteredTenders.length === 0 && (\r\n                <div className=\"text-center py-12\">\r\n                  <i className=\"ri-file-list-3-line text-4xl text-gray-400 mb-4\"></i>\r\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No tenders found</h3>\r\n                  <p className=\"text-gray-500 dark:text-gray-400\">\r\n                    {searchTerm || filterCategory !== 'all' || filterStatus !== 'all'\r\n                      ? 'Try adjusting your search criteria.'\r\n                      : 'There are no tenders available at the moment.'}\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* My Bids Tab */}\r\n        {activeTab === 'my-bids' && (\r\n          <div className=\"space-y-6\">\r\n            {myBids.map((bid) => {\r\n              const tender = tenders.find(t => t.id === bid.tenderId);\r\n              return (\r\n                <div key={bid.id} className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n                  <div className=\"flex justify-between items-start mb-4\">\r\n                    <div>\r\n                      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1\">\r\n                        {tender?.title || 'Unknown Tender'}\r\n                      </h3>\r\n                      <div className=\"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400\">\r\n                        <span>Bid Amount: {formatCurrency(bid.amount, bid.currency)}</span>\r\n                        <span>Submitted: {formatDate(bid.submittedAt)}</span>\r\n                      </div>\r\n                    </div>\r\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(bid.status)}`}>\r\n                      {bid.status.replace('_', ' ').charAt(0).toUpperCase() + bid.status.replace('_', ' ').slice(1)}\r\n                    </span>\r\n                  </div>\r\n\r\n                  <div className=\"mb-4\">\r\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">Submitted Documents:</h4>\r\n                    <div className=\"space-y-2\">\r\n                      {bid.documents.map((doc) => (\r\n                        <div key={doc.id} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600\">\r\n                          <div className=\"flex items-center flex-1\">\r\n                            <i className=\"ri-file-pdf-line text-red-500 mr-3 text-lg\"></i>\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"flex items-center\">\r\n                                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{doc.name}</span>\r\n                                <span className=\"text-xs text-gray-500 ml-2 bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded\">\r\n                                  {(doc.size / 1024).toFixed(0)} KB\r\n                                </span>\r\n                              </div>\r\n                              <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                                Uploaded: {formatDate(doc.uploadedAt)}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                          <button\r\n                            onClick={() => handleViewBidDocument(doc)}\r\n                            className=\"ml-3 px-3 py-1.5 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 text-xs font-medium transition-colors duration-300\"\r\n                          >\r\n                            <i className=\"ri-eye-line mr-1\"></i>\r\n                            View\r\n                          </button>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n\r\n            {myBids.length === 0 && (\r\n              <div className=\"text-center py-12\">\r\n                <i className=\"ri-file-text-line text-4xl text-gray-400 mb-4\"></i>\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No bids submitted</h3>\r\n                <p className=\"text-gray-500 dark:text-gray-400\">\r\n                  You haven&apos;t submitted any bids yet. Browse available tenders to get started.\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n\r\n\r\n        {/* Invoice Modal */}\r\n        {showInvoiceModal && selectedTender && (\r\n          <InvoiceModal\r\n            tender={selectedTender}\r\n            onClose={() => {\r\n              setShowInvoiceModal(false);\r\n              setSelectedTender(null);\r\n            }}\r\n            onInvoiceGenerated={() => {\r\n              // Close modal after invoice is generated\r\n              setShowInvoiceModal(false);\r\n              setSelectedTender(null);\r\n            }}\r\n          />\r\n        )}\r\n\r\n        {/* Bid Submission Modal */}\r\n        {showBidModal && selectedTender && (\r\n          <BidSubmissionModal\r\n            tender={selectedTender}\r\n            onClose={() => {\r\n              setShowBidModal(false);\r\n              setSelectedTender(null);\r\n            }}\r\n            onBidSubmitted={() => {\r\n              // Refresh data after bid submission\r\n              window.location.reload();\r\n            }}\r\n          />\r\n        )}\r\n      </div>\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\n// Invoice Modal Component\r\nconst InvoiceModal: React.FC<{\r\n  tender: Tender;\r\n  onClose: () => void;\r\n  onInvoiceGenerated: () => void;\r\n}> = ({ tender, onClose, onInvoiceGenerated }) => {\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [invoiceGenerated, setInvoiceGenerated] = useState(false);\r\n  const [invoiceData, setInvoiceData] = useState<Invoice | null>(null);\r\n\r\n  const generateInvoice = async () => {\r\n    setIsGenerating(true);\r\n    try {\r\n      // In a real implementation, this would call an API to generate the invoice\r\n      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call\r\n      \r\n      const invoice = {\r\n        invoiceNumber: `INV-${Date.now()}`,\r\n        issueDate: new Date().toISOString().split('T')[0],\r\n        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now\r\n        tenderId: tender.id,\r\n        tenderTitle: tender.title,\r\n        amount: tender.accessFee,\r\n        currency: tender.currency,\r\n        description: `Tender document access fee for ${tender.title}`,\r\n        paymentInstructions: {\r\n          bankName: '',\r\n          accountName: 'Malawi Communications Regulatory Authority (MACRA)',\r\n          accountNumber: '',\r\n          swiftCode: '',\r\n          reference: `TENDER-${tender.id}`,\r\n          generalInstructions: 'You can make this payment at any bank in Malawi. Please use the invoice number and reference when making payment. Mobile money payments are also accepted.'\r\n        }\r\n      };\r\n      \r\n      setInvoiceData(invoice);\r\n      setInvoiceGenerated(true);\r\n      // Don't call onInvoiceGenerated() here - let user see the invoice first\r\n    } catch (error) {\r\n      console.error('Invoice generation failed:', error);\r\n    } finally {\r\n      setIsGenerating(false);\r\n    }\r\n  };\r\n\r\n  const downloadInvoice = () => {\r\n    // In a real implementation, this would download a PDF invoice\r\n    console.log('Downloading invoice:', invoiceData);\r\n    console.log('Invoice downloaded successfully! Please check your downloads folder.');\r\n  };\r\n\r\n  const formatCurrency = (amount: number, currency: string) => {\r\n    return new Intl.NumberFormat('en-MW', {\r\n      style: 'currency',\r\n      currency: currency === 'MWK' ? 'MWK' : 'USD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\r\n        <div className=\"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            {invoiceGenerated ? 'Invoice Generated' : 'Generate Invoice for Tender Access'}\r\n          </h3>\r\n          <button\r\n            onClick={() => {\r\n              if (invoiceGenerated) {\r\n                onInvoiceGenerated(); // Call callback when user closes after seeing invoice\r\n              }\r\n              onClose();\r\n            }}\r\n            aria-label=\"Close invoice modal\"\r\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n          >\r\n            <i className=\"ri-close-line text-xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"p-6\">\r\n          {!invoiceGenerated ? (\r\n            // Invoice Generation Form\r\n            <div>\r\n              <div className=\"mb-6\">\r\n                <h4 className=\"font-medium text-gray-900 dark:text-gray-100 mb-2\">{tender.title}</h4>\r\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n                  To access the tender documents, you need to pay an access fee of {formatCurrency(tender.accessFee, tender.currency)}.\r\n                  Click the button below to generate an invoice that you can use to make the payment.\r\n                </p>\r\n                \r\n                <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4\">\r\n                  <div className=\"flex items-start\">\r\n                    <i className=\"ri-information-line text-blue-600 dark:text-blue-400 mt-0.5 mr-3\"></i>\r\n                    <div>\r\n                      <h5 className=\"text-sm font-medium text-blue-800 dark:text-blue-300 mb-1\">Payment Instructions</h5>\r\n                      <p className=\"text-sm text-blue-700 dark:text-blue-400\">\r\n                        Once you generate the invoice, you can make payment at any bank, through mobile money, or visit any MACRA office. \r\n                        After payment confirmation, you will receive access to the tender documents.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex gap-3\">\r\n                <button\r\n                  onClick={onClose}\r\n                  className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  onClick={generateInvoice}\r\n                  disabled={isGenerating}\r\n                  className=\"flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300\"\r\n                >\r\n                  {isGenerating ? (\r\n                    <>\r\n                      <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\r\n                      Generating...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <i className=\"ri-file-text-line mr-2\"></i>\r\n                      Generate Invoice\r\n                    </>\r\n                  )}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            // Invoice Display\r\n            <div>\r\n              <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6\">\r\n                <div className=\"flex items-center\">\r\n                  <i className=\"ri-check-circle-line text-green-600 dark:text-green-400 mr-3\"></i>\r\n                  <div>\r\n                    <h5 className=\"text-sm font-medium text-green-800 dark:text-green-300\">Invoice Generated Successfully</h5>\r\n                    <p className=\"text-sm text-green-700 dark:text-green-400\">\r\n                      Your invoice has been generated. Please download it and proceed with payment.\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Invoice Details */}\r\n              <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-6\">\r\n                {/* Invoice Header */}\r\n                <div className=\"text-center border-b border-gray-200 dark:border-gray-700 pb-4 mb-4\">\r\n                  <h4 className=\"text-lg font-bold text-gray-900 dark:text-gray-100\">INVOICE</h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Malawi Communications Regulatory Authority</p>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">Procurement Services</p>\r\n                </div>\r\n                <div className=\"grid grid-cols-2 gap-4 mb-4\">\r\n                  <div>\r\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">Invoice Number</label>\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{invoiceData?.invoiceNumber}</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">Issue Date</label>\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{invoiceData?.issueDate}</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">Due Date</label>\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{invoiceData?.dueDate}</p>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">Amount</label>\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{invoiceData ? formatCurrency(invoiceData.amount, invoiceData.currency) : '-'}</p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"mb-4\">\r\n                  <label className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">Description</label>\r\n                  <p className=\"text-sm text-gray-700 dark:text-gray-300\">{invoiceData?.description}</p>\r\n                </div>\r\n\r\n                <div className=\"border-t border-gray-200 dark:border-gray-700 pt-4\">\r\n                  <h6 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">Payment Instructions</h6>\r\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-2\">\r\n                    <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3\">\r\n                      <p className=\"text-blue-800 dark:text-blue-300 font-medium mb-1\">\r\n                        {invoiceData?.paymentInstructions.generalInstructions}\r\n                      </p>\r\n                      <div className=\"space-y-1 text-blue-700 dark:text-blue-400\">\r\n                        <p><strong>Payee:</strong> {invoiceData?.paymentInstructions.accountName}</p>\r\n                        <p><strong>Invoice Number:</strong> {invoiceData?.invoiceNumber}</p>\r\n                        <p><strong>Reference:</strong> {invoiceData?.paymentInstructions.reference}</p>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"mt-3\">\r\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                        <i className=\"ri-information-line mr-1\"></i>\r\n                        Please keep your payment receipt as proof of payment. Access to tender documents will be granted after payment verification.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex gap-3\">\r\n                <button\r\n                  onClick={() => {\r\n                    onInvoiceGenerated(); // Call callback when user closes after seeing invoice\r\n                    onClose();\r\n                  }}\r\n                  className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-300\"\r\n                >\r\n                  Close\r\n                </button>\r\n                <button\r\n                  onClick={downloadInvoice}\r\n                  className=\"flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300\"\r\n                >\r\n                  <i className=\"ri-download-line mr-2\"></i>\r\n                  Download Invoice\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Bid Submission Modal Component\r\nconst BidSubmissionModal: React.FC<{\r\n  tender: Tender;\r\n  onClose: () => void;\r\n  onBidSubmitted: () => void;\r\n}> = ({ tender, onClose, onBidSubmitted }) => {\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [bidAmount, setBidAmount] = useState('');\r\n  const [technicalProposal, setTechnicalProposal] = useState<File | null>(null);\r\n  const [financialProposal, setFinancialProposal] = useState<File | null>(null);\r\n  const [companyProfile, setCompanyProfile] = useState<File | null>(null);\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  const validateForm = () => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    if (!bidAmount || parseFloat(bidAmount) <= 0) {\r\n      newErrors.bidAmount = 'Please enter a valid bid amount';\r\n    }\r\n\r\n    if (!technicalProposal) {\r\n      newErrors.technicalProposal = 'Technical proposal is required';\r\n    } else if (technicalProposal.type !== 'application/pdf') {\r\n      newErrors.technicalProposal = 'Only PDF files are allowed';\r\n    }\r\n\r\n    if (!financialProposal) {\r\n      newErrors.financialProposal = 'Financial proposal is required';\r\n    } else if (financialProposal.type !== 'application/pdf') {\r\n      newErrors.financialProposal = 'Only PDF files are allowed';\r\n    }\r\n\r\n    if (!companyProfile) {\r\n      newErrors.companyProfile = 'Company profile is required';\r\n    } else if (companyProfile.type !== 'application/pdf') {\r\n      newErrors.companyProfile = 'Only PDF files are allowed';\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleFileChange = (file: File | null, field: string) => {\r\n    if (file && file.type !== 'application/pdf') {\r\n      setErrors(prev => ({ ...prev, [field]: 'Only PDF files are allowed' }));\r\n      return;\r\n    }\r\n    \r\n    if (file && file.size > 10 * 1024 * 1024) { // 10MB limit\r\n      setErrors(prev => ({ ...prev, [field]: 'File size must be less than 10MB' }));\r\n      return;\r\n    }\r\n\r\n    setErrors(prev => ({ ...prev, [field]: '' }));\r\n    \r\n    switch (field) {\r\n      case 'technicalProposal':\r\n        setTechnicalProposal(file);\r\n        break;\r\n      case 'financialProposal':\r\n        setFinancialProposal(file);\r\n        break;\r\n      case 'companyProfile':\r\n        setCompanyProfile(file);\r\n        break;\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!validateForm()) return;\r\n\r\n    setIsSubmitting(true);\r\n    try {\r\n      // In a real implementation, this would submit the bid\r\n      const formData = new FormData();\r\n      formData.append('tenderId', tender.id);\r\n      formData.append('bidAmount', bidAmount);\r\n      if (technicalProposal) formData.append('technicalProposal', technicalProposal);\r\n      if (financialProposal) formData.append('financialProposal', financialProposal);\r\n      if (companyProfile) formData.append('companyProfile', companyProfile);\r\n\r\n      // await customerApi.submitBid(formData);\r\n      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call\r\n      \r\n      onBidSubmitted();\r\n    } catch (error) {\r\n      console.error('Bid submission failed:', error);\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const formatCurrency = (amount: number, currency: string) => {\r\n    return new Intl.NumberFormat('en-MW', {\r\n      style: 'currency',\r\n      currency: currency === 'MWK' ? 'MWK' : 'USD',\r\n      minimumFractionDigits: 0\r\n    }).format(amount);\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\r\n        <div className=\"flex justify-between items-center mb-4\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            Submit Bid\r\n          </h3>\r\n          <button\r\n            onClick={onClose}\r\n            aria-label=\"Close modal\"\r\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n          >\r\n            <i className=\"ri-close-line text-xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"mb-4\">\r\n          <h4 className=\"font-medium text-gray-900 dark:text-gray-100 mb-2\">{tender.title}</h4>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\r\n            Estimated Value: {formatCurrency(tender.estimatedValue, tender.currency)}\r\n          </p>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n            Closing Date: {new Date(tender.closingDate).toLocaleDateString()}\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"space-y-4\">\r\n          {/* Bid Amount */}\r\n          <TextInput\r\n            label=\"Bid Amount (MWK) *\"\r\n            type=\"number\"\r\n            value={bidAmount}\r\n            onChange={(e) => setBidAmount(e.target.value)}\r\n            placeholder=\"Enter your bid amount\"\r\n            error={errors.bidAmount}\r\n            required\r\n          />\r\n\r\n          {/* Technical Proposal */}\r\n          <div>\r\n            <label htmlFor=\"technical-proposal\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n              Technical Proposal (PDF only) *\r\n            </label>\r\n            <input\r\n              id=\"technical-proposal\"\r\n              type=\"file\"\r\n              accept=\".pdf\"\r\n              onChange={(e) => handleFileChange(e.target.files?.[0] || null, 'technicalProposal')}\r\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100 ${\r\n                errors.technicalProposal ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\r\n              }`}\r\n            />\r\n            {errors.technicalProposal && (\r\n              <p className=\"text-red-500 text-xs mt-1\">{errors.technicalProposal}</p>\r\n            )}\r\n            <p className=\"text-xs text-gray-500 mt-1\">\r\n              Upload your technical proposal detailing how you will deliver the services/goods.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Financial Proposal */}\r\n          <div>\r\n            <label htmlFor=\"financial-proposal\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n              Financial Proposal (PDF only) *\r\n            </label>\r\n            <input\r\n              id=\"financial-proposal\"\r\n              type=\"file\"\r\n              accept=\".pdf\"\r\n              onChange={(e) => handleFileChange(e.target.files?.[0] || null, 'financialProposal')}\r\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100 ${\r\n                errors.financialProposal ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\r\n              }`}\r\n            />\r\n            {errors.financialProposal && (\r\n              <p className=\"text-red-500 text-xs mt-1\">{errors.financialProposal}</p>\r\n            )}\r\n            <p className=\"text-xs text-gray-500 mt-1\">\r\n              Upload your detailed cost breakdown and pricing structure.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Company Profile */}\r\n          <div>\r\n            <label htmlFor=\"company-profile\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\r\n              Company Profile (PDF only) *\r\n            </label>\r\n            <input\r\n              id=\"company-profile\"\r\n              type=\"file\"\r\n              accept=\".pdf\"\r\n              onChange={(e) => handleFileChange(e.target.files?.[0] || null, 'companyProfile')}\r\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100 ${\r\n                errors.companyProfile ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\r\n              }`}\r\n            />\r\n            {errors.companyProfile && (\r\n              <p className=\"text-red-500 text-xs mt-1\">{errors.companyProfile}</p>\r\n            )}\r\n            <p className=\"text-xs text-gray-500 mt-1\">\r\n              Upload your company profile including certifications, experience, and references.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded\">\r\n          <div className=\"flex items-start\">\r\n            <i className=\"ri-information-line text-yellow-600 mr-2 mt-0.5\"></i>\r\n            <div className=\"text-sm text-yellow-800 dark:text-yellow-200\">\r\n              <p className=\"font-medium mb-1\">Important Notes:</p>\r\n              <ul className=\"list-disc list-inside space-y-1\">\r\n                <li>All documents must be in PDF format</li>\r\n                <li>Maximum file size: 10MB per document</li>\r\n                <li>Ensure all required documents are complete and accurate</li>\r\n                <li>Bids cannot be modified after submission</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex gap-3 mt-6\">\r\n          <button\r\n            onClick={onClose}\r\n            className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button\r\n            onClick={handleSubmit}\r\n            disabled={isSubmitting}\r\n            className=\"flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            {isSubmitting ? 'Submitting...' : 'Submit Bid'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerProcurementPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AA2EA,8CAA8C;AAC9C,MAAM,gBAA0B;IAC9B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,aAAa;QACb,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;QACD,WAAW;QACX,iBAAiB;QACjB,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,aAAa;QACb,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;QACD,WAAW;QACX,iBAAiB;QACjB,WAAW;IACb;CACD;AAED,MAAM,aAAoB;IACxB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,UAAU;QACV,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,YAAY;YACd;SACD;IACH;CACD;AAID,MAAM,0BAA0B;IAC9B,MAAM,EAAE,eAAe,EAAE,SAAW,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;YACpC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI,CAAC,iBAAiB;YAEtB,IAAI;gBACF,aAAa;gBACb,SAAS;gBAET,qDAAqD;gBACrD,2DAA2D;gBAC3D,8BAA8B;gBAC9B,4BAA4B;gBAC5B,MAAM;gBAEN,2BAA2B;gBAC3B,WAAW;gBACX,UAAU;YAEZ,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAgB;IAEpB,iBAAiB;IACjB,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA;QACrC,MAAM,gBAAgB,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACrF,MAAM,kBAAkB,mBAAmB,SAAS,OAAO,QAAQ,KAAK;QACxE,MAAM,gBAAgB,iBAAiB,SAAS,OAAO,MAAM,KAAK;QAElE,OAAO,iBAAiB,mBAAmB;IAC7C;IAEA,wBAAwB;IACxB,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IAEjE,MAAM,qBAAqB,OAAO;QAChC,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,kBAAkB,CAAC;QACvB,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,yDAAyD;YACzD,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;QAClD,sEAAsE;QACtE,gDAAgD;QAChD,yCAAyC;QACzC,gBAAgB;QAChB,8BAA8B;QAC9B,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,IAAI;YACF,+EAA+E;YAC/E,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;YAClD,+DAA+D;YAC/D,gDAAgD;YAChD,8BAA8B;YAE9B,mCAAmC;YACnC,MAAM,CAAC,kBAAkB,EAAE,SAAS,IAAI,CAAC,kFAAkF,CAAC;QAC9H,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,wEAAwE;IAExE,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,eAAe,WAAW;QAC5B,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;oBAAC,SAAQ;;;;;;;;;;;;;;;;IAIxB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAG;;;;;;kCACJ,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,KAAK;gCAAa,OAAO;gCAAqB,OAAO,gBAAgB,MAAM;4BAAC;4BAC9E;gCAAE,KAAK;gCAAW,OAAO;gCAAW,OAAO,OAAO,MAAM;4BAAC;yBAC1D,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,GAAG;gCACnC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,GAAG,GACjB,gCACA,0HACJ;;oCAED,IAAI,KAAK;kDACV,8OAAC;wCAAK,WAAU;kDACb,IAAI,KAAK;;;;;;;+BAVP,IAAI,GAAG;;;;;;;;;;;;;;;gBAkBnB,cAAc,6BACb,8OAAC;;sCAEC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wIAAA,CAAA,UAAS;wCACR,OAAM;wCACN,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;;;;;;kDAEd,8OAAC,qIAAA,CAAA,UAAM;wCACL,OAAM;wCACN,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;;0DAEjD,8OAAC;gDAAO,OAAM;0DAAM;;;;;;4CACnB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;oDAAsB,OAAO;8DAAW;mDAA5B;;;;;;;;;;;kDAGjB,8OAAC,qIAAA,CAAA,UAAM;wCACL,OAAM;wCACN,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;;0DAE/C,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;gCACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,OAAO,KAAK;;;;;;8EAEf,8OAAC;oEAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,OAAO,MAAM,GAAG;8EAC3F,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;sEAGjE,8OAAC;4DAAE,WAAU;sEAAyC,OAAO,WAAW;;;;;;sEACxE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAK;wEAAW,OAAO,QAAQ;;;;;;;8EAChC,8OAAC;;wEAAK;wEAAkB,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,cAAc,EAAE,OAAO,QAAQ;;;;;;;8EAC7E,8OAAC;;wEAAK;wEAAY,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,OAAO,WAAW;;;;;;;8EAC/C,8OAAC;;wEAAK;wEAAS,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;;0DAMlD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAC1E,8OAAC;wDAAG,WAAU;kEACX,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC7B,8OAAC;0EAAgB;+DAAR;;;;;;;;;;;;;;;;4CAMd,OAAO,SAAS,iBACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAC1E,8OAAC;wDAAI,WAAU;kEACZ,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,oBACrB,8OAAC;gEAAiB,WAAU;;kFAC1B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAK,WAAU;0FAA4C,IAAI,IAAI;;;;;;0FACpE,8OAAC;gFAAK,WAAU;;oFAA6B;oFACzC,CAAC,IAAI,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;oFAAG;;;;;;;;;;;;;kFAGnC,8OAAC;wEACC,SAAS,IAAM,uBAAuB;wEACtC,WAAU;kFACX;;;;;;;+DAXO,IAAI,EAAE;;;;;;;;;;;;;;;2FAkBpB,OAAO,eAAe,kBACxB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;;;;;sEACb,8OAAC;4DAAK,WAAU;;gEAA+C;gEACjD,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS,EAAE,OAAO,QAAQ;gEAAE;;;;;;;;;;;;;;;;;;0DAOtE,8OAAC;gDAAI,WAAU;0DACZ,CAAC,OAAO,SAAS,IAAI,OAAO,eAAe,iBAC1C,8OAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,WAAU;;wDACX;wDACkB,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,SAAS,EAAE,OAAO,QAAQ;wDAAE;;;;;;+FAEnE,OAAO,SAAS,IAAI,OAAO,MAAM,KAAK,uBACxC,8OAAC;oDACC,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;8DACX;;;;;+FAGC;;;;;;;uCAlFE,OAAO,EAAE;;;;;gCAuFpB,gBAAgB,MAAM,KAAK,mBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAC1E,8OAAC;4CAAE,WAAU;sDACV,cAAc,mBAAmB,SAAS,iBAAiB,QACxD,wCACA;;;;;;;;;;;;;;;;;;;;;;;;gBASf,cAAc,2BACb,8OAAC;oBAAI,WAAU;;wBACZ,OAAO,GAAG,CAAC,CAAC;4BACX,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,QAAQ;4BACtD,qBACE,8OAAC;gCAAiB,WAAU;;kDAC1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,QAAQ,SAAS;;;;;;kEAEpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAK;oEAAa,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,MAAM,EAAE,IAAI,QAAQ;;;;;;;0EAC1D,8OAAC;;oEAAK;oEAAY,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,IAAI,WAAW;;;;;;;;;;;;;;;;;;;0DAGhD,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,IAAI,MAAM,GAAG;0DACxF,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;;;;;;;;;;;;kDAI/F,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,8OAAC;gDAAI,WAAU;0DACZ,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,oBAClB,8OAAC;wDAAiB,WAAU;;0EAC1B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;;;;;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAwD,IAAI,IAAI;;;;;;kGAChF,8OAAC;wFAAK,WAAU;;4FACb,CAAC,IAAI,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;4FAAG;;;;;;;;;;;;;0FAGlC,8OAAC;gFAAK,WAAU;;oFAA2C;oFAC9C,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE,IAAI,UAAU;;;;;;;;;;;;;;;;;;;0EAI1C,8OAAC;gEACC,SAAS,IAAM,sBAAsB;gEACrC,WAAU;;kFAEV,8OAAC;wEAAE,WAAU;;;;;;oEAAuB;;;;;;;;uDAnB9B,IAAI,EAAE;;;;;;;;;;;;;;;;;+BApBd,IAAI,EAAE;;;;;wBAgDpB;wBAEC,OAAO,MAAM,KAAK,mBACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAC1E,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;gBAWvD,oBAAoB,gCACnB,8OAAC;oBACC,QAAQ;oBACR,SAAS;wBACP,oBAAoB;wBACpB,kBAAkB;oBACpB;oBACA,oBAAoB;wBAClB,yCAAyC;wBACzC,oBAAoB;wBACpB,kBAAkB;oBACpB;;;;;;gBAKH,gBAAgB,gCACf,8OAAC;oBACC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,kBAAkB;oBACpB;oBACA,gBAAgB;wBACd,oCAAoC;wBACpC,OAAO,QAAQ,CAAC,MAAM;oBACxB;;;;;;;;;;;;;;;;;AAMZ;AAEA,0BAA0B;AAC1B,MAAM,eAID,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,kBAAkB,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAE/D,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,IAAI;YACF,2EAA2E;YAC3E,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,oBAAoB;YAE7E,MAAM,UAAU;gBACd,eAAe,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;gBAClC,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACjD,SAAS,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACnF,UAAU,OAAO,EAAE;gBACnB,aAAa,OAAO,KAAK;gBACzB,QAAQ,OAAO,SAAS;gBACxB,UAAU,OAAO,QAAQ;gBACzB,aAAa,CAAC,+BAA+B,EAAE,OAAO,KAAK,EAAE;gBAC7D,qBAAqB;oBACnB,UAAU;oBACV,aAAa;oBACb,eAAe;oBACf,WAAW;oBACX,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;oBAChC,qBAAqB;gBACvB;YACF;YAEA,eAAe;YACf,oBAAoB;QACpB,wEAAwE;QAC1E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB;QACtB,8DAA8D;QAC9D,QAAQ,GAAG,CAAC,wBAAwB;QACpC,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,iBAAiB,CAAC,QAAgB;QACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU,aAAa,QAAQ,QAAQ;YACvC,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,mBAAmB,sBAAsB;;;;;;sCAE5C,8OAAC;4BACC,SAAS;gCACP,IAAI,kBAAkB;oCACpB,sBAAsB,sDAAsD;gCAC9E;gCACA;4BACF;4BACA,cAAW;4BACX,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;8BACZ,CAAC,mBACA,0BAA0B;kCAC1B,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqD,OAAO,KAAK;;;;;;kDAC/E,8OAAC;wCAAE,WAAU;;4CAAgD;4CACO,eAAe,OAAO,SAAS,EAAE,OAAO,QAAQ;4CAAE;;;;;;;kDAItH,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;;;;;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4D;;;;;;sEAC1E,8OAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAShE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,8OAAC;oDAAE,WAAU;;;;;;gDAAyC;;yEAIxD;;8DACE,8OAAC;oDAAE,WAAU;;;;;;gDAA6B;;;;;;;;;;;;;;;;;;;mEAQpD,kBAAkB;kCAClB,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;;;;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyD;;;;;;8DACvE,8OAAC;oDAAE,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;;;;;;0CAQhE,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqD;;;;;;0DACnE,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;kDAE1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+E;;;;;;kEAChG,8OAAC;wDAAE,WAAU;kEAAwD,aAAa;;;;;;;;;;;;0DAEpF,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+E;;;;;;kEAChG,8OAAC;wDAAE,WAAU;kEAAwD,aAAa;;;;;;;;;;;;0DAEpF,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+E;;;;;;kEAChG,8OAAC;wDAAE,WAAU;kEAAwD,aAAa;;;;;;;;;;;;0DAEpF,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+E;;;;;;kEAChG,8OAAC;wDAAE,WAAU;kEAAwD,cAAc,eAAe,YAAY,MAAM,EAAE,YAAY,QAAQ,IAAI;;;;;;;;;;;;;;;;;;kDAIlJ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+E;;;;;;0DAChG,8OAAC;gDAAE,WAAU;0DAA4C,aAAa;;;;;;;;;;;;kDAGxE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EACV,aAAa,oBAAoB;;;;;;0EAEpC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAe;4EAAE,aAAa,oBAAoB;;;;;;;kFAC7D,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAwB;4EAAE,aAAa;;;;;;;kFAClD,8OAAC;;0FAAE,8OAAC;0FAAO;;;;;;4EAAmB;4EAAE,aAAa,oBAAoB;;;;;;;;;;;;;;;;;;;kEAGrE,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;8EACX,8OAAC;oEAAE,WAAU;;;;;;gEAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;4CACP,sBAAsB,sDAAsD;4CAC5E;wCACF;wCACA,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC;gDAAE,WAAU;;;;;;4CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3D;AAEA,iCAAiC;AACjC,MAAM,qBAID,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,aAAa,WAAW,cAAc,GAAG;YAC5C,UAAU,SAAS,GAAG;QACxB;QAEA,IAAI,CAAC,mBAAmB;YACtB,UAAU,iBAAiB,GAAG;QAChC,OAAO,IAAI,kBAAkB,IAAI,KAAK,mBAAmB;YACvD,UAAU,iBAAiB,GAAG;QAChC;QAEA,IAAI,CAAC,mBAAmB;YACtB,UAAU,iBAAiB,GAAG;QAChC,OAAO,IAAI,kBAAkB,IAAI,KAAK,mBAAmB;YACvD,UAAU,iBAAiB,GAAG;QAChC;QAEA,IAAI,CAAC,gBAAgB;YACnB,UAAU,cAAc,GAAG;QAC7B,OAAO,IAAI,eAAe,IAAI,KAAK,mBAAmB;YACpD,UAAU,cAAc,GAAG;QAC7B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,mBAAmB,CAAC,MAAmB;QAC3C,IAAI,QAAQ,KAAK,IAAI,KAAK,mBAAmB;YAC3C,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAA6B,CAAC;YACrE;QACF;QAEA,IAAI,QAAQ,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YACxC,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAmC,CAAC;YAC3E;QACF;QAEA,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAG,CAAC;QAE3C,OAAQ;YACN,KAAK;gBACH,qBAAqB;gBACrB;YACF,KAAK;gBACH,qBAAqB;gBACrB;YACF,KAAK;gBACH,kBAAkB;gBAClB;QACJ;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAChB,IAAI;YACF,sDAAsD;YACtD,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,YAAY,OAAO,EAAE;YACrC,SAAS,MAAM,CAAC,aAAa;YAC7B,IAAI,mBAAmB,SAAS,MAAM,CAAC,qBAAqB;YAC5D,IAAI,mBAAmB,SAAS,MAAM,CAAC,qBAAqB;YAC5D,IAAI,gBAAgB,SAAS,MAAM,CAAC,kBAAkB;YAEtD,yCAAyC;YACzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,oBAAoB;YAE7E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC,QAAgB;QACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU,aAAa,QAAQ,QAAQ;YACvC,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BACC,SAAS;4BACT,cAAW;4BACX,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqD,OAAO,KAAK;;;;;;sCAC/E,8OAAC;4BAAE,WAAU;;gCAAgD;gCACzC,eAAe,OAAO,cAAc,EAAE,OAAO,QAAQ;;;;;;;sCAEzE,8OAAC;4BAAE,WAAU;;gCAA2C;gCACvC,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;8BAIlE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,wIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4BAC5C,aAAY;4BACZ,OAAO,OAAO,SAAS;4BACvB,QAAQ;;;;;;sCAIV,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAqB,WAAU;8CAAkE;;;;;;8CAGhH,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,MAAM;oCAC/D,WAAW,CAAC,+IAA+I,EACzJ,OAAO,iBAAiB,GAAG,mBAAmB,wCAC9C;;;;;;gCAEH,OAAO,iBAAiB,kBACvB,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,iBAAiB;;;;;;8CAEpE,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAqB,WAAU;8CAAkE;;;;;;8CAGhH,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,MAAM;oCAC/D,WAAW,CAAC,+IAA+I,EACzJ,OAAO,iBAAiB,GAAG,mBAAmB,wCAC9C;;;;;;gCAEH,OAAO,iBAAiB,kBACvB,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,iBAAiB;;;;;;8CAEpE,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAkB,WAAU;8CAAkE;;;;;;8CAG7G,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,MAAM;oCAC/D,WAAW,CAAC,+IAA+I,EACzJ,OAAO,cAAc,GAAG,mBAAmB,wCAC3C;;;;;;gCAEH,OAAO,cAAc,kBACpB,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,cAAc;;;;;;8CAEjE,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;8BAM9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAmB;;;;;;kDAChC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAM9C;uCAEe", "debugId": null}}]}