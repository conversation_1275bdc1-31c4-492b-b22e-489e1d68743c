import { Repository } from 'typeorm';
import { ActivityNote } from '../entities/activity-notes.entity';
import { CreateActivityNoteDto, UpdateActivityNoteDto, ActivityNoteQueryDto } from '../dto/activity-notes.dto';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { ApplicationsService } from '../applications/applications.service';
export declare class ActivityNotesService {
    private activityNotesRepository;
    private notificationHelperService;
    private applicationsService;
    constructor(activityNotesRepository: Repository<ActivityNote>, notificationHelperService: NotificationHelperService, applicationsService: ApplicationsService);
    create(createDto: CreateActivityNoteDto, userId: string, skipNotification?: boolean): Promise<ActivityNote>;
    findAll(queryDto?: ActivityNoteQueryDto): Promise<ActivityNote[]>;
    findByEntity(entityType: string, entityId: string): Promise<ActivityNote[]>;
    findByEntityAndStep(entityType: string, entityId: string, step: string): Promise<ActivityNote[]>;
    findOne(id: string): Promise<ActivityNote>;
    update(id: string, updateDto: UpdateActivityNoteDto, userId: string): Promise<ActivityNote>;
    archive(id: string, userId: string): Promise<ActivityNote>;
    softDelete(id: string, userId: string): Promise<void>;
    hardDelete(id: string, userId: string): Promise<void>;
    createEvaluationComment(applicationId: string, step: string, comment: string, userId: string, metadata?: Record<string, any>): Promise<ActivityNote>;
    createStatusUpdate(applicationId: string, statusChange: string, userId: string, metadata?: Record<string, any>): Promise<ActivityNote>;
    private sendGeneralMessageNotification;
    private generateGeneralMessageHtml;
    private sendActivityNoteNotification;
}
