"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const license_types_entity_1 = require("../../entities/license-types.entity");
class LicenseTypesSeeder {
    async run(dataSource) {
        const repository = dataSource.getRepository(license_types_entity_1.LicenseTypes);
        const existingCount = await repository.count();
        if (existingCount > 0) {
            return;
        }
        const licenseTypes = [
            {
                name: 'Telecommunications',
                code: 'telecommunications',
                description: 'Licenses for telecommunications services including mobile networks, fixed networks, internet services, and broadcasting',
            },
            {
                name: 'Postal Services',
                code: 'postal_services',
                description: 'Licenses for postal and courier services including domestic and international mail delivery',
            },
            {
                name: 'Standards Compliance',
                code: 'standards_compliance',
                description: 'Certificates for standards compliance including type approval and short-codes',
            },
            {
                name: 'Broadcasting',
                code: 'broadcasting',
                description: 'Licenses for radio and television broadcasting services',
            },
            {
                name: 'Spectrum Management',
                code: 'spectrum_management',
                description: 'Licenses for radio frequency spectrum allocation and management',
            },
        ];
        for (const licenseTypeData of licenseTypes) {
            const licenseType = repository.create(licenseTypeData);
            await repository.save(licenseType);
        }
    }
}
exports.default = LicenseTypesSeeder;
//# sourceMappingURL=license-types.seeder.js.map