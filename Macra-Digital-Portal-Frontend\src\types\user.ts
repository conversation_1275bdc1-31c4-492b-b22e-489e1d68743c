import { Department } from "./department";

// Types
export interface User {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone: string;
  department_id?: string;
  organization_id?: string;
  status: 'active' | 'inactive' | 'suspended';
  profile_image?: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  isAdmin?: boolean;
  isCustomer?: boolean;
  roles?: string[];
  two_factor_enabled?: boolean;
  department?:Department;
}


export interface Role {
  role_id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  permissions?: Permission[];
}

export interface Permission {
  permission_id: string;
  name: string;
  description: string;
  category: string;
  created_at: string;
  updated_at: string;
  roles?: Role[];
}

export interface CreateUserDto {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone: string;
  department_id?: string;
  organization_id?: string;
  status?: 'active' | 'inactive' | 'suspended';
  profile_image?: string;
  role_ids?: string[];
}

export interface UpdateUserDto {
  email?: string;
  password?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  phone?: string;
  department_id?: string;
  organization_id?: string;
  status?: 'active' | 'inactive' | 'suspended';
  profile_image?: string;
  role_ids?: string[];
}

export interface UpdateProfileDto {
  email?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  phone?: string;
  profile_image?: string;
}

export interface UpdateUserData {
  user_id: string;
  email: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone?: string;
  status?: string;
  profile_image?: string;
  roles: (string | { name?: string; role_name?: string })[];
  isAdmin?: boolean;
}

export interface ChangePasswordDto {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

// PaginateQuery moved to index.ts as a common type

export interface UserFilters extends Record<string, string | undefined> {
  department_id?: string;
  organization_id?: string;
  role?: string;
  status?: string;
}


export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  phone: string;
  organization?: string;
}


export interface AuthResponse {
  access_token: string;
  user: User;
  requiresTwoFactor?: boolean;
  requiresRecovery?: boolean;
  message?: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  user_id: string;
  code: string;
  new_password: string;
  unique: string;
}

export interface TwoFactorData {
  user_id: string;
  code: string;
  unique: string;
}

export interface SetUpTwoFactorData {
  user_id: string;
}

export interface  TwoFactorResponse {
  access_token: string;
  user:User;
  message: string;
};

export interface SetupTwoFactorAuthResponse{
  otp_auth_url: string;
  qr_code_data_url: string;
  secret: string;
  message: string;
}

export interface OTPVerificationProps {
  /** Title for the verification section */
  title?: string;
  /** Description text */
  description?: string;
  /** Current OTP value */
  value: string;
  /** Callback when OTP value changes */
  onChange: (value: string) => void;
  /** Callback when form is submitted */
  onSubmit: (value: string) => void;
  /** Error message to display */
  error?: string;
  /** Whether the form is loading/submitting */
  loading?: boolean;
  /** Number of OTP digits (default: 6) */
  length?: number;
  /** Submit button text */
  submitText?: string;
  /** Loading button text */
  loadingText?: string;
  /** Whether to auto-submit when OTP is complete */
  autoSubmit?: boolean;
  /** Whether to show the submit button */
  showSubmitButton?: boolean;
  /** Custom className for the container */
  className?: string;
  /** Whether to auto-focus the first input */
  autoFocus?: boolean;
  /** Whether to allow only numeric input */
  numericOnly?: boolean;
  /** Additional content to show above the OTP input */
  children?: React.ReactNode;
} 

export interface OTPInputProps {
  /** Number of OTP digits (default: 6) */
  length?: number;
  /** Current OTP value */
  value: string;
  /** Callback when OTP value changes */
  onChange: (value: string) => void;
  /** Callback when OTP is complete */
  onComplete?: (value: string) => void;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Whether to auto-focus the first input */
  autoFocus?: boolean;
  /** Placeholder character for empty inputs */
  placeholder?: string;
  /** Error state */
  hasError?: boolean;
  /** Custom className for styling */
  className?: string;
  /** Input type (default: 'text') */
  type?: 'text' | 'number';
  /** Whether to allow only numeric input */
  numericOnly?: boolean;
}

// Type for 2FA user data that can handle both staff and customer user formats
export interface TwoFactorUserData {
  // Common properties
  email: string;
  roles: string[];
  isAdmin?: boolean;
  two_factor_enabled?: boolean;

  // Staff user properties (from auth.service.ts)
  user_id?: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  phone?: string;
  status?: string;
  profile_image?: string;

  // Customer user properties (from customer-api.ts)
  id?: string;
  firstName?: string;
  lastName?: string;
  organizationName?: string;
  profileImage?: string;
  createdAt?: string;
  lastLogin?: string;
  address?: string;
  city?: string;
  country?: string;
}

export interface TwoFactorVerificationProps {
  userId: string;
  email: string;
  isCustomerPortal?: boolean;
  onSuccess: (token: string, user: TwoFactorUserData) => void;
  onCancel: () => void;
  onError: (error: string) => void;
}

export interface SetupState {
  isClient: boolean;
  qrCodeUrl: string;
  secret: string;
  error: string;
  success: string;
  loadingMessage: string;
  alreadyEnabled: boolean;
  setUpComplete: boolean;
  loading: boolean;
  unauthorizedAccess: boolean;
  dynamicMessages: string[];
  showOTPInput: boolean;
  otpValue: string;
  otpLoading: boolean;
}

export const initialSetupState: SetupState = {
    isClient: false,
    qrCodeUrl: '',
    secret: '',
    error: '',
    success: '',
    loadingMessage: 'Initializing 2FA setup...',
    alreadyEnabled: false,
    setUpComplete: false,
    loading: true,
    unauthorizedAccess: false,
    dynamicMessages: [],
    showOTPInput: false,
    otpValue: '',
    otpLoading: false
  }