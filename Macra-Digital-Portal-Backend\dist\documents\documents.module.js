"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const platform_express_1 = require("@nestjs/platform-express");
const documents_controller_1 = require("./documents.controller");
const documents_service_1 = require("./documents.service");
const documents_entity_1 = require("../entities/documents.entity");
const user_entity_1 = require("../entities/user.entity");
const minio_module_1 = require("../common/modules/minio.module");
const activity_notes_module_1 = require("../activity-notes/activity-notes.module");
const notifications_module_1 = require("../notifications/notifications.module");
let DocumentsModule = class DocumentsModule {
};
exports.DocumentsModule = DocumentsModule;
exports.DocumentsModule = DocumentsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([documents_entity_1.Documents, user_entity_1.User]),
            platform_express_1.MulterModule.register({
                storage: require('multer').memoryStorage(),
                limits: {
                    fileSize: 5 * 1024 * 1024,
                },
            }),
            minio_module_1.MinioModule,
            (0, common_1.forwardRef)(() => activity_notes_module_1.ActivityNotesModule),
            (0, common_1.forwardRef)(() => notifications_module_1.NotificationsModule),
        ],
        controllers: [documents_controller_1.DocumentsController],
        providers: [documents_service_1.DocumentsService],
        exports: [documents_service_1.DocumentsService],
    })
], DocumentsModule);
//# sourceMappingURL=documents.module.js.map