{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/LoadingState.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\n\r\ninterface LoadingStateProps {\r\n  message?: string;\r\n  submessage?: string;\r\n  showProgress?: boolean;\r\n  progress?: number;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  dynamicMessages?: string[];\r\n  messageInterval?: number;\r\n}\r\n\r\nconst LoadingState: React.FC<LoadingStateProps> = ({\r\n  message = 'Loading...',\r\n  submessage,\r\n  showProgress = false,\r\n  progress = 0,\r\n  size = 'md',\r\n  className = '',\r\n  dynamicMessages = [],\r\n  messageInterval = 2000\r\n}) => {\r\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\r\n  const [displayMessage, setDisplayMessage] = useState(message);\r\n\r\n  // Handle dynamic message rotation\r\n  useEffect(() => {\r\n    if (dynamicMessages.length > 0) {\r\n      const interval = setInterval(() => {\r\n        setCurrentMessageIndex((prev) => (prev + 1) % dynamicMessages.length);\r\n      }, messageInterval);\r\n\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [dynamicMessages, messageInterval]);\r\n\r\n  // Update display message when dynamic messages change\r\n  useEffect(() => {\r\n    if (dynamicMessages.length > 0) {\r\n      setDisplayMessage(dynamicMessages[currentMessageIndex]);\r\n    } else {\r\n      setDisplayMessage(message);\r\n    }\r\n  }, [currentMessageIndex, dynamicMessages, message]);\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return {\r\n          container: 'w-12 h-12',\r\n          logo: 'h-6 w-6',\r\n          text: 'text-sm'\r\n        };\r\n      case 'lg':\r\n        return {\r\n          container: 'w-24 h-24',\r\n          logo: 'h-12 w-12',\r\n          text: 'text-lg'\r\n        };\r\n      case 'md':\r\n      default:\r\n        return {\r\n          container: 'w-20 h-20',\r\n          logo: 'h-10 w-10',\r\n          text: 'text-base'\r\n        };\r\n    }\r\n  };\r\n\r\n  const sizeClasses = getSizeClasses();\r\n\r\n  return (\r\n    <div className={`text-center ${className}`}>\r\n      {/* Loading Spinner with Logo */}\r\n      <div className={`relative ${sizeClasses.container} mx-auto`}>\r\n        {/* Animated Spinner */}\r\n        <svg\r\n          className=\"absolute inset-0 animate-spin\"\r\n          viewBox=\"0 0 50 50\"\r\n          fill=\"none\"\r\n        >\r\n          <defs>\r\n            <linearGradient id=\"fadeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n              <stop offset=\"0%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n              <stop offset=\"20%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n              <stop offset=\"80%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n              <stop offset=\"100%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n            </linearGradient>\r\n          </defs>\r\n\r\n          <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"rgba(255, 255, 255, 0.1)\"\r\n            strokeWidth=\"2\"\r\n          />\r\n          <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"url(#fadeGradient)\"\r\n            strokeWidth=\"2\"\r\n            strokeDasharray=\"70\"\r\n            strokeDashoffset=\"10\"\r\n            fill=\"none\"\r\n          />\r\n        </svg>\r\n\r\n        {/* MACRA Logo */}\r\n        <Image\r\n          src=\"/images/macra-logo.png\"\r\n          alt=\"MACRA Logo\"\r\n          width={40}\r\n          height={40}\r\n          className={`object-contain absolute inset-0 ${sizeClasses.logo} m-auto animate-pulse`}\r\n          priority\r\n        />\r\n      </div>\r\n\r\n      {/* Progress Bar */}\r\n      {showProgress && (\r\n        <div className=\"mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n          <div\r\n            className=\"bg-red-600 h-2 rounded-full transition-all duration-300 ease-out\"\r\n            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Loading Message */}\r\n      <div className=\"mt-4 space-y-1\">\r\n        <p className={`text-gray-600 dark:text-gray-400 font-medium ${sizeClasses.text} transition-opacity duration-300`}>\r\n          {displayMessage}\r\n        </p>\r\n        {submessage && (\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-500\">\r\n            {submessage}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoadingState;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAgBA,MAAM,eAA4C;QAAC,EACjD,UAAU,YAAY,EACtB,UAAU,EACV,eAAe,KAAK,EACpB,WAAW,CAAC,EACZ,OAAO,IAAI,EACX,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,kBAAkB,IAAI,EACvB;;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,MAAM,WAAW;uDAAY;wBAC3B;+DAAuB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,gBAAgB,MAAM;;oBACtE;sDAAG;gBAEH;8CAAO,IAAM,cAAc;;YAC7B;QACF;iCAAG;QAAC;QAAiB;KAAgB;IAErC,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,kBAAkB,eAAe,CAAC,oBAAoB;YACxD,OAAO;gBACL,kBAAkB;YACpB;QACF;iCAAG;QAAC;QAAqB;QAAiB;KAAQ;IAElD,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;YACL;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC;QAAI,WAAW,AAAC,eAAwB,OAAV;;0BAE7B,6LAAC;gBAAI,WAAW,AAAC,YAAiC,OAAtB,YAAY,SAAS,EAAC;;kCAEhD,6LAAC;wBACC,WAAU;wBACV,SAAQ;wBACR,MAAK;;0CAEL,6LAAC;0CACC,cAAA,6LAAC;oCAAe,IAAG;oCAAe,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC7D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;4CAAU,aAAY;;;;;;sDAClD,6LAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,6LAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,6LAAC;4CAAK,QAAO;4CAAO,WAAU;4CAAU,aAAY;;;;;;;;;;;;;;;;;0CAIxD,6LAAC;gCACC,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;;;;;;0CAEd,6LAAC;gCACC,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,iBAAgB;gCAChB,kBAAiB;gCACjB,MAAK;;;;;;;;;;;;kCAKT,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAW,AAAC,mCAAmD,OAAjB,YAAY,IAAI,EAAC;wBAC/D,QAAQ;;;;;;;;;;;;YAKX,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,AAAC,GAAuC,OAArC,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,YAAW;oBAAG;;;;;;;;;;;0BAMjE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAW,AAAC,gDAAgE,OAAjB,YAAY,IAAI,EAAC;kCAC5E;;;;;;oBAEF,4BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;GAnIM;KAAA;uCAqIS", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/PageTransition.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport LoadingState from './LoadingState';\r\n\r\ninterface PageTransitionProps {\r\n  children: React.ReactNode;\r\n  isLoading?: boolean;\r\n  loadingMessage?: string;\r\n  loadingSubmessage?: string;\r\n  redirectTo?: string;\r\n  redirectDelay?: number;\r\n  showProgress?: boolean;\r\n  dynamicMessages?: string[];\r\n  className?: string;\r\n}\r\n\r\nconst PageTransition: React.FC<PageTransitionProps> = ({\r\n  children,\r\n  isLoading = false,\r\n  loadingMessage = 'Loading...',\r\n  loadingSubmessage,\r\n  redirectTo,\r\n  redirectDelay = 2000,\r\n  showProgress = false,\r\n  dynamicMessages = [],\r\n  className = ''\r\n}) => {\r\n  const router = useRouter();\r\n  const [progress, setProgress] = useState(0);\r\n  const [isRedirecting, setIsRedirecting] = useState(false);\r\n\r\n  // Handle redirect with progress\r\n  useEffect(() => {\r\n    if (redirectTo && !isRedirecting) {\r\n      setIsRedirecting(true);\r\n      \r\n      if (showProgress) {\r\n        const progressInterval = setInterval(() => {\r\n          setProgress((prev) => {\r\n            if (prev >= 100) {\r\n              clearInterval(progressInterval);\r\n              return 100;\r\n            }\r\n            return prev + (100 / (redirectDelay / 100));\r\n          });\r\n        }, 100);\r\n\r\n        const redirectTimeout = setTimeout(() => {\r\n          router.push(redirectTo);\r\n        }, redirectDelay);\r\n\r\n        return () => {\r\n          clearInterval(progressInterval);\r\n          clearTimeout(redirectTimeout);\r\n        };\r\n      } else {\r\n        const redirectTimeout = setTimeout(() => {\r\n          router.push(redirectTo);\r\n        }, redirectDelay);\r\n\r\n        return () => clearTimeout(redirectTimeout);\r\n      }\r\n    }\r\n  }, [redirectTo, redirectDelay, router, showProgress, isRedirecting]);\r\n\r\n  if (isLoading || isRedirecting) {\r\n    return (\r\n      <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${className}`}>\r\n        <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\r\n          <LoadingState\r\n            message={loadingMessage}\r\n            submessage={loadingSubmessage}\r\n            showProgress={showProgress}\r\n            progress={progress}\r\n            size=\"lg\"\r\n            dynamicMessages={dynamicMessages}\r\n          />\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`transition-all duration-300 ease-in-out ${className}`}>\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageTransition;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAkBA,MAAM,iBAAgD;QAAC,EACrD,QAAQ,EACR,YAAY,KAAK,EACjB,iBAAiB,YAAY,EAC7B,iBAAiB,EACjB,UAAU,EACV,gBAAgB,IAAI,EACpB,eAAe,KAAK,EACpB,kBAAkB,EAAE,EACpB,YAAY,EAAE,EACf;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,cAAc,CAAC,eAAe;gBAChC,iBAAiB;gBAEjB,IAAI,cAAc;oBAChB,MAAM,mBAAmB;qEAAY;4BACnC;6EAAY,CAAC;oCACX,IAAI,QAAQ,KAAK;wCACf,cAAc;wCACd,OAAO;oCACT;oCACA,OAAO,OAAQ,MAAM,CAAC,gBAAgB,GAAG;gCAC3C;;wBACF;oEAAG;oBAEH,MAAM,kBAAkB;oEAAW;4BACjC,OAAO,IAAI,CAAC;wBACd;mEAAG;oBAEH;oDAAO;4BACL,cAAc;4BACd,aAAa;wBACf;;gBACF,OAAO;oBACL,MAAM,kBAAkB;oEAAW;4BACjC,OAAO,IAAI,CAAC;wBACd;mEAAG;oBAEH;oDAAO,IAAM,aAAa;;gBAC5B;YACF;QACF;mCAAG;QAAC;QAAY;QAAe;QAAQ;QAAc;KAAc;IAEnE,IAAI,aAAa,eAAe;QAC9B,qBACE,6LAAC;YAAI,WAAW,AAAC,6EAAsF,OAAV;sBAC3F,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6IAAA,CAAA,UAAY;oBACX,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,UAAU;oBACV,MAAK;oBACL,iBAAiB;;;;;;;;;;;;;;;;IAK3B;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,2CAAoD,OAAV;kBACxD;;;;;;AAGP;GAvEM;;QAWW,qIAAA,CAAA,YAAS;;;KAXpB;uCAyES", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/auth/setup-2fa/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport Image from 'next/image';\r\nimport { useRouter } from 'next/navigation';\r\nimport { authService } from '@/services/auth.service';\r\nimport { XCircleIcon } from '@heroicons/react/24/solid';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport PageTransition from '@/components/auth/PageTransition';\r\nimport Cookies from 'js-cookie';\r\nimport { User } from '@/types';\r\n\r\nexport default function CustomerSetupTwoFactorPage() {\r\n  const router = useRouter();\r\n  const { user, loading: authLoading } = useAuth();\r\n\r\n  const [qrCodeUrl, setQrCodeUrl] = useState('');\r\n  const [secret, setSecret] = useState('');\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [loadingMessage, setLoadingMessage] = useState('Initializing 2FA setup...');\r\n  const [alreadyEnabled, setAlreadyEnabled] = useState(false);\r\n  const [setUpComplete, setSetUpComplete] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);\r\n  const [dynamicMessages, setDynamicMessages] = useState<string[]>(['Initializing 2FA setup...']);\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [userId, setUserId] = useState(JSON.parse(Cookies.get('auth_user') || '').user_id || '');\r\n\r\n  const redirectGeneral = () => router.replace('/customer'); // Middleware takes care of routing\r\n  const handleRedirectLogin = () => {\r\n    setLoading(true);\r\n    sessionStorage.removeItem('2fa_setup_user');\r\n    sessionStorage.removeItem('2fa_verify_url');\r\n    sessionStorage.removeItem('2fa_setup_in_progress');\r\n    Cookies.remove('2fa_setup_user');\r\n    Cookies.remove('2fa_verify_url');\r\n    Cookies.remove('2fa_setup_in_progress');\r\n    setDynamicMessages(['Removing saved session data...', 'Cancelling 2FA setup...', 'Almost there...']);\r\n    setLoadingMessage('Redirecting to login...')\r\n    redirectGeneral(); //middleware will redirect to /customer/auth/login\r\n  };\r\n  const initiate2FA = async (currentUser: User) => {\r\n    sessionStorage.setItem('2fa_setup_in_progress', 'true');\r\n    Cookies.set('2fa_setup_in_progress', 'true', { expires: 0.01 });\r\n\r\n    try {\r\n      setDynamicMessages(['Account needs secure authentication', 'Initializing 2FA setup...', 'Almost there...']);\r\n      const {qr_code_data_url, secret, message} = await authService.setupTwoFactorAuth({\r\n        user_id: currentUser.user_id,\r\n      });\r\n\r\n      setQrCodeUrl(qr_code_data_url);\r\n      setSecret(secret);\r\n      setSuccess(message || '2FA setup successful');\r\n      setSetUpComplete(true);\r\n\r\n      const verifyUrl =  `/customer/auth/verify-2fa` +\r\n        `?i=${currentUser.user_id}` +\r\n        `&unique=${secret}` +\r\n        `&c=`;\r\n      sessionStorage.setItem('2fa_verify_url', verifyUrl);\r\n      Cookies.set('2fa_verify_url', verifyUrl, { expires: 0.01 });\r\n      setLoading(false);\r\n      setSuccess('An OTP has been sent to your email. Please confirm below to continue');\r\n      return;\r\n    } catch (err: any) {\r\n      const msg: string =\r\n        err?.response?.data?.message ||\r\n        err?.message ||\r\n        'Failed to initiate 2FA setup. Redirecting...';\r\n\r\n      const isEnabled = msg.toLowerCase().includes('enabled');\r\n      const isInitiated = msg.toLowerCase().includes('initiation');\r\n\r\n      setAlreadyEnabled(isEnabled);\r\n      setSetUpComplete(isInitiated);\r\n\r\n      if (isEnabled) {\r\n        setSuccess(msg);\r\n      } else {\r\n        setError(msg);\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n      sessionStorage.removeItem('2fa_setup_in_progress');\r\n      Cookies.remove('2fa_setup_in_progress');\r\n      sessionStorage.removeItem('2fa_verify_url');\r\n      Cookies.remove('2fa_verify_url');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (authLoading || !isClient) return;\r\n\r\n    const setupUser: string | null = Cookies.get('2fa_setup_user') || sessionStorage.getItem('2fa_setup_user') || Cookies.get('auth_user') || sessionStorage.getItem('auth_user') || null;\r\n\r\n    let currentUser: User | null = (setupUser) ? JSON.parse(setupUser) : null;\r\n\r\n    if (!currentUser) {\r\n      setUnauthorizedAccess(true);\r\n      setLoading(false);\r\n      setError('Your session has expired or is invalid. Please login again to continue.');\r\n      return;\r\n    }\r\n    if (isClient && !setUpComplete) {\r\n      console.log('Is Client. Now initiating 2FA setup...');\r\n      initiate2FA(currentUser);\r\n    } else {return;}\r\n\r\n  }, [authLoading, user, isClient, setUpComplete]);\r\n\r\n  if (!isClient || loading) {\r\n\r\n    return (\r\n      <PageTransition\r\n        isLoading={true}\r\n        loadingMessage={loadingMessage || (!isClient ? 'Loading...' : 'Processing your request')}\r\n        loadingSubmessage=\"Please wait..\"\r\n        dynamicMessages={dynamicMessages}\r\n        showProgress={loading}\r\n      >\r\n        <div />\r\n      </PageTransition>\r\n    );\r\n  }\r\n\r\n  const getPageTitle = () => {\r\n    if (setUpComplete && !alreadyEnabled) {\r\n      return '✅ 2FA Setup Complete!';\r\n    }\r\n    if (alreadyEnabled) {\r\n      return '✅ 2FA Already Enabled';\r\n    }\r\n    return '🔐 Secure Your Account';\r\n  };\r\n\r\n  const getPageSubtitle = () => {\r\n    if (setUpComplete && !alreadyEnabled) {\r\n      return 'Your account is now protected with two-factor authentication';\r\n    }\r\n    if (alreadyEnabled) {\r\n      return 'Your account is already secured with two-factor authentication';\r\n    }\r\n    return 'Enable two-factor authentication to protect your account with an extra layer of security';\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-blue-900\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10\"></div>\r\n\r\n      <div className=\"relative flex flex-col justify-center py-12 sm:px-6 lg:px-8 min-h-screen\">\r\n        {/* Header Section */}\r\n        <div className=\"sm:mx-auto sm:w-full sm:max-w-md text-center\">\r\n          <div className=\"flex justify-center\">\r\n            <div className=\"relative\">\r\n              <Image\r\n                src=\"/images/macra-logo.png\"\r\n                alt=\"MACRA Logo\"\r\n                width={50}\r\n                height={50}\r\n                className=\"mx-auto h-16 w-auto drop-shadow-lg animate-fadeLoop\"\r\n              />\r\n              <div className=\"absolute -inset-2 rounded-full opacity-20 blur-lg animate-pulse\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mt-6\">\r\n            <div className=\"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mb-3 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 animate-slideInFromTop animate-delay-100\">\r\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\r\n              </svg>\r\n              Two Factor Authentication\r\n            </div>\r\n\r\n            <h2 className=\"text-3xl font-extrabold text-gray-900 dark:text-white animate-slideInFromTop animate-delay-200\">\r\n              {getPageTitle()}\r\n            </h2>\r\n\r\n            <p className=\"mt-3 text-sm text-gray-600 dark:text-gray-400 animate-slideInFromTop animate-delay-300\">\r\n              {getPageSubtitle()}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <div className=\"bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10\">\r\n          {error && !alreadyEnabled && (\r\n            <div className=\"flex flex-col items-center justify-center\">\r\n              <div className=\"w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center\">\r\n                <XCircleIcon className=\"w-10 h-10 animate-pulse shadow-md text-red-600 dark:text-red-300\" />\r\n              </div>\r\n              <div className=\"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center\">\r\n                {error}\r\n              </div>\r\n              {unauthorizedAccess && (\r\n                <div className=\"mt-4\">\r\n                  <button\r\n                    onClick={handleRedirectLogin}\r\n                    className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                  >\r\n                    Go to Login\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n\r\n          {(success || alreadyEnabled || setUpComplete) && (\r\n            <div className=\"flex flex-col items-center justify-center text-center text-gray-600 dark:text-gray-400\">\r\n              <div className=\"w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md\">\r\n                <svg\r\n                  className=\"w-8 h-8 text-green-600 dark:text-green-300\"\r\n                  fill=\"none\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"3\"\r\n                  viewBox=\"0 0 24 24\"\r\n                >\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M5 13l4 4L19 7\" />\r\n                </svg>\r\n              </div>\r\n              <div>\r\n                {success}\r\n                {alreadyEnabled && (\r\n                  <p className=\"text-sm text-gray-400 dark:text-gray-200 p-2\">\r\n                    Two-Factor Authentication is already enabled. Please contact support if you need to reset it.\r\n                  </p>\r\n                )}\r\n                {setUpComplete && (\r\n                  <p className=\"text-sm text-gray-400 dark:text-gray-200 p-2\">\r\n                    This link is valid for 5 minutes and can only be used once.\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* QR Code Display - Enhanced */}\r\n          {qrCodeUrl && setUpComplete && !alreadyEnabled && (\r\n            <div className=\"mt-6 text-center animate-fadeIn\">\r\n              <div className=\"bg-white dark:bg-gray-700 shadow-lg p-6 rounded-lg border border-gray-200 dark:border-gray-600 inline-block\">\r\n                <div className=\"mb-4\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Scan QR Code</h3>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">Use your authenticator app</p>\r\n                </div>\r\n                <Image\r\n                  src={qrCodeUrl}\r\n                  alt=\"2FA QR Code\"\r\n                  width={200}\r\n                  height={200}\r\n                  className=\"rounded-lg\"\r\n                />\r\n              </div>\r\n\r\n              {secret && (\r\n                <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700\">\r\n                  <p className=\"font-medium text-gray-900 dark:text-white mb-2\">Manual Setup Key:</p>\r\n                  <p className=\"font-mono text-sm break-all text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-900 p-3 rounded border\">\r\n                    {secret}\r\n                  </p>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-2\">\r\n                    Enter this key manually if you can't scan the QR code\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n\r\n          {/* Button for OTP verification redirect to verify-2fa after setup complete */}\r\n          {setUpComplete && (\r\n            <div className=\"mt-6\">\r\n              <button\r\n                onClick={() => router.replace(`/customer/auth/verify-2fa?i=${encodeURIComponent(userId)}&unique=${encodeURIComponent(secret)}&c=`)}\r\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n              >\r\n                Verify OTP\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAA4B;IAC9F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,CAAC,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAO,IAAI;IAE3F,MAAM,kBAAkB,IAAM,OAAO,OAAO,CAAC,cAAc,mCAAmC;IAC9F,MAAM,sBAAsB;QAC1B,WAAW;QACX,eAAe,UAAU,CAAC;QAC1B,eAAe,UAAU,CAAC;QAC1B,eAAe,UAAU,CAAC;QAC1B,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,mBAAmB;YAAC;YAAkC;YAA2B;SAAkB;QACnG,kBAAkB;QAClB,mBAAmB,kDAAkD;IACvE;IACA,MAAM,cAAc,OAAO;QACzB,eAAe,OAAO,CAAC,yBAAyB;QAChD,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,QAAQ;YAAE,SAAS;QAAK;QAE7D,IAAI;YACF,mBAAmB;gBAAC;gBAAuC;gBAA6B;aAAkB;YAC1G,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAC,GAAG,MAAM,qIAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;gBAC/E,SAAS,YAAY,OAAO;YAC9B;YAEA,aAAa;YACb,UAAU;YACV,WAAW,WAAW;YACtB,iBAAiB;YAEjB,MAAM,YAAa,AAAC,8BAClB,AAAC,MAAyB,OAApB,YAAY,OAAO,IACzB,AAAC,WAAiB,OAAP,UACV;YACH,eAAe,OAAO,CAAC,kBAAkB;YACzC,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,kBAAkB,WAAW;gBAAE,SAAS;YAAK;YACzD,WAAW;YACX,WAAW;YACX;QACF,EAAE,OAAO,KAAU;gBAEf,oBAAA;YADF,MAAM,MACJ,CAAA,gBAAA,2BAAA,gBAAA,IAAK,QAAQ,cAAb,qCAAA,qBAAA,cAAe,IAAI,cAAnB,yCAAA,mBAAqB,OAAO,MAC5B,gBAAA,0BAAA,IAAK,OAAO,KACZ;YAEF,MAAM,YAAY,IAAI,WAAW,GAAG,QAAQ,CAAC;YAC7C,MAAM,cAAc,IAAI,WAAW,GAAG,QAAQ,CAAC;YAE/C,kBAAkB;YAClB,iBAAiB;YAEjB,IAAI,WAAW;gBACb,WAAW;YACb,OAAO;gBACL,SAAS;YACX;QACF,SAAU;YACR,WAAW;YACX,eAAe,UAAU,CAAC;YAC1B,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACf,eAAe,UAAU,CAAC;YAC1B,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACjB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,YAAY;QACd;+CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,eAAe,CAAC,UAAU;YAE9B,MAAM,YAA2B,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB,eAAe,OAAO,CAAC,qBAAqB,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,gBAAgB,eAAe,OAAO,CAAC,gBAAgB;YAEjL,IAAI,cAA2B,AAAC,YAAa,KAAK,KAAK,CAAC,aAAa;YAErE,IAAI,CAAC,aAAa;gBAChB,sBAAsB;gBACtB,WAAW;gBACX,SAAS;gBACT;YACF;YACA,IAAI,YAAY,CAAC,eAAe;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,YAAY;YACd,OAAO;gBAAC;YAAO;QAEjB;+CAAG;QAAC;QAAa;QAAM;QAAU;KAAc;IAE/C,IAAI,CAAC,YAAY,SAAS;QAExB,qBACE,6LAAC,+IAAA,CAAA,UAAc;YACb,WAAW;YACX,gBAAgB,kBAAkB,CAAC,CAAC,WAAW,eAAe,yBAAyB;YACvF,mBAAkB;YAClB,iBAAiB;YACjB,cAAc;sBAEd,cAAA,6LAAC;;;;;;;;;;IAGP;IAEA,MAAM,eAAe;QACnB,IAAI,iBAAiB,CAAC,gBAAgB;YACpC,OAAO;QACT;QACA,IAAI,gBAAgB;YAClB,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI,iBAAiB,CAAC,gBAAgB;YACpC,OAAO;QACT;QACA,IAAI,gBAAgB;YAClB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CAInB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,6LAAC;wCAAG,WAAU;kDACX;;;;;;kDAGH,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;kCAKT,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,CAAC,gCACT,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,6LAAC;4CAAI,WAAU;sDACZ;;;;;;wCAEF,oCACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;gCAQR,CAAC,WAAW,kBAAkB,aAAa,mBAC1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,MAAK;gDACL,QAAO;gDACP,aAAY;gDACZ,SAAQ;0DAER,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,GAAE;;;;;;;;;;;;;;;;sDAGzD,6LAAC;;gDACE;gDACA,gCACC,6LAAC;oDAAE,WAAU;8DAA+C;;;;;;gDAI7D,+BACC,6LAAC;oDAAE,WAAU;8DAA+C;;;;;;;;;;;;;;;;;;gCASnE,aAAa,iBAAiB,CAAC,gCAC9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAsD;;;;;;sEACpE,6LAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;8DAE1D,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;wCAIb,wBACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAiD;;;;;;8DAC9D,6LAAC;oDAAE,WAAU;8DACV;;;;;;8DAEH,6LAAC;oDAAE,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;gCASpE,+BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,OAAO,OAAO,CAAC,AAAC,+BAAmE,OAArC,mBAAmB,SAAQ,YAAqC,OAA3B,mBAAmB,SAAQ;wCAC7H,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUf;GArRwB;;QACP,qIAAA,CAAA,YAAS;QACe,kIAAA,CAAA,UAAO;;;KAFxB", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/%40heroicons/react/24/solid/esm/XCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,YAAY,KAIpB,EAAE,MAAM;QAJY,EACnB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJoB;IAKnB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}