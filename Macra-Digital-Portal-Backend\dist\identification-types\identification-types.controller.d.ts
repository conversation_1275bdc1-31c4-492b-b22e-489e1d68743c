import { IdentificationTypesService } from './identification-types.service';
import { CreateIdentificationTypeDto } from '../dto/identification-types/create-identification-type.dto';
import { UpdateIdentificationTypeDto } from '../dto/identification-types/update-identification-type.dto';
import { IdentificationType } from '../entities/identification-type.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class IdentificationTypesController {
    private readonly identificationTypesService;
    constructor(identificationTypesService: IdentificationTypesService);
    findAll(query: PaginateQuery): Promise<PaginatedResult<IdentificationType>>;
    findAllSimple(): Promise<IdentificationType[]>;
    findOne(id: string): Promise<IdentificationType>;
    create(createIdentificationTypeDto: CreateIdentificationTypeDto, req: any): Promise<IdentificationType>;
    update(id: string, updateIdentificationTypeDto: UpdateIdentificationTypeDto, req: any): Promise<IdentificationType>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
