"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const documents_entity_1 = require("../entities/documents.entity");
const user_entity_1 = require("../entities/user.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const minio_service_1 = require("../common/services/minio.service");
const activity_notes_service_1 = require("../services/activity-notes.service");
const notification_helper_service_1 = require("../notifications/notification-helper.service");
let DocumentsService = class DocumentsService {
    constructor(documentsRepository, usersRepository, minioService, activityNotesService, notificationHelperService) {
        this.documentsRepository = documentsRepository;
        this.usersRepository = usersRepository;
        this.minioService = minioService;
        this.activityNotesService = activityNotesService;
        this.notificationHelperService = notificationHelperService;
        this.paginateConfig = {
            sortableColumns: ['created_at', 'updated_at', 'file_name', 'document_type'],
            searchableColumns: ['file_name', 'document_type', 'entity_type'],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: 10,
            maxLimit: 100,
            relations: ['creator', 'updater'],
        };
    }
    async create(createDocumentDto, createdBy) {
        const document = this.documentsRepository.create({
            ...createDocumentDto,
            is_required: createDocumentDto.is_required || false,
            created_by: createdBy,
        });
        return this.documentsRepository.save(document);
    }
    async uploadFile(file, createDocumentDto, createdBy) {
        const uploadOptions = {
            folder: this.getFolderByDocumentType(createDocumentDto.document_type),
            allowedMimeTypes: this.getAllowedMimeTypes(),
            maxSize: 10 * 1024 * 1024,
        };
        const uploadResult = await this.minioService.uploadFile(file, uploadOptions);
        const document = this.documentsRepository.create({
            ...createDocumentDto,
            file_name: createDocumentDto.file_name || file.originalname,
            file_path: uploadResult.fileName,
            file_size: uploadResult.size,
            mime_type: uploadResult.mimeType,
            is_required: createDocumentDto.is_required || false,
            created_by: createdBy,
        });
        return this.documentsRepository.save(document);
    }
    async findAll(query, userRoles, userId) {
        const isCustomer = userRoles?.includes('customer');
        if (isCustomer && userId) {
            const customerQuery = {
                ...query,
                filter: {
                    ...query.filter,
                    created_by: userId
                }
            };
            return (0, nestjs_paginate_1.paginate)(customerQuery, this.documentsRepository, this.paginateConfig);
        }
        return (0, nestjs_paginate_1.paginate)(query, this.documentsRepository, this.paginateConfig);
    }
    async findOne(id) {
        const document = await this.documentsRepository.findOne({
            where: { document_id: id },
            relations: ['creator', 'updater'],
        });
        if (!document) {
            throw new common_1.NotFoundException(`Document with ID ${id} not found`);
        }
        return document;
    }
    async findByApplication(applicationId) {
        return this.documentsRepository.find({
            where: { entity_type: 'application', entity_id: applicationId },
            relations: ['creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async findByEntity(entityType, entityId) {
        return this.documentsRepository.find({
            where: { entity_type: entityType, entity_id: entityId },
            relations: ['creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async findByDocumentType(documentType) {
        return this.documentsRepository.find({
            where: { document_type: documentType },
            relations: ['creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async findRequiredDocuments() {
        return this.documentsRepository.find({
            where: { is_required: true },
            relations: ['creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async update(id, updateDocumentDto, updatedBy) {
        const document = await this.findOne(id);
        Object.assign(document, updateDocumentDto, { updated_by: updatedBy });
        return this.documentsRepository.save(document);
    }
    async remove(id) {
        const document = await this.findOne(id);
        await this.documentsRepository.softDelete(document.document_id);
    }
    async getDocumentStats() {
        const stats = await this.documentsRepository
            .createQueryBuilder('document')
            .select('document.document_type', 'document_type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('document.document_type')
            .getRawMany();
        return stats.reduce((acc, stat) => {
            acc[stat.document_type] = parseInt(stat.count);
            return acc;
        }, {});
    }
    async getDocumentsByMimeType(mimeType) {
        return this.documentsRepository.find({
            where: { mime_type: mimeType },
            relations: ['creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async getTotalFileSize() {
        const result = await this.documentsRepository
            .createQueryBuilder('document')
            .select('SUM(document.file_size)', 'total_size')
            .getRawOne();
        return parseInt(result.total_size) || 0;
    }
    async getFileStream(filePath) {
        try {
            return await this.minioService.getFile(filePath);
        }
        catch (error) {
            throw new common_1.NotFoundException(`File not found: ${filePath}`);
        }
    }
    async getFileUrl(filePath, expiry = 24 * 60 * 60) {
        try {
            return await this.minioService.getFileUrl(filePath, expiry);
        }
        catch (error) {
            throw new common_1.NotFoundException(`File not found: ${filePath}`);
        }
    }
    async approveDocument(documentId, approvedBy, comment) {
        const approver = await this.usersRepository.findOne({
            where: { user_id: approvedBy }
        });
        if (!approver) {
            throw new common_1.NotFoundException(`User with ID ${approvedBy} not found`);
        }
        const document = await this.documentsRepository.findOne({
            where: { document_id: documentId },
            relations: ['creator']
        });
        if (!document) {
            throw new common_1.NotFoundException(`Document with ID ${documentId} not found`);
        }
        document.approved_by = approvedBy;
        document.approved_at = new Date();
        document.comment = comment || undefined;
        document.status = 'approved';
        document.updated_by = approvedBy;
        const savedDocument = await this.documentsRepository.save(document);
        setImmediate(async () => {
            try {
                await this.activityNotesService.create({
                    entity_type: 'document',
                    entity_id: documentId,
                    note: `Document "${document.file_name}" has been approved${comment ? `. Comment: ${comment}` : ''}`,
                    note_type: 'approval',
                    category: 'document_approval',
                    is_internal: false,
                }, approvedBy);
            }
            catch (error) {
                console.error('Failed to create activity note for document approval:', error);
            }
        });
        setImmediate(async () => {
            try {
                if (document.creator?.email) {
                    await this.notificationHelperService.sendEmailNotification({
                        recipientId: document.created_by,
                        recipientEmail: document.creator.email,
                        recipientName: document.creator.first_name || 'User',
                        subject: `Document Approved - ${document.file_name}`,
                        message: `Your document "${document.file_name}" has been approved.`,
                        htmlContent: this.generateDocumentApprovalEmailTemplate({
                            userName: document.creator.first_name || 'User',
                            documentName: document.file_name,
                            comment: comment,
                            approvalDate: new Date().toLocaleDateString(),
                        }),
                        entityType: 'document',
                        entityId: documentId,
                        createdBy: approvedBy,
                    });
                }
            }
            catch (error) {
                console.error('Failed to send document approval email:', error);
            }
        });
        return savedDocument;
    }
    async rejectDocument(documentId, rejectedBy, comment) {
        const rejector = await this.usersRepository.findOne({
            where: { user_id: rejectedBy }
        });
        if (!rejector) {
            throw new common_1.NotFoundException(`User with ID ${rejectedBy} not found`);
        }
        const document = await this.documentsRepository.findOne({
            where: { document_id: documentId },
            relations: ['creator']
        });
        if (!document) {
            throw new common_1.NotFoundException(`Document with ID ${documentId} not found`);
        }
        document.approved_by = undefined;
        document.approved_at = undefined;
        document.comment = comment || undefined;
        document.status = 'rejected';
        document.updated_by = rejectedBy;
        const savedDocument = await this.documentsRepository.save(document);
        setImmediate(async () => {
            try {
                await this.activityNotesService.create({
                    entity_type: 'document',
                    entity_id: documentId,
                    note: `Document "${document.file_name}" has been rejected${comment ? `. Comment: ${comment}` : ''}`,
                    note_type: 'rejection',
                    category: 'document_approval',
                    is_internal: false,
                }, rejectedBy);
            }
            catch (error) {
                console.error('Failed to create activity note for document rejection:', error);
            }
        });
        setImmediate(async () => {
            try {
                if (document.creator?.email) {
                    await this.notificationHelperService.sendEmailNotification({
                        recipientId: document.created_by,
                        recipientEmail: document.creator.email,
                        recipientName: document.creator.first_name || 'User',
                        subject: `Document Rejected - ${document.file_name}`,
                        message: `Your document "${document.file_name}" has been rejected.`,
                        htmlContent: this.generateDocumentRejectionEmailTemplate({
                            userName: document.creator.first_name || 'User',
                            documentName: document.file_name,
                            comment: comment,
                            rejectionDate: new Date().toLocaleDateString(),
                        }),
                        entityType: 'document',
                        entityId: documentId,
                        createdBy: rejectedBy,
                    });
                }
            }
            catch (error) {
                console.error('Failed to send document rejection email:', error);
            }
        });
        return savedDocument;
    }
    async deleteFile(filePath) {
        try {
            await this.minioService.deleteFile(filePath);
        }
        catch (error) {
            throw new common_1.NotFoundException(`File not found: ${filePath}`);
        }
    }
    getFolderByDocumentType(documentType) {
        const folderMap = {
            [documents_entity_1.DocumentType.CERTIFICATE_INCORPORATION]: 'certificates',
            [documents_entity_1.DocumentType.MEMORANDUM_ASSOCIATION]: 'legal-documents',
            [documents_entity_1.DocumentType.BUSINESS_PLAN]: 'business-plans',
            [documents_entity_1.DocumentType.FINANCIAL_STATEMENTS]: 'financial',
            [documents_entity_1.DocumentType.TECHNICAL_PROPOSAL]: 'technical',
            [documents_entity_1.DocumentType.PROOF_OF_PAYMENT]: 'payments',
            [documents_entity_1.DocumentType.CV_DOCUMENT]: 'cvs',
            [documents_entity_1.DocumentType.OTHER]: 'other',
        };
        return folderMap[documentType] || 'documents';
    }
    getAllowedMimeTypes() {
        return [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'image/jpeg',
            'image/png',
            'image/gif',
            'text/plain',
        ];
    }
    generateDocumentApprovalEmailTemplate(data) {
        return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
          <h2 style="color: #28a745; margin-bottom: 20px;">Document Approved</h2>

          <p>Dear ${data.userName},</p>

          <p>We are pleased to inform you that your document has been <strong>approved</strong>!</p>

          <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 15px; margin: 20px 0;">
            <h4 style="color: #155724; margin: 0 0 10px 0;">Document Details:</h4>
            <p style="margin: 5px 0;"><strong>Document Name:</strong> ${data.documentName}</p>
            <p style="margin: 5px 0;"><strong>Approval Date:</strong> ${data.approvalDate}</p>
            ${data.comment ? `<p style="margin: 5px 0;"><strong>Comment:</strong> ${data.comment}</p>` : ''}
          </div>

          <p>Your document has been successfully processed and is now approved in our system.</p>

          <p>If you have any questions, please don't hesitate to contact us.</p>

          <p>Best regards,<br>MACRA Digital Portal Team</p>
        </div>
      </div>
    `;
    }
    generateDocumentRejectionEmailTemplate(data) {
        return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
          <h2 style="color: #dc3545; margin-bottom: 20px;">Document Rejected</h2>

          <p>Dear ${data.userName},</p>

          <p>We regret to inform you that your document has been <strong>rejected</strong>.</p>

          <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin: 20px 0;">
            <h4 style="color: #721c24; margin: 0 0 10px 0;">Document Details:</h4>
            <p style="margin: 5px 0;"><strong>Document Name:</strong> ${data.documentName}</p>
            <p style="margin: 5px 0;"><strong>Rejection Date:</strong> ${data.rejectionDate}</p>
            ${data.comment ? `<p style="margin: 5px 0;"><strong>Reason:</strong> ${data.comment}</p>` : ''}
          </div>

          <p>Please review the feedback provided and resubmit your document with the necessary corrections.</p>

          <p>If you have any questions about this decision, please don't hesitate to contact us.</p>

          <p>Best regards,<br>MACRA Digital Portal Team</p>
        </div>
      </div>
    `;
    }
};
exports.DocumentsService = DocumentsService;
exports.DocumentsService = DocumentsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(documents_entity_1.Documents)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(3, (0, common_1.Inject)((0, common_1.forwardRef)(() => activity_notes_service_1.ActivityNotesService))),
    __param(4, (0, common_1.Inject)((0, common_1.forwardRef)(() => notification_helper_service_1.NotificationHelperService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        minio_service_1.MinioService,
        activity_notes_service_1.ActivityNotesService,
        notification_helper_service_1.NotificationHelperService])
], DocumentsService);
//# sourceMappingURL=documents.service.js.map