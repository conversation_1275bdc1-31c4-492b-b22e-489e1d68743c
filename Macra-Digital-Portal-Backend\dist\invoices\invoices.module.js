"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoicesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const invoices_service_1 = require("./invoices.service");
const invoices_controller_1 = require("./invoices.controller");
const customer_invoices_controller_1 = require("./customer-invoices.controller");
const invoice_pdf_service_1 = require("./invoice-pdf.service");
const invoices_entity_1 = require("../entities/invoices.entity");
const applications_entity_1 = require("../entities/applications.entity");
const applicant_entity_1 = require("../entities/applicant.entity");
const license_categories_entity_1 = require("../entities/license-categories.entity");
const payment_entity_1 = require("../entities/payment.entity");
const notifications_module_1 = require("../notifications/notifications.module");
const applications_module_1 = require("../applications/applications.module");
let InvoicesModule = class InvoicesModule {
};
exports.InvoicesModule = InvoicesModule;
exports.InvoicesModule = InvoicesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([invoices_entity_1.Invoices, applications_entity_1.Applications, applicant_entity_1.Applicants, license_categories_entity_1.LicenseCategories, payment_entity_1.Payment]),
            notifications_module_1.NotificationsModule,
            (0, common_1.forwardRef)(() => applications_module_1.ApplicationsModule),
        ],
        controllers: [invoices_controller_1.InvoicesController, customer_invoices_controller_1.CustomerInvoicesController],
        providers: [invoices_service_1.InvoicesService, invoice_pdf_service_1.InvoicePdfService],
        exports: [invoices_service_1.InvoicesService, invoice_pdf_service_1.InvoicePdfService],
    })
], InvoicesModule);
//# sourceMappingURL=invoices.module.js.map