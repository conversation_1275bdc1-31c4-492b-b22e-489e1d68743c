"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DepartmentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const department_service_1 = require("./department.service");
const department_entity_1 = require("../entities/department.entity");
const create_department_dto_1 = require("../dto/department/create-department.dto");
const update_department_dto_1 = require("../dto/department/update-department.dto");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const nestjs_paginate_1 = require("nestjs-paginate");
let DepartmentController = class DepartmentController {
    constructor(departmentService) {
        this.departmentService = departmentService;
    }
    async create(createDto) {
        return this.departmentService.create(createDto);
    }
    async findAll(query) {
        return this.departmentService.findAllPaginated(query);
    }
    async findOne(id) {
        return this.departmentService.findOne(id);
    }
    async update(id, updateDto) {
        return this.departmentService.update(id, updateDto);
    }
    async remove(id) {
        return this.departmentService.remove(id);
    }
    async restore(id) {
        return this.departmentService.restore(id);
    }
    async findDeleted() {
        return this.departmentService.findDeleted();
    }
};
exports.DepartmentController = DepartmentController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new department' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Department created', type: department_entity_1.Department }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Department',
        description: 'Created department',
    }),
    (0, swagger_1.ApiBody)({ type: create_department_dto_1.CreateDepartmentDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_department_dto_1.CreateDepartmentDto]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all departments with pagination' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Paginated list of departments' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Department',
        description: 'Viewed departments list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a department by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Department ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Department details', type: department_entity_1.Department }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Department not found' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Department',
        description: 'Viewed department details',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a department by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Department ID' }),
    (0, swagger_1.ApiBody)({ type: update_department_dto_1.UpdateDepartmentDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Department updated', type: department_entity_1.Department }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Department not found' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Department',
        description: 'Updated department',
    }),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_department_dto_1.UpdateDepartmentDto]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete a department by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Department ID' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Department deleted' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Department not found' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Department',
        description: 'Deleted department',
    }),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/restore'),
    (0, swagger_1.ApiOperation)({ summary: 'Restore a soft-deleted department by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Department ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Department restored', type: department_entity_1.Department }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Department not found' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Department',
        description: 'Restored department',
    }),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "restore", null);
__decorate([
    (0, common_1.Get)('deleted/list'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all soft-deleted departments' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of deleted departments', type: [department_entity_1.Department] }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'Department',
        description: 'Viewed deleted departments list',
    }),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DepartmentController.prototype, "findDeleted", null);
exports.DepartmentController = DepartmentController = __decorate([
    (0, swagger_1.ApiTags)('Departments'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('department'),
    __metadata("design:paramtypes", [department_service_1.DepartmentService])
], DepartmentController);
//# sourceMappingURL=department.controller.js.map