{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/AuthLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Image from 'next/image';\r\nimport Link from 'next/link';\r\n\r\ninterface AuthLayoutProps {\r\n  children: React.ReactNode;\r\n  title: string;\r\n  subtitle?: string | React.ReactNode;\r\n  showBackToLogin?: boolean;\r\n  loginPath?: string;\r\n  isCustomerPortal?: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst AuthLayout: React.FC<AuthLayoutProps> = ({\r\n  children,\r\n  title,\r\n  subtitle,\r\n  showBackToLogin = false,\r\n  loginPath,\r\n  isCustomerPortal = false,\r\n  className = ''\r\n}) => {\r\n  const defaultLoginPath = isCustomerPortal ? '/customer/auth/login' : '/auth/login';\r\n  const backToLoginPath = loginPath || defaultLoginPath;\r\n\r\n  return (\r\n    <div className={`min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900 auth-layout ${className}`}>\r\n      {/* Header Section */}\r\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md auth-header\">\r\n        <div className=\"flex justify-center\">\r\n          <Image\r\n            src=\"/images/macra-logo.png\"\r\n            alt=\"MACRA Logo\"\r\n            width={64}\r\n            height={64}\r\n            className=\"h-16 w-auto animate-fadeLoop\"\r\n            priority\r\n          />\r\n        </div>\r\n\r\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100 animate-slideInFromTop animate-delay-100\">\r\n          {title}\r\n        </h2>\r\n\r\n        {subtitle && (\r\n          <p className=\"mt-2 text-center text-sm text-gray-600 dark:text-gray-400 animate-slideInFromTop animate-delay-200\">\r\n            {subtitle}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <div className=\"bg-white dark:bg-gray-800 py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 transition-smooth auth-form\">\r\n          {children}\r\n        </div>\r\n\r\n        {/* Back to Login Link */}\r\n        {showBackToLogin && (\r\n          <div className=\"mt-6 text-center animate-fadeIn animate-delay-300\">\r\n            <Link\r\n              href={backToLoginPath}\r\n              className=\"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200 hover:underline\"\r\n            >\r\n              ← Back to sign in\r\n            </Link>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AuthLayout;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAgBA,MAAM,aAAwC;QAAC,EAC7C,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,kBAAkB,KAAK,EACvB,SAAS,EACT,mBAAmB,KAAK,EACxB,YAAY,EAAE,EACf;IACC,MAAM,mBAAmB,mBAAmB,yBAAyB;IACrE,MAAM,kBAAkB,aAAa;IAErC,qBACE,6LAAC;QAAI,WAAW,AAAC,2GAAoH,OAAV;;0BAEzH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,QAAQ;;;;;;;;;;;kCAIZ,6LAAC;wBAAG,WAAU;kCACX;;;;;;oBAGF,0BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAMP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ;;;;;;oBAIF,iCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM;4BACN,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;KA1DM;uCA4DS", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/StatusMessage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { CheckCircleIcon, ExclamationCircleIcon, InformationCircleIcon } from '@heroicons/react/24/solid';\r\n\r\ninterface StatusMessageProps {\r\n  type: 'success' | 'error' | 'info' | 'warning';\r\n  message: string;\r\n  className?: string;\r\n  showIcon?: boolean;\r\n  dismissible?: boolean;\r\n  onDismiss?: () => void;\r\n}\r\n\r\nconst StatusMessage: React.FC<StatusMessageProps> = ({\r\n  type,\r\n  message,\r\n  className = '',\r\n  showIcon = true,\r\n  dismissible = false,\r\n  onDismiss\r\n}) => {\r\n  const getStatusStyles = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return {\r\n          container: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-600 dark:text-green-400',\r\n          icon: 'text-green-500 dark:text-green-400'\r\n        };\r\n      case 'error':\r\n        return {\r\n          container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-600 dark:text-red-400',\r\n          icon: 'text-red-500 dark:text-red-400'\r\n        };\r\n      case 'warning':\r\n        return {\r\n          container: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-600 dark:text-yellow-400',\r\n          icon: 'text-yellow-500 dark:text-yellow-400'\r\n        };\r\n      case 'info':\r\n      default:\r\n        return {\r\n          container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-600 dark:text-blue-400',\r\n          icon: 'text-blue-500 dark:text-blue-400'\r\n        };\r\n    }\r\n  };\r\n\r\n  const getIcon = () => {\r\n    switch (type) {\r\n      case 'success':\r\n        return <CheckCircleIcon className=\"h-5 w-5\" />;\r\n      case 'error':\r\n      case 'warning':\r\n        return <ExclamationCircleIcon className=\"h-5 w-5\" />;\r\n      case 'info':\r\n      default:\r\n        return <InformationCircleIcon className=\"h-5 w-5\" />;\r\n    }\r\n  };\r\n\r\n  const styles = getStatusStyles();\r\n\r\n  return (\r\n    <div className={`border px-4 py-3 rounded-md transition-smooth status-message-enter ${styles.container} ${className} ${type === 'error' ? 'animate-shake' : ''}`}>\r\n      <div className=\"flex items-start\">\r\n        {showIcon && (\r\n          <div className={`flex-shrink-0 ${styles.icon} animate-scaleIn`}>\r\n            {getIcon()}\r\n          </div>\r\n        )}\r\n        <div className={`${showIcon ? 'ml-3' : ''} flex-1 animate-slideInFromBottom animate-delay-100`}>\r\n          <p className=\"text-sm font-medium\">{message}</p>\r\n        </div>\r\n        {dismissible && onDismiss && (\r\n          <button\r\n            onClick={onDismiss}\r\n            className=\"ml-3 flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors hover:scale-110 transform duration-200\"\r\n          >\r\n            <span className=\"sr-only\">Dismiss</span>\r\n            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n              <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StatusMessage;\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAHA;;;AAcA,MAAM,gBAA8C;QAAC,EACnD,IAAI,EACJ,OAAO,EACP,YAAY,EAAE,EACd,WAAW,IAAI,EACf,cAAc,KAAK,EACnB,SAAS,EACV;IACC,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;gBACR;YACF,KAAK;YACL;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBA<PERSON>,qBAAO,6LAAC,8NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,0OAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;YAC1C,KAAK;YACL;gBACE,qBAAO,6LAAC,0OAAA,CAAA,wBAAqB;oBAAC,WAAU;;;;;;QAC5C;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,6LAAC;QAAI,WAAW,AAAC,sEAAyF,OAApB,OAAO,SAAS,EAAC,KAAgB,OAAb,WAAU,KAA2C,OAAxC,SAAS,UAAU,kBAAkB;kBAC1J,cAAA,6LAAC;YAAI,WAAU;;gBACZ,0BACC,6LAAC;oBAAI,WAAW,AAAC,iBAA4B,OAAZ,OAAO,IAAI,EAAC;8BAC1C;;;;;;8BAGL,6LAAC;oBAAI,WAAW,AAAC,GAAyB,OAAvB,WAAW,SAAS,IAAG;8BACxC,cAAA,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;gBAErC,eAAe,2BACd,6LAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,6LAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,6LAAC;4BAAI,WAAU;4BAAU,SAAQ;4BAAY,MAAK;sCAChD,cAAA,6LAAC;gCAAK,UAAS;gCAAU,GAAE;gCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvP;KA1EM;uCA4ES", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/LoadingState.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\n\r\ninterface LoadingStateProps {\r\n  message?: string;\r\n  submessage?: string;\r\n  showProgress?: boolean;\r\n  progress?: number;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  dynamicMessages?: string[];\r\n  messageInterval?: number;\r\n}\r\n\r\nconst LoadingState: React.FC<LoadingStateProps> = ({\r\n  message = 'Loading...',\r\n  submessage,\r\n  showProgress = false,\r\n  progress = 0,\r\n  size = 'md',\r\n  className = '',\r\n  dynamicMessages = [],\r\n  messageInterval = 2000\r\n}) => {\r\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\r\n  const [displayMessage, setDisplayMessage] = useState(message);\r\n\r\n  // Handle dynamic message rotation\r\n  useEffect(() => {\r\n    if (dynamicMessages.length > 0) {\r\n      const interval = setInterval(() => {\r\n        setCurrentMessageIndex((prev) => (prev + 1) % dynamicMessages.length);\r\n      }, messageInterval);\r\n\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [dynamicMessages, messageInterval]);\r\n\r\n  // Update display message when dynamic messages change\r\n  useEffect(() => {\r\n    if (dynamicMessages.length > 0) {\r\n      setDisplayMessage(dynamicMessages[currentMessageIndex]);\r\n    } else {\r\n      setDisplayMessage(message);\r\n    }\r\n  }, [currentMessageIndex, dynamicMessages, message]);\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return {\r\n          container: 'w-12 h-12',\r\n          logo: 'h-6 w-6',\r\n          text: 'text-sm'\r\n        };\r\n      case 'lg':\r\n        return {\r\n          container: 'w-24 h-24',\r\n          logo: 'h-12 w-12',\r\n          text: 'text-lg'\r\n        };\r\n      case 'md':\r\n      default:\r\n        return {\r\n          container: 'w-20 h-20',\r\n          logo: 'h-10 w-10',\r\n          text: 'text-base'\r\n        };\r\n    }\r\n  };\r\n\r\n  const sizeClasses = getSizeClasses();\r\n\r\n  return (\r\n    <div className={`text-center ${className}`}>\r\n      {/* Loading Spinner with Logo */}\r\n      <div className={`relative ${sizeClasses.container} mx-auto`}>\r\n        {/* Animated Spinner */}\r\n        <svg\r\n          className=\"absolute inset-0 animate-spin\"\r\n          viewBox=\"0 0 50 50\"\r\n          fill=\"none\"\r\n        >\r\n          <defs>\r\n            <linearGradient id=\"fadeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n              <stop offset=\"0%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n              <stop offset=\"20%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n              <stop offset=\"80%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n              <stop offset=\"100%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n            </linearGradient>\r\n          </defs>\r\n\r\n          <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"rgba(255, 255, 255, 0.1)\"\r\n            strokeWidth=\"2\"\r\n          />\r\n          <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"url(#fadeGradient)\"\r\n            strokeWidth=\"2\"\r\n            strokeDasharray=\"70\"\r\n            strokeDashoffset=\"10\"\r\n            fill=\"none\"\r\n          />\r\n        </svg>\r\n\r\n        {/* MACRA Logo */}\r\n        <Image\r\n          src=\"/images/macra-logo.png\"\r\n          alt=\"MACRA Logo\"\r\n          width={40}\r\n          height={40}\r\n          className={`object-contain absolute inset-0 ${sizeClasses.logo} m-auto animate-pulse`}\r\n          priority\r\n        />\r\n      </div>\r\n\r\n      {/* Progress Bar */}\r\n      {showProgress && (\r\n        <div className=\"mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n          <div\r\n            className=\"bg-red-600 h-2 rounded-full transition-all duration-300 ease-out\"\r\n            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Loading Message */}\r\n      <div className=\"mt-4 space-y-1\">\r\n        <p className={`text-gray-600 dark:text-gray-400 font-medium ${sizeClasses.text} transition-opacity duration-300`}>\r\n          {displayMessage}\r\n        </p>\r\n        {submessage && (\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-500\">\r\n            {submessage}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoadingState;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAgBA,MAAM,eAA4C;QAAC,EACjD,UAAU,YAAY,EACtB,UAAU,EACV,eAAe,KAAK,EACpB,WAAW,CAAC,EACZ,OAAO,IAAI,EACX,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,kBAAkB,IAAI,EACvB;;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,MAAM,WAAW;uDAAY;wBAC3B;+DAAuB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,gBAAgB,MAAM;;oBACtE;sDAAG;gBAEH;8CAAO,IAAM,cAAc;;YAC7B;QACF;iCAAG;QAAC;QAAiB;KAAgB;IAErC,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,kBAAkB,eAAe,CAAC,oBAAoB;YACxD,OAAO;gBACL,kBAAkB;YACpB;QACF;iCAAG;QAAC;QAAqB;QAAiB;KAAQ;IAElD,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;YACL;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,6LAAC;QAAI,WAAW,AAAC,eAAwB,OAAV;;0BAE7B,6LAAC;gBAAI,WAAW,AAAC,YAAiC,OAAtB,YAAY,SAAS,EAAC;;kCAEhD,6LAAC;wBACC,WAAU;wBACV,SAAQ;wBACR,MAAK;;0CAEL,6LAAC;0CACC,cAAA,6LAAC;oCAAe,IAAG;oCAAe,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC7D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;4CAAU,aAAY;;;;;;sDAClD,6LAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,6LAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,6LAAC;4CAAK,QAAO;4CAAO,WAAU;4CAAU,aAAY;;;;;;;;;;;;;;;;;0CAIxD,6LAAC;gCACC,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;;;;;;0CAEd,6LAAC;gCACC,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,iBAAgB;gCAChB,kBAAiB;gCACjB,MAAK;;;;;;;;;;;;kCAKT,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAW,AAAC,mCAAmD,OAAjB,YAAY,IAAI,EAAC;wBAC/D,QAAQ;;;;;;;;;;;;YAKX,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,AAAC,GAAuC,OAArC,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,YAAW;oBAAG;;;;;;;;;;;0BAMjE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAW,AAAC,gDAAgE,OAAjB,YAAY,IAAI,EAAC;kCAC5E;;;;;;oBAEF,4BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;GAnIM;KAAA;uCAqIS", "debugId": null}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/PageTransition.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport LoadingState from './LoadingState';\r\n\r\ninterface PageTransitionProps {\r\n  children: React.ReactNode;\r\n  isLoading?: boolean;\r\n  loadingMessage?: string;\r\n  loadingSubmessage?: string;\r\n  redirectTo?: string;\r\n  redirectDelay?: number;\r\n  showProgress?: boolean;\r\n  dynamicMessages?: string[];\r\n  className?: string;\r\n}\r\n\r\nconst PageTransition: React.FC<PageTransitionProps> = ({\r\n  children,\r\n  isLoading = false,\r\n  loadingMessage = 'Loading...',\r\n  loadingSubmessage,\r\n  redirectTo,\r\n  redirectDelay = 2000,\r\n  showProgress = false,\r\n  dynamicMessages = [],\r\n  className = ''\r\n}) => {\r\n  const router = useRouter();\r\n  const [progress, setProgress] = useState(0);\r\n  const [isRedirecting, setIsRedirecting] = useState(false);\r\n\r\n  // Handle redirect with progress\r\n  useEffect(() => {\r\n    if (redirectTo && !isRedirecting) {\r\n      setIsRedirecting(true);\r\n      \r\n      if (showProgress) {\r\n        const progressInterval = setInterval(() => {\r\n          setProgress((prev) => {\r\n            if (prev >= 100) {\r\n              clearInterval(progressInterval);\r\n              return 100;\r\n            }\r\n            return prev + (100 / (redirectDelay / 100));\r\n          });\r\n        }, 100);\r\n\r\n        const redirectTimeout = setTimeout(() => {\r\n          router.push(redirectTo);\r\n        }, redirectDelay);\r\n\r\n        return () => {\r\n          clearInterval(progressInterval);\r\n          clearTimeout(redirectTimeout);\r\n        };\r\n      } else {\r\n        const redirectTimeout = setTimeout(() => {\r\n          router.push(redirectTo);\r\n        }, redirectDelay);\r\n\r\n        return () => clearTimeout(redirectTimeout);\r\n      }\r\n    }\r\n  }, [redirectTo, redirectDelay, router, showProgress, isRedirecting]);\r\n\r\n  if (isLoading || isRedirecting) {\r\n    return (\r\n      <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${className}`}>\r\n        <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\r\n          <LoadingState\r\n            message={loadingMessage}\r\n            submessage={loadingSubmessage}\r\n            showProgress={showProgress}\r\n            progress={progress}\r\n            size=\"lg\"\r\n            dynamicMessages={dynamicMessages}\r\n          />\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`transition-all duration-300 ease-in-out ${className}`}>\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageTransition;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAkBA,MAAM,iBAAgD;QAAC,EACrD,QAAQ,EACR,YAAY,KAAK,EACjB,iBAAiB,YAAY,EAC7B,iBAAiB,EACjB,UAAU,EACV,gBAAgB,IAAI,EACpB,eAAe,KAAK,EACpB,kBAAkB,EAAE,EACpB,YAAY,EAAE,EACf;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,cAAc,CAAC,eAAe;gBAChC,iBAAiB;gBAEjB,IAAI,cAAc;oBAChB,MAAM,mBAAmB;qEAAY;4BACnC;6EAAY,CAAC;oCACX,IAAI,QAAQ,KAAK;wCACf,cAAc;wCACd,OAAO;oCACT;oCACA,OAAO,OAAQ,MAAM,CAAC,gBAAgB,GAAG;gCAC3C;;wBACF;oEAAG;oBAEH,MAAM,kBAAkB;oEAAW;4BACjC,OAAO,IAAI,CAAC;wBACd;mEAAG;oBAEH;oDAAO;4BACL,cAAc;4BACd,aAAa;wBACf;;gBACF,OAAO;oBACL,MAAM,kBAAkB;oEAAW;4BACjC,OAAO,IAAI,CAAC;wBACd;mEAAG;oBAEH;oDAAO,IAAM,aAAa;;gBAC5B;YACF;QACF;mCAAG;QAAC;QAAY;QAAe;QAAQ;QAAc;KAAc;IAEnE,IAAI,aAAa,eAAe;QAC9B,qBACE,6LAAC;YAAI,WAAW,AAAC,6EAAsF,OAAV;sBAC3F,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6IAAA,CAAA,UAAY;oBACX,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,UAAU;oBACV,MAAK;oBACL,iBAAiB;;;;;;;;;;;;;;;;IAK3B;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,2CAAoD,OAAV;kBACxD;;;;;;AAGP;GAvEM;;QAWW,qIAAA,CAAA,YAAS;;;KAXpB;uCAyES", "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/SuccessState.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { CheckCircleIcon, EnvelopeIcon } from '@heroicons/react/24/solid';\r\n\r\ninterface SuccessStateProps {\r\n  title: string;\r\n  message: string;\r\n  submessage?: string;\r\n  showEmailIcon?: boolean;\r\n  actionText?: string;\r\n  actionHref?: string;\r\n  secondaryActionText?: string;\r\n  secondaryActionHref?: string;\r\n  autoRedirect?: boolean;\r\n  redirectDelay?: number;\r\n  redirectMessage?: string;\r\n  className?: string;\r\n}\r\n\r\nconst SuccessState: React.FC<SuccessStateProps> = ({\r\n  title,\r\n  message,\r\n  submessage,\r\n  showEmailIcon = false,\r\n  actionText,\r\n  actionHref,\r\n  secondaryActionText,\r\n  secondaryActionHref,\r\n  autoRedirect = false,\r\n  redirectDelay = 5000,\r\n  redirectMessage,\r\n  className = ''\r\n}) => {\r\n  const [countdown, setCountdown] = useState(Math.ceil(redirectDelay / 1000));\r\n\r\n  useEffect(() => {\r\n    if (autoRedirect && actionHref) {\r\n      const timer = setInterval(() => {\r\n        setCountdown((prev) => {\r\n          if (prev <= 1) {\r\n            clearInterval(timer);\r\n            window.location.href = actionHref;\r\n            return 0;\r\n          }\r\n          return prev - 1;\r\n        });\r\n      }, 1000);\r\n\r\n      return () => clearInterval(timer);\r\n    }\r\n  }, [autoRedirect, actionHref]);\r\n\r\n  return (\r\n    <div className={`flex flex-col items-center justify-center text-center ${className}`}>\r\n      {/* Success Icon */}\r\n      <div className=\"w-16 h-16 mb-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center shadow-lg success-icon\">\r\n        {showEmailIcon ? (\r\n          <EnvelopeIcon className=\"w-8 h-8 text-green-600 dark:text-green-300\" />\r\n        ) : (\r\n          <CheckCircleIcon className=\"w-8 h-8 text-green-600 dark:text-green-300\" />\r\n        )}\r\n      </div>\r\n\r\n      {/* Title */}\r\n      <h3 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3 success-content\">\r\n        {title}\r\n      </h3>\r\n\r\n      {/* Main Message */}\r\n      <p className=\"text-gray-600 dark:text-gray-400 mb-2 max-w-md success-content\">\r\n        {message}\r\n      </p>\r\n\r\n      {/* Submessage */}\r\n      {submessage && (\r\n        <p className=\"text-sm text-gray-500 dark:text-gray-500 mb-6 max-w-md success-content\">\r\n          {submessage}\r\n        </p>\r\n      )}\r\n\r\n      {/* Auto Redirect Message */}\r\n      {autoRedirect && redirectMessage && countdown > 0 && (\r\n        <div className=\"mb-6 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg animate-pulse\">\r\n          <p className=\"text-sm text-blue-600 dark:text-blue-400\">\r\n            {redirectMessage.replace('{countdown}', countdown.toString())}\r\n          </p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex flex-col sm:flex-row gap-3 w-full max-w-sm success-content\">\r\n        {actionText && actionHref && (\r\n          <Link\r\n            href={actionHref}\r\n            className=\"flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 button-hover-lift transition-smooth\"\r\n          >\r\n            {actionText}\r\n          </Link>\r\n        )}\r\n\r\n        {secondaryActionText && secondaryActionHref && (\r\n          <Link\r\n            href={secondaryActionHref}\r\n            className=\"flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 button-hover-lift transition-smooth\"\r\n          >\r\n            {secondaryActionText}\r\n          </Link>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SuccessState;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAqBA,MAAM,eAA4C;QAAC,EACjD,KAAK,EACL,OAAO,EACP,UAAU,EACV,gBAAgB,KAAK,EACrB,UAAU,EACV,UAAU,EACV,mBAAmB,EACnB,mBAAmB,EACnB,eAAe,KAAK,EACpB,gBAAgB,IAAI,EACpB,eAAe,EACf,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,IAAI,CAAC,gBAAgB;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,gBAAgB,YAAY;gBAC9B,MAAM,QAAQ;oDAAY;wBACxB;4DAAa,CAAC;gCACZ,IAAI,QAAQ,GAAG;oCACb,cAAc;oCACd,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACvB,OAAO;gCACT;gCACA,OAAO,OAAO;4BAChB;;oBACF;mDAAG;gBAEH;8CAAO,IAAM,cAAc;;YAC7B;QACF;iCAAG;QAAC;QAAc;KAAW;IAE7B,qBACE,6LAAC;QAAI,WAAW,AAAC,yDAAkE,OAAV;;0BAEvE,6LAAC;gBAAI,WAAU;0BACZ,8BACC,6LAAC,wNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;6EAExB,6LAAC,8NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;0BAK/B,6LAAC;gBAAG,WAAU;0BACX;;;;;;0BAIH,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAIF,4BACC,6LAAC;gBAAE,WAAU;0BACV;;;;;;YAKJ,gBAAgB,mBAAmB,YAAY,mBAC9C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BACV,gBAAgB,OAAO,CAAC,eAAe,UAAU,QAAQ;;;;;;;;;;;0BAMhE,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,4BACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM;wBACN,WAAU;kCAET;;;;;;oBAIJ,uBAAuB,qCACtB,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAM;wBACN,WAAU;kCAET;;;;;;;;;;;;;;;;;;AAMb;GA5FM;KAAA;uCA8FS", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/index.ts"], "sourcesContent": ["export { default as AuthLayout } from './AuthLayout';\r\nexport { default as StatusMessage } from './StatusMessage';\r\nexport { default as LoadingState } from './LoadingState';\r\nexport { default as PageTransition } from './PageTransition';\r\nexport { default as SuccessState } from './SuccessState';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/auth/login/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, Suspense } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { AuthLayout, StatusMessage, PageTransition } from '@/components/auth';\r\nimport { getErrorMessage } from '@/lib';\r\nimport { authService } from '@/services/auth.service';\r\n\r\n\r\nfunction LoginForm() {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [rememberMe, setRememberMe] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [fieldErrors, setFieldErrors] = useState<{email?: string; password?: string}>({});\r\n  const [isCustomerPortal, setIsCustomerPortal] = useState(false);\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [requires2FA, setRequires2FA] = useState(false);\r\n  const [loadingMessage, setLoadingMessage] = useState('');\r\n  const [forgotPasswordLoading, setForgotPasswordLoading] = useState(false);\r\n  const [dynamicMessages, setDynamicMessages] = useState<string[]>(['Connecting to staff portal...']);\r\n  const { login, isAuthenticated, loading: staffLoading } = useAuth();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n\r\n  // Client-side initialization\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Check if we're on staff portal - fix the logic completely\r\n\r\n    const message = searchParams.get('message');\r\n    if (message) {\r\n      setSuccessMessage(message);\r\n    }\r\n\r\n    // Add timeout to prevent infinite loading - but don't redirect on timeout\r\n    const loadingTimeout = setTimeout(() => {\r\n      if (loading && !error) {\r\n        setLoading(false);\r\n      }\r\n    }, 10000); // 10 second timeout\r\n\r\n    return () => clearTimeout(loadingTimeout);\r\n  }, [searchParams, loading, isClient, error, isCustomerPortal, router]);\r\n\r\n  // Redirect if already authenticated - only within staff portal, never to customer\r\n  useEffect(() => {\r\n    // Don't redirect in any of these conditions:\r\n    if (requires2FA) return;\r\n    if (error) return; // Explicit error check first\r\n    if (loading || staffLoading) return; // Don't redirect during loading\r\n    if (!isClient) return; // Wait for client-side hydration\r\n    if (isCustomerPortal) return; // Only redirect in staff portal\r\n\r\n    // Only redirect if user is authenticated and no errors\r\n    if (isAuthenticated && !error) {\r\n      console.log('User already authenticated, redirecting to staff dashboard');\r\n      setIsCustomerPortal(false);\r\n      router.replace('/dashboard');\r\n    }\r\n  }, [requires2FA, isCustomerPortal, isAuthenticated, staffLoading, loading, router, error, isClient]);\r\n\r\n  const validateEmail = (email: string): string | null => {\r\n    if (!email.trim()) {\r\n      return 'Email address is required';\r\n    }\r\n\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    if (!emailRegex.test(email)) {\r\n      return 'Please enter a valid email address';\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const validatePassword = (password: string): string | null => {\r\n    if (!password) {\r\n      return 'Password is required';\r\n    }\r\n\r\n    if (password.length < 8) {\r\n      return 'Password must be at least 8 characters long';\r\n    }\r\n\r\n    return null;\r\n  };\r\n\r\n  const validateForm = (): string | null => {\r\n    const emailError = validateEmail(email);\r\n    const passwordError = validatePassword(password);\r\n\r\n    setFieldErrors({\r\n      email: emailError || undefined,\r\n      password: passwordError || undefined,\r\n    });\r\n\r\n    if (emailError) return emailError;\r\n    if (passwordError) return passwordError;\r\n\r\n    return null;\r\n  };\r\n\r\n  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setEmail(value);\r\n\r\n    // Clear field error when user starts typing\r\n    if (fieldErrors.email) {\r\n      setFieldErrors(prev => ({ ...prev, email: undefined }));\r\n    }\r\n\r\n    // Clear general error when user starts typing (with slight delay to let user see the error)\r\n    if (error) {\r\n      setTimeout(() => {\r\n        setError('');\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setPassword(value);\r\n\r\n    // Clear field error when user starts typing\r\n    if (fieldErrors.password) {\r\n      setFieldErrors(prev => ({ ...prev, password: undefined }));\r\n    }\r\n\r\n    // Clear general error when user starts typing (with slight delay to let user see the error)\r\n    if (error) {\r\n      setTimeout(() => {\r\n        setError('');\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Clear previous messages\r\n    setError('');\r\n    setSuccessMessage('');\r\n\r\n    // Validate form before submission\r\n    const validationError = validateForm();\r\n    if (validationError) {\r\n      setError(validationError);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      // Always treat this as staff login since we're on staff portal\r\n      setDynamicMessages(['Verifying your credentials...', 'Please wait...']);\r\n      const response = await login(email.trim().toLowerCase(), password, rememberMe);\r\n      if (response) {\r\n        // Check if 2FA is required\r\n        const requires2FA = response.user.two_factor_enabled;\r\n        if (requires2FA) {\r\n          setRequires2FA(true);\r\n          setSuccessMessage('Login successful! Two-factor authentication is enabled for your account. Please check your email for the verification code.');\r\n          setDynamicMessages(['Login successful!', 'Sending verification code...', 'Redirecting to 2FA verification...']);\r\n          setTimeout(() => {\r\n            router.replace('/auth/verify-2fa');\r\n          }, 1500);\r\n        } else {\r\n          setSuccessMessage('Login successful! Redirecting to your dashboard...');\r\n          setDynamicMessages(['Login successful!', 'Setting up your session...', 'Redirecting...']);\r\n\r\n          setTimeout(() => {\r\n            router.replace('/dashboard');\r\n          }, 1500);\r\n        }\r\n      } else {\r\n        setError('Invalid user session. Please try again.');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n    } catch (err: unknown) {\r\n      // AuthService now provides clean error messages, so we can use them directly\r\n      setLoading(false);\r\n      console.error('Staff login error:', err)\r\n      const errorMessage = getErrorMessage(err);\r\n      setError(errorMessage);\r\n      // Return early - stay on login page when authentication fails\r\n      return;\r\n    }\r\n  };\r\n\r\n  const handleForgotPasswordClick = (e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n    setForgotPasswordLoading(true);\r\n    setLoadingMessage('Redirecting to forgot password...');\r\n    setTimeout(() => {\r\n      router.push('/auth/forgot-password');\r\n    }, 3000);\r\n  };\r\n\r\n  // Show loading while client is initializing, checking authentication, or during login/redirect\r\n  if (!isClient || loading || forgotPasswordLoading) {\r\n\r\n    return (\r\n      <PageTransition\r\n        isLoading={true}\r\n        loadingMessage={loadingMessage || (!isClient ? 'Loading...' : 'Signing in...')}\r\n        loadingSubmessage=\"Please wait while we process your request\"\r\n        dynamicMessages={loading ? dynamicMessages : undefined}\r\n        showProgress={loading}\r\n      >\r\n        <div />\r\n      </PageTransition>\r\n    );\r\n  }\r\n\r\n  return (\r\n          <AuthLayout\r\n            title=\"Welcome Back!\"\r\n            subtitle={(\r\n              <>\r\n                Staff Portal Access{' '}\r\n                <span className=\"text-red-600 dark:text-red-400\">\r\n                  • Secure Dashboard Login\r\n                </span>\r\n              </>\r\n            )}\r\n            isCustomerPortal={false}\r\n          >\r\n          {error && (\r\n            <StatusMessage\r\n              type=\"error\"\r\n              message={error}\r\n              className=\"mb-4\"\r\n              dismissible={true}\r\n              onDismiss={() => setError('')}\r\n            />\r\n          )}\r\n\r\n          {successMessage && (\r\n            <StatusMessage\r\n              type=\"success\"\r\n              message={successMessage}\r\n              className=\"mb-4\"\r\n              dismissible={true}\r\n              onDismiss={() => setSuccessMessage('')}\r\n            />\r\n          )}\r\n\r\n          <form className=\"space-y-6 animate-fadeIn animate-delay-200\" onSubmit={handleSubmit}>\r\n            <div className=\"animate-slideInFromBottom animate-delay-300\">\r\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Email address\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  autoComplete=\"email\"\r\n                  required\r\n                  value={email}\r\n                  onChange={handleEmailChange}\r\n                  className={`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ${\r\n                    fieldErrors.email\r\n                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake'\r\n                      : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'\r\n                  }`}\r\n                  placeholder=\"Enter your email address\"\r\n                />\r\n              </div>\r\n              {fieldErrors.email && (\r\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop\">\r\n                  <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  {fieldErrors.email}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"animate-slideInFromBottom animate-delay-500\">\r\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Password\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"password\"\r\n                  name=\"password\"\r\n                  type=\"password\"\r\n                  autoComplete=\"current-password\"\r\n                  required\r\n                  value={password}\r\n                  onChange={handlePasswordChange}\r\n                  className={`appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ${\r\n                    fieldErrors.password\r\n                      ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake'\r\n                      : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'\r\n                  }`}\r\n                  placeholder=\"Enter your password\"\r\n                />\r\n              </div>\r\n              {fieldErrors.password && (\r\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop\">\r\n                  <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                  {fieldErrors.password}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            {/* Show Remember Me and Forgot Password only for Staff Portal */}\r\n            {!isCustomerPortal && (\r\n              <div className=\"flex items-center justify-between animate-fadeIn animate-delay-500\">\r\n                <div className=\"flex items-center\">\r\n                  <input\r\n                    id=\"remember-me\"\r\n                    name=\"remember-me\"\r\n                    type=\"checkbox\"\r\n                    checked={rememberMe}\r\n                    onChange={(e) => setRememberMe(e.target.checked)}\r\n                    className=\"h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 transition-smooth\"\r\n                  />\r\n                  <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900 dark:text-gray-100\">\r\n                    Remember me\r\n                  </label>\r\n                </div>\r\n\r\n                <div className=\"text-sm\">\r\n                  <a\r\n                    href=\"/auth/forgot-password\"\r\n                    onClick={handleForgotPasswordClick}\r\n                    className=\"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors hover:underline cursor-pointer\"\r\n                  >\r\n                    Forgot your password?\r\n                  </a>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"animate-slideInFromBottom animate-delay-500\">\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className=\"w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 button-hover-lift transition-smooth disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? (\r\n                  <>\r\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                    Signing in...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3 3v1\" />\r\n                    </svg>\r\n                    Sign in\r\n                  </>\r\n                )}\r\n              </button>\r\n            </div>\r\n          </form>\r\n    </AuthLayout>\r\n  );\r\n}\r\n\r\nexport default function LoginPage() {\r\n  console.log('LoginPage component rendering...');\r\n  return (\r\n    <Suspense fallback={\r\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600\"></div>\r\n        <p className=\"mt-4 text-gray-600\">Loading login page...</p>\r\n      </div>\r\n    }>\r\n      <LoginForm />\r\n    </Suspense>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;;;AANA;;;;;;AAUA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC,CAAC;IACrF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAgC;IAClG,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,YAAY,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAEnC,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,YAAY;QACd;8BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,4DAA4D;YAE5D,MAAM,UAAU,aAAa,GAAG,CAAC;YACjC,IAAI,SAAS;gBACX,kBAAkB;YACpB;YAEA,0EAA0E;YAC1E,MAAM,iBAAiB;sDAAW;oBAChC,IAAI,WAAW,CAAC,OAAO;wBACrB,WAAW;oBACb;gBACF;qDAAG,QAAQ,oBAAoB;YAE/B;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;QAAc;QAAS;QAAU;QAAO;QAAkB;KAAO;IAErE,kFAAkF;IAClF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,6CAA6C;YAC7C,IAAI,aAAa;YACjB,IAAI,OAAO,QAAQ,6BAA6B;YAChD,IAAI,WAAW,cAAc,QAAQ,gCAAgC;YACrE,IAAI,CAAC,UAAU,QAAQ,iCAAiC;YACxD,IAAI,kBAAkB,QAAQ,gCAAgC;YAE9D,uDAAuD;YACvD,IAAI,mBAAmB,CAAC,OAAO;gBAC7B,QAAQ,GAAG,CAAC;gBACZ,oBAAoB;gBACpB,OAAO,OAAO,CAAC;YACjB;QACF;8BAAG;QAAC;QAAa;QAAkB;QAAiB;QAAc;QAAS;QAAQ;QAAO;KAAS;IAEnG,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,OAAO;QACT;QAEA,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,MAAM,aAAa,cAAc;QACjC,MAAM,gBAAgB,iBAAiB;QAEvC,eAAe;YACb,OAAO,cAAc;YACrB,UAAU,iBAAiB;QAC7B;QAEA,IAAI,YAAY,OAAO;QACvB,IAAI,eAAe,OAAO;QAE1B,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QAET,4CAA4C;QAC5C,IAAI,YAAY,KAAK,EAAE;YACrB,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO;gBAAU,CAAC;QACvD;QAEA,4FAA4F;QAC5F,IAAI,OAAO;YACT,WAAW;gBACT,SAAS;YACX,GAAG;QACL;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,YAAY;QAEZ,4CAA4C;QAC5C,IAAI,YAAY,QAAQ,EAAE;YACxB,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,UAAU;gBAAU,CAAC;QAC1D;QAEA,4FAA4F;QAC5F,IAAI,OAAO;YACT,WAAW;gBACT,SAAS;YACX,GAAG;QACL;IACF;IAGA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,0BAA0B;QAC1B,SAAS;QACT,kBAAkB;QAElB,kCAAkC;QAClC,MAAM,kBAAkB;QACxB,IAAI,iBAAiB;YACnB,SAAS;YACT,WAAW;YACX;QACF;QAEA,WAAW;QACX,IAAI;YACF,+DAA+D;YAC/D,mBAAmB;gBAAC;gBAAiC;aAAiB;YACtE,MAAM,WAAW,MAAM,MAAM,MAAM,IAAI,GAAG,WAAW,IAAI,UAAU;YACnE,IAAI,UAAU;gBACZ,2BAA2B;gBAC3B,MAAM,cAAc,SAAS,IAAI,CAAC,kBAAkB;gBACpD,IAAI,aAAa;oBACf,eAAe;oBACf,kBAAkB;oBAClB,mBAAmB;wBAAC;wBAAqB;wBAAgC;qBAAqC;oBAC9G,WAAW;wBACT,OAAO,OAAO,CAAC;oBACjB,GAAG;gBACL,OAAO;oBACL,kBAAkB;oBAClB,mBAAmB;wBAAC;wBAAqB;wBAA8B;qBAAiB;oBAExF,WAAW;wBACT,OAAO,OAAO,CAAC;oBACjB,GAAG;gBACL;YACF,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX;YACF;QACF,EAAE,OAAO,KAAc;YACrB,6EAA6E;YAC7E,WAAW;YACX,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE;YACrC,SAAS;YACT,8DAA8D;YAC9D;QACF;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,EAAE,cAAc;QAChB,yBAAyB;QACzB,kBAAkB;QAClB,WAAW;YACT,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,+FAA+F;IAC/F,IAAI,CAAC,YAAY,WAAW,uBAAuB;QAEjD,qBACE,6LAAC,4LAAA,CAAA,iBAAc;YACb,WAAW;YACX,gBAAgB,kBAAkB,CAAC,CAAC,WAAW,eAAe,eAAe;YAC7E,mBAAkB;YAClB,iBAAiB,UAAU,kBAAkB;YAC7C,cAAc;sBAEd,cAAA,6LAAC;;;;;;;;;;IAGP;IAEA,qBACQ,6LAAC,oLAAA,CAAA,aAAU;QACT,OAAM;QACN,wBACE;;gBAAE;gBACoB;8BACpB,6LAAC;oBAAK,WAAU;8BAAiC;;;;;;;;QAKrD,kBAAkB;;YAEnB,uBACC,6LAAC,0LAAA,CAAA,gBAAa;gBACZ,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,aAAa;gBACb,WAAW,IAAM,SAAS;;;;;;YAI7B,gCACC,6LAAC,0LAAA,CAAA,gBAAa;gBACZ,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,aAAa;gBACb,WAAW,IAAM,kBAAkB;;;;;;0BAIvC,6LAAC;gBAAK,WAAU;gBAA6C,UAAU;;kCACrE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA6D;;;;;;0CAG9F,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,cAAa;oCACb,QAAQ;oCACR,OAAO;oCACP,UAAU;oCACV,WAAW,AAAC,wSAIX,OAHC,YAAY,KAAK,GACb,6FACA;oCAEN,aAAY;;;;;;;;;;;4BAGf,YAAY,KAAK,kBAChB,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoH,UAAS;;;;;;;;;;;oCAEzJ,YAAY,KAAK;;;;;;;;;;;;;kCAKxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA6D;;;;;;0CAGjG,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,cAAa;oCACb,QAAQ;oCACR,OAAO;oCACP,UAAU;oCACV,WAAW,AAAC,wSAIX,OAHC,YAAY,QAAQ,GAChB,6FACA;oCAEN,aAAY;;;;;;;;;;;4BAGf,YAAY,QAAQ,kBACnB,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAe,SAAQ;kDACxD,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoH,UAAS;;;;;;;;;;;oCAEzJ,YAAY,QAAQ;;;;;;;;;;;;;oBAM1B,CAAC,kCACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;wCAC/C,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAsD;;;;;;;;;;;;0CAK/F,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAOP,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,wBACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAAuE;;6DAIxF;;kDACE,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;;;;;;;;;AAS1B;GAxWS;;QAcmD,kIAAA,CAAA,UAAO;QAClD,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAhB7B;AA0WM,SAAS;IACtB,QAAQ,GAAG,CAAC;IACZ,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBACR,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;kBAGpC,cAAA,6LAAC;;;;;;;;;;AAGP;MAZwB", "debugId": null}}, {"offset": {"line": 1473, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1674, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1890, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1919, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,wCAA2C;IACzC,MAAMI,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1946, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `\"auto\"`, `null`, `undefined` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | 'auto' | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  if (onNavigate) {\n    let isDefaultPrevented = false\n\n    onNavigate({\n      preventDefault: () => {\n        isDefaultPrevented = true\n      },\n    })\n\n    if (isDefaultPrevented) {\n      return\n    }\n  }\n\n  React.startTransition(() => {\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  })\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null || prefetchProp === 'auto'\n      ? PrefetchKind.AUTO\n      : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else if (key === 'prefetch') {\n        if (\n          props[key] != null &&\n          valType !== 'boolean' &&\n          props[key] !== 'auto'\n        ) {\n          throw createPropError({\n            key,\n            expected: '`boolean | \"auto\"`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "isDefaultPrevented", "React", "startTransition", "dispatchNavigateAction", "current", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "AAkXMsE,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlX/B;;;;;;;;;;;;;;;;IAiTA;;;;;;;;;CASC,GACD,OAsaC,EAAA;eAtauBxE;;IA4aXC,aAAa,EAAA;eAAbA;;;;;iEAruB2D;2BAE9C;+CACO;oCACJ;8BACA;uBACC;6BACF;0BACH;uBASlB;4BACoB;mCACY;2BACb;AA0M1B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,IAAY,EACZC,EAAU,EACVC,eAAqD,EACrDC,OAAiB,EACjBC,MAAgB,EAChBC,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACGD,oBAAoBrB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACV,OAAO;QACrB,IAAIG,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BJ,EAAEY,cAAc;YAChBC,SAAST,OAAO,CAACH;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEAD,EAAEY,cAAc;IAEhB,IAAIN,YAAY;QACd,IAAIQ,qBAAqB;QAEzBR,WAAW;YACTM,gBAAgB;gBACdE,qBAAqB;YACvB;QACF;QAEA,IAAIA,oBAAoB;YACtB;QACF;IACF;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAAC;QACpBC,CAAAA,GAAAA,mBAAAA,sBAAsB,EACpBf,MAAMD,MACNG,UAAU,YAAY,QACtBC,UAAAA,OAAAA,SAAU,MACVF,gBAAgBe,OAAO;IAE3B;AACF;AAEA,SAASC,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAYe,SAASnC,cACtBqC,KAGC;IAED,MAAM,CAACC,YAAYC,wBAAwB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa,EAACC,OAAAA,gBAAgB;IAE5E,IAAIC;IAEJ,MAAMxB,kBAAkByB,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAEpD,MAAM,EACJ3B,MAAM4B,QAAQ,EACd3B,IAAI4B,MAAM,EACVH,UAAUI,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACR9B,OAAO,EACP+B,OAAO,EACP9B,MAAM,EACN+B,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtBnC,UAAU,EACVoC,KAAKC,YAAY,EACjBC,uBAAuB,EACvB,GAAGC,WACJ,GAAGvB;IAEJK,WAAWI;IAEX,IACEU,kBACC,CAAA,OAAOd,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACmB,KAAAA;sBAAGnB;;IACjB;IAEA,MAAMoB,SAAShC,OAAAA,OAAK,CAACiC,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBjB,iBAAiB;IACzC;;;;;;GAMC,GACD,MAAMkB,kBACJlB,iBAAiB,QAAQA,iBAAiB,SACtCmB,oBAAAA,YAAY,CAACC,IAAI,GACjBD,oBAAAA,YAAY,CAACE,IAAI;IAEvB,wCAA2C;QACzC,SAASI,gBAAgBC,IAIxB;YACC,OAAO,OAAA,cAKN,CALM,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DhE,MAAM;QACR;QACA,MAAMiE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACb,OAAOvC,KAAK,CAACuC,IAAI,KAAK,YAAY,OAAOvC,KAAK,CAACuC,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQzC,KAAK,CAACuC,IAAI,KAAK,OAAO,SAAS,OAAOvC,KAAK,CAACuC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DrE,IAAI;YACJE,SAAS;YACTC,QAAQ;YACR8B,SAAS;YACTD,UAAU;YACVF,UAAU;YACVY,yBAAyB;YACzBR,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChBnC,YAAY;QACd;QACA,MAAMkE,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOnD,KAAK,CAACuC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,oBACRA,QAAQ,2BACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IAAIZ,QAAQ,YAAY;gBAC7B,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACdY,YAAY,aACZnD,KAAK,CAACuC,IAAI,KAAK,QACf;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAInC,MAAMoD,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAAC7C,QAAQ;YACX,IAAI7B;YACJ,IAAI,OAAO4B,aAAa,UAAU;gBAChC5B,OAAO4B;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS+C,QAAQ,KAAK,UAC7B;gBACA3E,OAAO4B,SAAS+C,QAAQ;YAC1B;YAEA,IAAI3E,MAAM;gBACR,MAAM4E,oBAAoB5E,KACvB6E,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,mBAAiB3D,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGa,OAAAA,OAAK,CAACoE,OAAO;iCAAC;YACjC,MAAMC,eAAejE,kBAAkBU;YACvC,OAAO;gBACL5B,MAAMmF;gBACNlF,IAAI4B,SAASX,kBAAkBW,UAAUsD;YAC3C;QACF;gCAAG;QAACvD;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAIuD;IACJ,IAAI5C,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIrB,SAAS;gBACXkD,QAAQC,IAAI,CACT,oDAAoD1D,WAAS;YAElE;YACA,IAAIS,kBAAkB;gBACpBgD,QAAQC,IAAI,CACT,yDAAyD1D,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQtE,OAAAA,OAAK,CAACyE,QAAQ,CAACC,IAAI,CAAC9D;YAC9B,EAAE,OAAO+D,KAAK;gBACZ,IAAI,CAAC/D,UAAU;oBACb,MAAM,OAAA,cAEL,CAFK,IAAIiC,MACP,uDAAuD/B,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,OAAA,cAKL,CALK,IAAI+B,MACP,6DAA6D/B,WAAS,8FACpE,CAAA,OAAOmC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;;IAGT,OAAO;QACL,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAAC9B,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBgE,IAAI,MAAK,KAAK;gBACnC,MAAM,OAAA,cAEL,CAFK,IAAI/B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMgC,WAAgBnD,iBAClB4C,SAAS,OAAOA,UAAU,YAAYA,MAAM3C,GAAG,GAC/CC;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMkD,+BAA+B9E,OAAAA,OAAK,CAAC+E,WAAW;mEACpD,CAACC;YACC,IAAIhD,WAAW,MAAM;gBACnB5C,gBAAgBe,OAAO,GAAG8E,CAAAA,GAAAA,OAAAA,iBAAiB,EACzCD,SACA9F,MACA8C,QACAI,iBACAD,iBACA1B;YAEJ;YAEA;2EAAO;oBACL,IAAIrB,gBAAgBe,OAAO,EAAE;wBAC3B+E,CAAAA,GAAAA,OAAAA,+BAA+B,EAAC9F,gBAAgBe,OAAO;wBACvDf,gBAAgBe,OAAO,GAAG;oBAC5B;oBACAgF,CAAAA,GAAAA,OAAAA,2BAA2B,EAACH;gBAC9B;;QACF;kEACA;QAAC7C;QAAiBjD;QAAM8C;QAAQI;QAAiB3B;KAAwB;IAG3E,MAAM2E,YAAYC,CAAAA,GAAAA,cAAAA,YAAY,EAACP,8BAA8BD;IAE7D,MAAMS,aAMF;QACF3D,KAAKyD;QACL/D,SAAQpC,CAAC;YACP,IAAIuD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAACzD,GAAG;oBACN,MAAM,OAAA,cAEL,CAFK,IAAI4D,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACnB,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQpC;YACV;YAEA,IACEyC,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACc,OAAO,KAAK,YAC/B;gBACAiD,MAAM/D,KAAK,CAACc,OAAO,CAACpC;YACtB;YAEA,IAAI,CAAC+C,QAAQ;gBACX;YACF;YAEA,IAAI/C,EAAEsG,gBAAgB,EAAE;gBACtB;YACF;YAEAvG,YAAYC,GAAGC,MAAMC,IAAIC,iBAAiBC,SAASC,QAAQC;QAC7D;QACA+B,cAAarC,CAAC;YACZ,IAAI,CAACyC,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBtC;YACnB;YAEA,IACEyC,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACe,YAAY,KAAK,YACpC;gBACAgD,MAAM/D,KAAK,CAACe,YAAY,CAACrC;YAC3B;YAEA,IAAI,CAAC+C,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,mBAAmBK,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;;YAEA,MAAM8C,2BAA2B3D,4BAA4B;QAK/D;QACAL,cAAcgB,QAAQC,GAAG,CAACiD,0BAA0B,AAChDC,0BACA,SAASnE,aAAavC,CAAC;YACrB,IAAI,CAACyC,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBxC;YACnB;YAEA,IACEyC,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACiB,YAAY,KAAK,YACpC;gBACA8C,MAAM/D,KAAK,CAACiB,YAAY,CAACvC;YAC3B;YAEA,IAAI,CAAC+C,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,iBAAiB;gBACpB;YACF;YAEA,MAAMqD,2BAA2B3D,4BAA4B;YAC7D4D,CAAAA,GAAAA,OAAAA,kBAAkB,EAChBxG,EAAEV,aAAa,EACfiH;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAII,CAAAA,GAAAA,OAAAA,aAAa,EAACzG,KAAK;QACrBmG,WAAWpG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACuC,kBACDP,YACCmD,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAM/D,KAAI,GAC7C;QACA+E,WAAWpG,IAAI,GAAG2G,CAAAA,GAAAA,aAAAA,WAAW,EAAC1G;IAChC;IAEA,IAAI2G;IAEJ,IAAIpE,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CqD,CAAAA,GAAAA,WAAAA,SAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACAD,OAAAA,WAAAA,GAAO9F,OAAAA,OAAK,CAACgG,YAAY,CAAC1B,OAAOgB;IACnC,OAAO;QACLQ,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC/D,KAAAA;YAAG,GAAGD,SAAS;YAAG,GAAGwD,UAAU;sBAC7B1E;;IAGP;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACqF,kBAAkBC,QAAQ,EAAA;QAACC,OAAO3F;kBAChCsF;;AAGP;AAEA,MAAMG,oBAAAA,WAAAA,GAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAErCzF,OAAAA,gBAAgB;AAEX,MAAMxC,gBAAgB;IAC3B,OAAO8D,CAAAA,GAAAA,OAAAA,UAAU,EAACgE;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2347, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/%40heroicons/react/24/solid/esm/CheckCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction CheckCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,KAIxB,EAAE,MAAM;QAJgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJwB;IAKvB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2384, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/%40heroicons/react/24/solid/esm/ExclamationCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ExclamationCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,sBAAsB,KAI9B,EAAE,MAAM;QAJsB,EAC7B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJ8B;IAK7B,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2421, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/%40heroicons/react/24/solid/esm/InformationCircleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction InformationCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 0 1 .67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 1 1-.671-1.34l.041-.022ZM12 9a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(InformationCircleIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,sBAAsB,KAI9B,EAAE,MAAM;QAJsB,EAC7B,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJ8B;IAK7B,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2458, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/node_modules/%40heroicons/react/24/solid/esm/EnvelopeIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction EnvelopeIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M1.5 8.67v8.58a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V8.67l-8.928 5.493a3 3 0 0 1-3.144 0L1.5 8.67Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M22.5 6.908V6.75a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3v.158l9.714 5.978a1.5 1.5 0 0 0 1.572 0L22.5 6.908Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(EnvelopeIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,aAAa,KAIrB,EAAE,MAAM;QAJa,EACpB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJqB;IAKpB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,GAAG;IACL,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}