import { AxiosInstance } from 'axios';
import { authApiClient } from '../lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';
import { AuthResponse, ForgotPasswordData, LoginData, RegisterData, ResetPasswordData, SetupTwoFactorAuthResponse, SetUpTwoFactorData, TwoFactorData, TwoFactorResponse } from '@/types';
import Cookies from 'js-cookie';


class AuthService {
  private api: AxiosInstance;

  constructor() {
    // Use the centralized auth API client with proper error handling
    this.api = authApiClient;
  }

  async login(data: LoginData): Promise<AuthResponse> {
    const response = await this.api.post('/login', data);

    // The backend returns data directly, not nested in a data property
    const authData = processApiResponse(response);

    // Check if the auth data is empty (which indicates an error)
    if (!authData || Object.keys(authData).length === 0) {
      throw new Error('Authentication failed - invalid credentials');
    }

    // Validate that we have the required fields
    // For account recovery, we might not have access_token but should have user info
    if (!authData.user) {
      throw new Error('Authentication failed - incomplete response: missing user data');
    }

    // For normal login, we need access_token unless it's a recovery scenario or 2FA is required
    if (!authData.access_token && !authData.requiresRecovery && !authData.requiresTwoFactor) {
      throw new Error('Authentication failed - incomplete response: missing access token');
    }

    return authData;
  }

  async register(data: RegisterData): Promise<AuthResponse> {
    const response = await this.api.post('/register', data);

    // The backend returns data directly, not nested in a data property
    return response.data;
  }

  async forgotPassword(data: ForgotPasswordData): Promise<{ message: string }> {
    const response = await this.api.post('/forgot-password', data);
    return processApiResponse(response);
  }

  async resetPassword(data: ResetPasswordData): Promise<{ message: string }> {
    try {
      console.log('🔄 Calling reset password API:', { ...data, new_password: '***' });
      const response = await this.api.post('/reset-password', data);
      console.log('✅ Reset password API response:', response.data);
      return processApiResponse(response);
    } catch (error) {
      console.error('❌ Reset password API error:', error);
      throw error;
    }
  }

  async verify2FA(data: TwoFactorData): Promise<TwoFactorResponse> {
    try {
      console.log('🔄 Calling verify 2FA API:', data);
      const response = await this.api.post('/verify-2fa', data);
      console.log('✅ Verify 2FA API response:', response.data);

      // Process the API response to extract the actual data and preserve message
      const processedResponse = processApiResponse(response);
      console.log('✅ Processed verify 2FA response:', processedResponse);

      return processedResponse;
    } catch (error) {
      console.error('❌ Verify 2FA API error:', error);
      throw error;
    }
  }

  async verifyEmail(data: TwoFactorData): Promise<TwoFactorResponse> {
    try {
      console.log('🔄 Calling verify email API:', data);
      // Use GET request with query parameters to match backend route
      const response = await this.api.get('/verify-email', {
        params: {
          i: data.user_id,
          unique: data.unique,
          c: data.code
        }
      });
      console.log('✅ Verify email API response:', response.data);

      // Process the API response to extract the actual data and preserve message
      const processedResponse = processApiResponse(response);
      console.log('✅ Processed verify email response:', processedResponse);

      return processedResponse;
    } catch (error) {
      console.error('❌ Verify email API error:', error);
      throw error;
    }
  }

  async setupTwoFactorAuth(data: SetUpTwoFactorData): Promise<SetupTwoFactorAuthResponse> {
    // Debug: Check multiple token sources
    const localStorageToken = this.getAuthToken();
    const cookieToken = typeof window !== 'undefined' ?
    document.cookie.split('; ').find(row => row.startsWith('auth_token='))?.split('=')[1] : null;
    // Use the most reliable token source
    const token = localStorageToken || cookieToken;

    if (!token) {
      throw new Error('No authentication token found. Please login again.');
    }

    // Validate token format (basic JWT check)
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) {
      console.error('🔐 Invalid JWT format - expected 3 parts, got:', tokenParts.length);
      throw new Error('Invalid authentication token format. Please login again.');
    }

    // Ensure token is set in the request headers
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

    try {
      const response = await this.api.post('/setup-2fa', data);
      return response.data.data; // Its wrapped in another data object
    } catch (error: any) {
      console.error('🔐 SetupTwoFactorAuth API Error:', error.response?.data || error.message);
      throw error;
    }
  }


  async verifyTwoFactorCode(data: TwoFactorData): Promise<{ message: string }> {
    const response = await this.api.post('/verify-2fa', data);
    return processApiResponse(response);
  }

  async generateTwoFactorCode(userId: string, action: string): Promise<{ message: string }> {
    const response = await this.api.post('/generate-2fa', { user_id: userId, action });
    return processApiResponse(response);
  }

  async refreshToken(): Promise<AuthResponse> {
    const response = await this.api.post('/refresh');
    return response.data;
  }

  setAuthToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      // Try localStorage first
      const localToken = localStorage.getItem('auth_token');
      if (localToken) return localToken;

      // Fallback to cookies
      const cookieToken = document.cookie
        .split('; ')
        .find(row => row.startsWith('auth_token='))
        ?.split('=')[1];

      return cookieToken || null;
    }
    return null;
  }

  private getCookieValue(name: string): string | null {
    if (typeof window === 'undefined') return null;

    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  }

  clearAuthToken(): void {
    try{
      Cookies.remove('auth_token');
      Cookies.remove('auth_user');
      Cookies.remove('2fa_login_user');
    } catch(e) {}

    
    // Clear any other localStorage items related to auth
    try{
      // Clear any cached data
      sessionStorage.clear();
      localStorage.clear();
    } catch(e) {}

    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  clear2FASession(): void {
    try{
      Cookies.remove('2fa_login_user');
    } catch(e) {}
    if (typeof window !== 'undefined') {
      localStorage.removeItem('2fa_login_user');
    }
  }
}

export const authService = new AuthService();
