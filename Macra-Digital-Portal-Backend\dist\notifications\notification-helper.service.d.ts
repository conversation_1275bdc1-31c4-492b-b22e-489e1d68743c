import { NotificationsService } from './notifications.service';
import { EmailTemplateService } from './email-template.service';
import { RecipientType } from '../entities/notifications.entity';
import { MailerService } from '@nestjs-modules/mailer';
export declare class NotificationHelperService {
    private readonly notificationsService;
    private readonly emailTemplateService;
    private readonly mailerService;
    constructor(notificationsService: NotificationsService, emailTemplateService: EmailTemplateService, mailerService: MailerService);
    notifyApplicationStatus(applicationId: string, applicantId: string, applicantEmail: string, applicationNumber: string, status: string, createdBy: string, applicantName?: string, licenseType?: string, oldStatus?: string): Promise<void>;
    notifyInvoiceGenerated(applicationId: string, applicantId: string, applicantEmail: string, applicationNumber: string, invoiceNumber: string, amount: number, dueDate: string, description: string, createdBy: string, applicantName?: string, licenseType?: string): Promise<void>;
    notifyPaymentConfirmation(invoiceId: string, clientId: string, clientEmail: string, clientName: string, invoiceNumber: string, invoiceAmount: number, paidAmount: number, paymentDate: string, processedBy: string): Promise<void>;
    notifyActivityNote(applicationId: string, applicantId: string | null, applicantEmail: string, applicantName: string, applicationNumber: string, licenseType: string, noteContent: string, noteType: string, category: string, step: string | undefined, createdBy: string, createdDate: string, userId: string, isAdditionalRecipient?: boolean): Promise<void>;
    notifyTaskAssignment(taskId: string, assigneeId: string, assigneeEmail: string, taskTitle: string, taskDescription: string, createdBy: string, assigneeName?: string, applicationNumber?: string, applicantName?: string, priority?: string, dueDate?: string): Promise<void>;
    notifyLicenseExpiry(licenseId: string, customerId: string, customerEmail: string, licenseNumber: string, expiryDate: Date, daysUntilExpiry: number, createdBy: string): Promise<void>;
    notifyTaskCompletion(taskId: string, applicationId: string, applicantId: string, applicantEmail: string, assigneeId: string, assigneeEmail: string, taskTitle: string, applicationNumber: string, outcome: string, createdBy: string, applicantName?: string, licenseType?: string, comments?: string, nextSteps?: string): Promise<void>;
    notifyLicenseApproval(applicationId: string, applicantId: string, applicantEmail: string, applicationNumber: string, licenseNumber: string, licenseType: string, createdBy: string, applicantName?: string, expiryDate?: string): Promise<void>;
    sendEmailNotification(params: {
        recipientId?: string;
        recipientEmail: string;
        recipientName?: string;
        subject: string;
        message: string;
        htmlContent: string;
        entityType?: string;
        entityId?: string;
        actionUrl?: string;
        recipientType?: RecipientType;
        createdBy: string;
        sendEmail?: boolean;
        createInApp?: boolean;
    }): Promise<{
        emailNotification?: any;
        inAppNotification?: any;
    }>;
}
