"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentificationTypesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const identification_types_controller_1 = require("./identification-types.controller");
const identification_types_service_1 = require("./identification-types.service");
const identification_type_entity_1 = require("../entities/identification-type.entity");
let IdentificationTypesModule = class IdentificationTypesModule {
};
exports.IdentificationTypesModule = IdentificationTypesModule;
exports.IdentificationTypesModule = IdentificationTypesModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([identification_type_entity_1.IdentificationType])],
        controllers: [identification_types_controller_1.IdentificationTypesController],
        providers: [identification_types_service_1.IdentificationTypesService],
        exports: [identification_types_service_1.IdentificationTypesService],
    })
], IdentificationTypesModule);
//# sourceMappingURL=identification-types.module.js.map