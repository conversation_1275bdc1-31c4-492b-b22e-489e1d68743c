# Types Structure Documentation

This document explains the organization and usage of TypeScript types in the MACRA Digital Portal Frontend application.

## 📁 Types Organization

### Core Entity Types
- **`user.ts`** - User management types (User, CreateUserDto, UpdateUserDto, etc.)
- **`license.ts`** - License and application types (Application, LicenseCategory, etc.)
- **`license-type.ts`** - License type management (LicenseTypeEntity, CreateLicenseTypeDto, etc.)
- **`identification.ts`** - Identification type management
- **`invoice.ts`** - Invoice and payment types (Invoice, Payment, InvoiceItem, etc.)
- **`notification.ts`** - Notification system types
- **`department.ts`** - Department management types
- **`organization.ts`** - Organization management types
- **`task.ts`** - Task management types (Task, TaskType, TaskStatus, etc.)
- **`audit.ts`** - Audit logging types (AuditLog, AuditAction, etc.)

### Utility Types
- **`service.ts`** - Service-specific types (ServiceResponse, ValidationResult, etc.)
- **`public.ts`** - Public-facing types
- **`index.ts`** - Central export hub with common shared types

## 🎯 Key Improvements Made

### 1. **Centralized Common Types**
Moved frequently used types to `index.ts`:
- `PaginatedResponse<T>` - Generic paginated API response
- `PaginateQuery` - Query parameters for pagination
- `ApiResponse<T>` - Standard API response wrapper
- `BaseEntity` - Common entity fields (created_at, updated_at, etc.)
- `UserReference` - User reference for creator/updater fields

### 2. **Eliminated Redundancy**
- Removed duplicate `PaginateQuery` from `user.ts`
- Consolidated similar types across services
- Created generic types that can be reused

### 3. **Better Organization**
- Grouped related types in dedicated files
- Used explicit re-exports to avoid naming conflicts
- Added comprehensive service types for cross-cutting concerns

### 4. **Enhanced Type Safety**
- Added proper generic types for better type inference
- Included comprehensive enum definitions
- Added validation and error handling types

## 🚀 Usage Examples

### Importing Types

```typescript
// Import from main index (recommended)
import { User, PaginateQuery, ApiResponse } from '@/types';

// Import specific types from individual files
import { Task, TaskType, TaskStatus } from '@/types/task';
import { AuditLog, AuditAction } from '@/types/audit';

// Import service types
import { ServiceResponse, ValidationResult } from '@/types/service';
```

### Using Generic Types

```typescript
// Paginated response for any entity
const usersResponse: PaginatedResponse<User> = await userService.getUsers();
const tasksResponse: PaginatedResponse<Task> = await taskService.getTasks();

// API response wrapper
const apiResponse: ApiResponse<User> = await api.getUser(id);

// Service response
const serviceResult: ServiceResponse<User[]> = await userService.bulkCreate(users);
```

### Extending Base Types

```typescript
// Custom entity extending BaseEntity
interface CustomEntity extends BaseEntity {
  custom_id: string;
  name: string;
  description?: string;
}

// Custom filters extending BaseFilters
interface CustomFilters extends BaseFilters {
  category?: string;
  priority?: string;
}
```

## 📋 Type Categories

### 1. **Entity Types**
Core business entities with full CRUD operations:
- User, Application, Invoice, Task, etc.
- Include create/update DTOs
- Include filter and response types

### 2. **Enum Types**
Standardized enumerations:
- `ApplicationStatus`, `TaskType`, `TaskPriority`
- `AuditAction`, `AuditModule`
- `EntityStatus`, `TaskStatus`

### 3. **DTO Types**
Data Transfer Objects for API operations:
- `CreateUserDto`, `UpdateUserDto`
- `CreateTaskDto`, `AssignTaskDto`
- `CreateInvoiceDto`, `UpdateInvoiceDto`

### 4. **Response Types**
API response structures:
- `UsersResponse`, `TasksResponse`
- `PaginatedResponse<T>`
- `ApiResponse<T>`, `ServiceResponse<T>`

### 5. **Filter Types**
Query and filter parameters:
- `UserFilters`, `TaskFilters`
- `BaseFilters`, `PaginateQuery`
- `SearchOptions`, `SortOptions`

### 6. **UI Types**
User interface specific types:
- `ModalProps`, `TableColumn<T>`
- `FormField`, `FormConfig`
- `ToastNotification`

## 🔧 Best Practices

### 1. **Import Strategy**
```typescript
// ✅ Good - Import from main index
import { User, Task, PaginateQuery } from '@/types';

// ❌ Avoid - Direct file imports unless necessary
import { User } from '@/types/user';
```

### 2. **Generic Usage**
```typescript
// ✅ Good - Use generics for reusable types
const response: PaginatedResponse<User> = await getUsers();

// ❌ Avoid - Specific types when generics work
interface UsersPaginatedResponse { ... }
```

### 3. **Type Extensions**
```typescript
// ✅ Good - Extend base types
interface CustomUser extends User {
  additional_field: string;
}

// ❌ Avoid - Duplicating common fields
interface CustomUser {
  user_id: string;
  created_at: string; // Already in BaseEntity
  // ...
}
```

### 4. **Naming Conventions**
- **Entities**: PascalCase (User, Task, Invoice)
- **DTOs**: PascalCase with suffix (CreateUserDto, UpdateTaskDto)
- **Enums**: PascalCase (TaskType, ApplicationStatus)
- **Interfaces**: PascalCase (ApiResponse, ServiceResult)
- **Types**: PascalCase (EntityStatus, UserReference)

## 🔄 Migration Guide

### For Existing Code

1. **Update Imports**
   ```typescript
   // Before
   import { PaginateQuery } from '@/types/user';
   
   // After
   import { PaginateQuery } from '@/types';
   ```

2. **Use Generic Types**
   ```typescript
   // Before
   interface UsersResponse { ... }
   
   // After
   const response: PaginatedResponse<User> = ...;
   ```

3. **Extend Base Types**
   ```typescript
   // Before
   interface CustomEntity {
     id: string;
     created_at: string;
     updated_at: string;
     // ...
   }
   
   // After
   interface CustomEntity extends BaseEntity {
     id: string;
     // created_at and updated_at inherited
   }
   ```

## 📚 Additional Resources

- **Type Definitions**: All types are fully documented with JSDoc comments
- **Examples**: See `formatters.example.tsx` for usage examples
- **Testing**: Use the types in your test files for better type safety
- **IDE Support**: Full IntelliSense support with auto-completion

## 🤝 Contributing

When adding new types:

1. **Determine Category**: Entity, DTO, Response, Filter, etc.
2. **Choose File**: Add to existing file or create new one if needed
3. **Follow Conventions**: Use established naming and structure patterns
4. **Update Index**: Add exports to `index.ts` if needed
5. **Document**: Add JSDoc comments for complex types
6. **Test**: Ensure types work correctly in components and services

This structure provides a solid foundation for type safety and maintainability across the entire application.
