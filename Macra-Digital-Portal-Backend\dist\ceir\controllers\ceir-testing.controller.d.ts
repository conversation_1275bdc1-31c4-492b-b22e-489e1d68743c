import { CeirEquipmentSpecificationsService } from '../services/ceir-equipment-specifications.service';
import { CeirTestReportsService } from '../services/ceir-test-reports.service';
import { CreateCeirEquipmentSpecificationDto, UpdateCeirEquipmentSpecificationDto } from '../dto/ceir-equipment-specifications';
import { CreateCeirTestReportDto, UpdateCeirTestReportDto } from '../dto/ceir-test-reports';
import { CeirEquipmentSpecifications } from '../entities/ceir-equipment-specifications.entity';
import { CeirTestReports } from '../entities/ceir-test-reports.entity';
export declare class CeirTestingController {
    private readonly ceirEquipmentSpecificationsService;
    private readonly ceirTestReportsService;
    constructor(ceirEquipmentSpecificationsService: CeirEquipmentSpecificationsService, ceirTestReportsService: CeirTestReportsService);
    createEquipmentSpecification(createDto: CreateCeirEquipmentSpecificationDto, req: any): Promise<CeirEquipmentSpecifications>;
    findAllEquipmentSpecifications(active?: boolean): Promise<CeirEquipmentSpecifications[]>;
    findSpecificationsByDevice(deviceId: string): Promise<CeirEquipmentSpecifications[]>;
    findSpecificationsByCategory(categoryId: string): Promise<CeirEquipmentSpecifications[]>;
    findSpecificationsByFrequency(frequency: number): Promise<CeirEquipmentSpecifications[]>;
    findOneEquipmentSpecification(id: string): Promise<CeirEquipmentSpecifications>;
    updateEquipmentSpecification(id: string, updateDto: UpdateCeirEquipmentSpecificationDto, req: any): Promise<CeirEquipmentSpecifications>;
    removeEquipmentSpecification(id: string): Promise<void>;
    createTestReport(createDto: CreateCeirTestReportDto, req: any): Promise<CeirTestReports>;
    findAllTestReports(valid?: boolean): Promise<CeirTestReports[]>;
    getTestReportsStatistics(): Promise<{
        total: number;
        valid: number;
        invalid: number;
        byTestType: Record<string, number>;
        byTestResult: Record<string, number>;
        byStatus: Record<string, number>;
        expiring: number;
        thisMonth: number;
    }>;
    findTestReportsByDevice(deviceId: string): Promise<CeirTestReports[]>;
    findTestReportsByCertificationBody(bodyId: string): Promise<CeirTestReports[]>;
    findExpiringTestReports(days?: number): Promise<CeirTestReports[]>;
    findOneTestReport(id: string): Promise<CeirTestReports>;
    updateTestReport(id: string, updateDto: UpdateCeirTestReportDto, req: any): Promise<CeirTestReports>;
    removeTestReport(id: string): Promise<void>;
    approveTestReport(id: string, approvedBy: string, req: any): Promise<CeirTestReports>;
    rejectTestReport(id: string, req: any): Promise<CeirTestReports>;
    validateTestReport(id: string, req: any): Promise<CeirTestReports>;
}
