"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const payments_service_1 = require("./payments.service");
const create_payment_dto_1 = require("../dto/payments/create-payment.dto");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
const update_payment_dto_1 = require("../dto/payments/update-payment.dto");
let PaymentsController = class PaymentsController {
    constructor(paymentsService) {
        this.paymentsService = paymentsService;
    }
    async create(createPaymentDto) {
        return this.paymentsService.createPayment(createPaymentDto);
    }
    async findAll(query, status, paymentType, dateRange, search, req) {
        const userRoles = req.user?.roles || [];
        const isCustomer = userRoles.includes('customer');
        const filters = {
            status,
            paymentType,
            dateRange,
            search,
            ...(isCustomer && { userId: req.user?.user_id })
        };
        const result = await this.paymentsService.getPayments(filters, {
            page: query.page || 1,
            limit: query.limit || 10,
        });
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async getStatistics(req) {
        const userRoles = req.user?.roles || [];
        const isCustomer = userRoles.includes('customer');
        const isAdminOrStaff = userRoles.some((role) => ['administrator', 'admin', 'staff', 'finance', 'officer'].includes(role));
        const userId = (isCustomer && !isAdminOrStaff) ? req.user?.user_id : undefined;
        console.log('📊 PaymentsController.getStatistics called with userId:', userId, 'isCustomer:', isCustomer, 'isAdminOrStaff:', isAdminOrStaff);
        return this.paymentsService.getPaymentStatistics(userId);
    }
    async findOne(id) {
        return this.paymentsService.getPaymentById(id);
    }
    async update(id, updatePaymentDto, req) {
        try {
            const userId = req.user?.userId || null;
            const result = await this.paymentsService.updatePayment(id, updatePaymentDto, userId);
            return {
                success: true,
                message: 'Payment updated successfully',
                data: result
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException({
                    success: false,
                    message: 'Payment not found',
                    error: 'PAYMENT_NOT_FOUND'
                });
            }
            if (error instanceof common_1.ConflictException) {
                throw new common_1.ConflictException({
                    success: false,
                    message: error.message || 'Payment update conflict',
                    error: 'PAYMENT_UPDATE_CONFLICT'
                });
            }
            if (error instanceof common_1.BadRequestException) {
                throw new common_1.BadRequestException({
                    success: false,
                    message: error.message || 'Invalid payment data provided',
                    error: 'INVALID_PAYMENT_DATA'
                });
            }
            throw new common_1.InternalServerErrorException({
                success: false,
                message: 'Failed to update payment. Please try again later.',
                error: 'PAYMENT_UPDATE_FAILED'
            });
        }
    }
    async remove(id) {
        await this.paymentsService.deletePayment(id);
        return { message: 'Payment deleted successfully' };
    }
    async markOverduePayments() {
        return this.paymentsService.markOverduePayments();
    }
    async getPaymentsByEntity(entityType, entityId, query) {
        const result = await this.paymentsService.getPaymentsByEntity(entityType, entityId, {
            page: query.page || 1,
            limit: query.limit || 10,
        });
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async createPaymentForEntity(entityType, entityId, createPaymentDto) {
        return this.paymentsService.createPaymentForEntity(entityType, entityId, createPaymentDto);
    }
    async getCustomerPayments(status, paymentType, page, limit, search, dateRange, req) {
        const userId = req.user?.user_id || null;
        return this.paymentsService.getPaymentsByCustomer(userId, {
            status,
            paymentType,
            page: page || 1,
            limit: limit || 10,
            search,
            dateRange,
        });
    }
    async getCustomerPaymentStatistics(req) {
        const userId = req.user?.user_id || null;
        return this.paymentsService.getPaymentStatisticsByCustomer(userId);
    }
    async getCustomerPayment(id, req) {
        const userId = req.user?.user_id || null;
        return this.paymentsService.getPaymentByCustomer(id, userId);
    }
    async getCustomerApplicationPayments(applicationId, req) {
        const userId = req.user?.user_id || null;
        return this.paymentsService.getApplicationPaymentsByCustomer(applicationId, userId);
    }
    async getCustomerInvoicePayments(invoiceId, req) {
        const userId = req.user?.user_id || null;
        return this.paymentsService.getInvoicePaymentsByCustomer(invoiceId);
    }
    async linkProofOfPayment(invoiceId, proofData, req) {
        try {
            const userId = req.user?.user_id || null;
            if (!proofData.documentId) {
                throw new common_1.BadRequestException({
                    success: false,
                    message: 'Document ID is required. Please upload the document first via /documents endpoint.',
                    error: 'NO_DOCUMENT_ID'
                });
            }
            console.log(`📤 Processing proof of payment link for invoice ${invoiceId} by user ${userId}`);
            const result = await this.paymentsService.uploadProofOfPaymentByCustomer(invoiceId, userId, proofData, proofData.documentId);
            return {
                success: true,
                message: 'Proof of payment linked successfully. Your payment is now under review.',
                data: result
            };
        }
        catch (error) {
            console.error(`❌ Error uploading proof of payment for invoice ${invoiceId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw new common_1.NotFoundException({
                    success: false,
                    message: 'Invoice not found or you do not have access to it.',
                    error: 'INVOICE_NOT_FOUND'
                });
            }
            if (error instanceof common_1.ConflictException) {
                throw new common_1.ConflictException({
                    success: false,
                    message: error.message || 'A payment already exists for this invoice.',
                    error: 'PAYMENT_ALREADY_EXISTS'
                });
            }
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            if (error.message?.includes('File too large')) {
                throw new common_1.BadRequestException({
                    success: false,
                    message: 'File size too large. Maximum allowed size is 5MB.',
                    error: 'FILE_TOO_LARGE'
                });
            }
            if (error.message?.includes('Invalid file type')) {
                throw new common_1.BadRequestException({
                    success: false,
                    message: 'Invalid file type. Only JPEG, PNG, and PDF files are allowed.',
                    error: 'INVALID_FILE_TYPE'
                });
            }
            throw new common_1.InternalServerErrorException({
                success: false,
                message: 'Failed to upload proof of payment. Please try again later.',
                error: 'UPLOAD_FAILED'
            });
        }
    }
};
exports.PaymentsController = PaymentsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new payment' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Invoice number already exists' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Created new payment',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_payment_dto_1.CreatePaymentDto]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all payments with optional filters' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payments retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'paymentType', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'dateRange', required: false, enum: ['last-30', 'last-90', 'last-year'] }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Query)('status')),
    __param(2, (0, common_1.Query)('paymentType')),
    __param(3, (0, common_1.Query)('dateRange')),
    __param(4, (0, common_1.Query)('search')),
    __param(5, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payment statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statistics retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payment by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Payment ID', type: String }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Viewed payment details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update payment' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid payment data provided' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Payment update conflict (e.g., duplicate invoice number)' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal server error' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Payment ID', type: String }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('administrator', 'finance'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Updated payment',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_payment_dto_1.UpdatePaymentDto, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete payment' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Payment ID', type: String }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Deleted payment',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('mark-overdue'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark overdue payments (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Overdue payments marked successfully' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Marked overdue payments',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "markOverduePayments", null);
__decorate([
    (0, common_1.Get)('entity/:entityType/:entityId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payments for a specific entity' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Entity payments retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'entityType', description: 'Entity type (e.g., application, license)', type: String }),
    (0, swagger_1.ApiParam)({ name: 'entityId', description: 'Entity ID', type: String }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __param(2, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getPaymentsByEntity", null);
__decorate([
    (0, common_1.Post)('entity/:entityType/:entityId'),
    (0, swagger_1.ApiOperation)({ summary: 'Create payment for a specific entity' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment created successfully for entity' }),
    (0, swagger_1.ApiParam)({ name: 'entityType', description: 'Entity type (e.g., application, license)', type: String }),
    (0, swagger_1.ApiParam)({ name: 'entityId', description: 'Entity ID', type: String }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Payment',
        description: 'Created payment for entity',
    }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "createPaymentForEntity", null);
__decorate([
    (0, common_1.Get)('customer/my-payments'),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer payments (filtered by user applications)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer payments retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: 'Filter by payment status' }),
    (0, swagger_1.ApiQuery)({ name: 'paymentType', required: false, description: 'Filter by payment type' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search term' }),
    (0, swagger_1.ApiQuery)({ name: 'dateRange', required: false, enum: ['last-30', 'last-90', 'last-year'] }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('customer'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerPayment',
        description: 'Viewed customer payments',
    }),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('paymentType')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('limit')),
    __param(4, (0, common_1.Query)('search')),
    __param(5, (0, common_1.Query)('dateRange')),
    __param(6, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, Number, String, String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getCustomerPayments", null);
__decorate([
    (0, common_1.Get)('customer/statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer payment statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer payment statistics retrieved successfully' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('customer'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerPayment',
        description: 'Viewed customer payment statistics',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getCustomerPaymentStatistics", null);
__decorate([
    (0, common_1.Get)('customer/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get specific customer payment' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer payment retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found or not accessible' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Payment ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerPayment',
        description: 'Viewed specific customer payment',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getCustomerPayment", null);
__decorate([
    (0, common_1.Get)('customer/application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payments for specific customer application' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application payments retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found or not accessible' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('customer'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerPayment',
        description: 'Viewed customer application payments',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getCustomerApplicationPayments", null);
__decorate([
    (0, common_1.Get)('customer/invoice/:invoiceId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payments for specific customer invoice' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoice payments retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invoice not found or not accessible' }),
    (0, swagger_1.ApiParam)({ name: 'invoiceId', description: 'Invoice ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('customer', 'finance', 'administrator'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerPayment',
        description: 'Viewed customer invoice payments',
    }),
    __param(0, (0, common_1.Param)('invoiceId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getCustomerInvoicePayments", null);
__decorate([
    (0, common_1.Post)('customer/invoice/:invoiceId/proof-of-payment'),
    (0, swagger_1.ApiOperation)({
        summary: 'Link proof of payment document to customer invoice',
        description: 'Links an already uploaded document (via documents controller) to a payment for the specified invoice'
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Proof of payment linked successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid document ID or missing required data' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invoice or document not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Payment already exists for this invoice' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal server error' }),
    (0, swagger_1.ApiParam)({ name: 'invoiceId', description: 'Invoice ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('customer'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerPayment',
        description: 'Linked proof of payment document to invoice',
    }),
    __param(0, (0, common_1.Param)('invoiceId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "linkProofOfPayment", null);
exports.PaymentsController = PaymentsController = __decorate([
    (0, swagger_1.ApiTags)('Payments'),
    (0, common_1.Controller)('payments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [payments_service_1.PaymentsService])
], PaymentsController);
//# sourceMappingURL=payments.controller.js.map