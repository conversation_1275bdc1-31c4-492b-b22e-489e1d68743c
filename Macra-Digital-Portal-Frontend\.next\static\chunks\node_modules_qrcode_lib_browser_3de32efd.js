(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/qrcode/lib/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_e6c4fc17._.js",
  "static/chunks/node_modules_qrcode_lib_browser_eeb25fc9.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/qrcode/lib/browser.js [app-client] (ecmascript)");
    });
});
}),
}]);