"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CeirModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const ceir_management_controller_1 = require("./controllers/ceir-management.controller");
const ceir_testing_controller_1 = require("./controllers/ceir-testing.controller");
const ceir_certification_bodies_service_1 = require("./services/ceir-certification-bodies.service");
const ceir_equipment_categories_service_1 = require("./services/ceir-equipment-categories.service");
const ceir_equipment_specifications_service_1 = require("./services/ceir-equipment-specifications.service");
const ceir_technical_standards_service_1 = require("./services/ceir-technical-standards.service");
const ceir_test_reports_service_1 = require("./services/ceir-test-reports.service");
const ceir_certification_bodies_entity_1 = require("./entities/ceir-certification-bodies.entity");
const ceir_equipment_type_categories_entity_1 = require("./entities/ceir-equipment-type-categories.entity");
const ceir_equipment_specifications_entity_1 = require("./entities/ceir-equipment-specifications.entity");
const ceir_technical_standards_entity_1 = require("./entities/ceir-technical-standards.entity");
const ceir_test_reports_entity_1 = require("./entities/ceir-test-reports.entity");
let CeirModule = class CeirModule {
};
exports.CeirModule = CeirModule;
exports.CeirModule = CeirModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                ceir_certification_bodies_entity_1.CeirCertificationBodies,
                ceir_equipment_type_categories_entity_1.CeirEquipmentTypeCategories,
                ceir_equipment_specifications_entity_1.CeirEquipmentSpecifications,
                ceir_technical_standards_entity_1.CeirTechnicalStandards,
                ceir_test_reports_entity_1.CeirTestReports,
            ]),
        ],
        controllers: [
            ceir_management_controller_1.CeirManagementController,
            ceir_testing_controller_1.CeirTestingController,
        ],
        providers: [
            ceir_certification_bodies_service_1.CeirCertificationBodiesService,
            ceir_equipment_categories_service_1.CeirEquipmentCategoriesService,
            ceir_equipment_specifications_service_1.CeirEquipmentSpecificationsService,
            ceir_technical_standards_service_1.CeirTechnicalStandardsService,
            ceir_test_reports_service_1.CeirTestReportsService,
        ],
        exports: [
            ceir_certification_bodies_service_1.CeirCertificationBodiesService,
            ceir_equipment_categories_service_1.CeirEquipmentCategoriesService,
            ceir_equipment_specifications_service_1.CeirEquipmentSpecificationsService,
            ceir_technical_standards_service_1.CeirTechnicalStandardsService,
            ceir_test_reports_service_1.CeirTestReportsService,
        ],
    })
], CeirModule);
//# sourceMappingURL=ceir.module.js.map