"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeederService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const bcrypt = __importStar(require("bcryptjs"));
const user_entity_1 = require("../../entities/user.entity");
const role_entity_1 = require("../../entities/role.entity");
const permission_entity_1 = require("../../entities/permission.entity");
const user_identification_entity_1 = require("../../entities/user-identification.entity");
const identification_type_entity_1 = require("../../entities/identification-type.entity");
const license_seeder_service_1 = require("./license.seeder.service");
const organization_entity_1 = require("../../entities/organization.entity");
const department_entity_1 = require("../../entities/department.entity");
const organizations_seeder_1 = require("./organizations.seeder");
const departments_seeder_1 = require("./departments.seeder");
const postal_code_seeder_1 = require("./postal-code.seeder");
let SeederService = class SeederService {
    constructor(dataSource, usersRepository, rolesRepository, permissionsRepository, userIdentificationsRepository, identificationTypesRepository, licenseSeederService, organizationSeederService, departmentSeederService, postalCodeSeederService, organizationRepository, departmentRepository) {
        this.dataSource = dataSource;
        this.usersRepository = usersRepository;
        this.rolesRepository = rolesRepository;
        this.permissionsRepository = permissionsRepository;
        this.userIdentificationsRepository = userIdentificationsRepository;
        this.identificationTypesRepository = identificationTypesRepository;
        this.licenseSeederService = licenseSeederService;
        this.organizationSeederService = organizationSeederService;
        this.departmentSeederService = departmentSeederService;
        this.postalCodeSeederService = postalCodeSeederService;
        this.organizationRepository = organizationRepository;
        this.departmentRepository = departmentRepository;
    }
    async createCustomPermission(name, description, category) {
        const existingPermission = await this.permissionsRepository.findOne({ where: { name } });
        if (existingPermission) {
            return existingPermission;
        }
        const permission = this.permissionsRepository.create({ name, description, category });
        return await this.permissionsRepository.save(permission);
    }
    async generateCrudPermissions(entityName, category) {
        const permissionCategory = category || `${entityName.charAt(0).toUpperCase() + entityName.slice(1)} Management`;
        const permissions = [];
        const crudOperations = [
            { action: 'create', description: `Create new ${entityName}s` },
            { action: 'read', description: `View ${entityName} information` },
            { action: 'update', description: `Update ${entityName} information` },
            { action: 'delete', description: `Delete ${entityName}s` },
        ];
        for (const operation of crudOperations) {
            const permissionName = `${entityName}:${operation.action}`;
            const permission = await this.createCustomPermission(permissionName, operation.description, permissionCategory);
            permissions.push(permission);
        }
        return permissions;
    }
    async seedPermissions() {
        console.log('🌱 Seeding permissions...');
        try {
            const existingPermissions = await this.permissionsRepository.find();
            if (existingPermissions.length > 0) {
                console.log('✅ Permissions already exist, skipping...');
                return existingPermissions;
            }
        }
        catch (error) {
            console.log('⚠️ Error checking existing permissions, proceeding with seeding...');
        }
        const permissionsData = [
            { name: permission_entity_1.PERMISSION_NAMES.USER_CREATE, description: 'Create new users', category: 'User Management' },
            { name: permission_entity_1.PERMISSION_NAMES.USER_READ, description: 'View user information', category: 'User Management' },
            { name: permission_entity_1.PERMISSION_NAMES.USER_UPDATE, description: 'Update user information', category: 'User Management' },
            { name: permission_entity_1.PERMISSION_NAMES.USER_DELETE, description: 'Delete users', category: 'User Management' },
            { name: permission_entity_1.PERMISSION_NAMES.ROLE_CREATE, description: 'Create new roles', category: 'Role Management' },
            { name: permission_entity_1.PERMISSION_NAMES.ROLE_READ, description: 'View role information', category: 'Role Management' },
            { name: permission_entity_1.PERMISSION_NAMES.ROLE_UPDATE, description: 'Update role information', category: 'Role Management' },
            { name: permission_entity_1.PERMISSION_NAMES.ROLE_DELETE, description: 'Delete roles', category: 'Role Management' },
            { name: permission_entity_1.PERMISSION_NAMES.PERMISSION_CREATE, description: 'Create new permissions', category: 'Permission Management' },
            { name: permission_entity_1.PERMISSION_NAMES.PERMISSION_READ, description: 'View permission information', category: 'Permission Management' },
            { name: permission_entity_1.PERMISSION_NAMES.PERMISSION_UPDATE, description: 'Update permission information', category: 'Permission Management' },
            { name: permission_entity_1.PERMISSION_NAMES.PERMISSION_DELETE, description: 'Delete permissions', category: 'Permission Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_CREATE, description: 'Create new licenses', category: 'License Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_READ, description: 'View license information', category: 'License Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_UPDATE, description: 'Update license information', category: 'License Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_DELETE, description: 'Delete licenses', category: 'License Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_APPROVE, description: 'Approve license applications', category: 'License Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_REJECT, description: 'Reject license applications', category: 'License Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_TYPE_CREATE, description: 'Create new license types', category: 'License Types Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_TYPE_READ, description: 'View license types', category: 'License Types Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_TYPE_UPDATE, description: 'Update license types', category: 'License Types Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_TYPE_DELETE, description: 'Delete license types', category: 'License Types Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_CATEGORY_CREATE, description: 'Create new license categories', category: 'License Categories Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_CATEGORY_READ, description: 'View license categories', category: 'License Categories Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_CATEGORY_UPDATE, description: 'Update license categories', category: 'License Categories Management' },
            { name: permission_entity_1.PERMISSION_NAMES.LICENSE_CATEGORY_DELETE, description: 'Delete license categories', category: 'License Categories Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICATION_CREATE, description: 'Create new applications', category: 'Application Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICATION_READ, description: 'View application information', category: 'Application Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICATION_UPDATE, description: 'Update application information', category: 'Application Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICATION_DELETE, description: 'Delete applications', category: 'Application Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICATION_EVALUATE, description: 'Evaluate applications', category: 'Application Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICATION_SUBMIT, description: 'Submit applications', category: 'Application Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICATION_APPROVE, description: 'Approve applications', category: 'Application Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICATION_REJECT, description: 'Reject applications', category: 'Application Management' },
            { name: permission_entity_1.PERMISSION_NAMES.FINANCIAL_READ, description: 'View financial information', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.FINANCIAL_UPDATE, description: 'Update financial information', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.FINANCIAL_REPORTS, description: 'Generate financial reports', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.INVOICE_CREATE, description: 'Create invoices', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.INVOICE_READ, description: 'View invoices', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.INVOICE_UPDATE, description: 'Update invoices', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.INVOICE_DELETE, description: 'Delete invoices', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.PAYMENT_CREATE, description: 'Create payments', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.PAYMENT_READ, description: 'View payments', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.PAYMENT_UPDATE, description: 'Update payments', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.PAYMENT_DELETE, description: 'Delete payments', category: 'Financial Management' },
            { name: permission_entity_1.PERMISSION_NAMES.DOCUMENT_CREATE, description: 'Upload documents', category: 'Document Management' },
            { name: permission_entity_1.PERMISSION_NAMES.DOCUMENT_READ, description: 'View documents', category: 'Document Management' },
            { name: permission_entity_1.PERMISSION_NAMES.DOCUMENT_UPDATE, description: 'Update documents', category: 'Document Management' },
            { name: permission_entity_1.PERMISSION_NAMES.DOCUMENT_DELETE, description: 'Delete documents', category: 'Document Management' },
            { name: permission_entity_1.PERMISSION_NAMES.DOCUMENT_DOWNLOAD, description: 'Download documents', category: 'Document Management' },
            { name: permission_entity_1.PERMISSION_NAMES.IDENTIFICATION_TYPE_CREATE, description: 'Create identification types', category: 'Identification Types Management' },
            { name: permission_entity_1.PERMISSION_NAMES.IDENTIFICATION_TYPE_READ, description: 'View identification types', category: 'Identification Types Management' },
            { name: permission_entity_1.PERMISSION_NAMES.IDENTIFICATION_TYPE_UPDATE, description: 'Update identification types', category: 'Identification Types Management' },
            { name: permission_entity_1.PERMISSION_NAMES.IDENTIFICATION_TYPE_DELETE, description: 'Delete identification types', category: 'Identification Types Management' },
            { name: permission_entity_1.PERMISSION_NAMES.CONTACT_CREATE, description: 'Create contacts', category: 'Contact Management' },
            { name: permission_entity_1.PERMISSION_NAMES.CONTACT_READ, description: 'View contacts', category: 'Contact Management' },
            { name: permission_entity_1.PERMISSION_NAMES.CONTACT_UPDATE, description: 'Update contacts', category: 'Contact Management' },
            { name: permission_entity_1.PERMISSION_NAMES.CONTACT_DELETE, description: 'Delete contacts', category: 'Contact Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICANT_CREATE, description: 'Create applicants', category: 'Applicant Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICANT_READ, description: 'View applicants', category: 'Applicant Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICANT_UPDATE, description: 'Update applicants', category: 'Applicant Management' },
            { name: permission_entity_1.PERMISSION_NAMES.APPLICANT_DELETE, description: 'Delete applicants', category: 'Applicant Management' },
            { name: permission_entity_1.PERMISSION_NAMES.EVALUATION_CREATE, description: 'Create evaluations', category: 'Evaluation Management' },
            { name: permission_entity_1.PERMISSION_NAMES.EVALUATION_READ, description: 'View evaluations', category: 'Evaluation Management' },
            { name: permission_entity_1.PERMISSION_NAMES.EVALUATION_UPDATE, description: 'Update evaluations', category: 'Evaluation Management' },
            { name: permission_entity_1.PERMISSION_NAMES.EVALUATION_DELETE, description: 'Delete evaluations', category: 'Evaluation Management' },
            { name: permission_entity_1.PERMISSION_NAMES.EVALUATION_SUBMIT, description: 'Submit evaluations', category: 'Evaluation Management' },
            { name: permission_entity_1.PERMISSION_NAMES.NOTIFICATION_CREATE, description: 'Create notifications', category: 'Notification Management' },
            { name: permission_entity_1.PERMISSION_NAMES.NOTIFICATION_READ, description: 'View notifications', category: 'Notification Management' },
            { name: permission_entity_1.PERMISSION_NAMES.NOTIFICATION_UPDATE, description: 'Update notifications', category: 'Notification Management' },
            { name: permission_entity_1.PERMISSION_NAMES.NOTIFICATION_DELETE, description: 'Delete notifications', category: 'Notification Management' },
            { name: permission_entity_1.PERMISSION_NAMES.NOTIFICATION_SEND, description: 'Send notifications', category: 'Notification Management' },
            { name: permission_entity_1.PERMISSION_NAMES.REPORT_GENERATE, description: 'Generate reports', category: 'Reports Management' },
            { name: permission_entity_1.PERMISSION_NAMES.REPORT_VIEW, description: 'View reports', category: 'Reports Management' },
            { name: permission_entity_1.PERMISSION_NAMES.REPORT_EXPORT, description: 'Export reports', category: 'Reports Management' },
            { name: permission_entity_1.PERMISSION_NAMES.REPORT_SCHEDULE, description: 'Schedule reports', category: 'Reports Management' },
            { name: permission_entity_1.PERMISSION_NAMES.SYSTEM_SETTINGS, description: 'Manage system settings', category: 'System Administration' },
            { name: permission_entity_1.PERMISSION_NAMES.SYSTEM_AUDIT, description: 'View audit logs', category: 'System Administration' },
            { name: permission_entity_1.PERMISSION_NAMES.SYSTEM_BACKUP, description: 'Manage system backups', category: 'System Administration' },
            { name: permission_entity_1.PERMISSION_NAMES.SYSTEM_MAINTENANCE, description: 'Perform system maintenance', category: 'System Administration' },
        ];
        const permissions = [];
        for (const permissionData of permissionsData) {
            const permission = this.permissionsRepository.create(permissionData);
            const savedPermission = await this.permissionsRepository.save(permission);
            permissions.push(savedPermission);
            console.log(`✅ Created permission: ${permissionData.name}`);
        }
        const customPermissions = await this.seedCustomPermissions();
        permissions.push(...customPermissions);
        console.log(`✅ Total permissions created: ${permissions.length}`);
        return permissions;
    }
    async seedCustomPermissions() {
        const customPermissions = [];
        const additionalPermissions = [
            { name: 'postal_code:create', description: 'Create postal codes', category: 'Postal Code Management' },
            { name: 'postal_code:read', description: 'View postal codes', category: 'Postal Code Management' },
            { name: 'postal_code:update', description: 'Update postal codes', category: 'Postal Code Management' },
            { name: 'postal_code:delete', description: 'Delete postal codes', category: 'Postal Code Management' },
            { name: 'employee:create', description: 'Create employees', category: 'Employee Management' },
            { name: 'employee:read', description: 'View employees', category: 'Employee Management' },
            { name: 'employee:update', description: 'Update employees', category: 'Employee Management' },
            { name: 'employee:delete', description: 'Delete employees', category: 'Employee Management' },
            { name: 'address:create', description: 'Create addresses', category: 'Address Management' },
            { name: 'address:read', description: 'View addresses', category: 'Address Management' },
            { name: 'address:update', description: 'Update addresses', category: 'Address Management' },
            { name: 'address:delete', description: 'Delete addresses', category: 'Address Management' },
            { name: 'bulk:import', description: 'Import data in bulk', category: 'Data Management' },
            { name: 'bulk:export', description: 'Export data in bulk', category: 'Data Management' },
            { name: 'data:archive', description: 'Archive old data', category: 'Data Management' },
            { name: 'data:restore', description: 'Restore archived data', category: 'Data Management' },
        ];
        for (const permissionData of additionalPermissions) {
            try {
                const permission = await this.createCustomPermission(permissionData.name, permissionData.description, permissionData.category);
                customPermissions.push(permission);
                console.log(`✅ Created custom permission: ${permissionData.name}`);
            }
            catch (error) {
                console.log(`⚠️ Permission ${permissionData.name} already exists or failed to create`);
            }
        }
        return customPermissions;
    }
    async seedRoles(permissions) {
        console.log('🌱 Seeding roles...');
        const existingRoles = await this.rolesRepository.find();
        if (existingRoles.length > 0) {
            console.log('✅ Roles already exist, skipping...');
            return existingRoles;
        }
        const adminPermissions = permissions;
        const managerPermissions = permissions.filter(p => p.name.includes('application:') ||
            p.name.includes('license:') ||
            p.name.includes('evaluation:') ||
            p.name.includes('document:') ||
            p.name === permission_entity_1.PERMISSION_NAMES.USER_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICANT_READ);
        const officerPermissions = permissions.filter(p => p.name.includes('application:') ||
            p.name.includes('license:') ||
            p.name.includes('evaluation:') ||
            p.name.includes('document:') ||
            p.name === permission_entity_1.PERMISSION_NAMES.USER_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICANT_READ);
        const customerPermissions = permissions.filter(p => p.name === permission_entity_1.PERMISSION_NAMES.APPLICATION_CREATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICATION_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICATION_UPDATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICATION_SUBMIT ||
            p.name === permission_entity_1.PERMISSION_NAMES.LICENSE_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.DOCUMENT_CREATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.DOCUMENT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.DOCUMENT_DOWNLOAD ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_CREATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_UPDATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.PAYMENT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.INVOICE_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.NOTIFICATION_READ);
        const legalPermissions = permissions.filter(p => p.name.includes('license:') ||
            p.name.includes('application:') ||
            p.name.includes('evaluation:') ||
            p.name.includes('document:') ||
            p.name === permission_entity_1.PERMISSION_NAMES.USER_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICANT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.REPORT_GENERATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.REPORT_VIEW);
        const accountantPermissions = permissions.filter(p => p.name.includes('financial:') ||
            p.name.includes('invoice:') ||
            p.name.includes('payment:') ||
            p.name.includes('report:') ||
            p.name === permission_entity_1.PERMISSION_NAMES.LICENSE_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICATION_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.USER_READ);
        const salesPermissions = permissions.filter(p => p.name === permission_entity_1.PERMISSION_NAMES.APPLICATION_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.LICENSE_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_CREATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_UPDATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICANT_CREATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICANT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICANT_UPDATE ||
            p.name === permission_entity_1.PERMISSION_NAMES.REPORT_VIEW);
        const departmentPermissions = permissions.filter(p => p.name.includes('application:') ||
            p.name.includes('license:') ||
            p.name.includes('evaluation:') ||
            p.name.includes('document:') ||
            p.name === permission_entity_1.PERMISSION_NAMES.USER_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.CONTACT_READ ||
            p.name === permission_entity_1.PERMISSION_NAMES.APPLICANT_READ);
        const rolesData = [
            {
                name: role_entity_1.RoleName.ADMINISTRATOR,
                description: 'Full system access with all permissions',
                permissions: adminPermissions,
            },
            {
                name: role_entity_1.RoleName.MANAGER,
                description: 'Full system access with all permissions',
                permissions: managerPermissions,
            },
            {
                name: role_entity_1.RoleName.CUSTOMER,
                description: 'Customer access for license applications and document management',
                permissions: customerPermissions,
            },
            {
                name: role_entity_1.RoleName.OFFICER,
                description: 'Officer access for reviewing applications and managing evaluations',
                permissions: officerPermissions,
            },
            {
                name: role_entity_1.RoleName.LEGAL,
                description: 'Legal team access for license and application management',
                permissions: legalPermissions,
            },
            {
                name: role_entity_1.RoleName.FINANCE,
                description: 'Financial management, invoicing, payments and reporting access',
                permissions: accountantPermissions,
            },
            {
                name: role_entity_1.RoleName.DIRECTOR_GENERAL,
                description: 'Director general access for license and application management, invoicing, payments and reporting access',
                permissions: accountantPermissions,
            },
        ];
        const roles = [];
        for (const roleData of rolesData) {
            const role = this.rolesRepository.create({
                name: roleData.name,
                description: roleData.description,
                permissions: roleData.permissions,
            });
            const savedRole = await this.rolesRepository.save(role);
            roles.push(savedRole);
            console.log(`✅ Created role: ${roleData.name} with ${roleData.permissions.length} permissions`);
        }
        return roles;
    }
    async seedUsers(roles) {
        console.log('🌱 Seeding users...');
        let users = [];
        const existingUsers = await this.usersRepository.find();
        if (existingUsers.length > 0) {
            return existingUsers;
        }
        const adminRole = roles.find(role => role.name === role_entity_1.RoleName.ADMINISTRATOR);
        const officerRole = roles.find(role => role.name === role_entity_1.RoleName.OFFICER);
        const financeRole = roles.find(role => role.name === role_entity_1.RoleName.FINANCE);
        const dgRole = roles.find(role => role.name === role_entity_1.RoleName.DIRECTOR_GENERAL);
        const adminUser = this.usersRepository.create({
            email: '<EMAIL>',
            password: await bcrypt.hash('Admin123!', 12),
            first_name: 'System',
            last_name: 'Administrator',
            phone: '+265999000001',
            status: user_entity_1.UserStatus.ACTIVE,
            email_verified_at: new Date(),
            roles: adminRole ? [adminRole] : [],
        });
        const savedAdminUser = await this.usersRepository.save(adminUser);
        users.push(savedAdminUser);
        console.log('✅ Created admin user: <EMAIL>');
        const officerUser = this.usersRepository.create({
            email: '<EMAIL>',
            password: await bcrypt.hash('Officer123!', 12),
            first_name: 'Officer',
            last_name: 'Evaluator',
            phone: '+265999000002',
            status: user_entity_1.UserStatus.ACTIVE,
            email_verified_at: new Date(),
            roles: officerRole ? [officerRole] : [],
        });
        const savedOfficerUser = await this.usersRepository.save(officerUser);
        users.push(savedOfficerUser);
        console.log('✅ Created officer user: <EMAIL>');
        const financeUser = this.usersRepository.create({
            email: '<EMAIL>',
            password: await bcrypt.hash('Finance123!', 12),
            first_name: 'Finance',
            last_name: 'Officer',
            phone: '+265999000003',
            status: user_entity_1.UserStatus.ACTIVE,
            email_verified_at: new Date(),
            roles: financeRole ? [financeRole] : [],
        });
        const savedFinanceUser = await this.usersRepository.save(financeUser);
        users.push(savedFinanceUser);
        const dgUser = this.usersRepository.create({
            email: '<EMAIL>',
            password: await bcrypt.hash('Dg123!', 12),
            first_name: 'Director',
            last_name: 'General',
            phone: '+265999000004',
            status: user_entity_1.UserStatus.ACTIVE,
            email_verified_at: new Date(),
            roles: dgRole ? [dgRole] : [],
        });
        const savedDgUser = await this.usersRepository.save(dgUser);
        users.push(savedDgUser);
        console.log('✅ Created director general user: <EMAIL>');
        console.log(`✅ Total users created: ${users.length}`);
        return users;
    }
    async seedUserIdentifications(_users) {
        console.log('🌱 Seeding user identifications...');
        const existingIdentifications = await this.userIdentificationsRepository.find();
        if (existingIdentifications.length > 0) {
            console.log('✅ User identifications already exist, skipping...');
            return existingIdentifications;
        }
        console.log('⚠️ User identification seeding skipped - requires identification types to be seeded first');
        return [];
    }
    async handleForeignKeyConstraints() {
        console.log('🔧 Checking and handling foreign key constraints...');
        const dataSource = this.usersRepository.manager.connection;
        const dbType = dataSource.options.type;
        try {
            if (dbType === 'postgres') {
                const constraintExists = await dataSource.query(`
          SELECT constraint_name
          FROM information_schema.table_constraints
          WHERE table_name = 'permissions'
          AND constraint_name = 'FK_58fae278276b7c2c6dde2bc19a5'
          AND constraint_type = 'FOREIGN KEY'
        `);
                if (constraintExists.length > 0) {
                    console.log('⚠️ Found existing foreign key constraint, dropping it...');
                    await dataSource.query(`
            ALTER TABLE "permissions"
            DROP CONSTRAINT IF EXISTS "FK_58fae278276b7c2c6dde2bc19a5"
          `);
                    console.log('✅ Dropped existing foreign key constraint');
                }
                const createdByConstraintExists = await dataSource.query(`
          SELECT constraint_name
          FROM information_schema.table_constraints
          WHERE table_name = 'permissions'
          AND constraint_name LIKE 'FK_%'
          AND constraint_type = 'FOREIGN KEY'
        `);
                for (const constraint of createdByConstraintExists) {
                    const constraintDetails = await dataSource.query(`
            SELECT
              tc.constraint_name,
              kcu.column_name,
              ccu.table_name AS foreign_table_name,
              ccu.column_name AS foreign_column_name
            FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_name = $1
          `, [constraint.constraint_name]);
                    if (constraintDetails.length > 0 && constraintDetails[0].foreign_table_name === 'users') {
                        console.log(`⚠️ Found existing foreign key constraint ${constraint.constraint_name}, dropping it...`);
                        await dataSource.query(`
              ALTER TABLE "permissions"
              DROP CONSTRAINT IF EXISTS "${constraint.constraint_name}"
            `);
                        console.log(`✅ Dropped existing foreign key constraint ${constraint.constraint_name}`);
                    }
                }
            }
            else if (dbType === 'mysql' || dbType === 'mariadb') {
                const constraints = await dataSource.query(`
          SELECT CONSTRAINT_NAME
          FROM information_schema.KEY_COLUMN_USAGE
          WHERE TABLE_NAME = 'permissions'
          AND REFERENCED_TABLE_NAME = 'users'
          AND TABLE_SCHEMA = DATABASE()
        `);
                for (const constraint of constraints) {
                    console.log(`⚠️ Found existing foreign key constraint ${constraint.CONSTRAINT_NAME}, dropping it...`);
                    await dataSource.query(`
            ALTER TABLE permissions
            DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}
          `);
                    console.log(`✅ Dropped existing foreign key constraint ${constraint.CONSTRAINT_NAME}`);
                }
            }
            console.log('✅ Foreign key constraint handling completed');
        }
        catch (error) {
            console.log('⚠️ Warning during foreign key constraint handling:', error.message);
        }
    }
    async ensureDatabaseSchema() {
        console.log('🔧 Ensuring database schema is synchronized...');
        const dataSource = this.usersRepository.manager.connection;
        try {
            await dataSource.synchronize(false);
            console.log('✅ Database schema synchronized successfully');
        }
        catch (error) {
            console.log('⚠️ Warning during schema synchronization:', error.message);
            if (error.message.includes('already exists')) {
                console.log('🔧 Attempting to resolve constraint conflicts...');
                await this.handleForeignKeyConstraints();
                try {
                    await dataSource.synchronize(false);
                    console.log('✅ Database schema synchronized successfully after constraint resolution');
                }
                catch (retryError) {
                    console.log('⚠️ Schema synchronization still failed, continuing with seeding...');
                }
            }
        }
    }
    async seedAllSafe() {
        console.log('🚀 Starting safe database seeding...');
        try {
            await this.resolveConstraintConflicts();
            const permissions = await this.seedPermissions();
            const roles = await this.seedRoles(permissions);
            const users = await this.seedUsers(roles);
            await this.seedUserIdentifications(users);
            await this.licenseSeederService.seedAll();
            await this.organizationSeederService.seedAll();
            await this.departmentSeederService.seedAll();
            await this.postalCodeSeederService.seedAll();
            console.log('🎉 Safe database seeding completed successfully!');
            console.log('');
            console.log('📋 Default accounts created:');
            console.log('👤 Admin: <EMAIL> / Admin123!');
            console.log('👤 Evaluator: <EMAIL> / Evaluator123!');
            console.log('👤 Test User: <EMAIL> / Password123!');
            console.log('');
        }
        catch (error) {
            console.error('❌ Error during safe seeding:', error);
            throw error;
        }
    }
    async resolveConstraintConflicts() {
        console.log('🔧 Resolving constraint conflicts...');
        const dataSource = this.usersRepository.manager.connection;
        const dbType = dataSource.options.type;
        try {
            if (dbType === 'postgres') {
                await dataSource.query(`
          DO $$
          DECLARE
            constraint_name text;
          BEGIN
            FOR constraint_name IN
              SELECT tc.constraint_name
              FROM information_schema.table_constraints tc
              JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
              JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
              WHERE tc.table_name = 'permissions'
              AND tc.constraint_type = 'FOREIGN KEY'
              AND ccu.table_name = 'users'
            LOOP
              EXECUTE 'ALTER TABLE permissions DROP CONSTRAINT IF EXISTS ' || constraint_name;
            END LOOP;
          END $$;
        `);
                console.log('✅ Dropped existing foreign key constraints on permissions table');
            }
            else if (dbType === 'mysql' || dbType === 'mariadb') {
                const constraints = await dataSource.query(`
          SELECT CONSTRAINT_NAME
          FROM information_schema.KEY_COLUMN_USAGE
          WHERE TABLE_NAME = 'permissions'
          AND REFERENCED_TABLE_NAME = 'users'
          AND TABLE_SCHEMA = DATABASE()
        `);
                for (const constraint of constraints) {
                    await dataSource.query(`
            ALTER TABLE permissions
            DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}
          `);
                }
                console.log('✅ Dropped existing foreign key constraints on permissions table');
            }
        }
        catch (error) {
            console.log('⚠️ Warning during constraint conflict resolution:', error.message);
        }
    }
    async seedAll() {
        console.log('🚀 Starting database seeding...');
        try {
            await this.handleForeignKeyConstraints();
            await this.ensureDatabaseSchema();
            const permissions = await this.seedPermissions();
            const roles = await this.seedRoles(permissions);
            const users = await this.seedUsers(roles);
            await this.seedUserIdentifications(users);
            await this.licenseSeederService.seedAll();
            await this.organizationSeederService.seedAll();
            await this.departmentSeederService.seedAll();
            await this.postalCodeSeederService.seedAll();
            console.log('🎉 Database seeding completed successfully!');
            console.log('');
            console.log('📋 Default accounts created:');
            console.log('👤 Admin: <EMAIL> / Admin123!');
            console.log('👤 Operator: <EMAIL> / Officer123!');
            console.log('👤 Director General: <EMAIL> / Dg123!');
            console.log('👤 Finance: <EMAIL> / Finance123!');
            console.log('');
        }
        catch (error) {
            console.error('❌ Error during seeding:', error);
            throw error;
        }
    }
    async clearAll() {
        console.log('🗑️ Clearing all seeded data...');
        try {
            const userIdentifications = await this.userIdentificationsRepository.find();
            if (userIdentifications.length > 0) {
                await this.userIdentificationsRepository.remove(userIdentifications);
                console.log('✅ Cleared user identifications');
            }
            const dataSource = this.usersRepository.manager.connection;
            if (dataSource.options.type === 'mysql') {
                await dataSource.query('SET FOREIGN_KEY_CHECKS = 0');
            }
            const tablesToClear = [
                'license_category_documents',
                'license_categories',
                'license_types',
                'identification_types',
                'permissions',
                'roles',
                'departments',
                'organizations',
                'user_identifications'
            ];
            for (const table of tablesToClear) {
                try {
                    await dataSource.query(`DELETE FROM ${table}`);
                    console.log(`✅ Cleared ${table}`);
                }
                catch (error) {
                    console.log(`⚠️ Could not clear ${table}: ${error.message}`);
                }
            }
            await dataSource.query('DELETE FROM user_roles');
            await dataSource.query('DELETE FROM role_permissions');
            console.log('✅ Cleared junction tables');
            await dataSource.query('DELETE FROM users');
            console.log('✅ Cleared users');
            if (dataSource.options.type === 'mysql') {
                await dataSource.query('SET FOREIGN_KEY_CHECKS = 1');
            }
            console.log('✅ All seeded data cleared successfully!');
        }
        catch (error) {
            console.error('❌ Error during clearing:', error);
            throw error;
        }
    }
};
exports.SeederService = SeederService;
exports.SeederService = SeederService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(2, (0, typeorm_1.InjectRepository)(role_entity_1.Role)),
    __param(3, (0, typeorm_1.InjectRepository)(permission_entity_1.Permission)),
    __param(4, (0, typeorm_1.InjectRepository)(user_identification_entity_1.UserIdentification)),
    __param(5, (0, typeorm_1.InjectRepository)(identification_type_entity_1.IdentificationType)),
    __param(10, (0, typeorm_1.InjectRepository)(organization_entity_1.Organization)),
    __param(11, (0, typeorm_1.InjectRepository)(department_entity_1.Department)),
    __metadata("design:paramtypes", [typeorm_2.DataSource,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        license_seeder_service_1.LicenseSeederService,
        organizations_seeder_1.OrganizationSeederService,
        departments_seeder_1.DepartmentSeederService,
        postal_code_seeder_1.PostalCodeSeederService,
        typeorm_2.Repository,
        typeorm_2.Repository])
], SeederService);
//# sourceMappingURL=seeder.service.js.map