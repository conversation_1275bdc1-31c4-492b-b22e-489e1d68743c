"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const applications_entity_1 = require("../entities/applications.entity");
const application_status_history_entity_1 = require("../entities/application-status-history.entity");
const user_entity_1 = require("../entities/user.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const application_task_helper_service_1 = require("./application-task-helper.service");
const notification_helper_service_1 = require("../notifications/notification-helper.service");
const tasks_service_1 = require("../tasks/tasks.service");
const licenses_service_1 = require("../licenses/licenses.service");
const activity_notes_service_1 = require("../services/activity-notes.service");
const class_validator_1 = require("class-validator");
let ApplicationsService = class ApplicationsService {
    constructor(applicationsRepository, statusHistoryRepository, usersRepository, applicationTaskHelper, notificationHelper, tasksService, licensesService, activityNotesService) {
        this.applicationsRepository = applicationsRepository;
        this.statusHistoryRepository = statusHistoryRepository;
        this.usersRepository = usersRepository;
        this.applicationTaskHelper = applicationTaskHelper;
        this.notificationHelper = notificationHelper;
        this.tasksService = tasksService;
        this.licensesService = licensesService;
        this.activityNotesService = activityNotesService;
        this.paginateConfig = {
            sortableColumns: ['created_at', 'updated_at', 'application_number', 'status'],
            searchableColumns: ['application_number', 'status'],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: 10,
            maxLimit: 100,
            relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater', 'assignee'],
            filterableColumns: {
                status: true,
                created_by: true,
                license_category_id: true,
                applicant_id: true,
                assigned_to: true,
                'license_category.license_type_id': true,
            },
        };
    }
    async create(createApplicationDto, createdBy) {
        let applicationNumber = createApplicationDto.application_number;
        if (!applicationNumber) {
            applicationNumber = await this.generateApplicationNumber();
        }
        else {
            const existingApplication = await this.applicationsRepository.findOne({
                where: { application_number: applicationNumber,
                    deleted_at: (0, typeorm_2.IsNull)()
                },
            });
            if (existingApplication) {
                throw new common_1.ConflictException('An application with this number already exists');
            }
        }
        const existingCategoryApplication = await this.applicationsRepository.findOne({
            where: { license_category_id: createApplicationDto.license_category_id,
                applicant_id: createApplicationDto.applicant_id,
                deleted_at: (0, typeorm_2.IsNull)()
            },
        });
        if (existingCategoryApplication) {
            throw new common_1.ConflictException('You can only submit one application per licence category');
        }
        const application = this.applicationsRepository.create({
            ...createApplicationDto,
            application_number: applicationNumber,
            current_step: createApplicationDto.current_step || 1,
            progress_percentage: createApplicationDto.progress_percentage || 0,
            status: createApplicationDto.status || 'draft',
            created_by: createdBy,
        });
        return this.applicationsRepository.save(application);
    }
    async generateApplicationNumber() {
        const currentYear = new Date().getFullYear();
        const prefix = `APP-${currentYear}`;
        try {
            console.log(`🔧 Generating application number with prefix: ${prefix}`);
            const latestApplication = await this.applicationsRepository
                .createQueryBuilder('application')
                .where('application.application_number LIKE :pattern', {
                pattern: `${prefix}-%`
            })
                .andWhere('application.deleted_at IS NULL')
                .orderBy('application.application_number', 'DESC')
                .getOne();
            let nextSequence = 1;
            if (latestApplication) {
                console.log(`📄 Latest application found: ${latestApplication.application_number}`);
                const parts = latestApplication.application_number.split('-');
                if (parts.length === 3) {
                    const lastSequence = parseInt(parts[2], 10);
                    if (!isNaN(lastSequence)) {
                        nextSequence = lastSequence + 1;
                    }
                }
            }
            console.log(`🔢 Next sequence number: ${nextSequence}`);
            let attempts = 0;
            const maxAttempts = 100;
            while (attempts < maxAttempts) {
                const sequence = String(nextSequence).padStart(6, '0');
                const applicationNumber = `${prefix}-${sequence}`;
                console.log(`🔍 Checking uniqueness for: ${applicationNumber}`);
                const existingApplication = await this.applicationsRepository.findOne({
                    where: {
                        application_number: applicationNumber,
                        deleted_at: (0, typeorm_2.IsNull)()
                    }
                });
                if (!existingApplication) {
                    console.log(`✅ Generated unique application number: ${applicationNumber}`);
                    return applicationNumber;
                }
                nextSequence++;
                attempts++;
                console.warn(`⚠️ Application number ${applicationNumber} already exists, trying next sequence: ${nextSequence}`);
            }
            throw new Error(`Could not generate unique application number after ${maxAttempts} attempts`);
        }
        catch (error) {
            console.error('❌ Error generating application number:', error);
            throw new Error('Failed to generate application number');
        }
    }
    async findAll(query, userRoles, userId) {
        const isCustomer = userRoles?.includes('customer');
        if (isCustomer && userId) {
            const customerQuery = {
                ...query,
                filter: {
                    ...query.filter,
                    created_by: userId
                }
            };
            return (0, nestjs_paginate_1.paginate)(customerQuery, this.applicationsRepository, this.paginateConfig);
        }
        const includeDraftValue = query.filter?.['include_draft'];
        const shouldIncludeDrafts = includeDraftValue === 'true' ||
            (typeof includeDraftValue === 'boolean' && includeDraftValue === true);
        if (shouldIncludeDrafts) {
            return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, this.paginateConfig);
        }
        else {
            const queryBuilder = this.applicationsRepository
                .createQueryBuilder('application')
                .leftJoinAndSelect('application.applicant', 'applicant')
                .leftJoinAndSelect('application.license_category', 'license_category')
                .leftJoinAndSelect('license_category.license_type', 'license_type')
                .leftJoinAndSelect('application.creator', 'creator')
                .leftJoinAndSelect('application.updater', 'updater')
                .leftJoinAndSelect('application.assignee', 'assignee')
                .where('application.status != :draftStatus', { draftStatus: 'draft' });
            if (query.filter) {
                Object.entries(query.filter).forEach(([key, value]) => {
                    if (key !== 'include_draft' && value !== undefined && value !== '') {
                        if (key.includes('.')) {
                            queryBuilder.andWhere(`${key} = :${key.replace('.', '_')}`, { [key.replace('.', '_')]: value });
                        }
                        else {
                            queryBuilder.andWhere(`application.${key} = :${key}`, { [key]: value });
                        }
                    }
                });
            }
            if (query.search) {
                queryBuilder.andWhere('(application.application_number LIKE :search OR application.status LIKE :search)', { search: `%${query.search}%` });
            }
            return (0, nestjs_paginate_1.paginate)(query, queryBuilder, this.paginateConfig);
        }
    }
    async findUserApplications(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, this.paginateConfig);
    }
    async findOne(id) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: id },
            relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${id} not found`);
        }
        return application;
    }
    async findByApplicant(applicantId) {
        return this.applicationsRepository.find({
            where: { applicant_id: applicantId },
            relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async findByStatus(status) {
        return this.applicationsRepository.find({
            where: { status },
            relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async update(id, updateApplicationDto, updatedBy) {
        const application = await this.findOne(id);
        const previousStatus = application.status;
        if (updateApplicationDto.application_number && updateApplicationDto.application_number !== application.application_number) {
            const existingApplication = await this.applicationsRepository.findOne({
                where: { application_number: updateApplicationDto.application_number },
            });
            if (existingApplication) {
                throw new common_1.ConflictException('Application number already exists');
            }
        }
        Object.assign(application, updateApplicationDto, { updated_by: updatedBy });
        if (updateApplicationDto.status === applications_entity_1.ApplicationStatus.SUBMITTED && !application.submitted_at) {
            application.submitted_at = new Date();
        }
        const savedApplication = await this.applicationsRepository.save(application);
        const currentStatus = savedApplication.status;
        if (currentStatus !== previousStatus) {
            await this.applicationTaskHelper.handleApplicationSubmission(application.application_id, previousStatus, currentStatus, updatedBy);
        }
        return savedApplication;
    }
    async remove(id) {
        const application = await this.findOne(id);
        await this.applicationsRepository.softDelete(application.application_id);
    }
    async updateStatus(id, status, updatedBy) {
        const application = await this.findOne(id);
        const previousStatus = application.status;
        application.status = status;
        application.updated_by = updatedBy;
        if (status === applications_entity_1.ApplicationStatus.SUBMITTED && !application.submitted_at) {
            application.submitted_at = new Date();
        }
        const savedApplication = await this.applicationsRepository.save(application);
        await this.applicationTaskHelper.handleApplicationSubmission(application.application_id, previousStatus, status, updatedBy);
        return savedApplication;
    }
    async updateApplicationStatus(applicationId, updateStatusDto, userId) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: applicationId },
            relations: ['applicant', 'license_category']
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
        }
        this.validateStatusTransition(application.status, updateStatusDto.status);
        const isFree = application.license_category && (0, class_validator_1.isNumber)(application.license_category.fee) && Number(application.license_category.fee) > 0 ? false : true;
        if (isFree && updateStatusDto.status == applications_entity_1.ApplicationStatus.PASS_EVALUATION) {
            updateStatusDto.status = applications_entity_1.ApplicationStatus.WAITING_FOR_APPROVAL;
        }
        const previousStatus = application.status;
        const { newStep, newProgress } = this.calculateStepAndProgress(updateStatusDto.status);
        application.status = updateStatusDto.status;
        application.current_step = newStep;
        application.progress_percentage = newProgress;
        application.updated_by = userId;
        if (updateStatusDto.status === applications_entity_1.ApplicationStatus.SUBMITTED && !application.submitted_at) {
            application.submitted_at = new Date();
        }
        await this.applicationsRepository.save(application);
        await this.applicationTaskHelper.handleApplicationSubmission(applicationId, previousStatus, updateStatusDto.status, userId);
        try {
            await this.tasksService.closeAllTasksForApplication(applicationId, userId, `Application status changed to ${updateStatusDto.status}. All tasks automatically closed.`);
        }
        catch (taskError) {
            console.error(`⚠️ Failed to close tasks for application ${applicationId}:`, taskError);
        }
        if (updateStatusDto.status === 'approved') {
            try {
                const license = await this.licensesService.createLicenseFromApplication(applicationId, userId);
                const licenseActivityNote = `License ${license.license_number} has been issued for this application. The license is valid from ${license.issue_date} to ${license.expiry_date}.`;
                await this.activityNotesService.create({
                    entity_type: 'application',
                    entity_id: applicationId,
                    note: licenseActivityNote,
                    note_type: 'license_issued',
                    category: 'status',
                    priority: 'high',
                    is_internal: false,
                }, userId);
            }
            catch (licenseError) {
                console.error(`❌ Failed to create license for application ${applicationId}:`, licenseError);
            }
        }
        const statusHistory = new application_status_history_entity_1.ApplicationStatusHistory();
        statusHistory.application_id = applicationId;
        statusHistory.status = updateStatusDto.status;
        statusHistory.previous_status = previousStatus;
        statusHistory.comments = updateStatusDto.comments;
        statusHistory.reason = updateStatusDto.reason;
        statusHistory.changed_by = updateStatusDto.changed_by || userId;
        if (updateStatusDto.estimated_completion_date) {
            statusHistory.estimated_completion_date = new Date(updateStatusDto.estimated_completion_date);
        }
        await this.statusHistoryRepository.save(statusHistory);
        if (updateStatusDto.send_email && application.applicant) {
            try {
                await this.notificationHelper.notifyApplicationStatus(applicationId, application.applicant.applicant_id, application.applicant.email, application.application_number, updateStatusDto.status, userId, application.applicant.name, application.license_category?.name || 'License', previousStatus);
            }
            catch (error) {
                console.error(`Failed to send email notification for application ${applicationId}:`, error);
            }
        }
        return this.getApplicationStatusTracking(applicationId);
    }
    async getApplicationStatusTracking(applicationId) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: applicationId },
            relations: ['applicant', 'license_category']
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
        }
        const statusHistory = await this.statusHistoryRepository.find({
            where: { application_id: applicationId },
            relations: ['user'],
            order: { changed_at: 'DESC' }
        });
        const transformedHistory = statusHistory.map(history => ({
            history_id: history.history_id,
            application_id: history.application_id,
            status: history.status,
            previous_status: history.previous_status,
            comments: history.comments,
            reason: history.reason,
            changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
            changed_at: history.changed_at,
            estimated_completion_date: history.estimated_completion_date
        }));
        return {
            application_id: application.application_id,
            application_number: application.application_number,
            current_status: application.status,
            current_step: application.current_step,
            progress_percentage: application.progress_percentage,
            submitted_at: application.submitted_at,
            created_at: application.created_at,
            updated_at: application.updated_at,
            status_history: transformedHistory,
            applicant: {
                name: application.applicant.name,
                email: application.applicant.email,
                business_registration_number: application.applicant.business_registration_number || ''
            },
            license_category: {
                name: application.license_category.name,
                description: application.license_category.description
            }
        };
    }
    async getApplicationsByStatus(status) {
        const applications = await this.applicationsRepository.find({
            where: { status },
            relations: ['applicant', 'license_category', 'license_category.license_type'],
            order: { updated_at: 'DESC' }
        });
        if (applications.length === 0) {
            return [];
        }
        const applicationIds = applications.map(app => app.application_id);
        const statusHistories = await this.statusHistoryRepository.find({
            where: { application_id: (0, typeorm_2.In)(applicationIds) },
            relations: ['user'],
            order: { changed_at: 'DESC' }
        });
        const historiesByAppId = statusHistories.reduce((acc, history) => {
            if (!acc[history.application_id]) {
                acc[history.application_id] = [];
            }
            acc[history.application_id].push(history);
            return acc;
        }, {});
        return applications.map(application => {
            const appHistories = historiesByAppId[application.application_id] || [];
            const transformedHistory = appHistories.map(history => ({
                history_id: history.history_id,
                application_id: history.application_id,
                status: history.status,
                previous_status: history.previous_status,
                comments: history.comments,
                reason: history.reason,
                changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
                changed_at: history.changed_at,
                estimated_completion_date: history.estimated_completion_date
            }));
            return {
                application_id: application.application_id,
                application_number: application.application_number,
                current_status: application.status,
                current_step: application.current_step,
                progress_percentage: application.progress_percentage,
                submitted_at: application.submitted_at,
                created_at: application.created_at,
                updated_at: application.updated_at,
                status_history: transformedHistory,
                applicant: {
                    name: application.applicant.name,
                    email: application.applicant.email,
                    business_registration_number: application.applicant.business_registration_number || ''
                },
                license_category: {
                    name: application.license_category.name,
                    description: application.license_category.description
                }
            };
        });
    }
    async getStatusHistory(applicationId) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: applicationId }
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
        }
        const statusHistory = await this.statusHistoryRepository.find({
            where: { application_id: applicationId },
            relations: ['user'],
            order: { changed_at: 'DESC' }
        });
        return statusHistory.map(history => ({
            history_id: history.history_id,
            application_id: history.application_id,
            status: history.status,
            previous_status: history.previous_status,
            comments: history.comments,
            reason: history.reason,
            changed_by_name: history.user ? `${history.user.first_name} ${history.user.last_name}` : 'System',
            changed_at: history.changed_at,
            estimated_completion_date: history.estimated_completion_date
        }));
    }
    validateStatusTransition(currentStatus, newStatus) {
        const validTransitions = {
            'draft': ['submitted'],
            'submitted': ['under_review', 'evaluation', 'rejected'],
            'under_review': ['evaluation', 'rejected'],
            'evaluation': ['pass_evaluation', 'approved', 'rejected'],
            'pass_evaluation': ['approved', 'rejected', 'evaluation', 'pass_evaluation'],
            'pending_payment': ['waiting_for_approval', 'draft', 'rejected'],
            'waiting_for_approval': ['approved', 'rejected', 'waiting_for_approval'],
            'approved': ['approved'],
            'rejected': ['rejected'],
            'withdrawn': ['withdrawn']
        };
        if (!validTransitions[currentStatus] || !validTransitions[currentStatus].includes(newStatus)) {
            throw new common_1.BadRequestException(`Invalid status transition from ${currentStatus} to ${newStatus}`);
        }
    }
    calculateStepAndProgress(status) {
        const statusStepMap = {
            'draft': { step: 1, progress: 14 },
            'submitted': { step: 2, progress: 25 },
            'under_review': { step: 3, progress: 50 },
            'evaluation': { step: 4, progress: 75 },
            'pass_evaluation': { step: 5, progress: 80 },
            'waiting_for_approval': { step: 5, progress: 90 },
            'approved': { step: 6, progress: 100 },
            'rejected': { step: 1, progress: 0 },
            'withdrawn': { step: 1, progress: 0 }
        };
        const mapping = statusStepMap[status];
        if (!mapping) {
            throw new common_1.BadRequestException(`Unknown status: ${status}`);
        }
        return {
            newStep: mapping.step,
            newProgress: mapping.progress
        };
    }
    async updateProgress(id, currentStep, progressPercentage, updatedBy) {
        const application = await this.findOne(id);
        application.current_step = currentStep;
        application.progress_percentage = progressPercentage;
        application.updated_by = updatedBy;
        return this.applicationsRepository.save(application);
    }
    async getApplicationStats() {
        const stats = await this.applicationsRepository
            .createQueryBuilder('application')
            .select('application.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('application.status')
            .getRawMany();
        return stats.reduce((acc, stat) => {
            acc[stat.status] = parseInt(stat.count);
            return acc;
        }, {});
    }
    async assignApplication(applicationId, assignedTo, assignedBy) {
        const application = await this.findOne(applicationId);
        application.assigned_to = assignedTo;
        application.assigned_at = new Date();
        application.updated_by = assignedBy;
        return this.applicationsRepository.save(application);
    }
    async getUnassignedApplications(query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: (0, typeorm_2.IsNull)() },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, config);
    }
    async getAssignedApplications(userId, query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: userId },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, config);
    }
    async findAllDebug(query) {
        const count = await this.applicationsRepository.count();
        const statusStats = await this.applicationsRepository
            .createQueryBuilder('app')
            .select('app.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('app.status')
            .getRawMany();
        const debugConfig = {
            ...this.paginateConfig,
        };
        return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, debugConfig);
    }
};
exports.ApplicationsService = ApplicationsService;
exports.ApplicationsService = ApplicationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(applications_entity_1.Applications)),
    __param(1, (0, typeorm_1.InjectRepository)(application_status_history_entity_1.ApplicationStatusHistory)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(5, (0, common_1.Inject)((0, common_1.forwardRef)(() => tasks_service_1.TasksService))),
    __param(6, (0, common_1.Inject)((0, common_1.forwardRef)(() => licenses_service_1.LicensesService))),
    __param(7, (0, common_1.Inject)((0, common_1.forwardRef)(() => activity_notes_service_1.ActivityNotesService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        application_task_helper_service_1.ApplicationTaskHelperService,
        notification_helper_service_1.NotificationHelperService,
        tasks_service_1.TasksService,
        licenses_service_1.LicensesService,
        activity_notes_service_1.ActivityNotesService])
], ApplicationsService);
//# sourceMappingURL=applications.service.js.map