import { Repository } from 'typeorm';
import { LicenseTypes } from '../../entities/license-types.entity';
import { LicenseCategories } from '../../entities/license-categories.entity';
import { LicenseCategoryDocument } from '../../entities/license-category-document.entity';
import { User } from '../../entities/user.entity';
export declare class LicenseSeederService {
    private licenseTypesRepository;
    private licenseCategoriesRepository;
    private licenseCategoryDocumentsRepository;
    private usersRepository;
    constructor(licenseTypesRepository: Repository<LicenseTypes>, licenseCategoriesRepository: Repository<LicenseCategories>, licenseCategoryDocumentsRepository: Repository<LicenseCategoryDocument>, usersRepository: Repository<User>);
    seedLicenseTypes(): Promise<void>;
    seedLicenseCategories(): Promise<void>;
    seedLicenseCategoryDocuments(): Promise<void>;
    seedAll(): Promise<void>;
    clearLicenseCategories(): Promise<void>;
    clearLicenseTypes(): Promise<void>;
    clearAll(): Promise<void>;
}
