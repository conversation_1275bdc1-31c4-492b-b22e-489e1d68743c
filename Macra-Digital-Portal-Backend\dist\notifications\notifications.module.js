"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const notifications_controller_1 = require("./notifications.controller");
const notifications_service_1 = require("./notifications.service");
const notification_helper_service_1 = require("./notification-helper.service");
const email_template_service_1 = require("./email-template.service");
const notification_processor_service_1 = require("./notification-processor.service");
const notifications_entity_1 = require("../entities/notifications.entity");
const users_module_1 = require("../users/users.module");
const activity_notes_module_1 = require("../activity-notes/activity-notes.module");
const applications_module_1 = require("../applications/applications.module");
let NotificationsModule = class NotificationsModule {
};
exports.NotificationsModule = NotificationsModule;
exports.NotificationsModule = NotificationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([notifications_entity_1.Notifications]),
            (0, common_1.forwardRef)(() => users_module_1.UsersModule),
            (0, common_1.forwardRef)(() => activity_notes_module_1.ActivityNotesModule),
            (0, common_1.forwardRef)(() => applications_module_1.ApplicationsModule),
        ],
        controllers: [notifications_controller_1.NotificationsController],
        providers: [notifications_service_1.NotificationsService, notification_helper_service_1.NotificationHelperService, email_template_service_1.EmailTemplateService, notification_processor_service_1.NotificationProcessorService],
        exports: [notifications_service_1.NotificationsService, notification_helper_service_1.NotificationHelperService, email_template_service_1.EmailTemplateService, notification_processor_service_1.NotificationProcessorService],
    })
], NotificationsModule);
//# sourceMappingURL=notifications.module.js.map