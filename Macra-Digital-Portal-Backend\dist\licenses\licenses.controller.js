"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LicensesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicensesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const licenses_service_1 = require("./licenses.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_license_dto_1 = require("../dto/licenses/create-license.dto");
const update_license_dto_1 = require("../dto/licenses/update-license.dto");
const licenses_entity_1 = require("../entities/licenses.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let LicensesController = LicensesController_1 = class LicensesController {
    constructor(licensesService) {
        this.licensesService = licensesService;
        this.logger = new common_1.Logger(LicensesController_1.name);
    }
    async create(createLicenseDto, req) {
        return this.licensesService.create(createLicenseDto, req.user.userId);
    }
    async findAll(query, req) {
        const userRoles = req.user?.roles || [];
        const userId = req.user?.userId;
        return this.licensesService.findAll(query, userRoles, userId);
    }
    async getStats() {
        return this.licensesService.getLicenseStats();
    }
    async findExpiringSoon(days) {
        return this.licensesService.findExpiringSoon(days ? parseInt(days.toString()) : 30);
    }
    async findByApplication(applicationId) {
        return this.licensesService.findByApplicationId(applicationId);
    }
    async findByLicenseNumber(licenseNumber) {
        return this.licensesService.findByLicenseNumber(licenseNumber);
    }
    async generatePDF(id, res) {
        try {
            const license = await this.licensesService.findOne(id);
            const pdfBuffer = await this.licensesService.generateLicensePDF(id);
            const filename = `${license.license_number}_license.pdf`;
            res.set({
                'Content-Type': 'application/pdf',
                'Content-Disposition': `attachment; filename="${filename}"`,
                'Content-Length': pdfBuffer.length.toString(),
            });
            res.end(pdfBuffer);
        }
        catch (error) {
            this.logger.error('Error generating PDF:', error);
            if (error.message.includes('Parse error') && !error.retried) {
                this.logger.warn('Template parse error detected, clearing cache and retrying...');
                this.licensesService.clearTemplateCache();
                try {
                    const pdfBuffer = await this.licensesService.generateLicensePDF(id);
                    const license = await this.licensesService.findOne(id);
                    const filename = `${license.license_number}_license.pdf`;
                    res.set({
                        'Content-Type': 'application/pdf',
                        'Content-Disposition': `attachment; filename="${filename}"`,
                        'Content-Length': pdfBuffer.length.toString(),
                    });
                    res.end(pdfBuffer);
                    return;
                }
                catch (retryError) {
                    retryError.retried = true;
                    this.logger.error('Retry after cache clear also failed:', retryError);
                }
            }
            if (!res.headersSent) {
                res.status(500).json({
                    statusCode: 500,
                    message: 'Failed to generate PDF',
                    error: 'Internal Server Error'
                });
            }
        }
    }
    async clearTemplateCache() {
        this.licensesService.clearTemplateCache();
        return { message: 'Template cache cleared successfully' };
    }
    async findOne(id) {
        return this.licensesService.findOne(id);
    }
    async update(id, updateLicenseDto, req) {
        return this.licensesService.update(id, updateLicenseDto, req.user.userId);
    }
    async remove(id) {
        await this.licensesService.remove(id);
        return { message: 'Licence deleted successfully' };
    }
};
exports.LicensesController = LicensesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new licence' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Licence created successfully',
        type: licenses_entity_1.Licenses,
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Licence with this number already exists',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Created new licence',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_license_dto_1.CreateLicenseDto, Object]),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all licences with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Licences retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Viewed licences list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get licence statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Licence statistics retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Viewed licence statistics',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('expiring-soon'),
    (0, swagger_1.ApiOperation)({ summary: 'Get licences expiring soon' }),
    (0, swagger_1.ApiQuery)({ name: 'days', required: false, description: 'Number of days ahead to check (default: 30)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Expiring licences retrieved successfully',
        type: [licenses_entity_1.Licenses],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Viewed expiring licences',
    }),
    __param(0, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "findExpiringSoon", null);
__decorate([
    (0, common_1.Get)('by-application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get licences by application' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Licences retrieved successfully',
        type: [licenses_entity_1.Licenses],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Viewed licences by applicant',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "findByApplication", null);
__decorate([
    (0, common_1.Get)('by-number/:licenseNumber'),
    (0, swagger_1.ApiOperation)({ summary: 'Get licence by licence number' }),
    (0, swagger_1.ApiParam)({ name: 'licenseNumber', description: 'Licence number' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Licence retrieved successfully',
        type: licenses_entity_1.Licenses,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Licence not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Viewed licence by number',
    }),
    __param(0, (0, common_1.Param)('licenseNumber')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "findByLicenseNumber", null);
__decorate([
    (0, common_1.Get)(':id/pdf'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate and download licence PDF' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Licence UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'PDF generated and downloaded successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Licence not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Generated licence PDF',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "generatePDF", null);
__decorate([
    (0, common_1.Post)('clear-template-cache'),
    (0, swagger_1.ApiOperation)({ summary: 'Clear PDF template cache (development only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Template cache cleared successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'System',
        description: 'Cleared PDF template cache',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "clearTemplateCache", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get licence by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Licence UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Licence retrieved successfully',
        type: licenses_entity_1.Licenses,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Licence not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Viewed licence details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update licence' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Licence UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Licence updated successfully',
        type: licenses_entity_1.Licenses,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Licence not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Licence with this number already exists',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Updated licence',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_license_dto_1.UpdateLicenseDto, Object]),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete licence' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Licence UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Licence deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Licence not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'License',
        description: 'Deleted licence',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicensesController.prototype, "remove", null);
exports.LicensesController = LicensesController = LicensesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Licences'),
    (0, common_1.Controller)('licenses'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [licenses_service_1.LicensesService])
], LicensesController);
//# sourceMappingURL=licenses.controller.js.map