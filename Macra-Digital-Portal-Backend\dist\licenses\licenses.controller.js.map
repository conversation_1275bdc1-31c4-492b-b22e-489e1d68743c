{"version": 3, "file": "licenses.controller.js", "sourceRoot": "", "sources": ["../../src/licenses/licenses.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAcwB;AACxB,6CAOyB;AACzB,yDAAqD;AACrD,kEAA6D;AAC7D,2EAAsE;AACtE,2EAAsE;AACtE,iEAAuD;AACvD,qDAA0D;AAE1D,gFAAiE;AACjE,uEAA0E;AAOnE,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAG7B,YAA6B,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;QAF5C,WAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IAEE,CAAC;IAmB3D,AAAN,KAAK,CAAC,MAAM,CACF,gBAAkC,EAC/B,GAAQ;QAEnB,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxE,CAAC;IAcK,AAAN,KAAK,CAAC,OAAO,CACC,KAAoB,EACrB,GAAQ;QAEnB,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;QACxC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC;QAChC,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAChE,CAAC;IAcK,AAAN,KAAK,CAAC,QAAQ;QACZ,OAAO,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,CAAC;IAChD,CAAC;IAgBK,AAAN,KAAK,CAAC,gBAAgB,CAAgB,IAAa;QACjD,OAAO,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACtF,CAAC;IAgBK,AAAN,KAAK,CAAC,iBAAiB,CAAwC,aAAqB;QAClF,OAAO,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IACjE,CAAC;IAoBK,AAAN,KAAK,CAAC,mBAAmB,CAAyB,aAAqB;QACrE,OAAO,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IACjE,CAAC;IAmBK,AAAN,KAAK,CAAC,WAAW,CACa,EAAU,EAC/B,GAAa;QAEpB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACvD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;YAEpE,MAAM,QAAQ,GAAG,GAAG,OAAO,CAAC,cAAc,cAAc,CAAC;YAEzD,GAAG,CAAC,GAAG,CAAC;gBACN,cAAc,EAAE,iBAAiB;gBACjC,qBAAqB,EAAE,yBAAyB,QAAQ,GAAG;gBAC3D,gBAAgB,EAAE,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE;aAC9C,CAAC,CAAC;YAEH,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAGlD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC5D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;gBAClF,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;gBAE1C,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;oBACpE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;oBACvD,MAAM,QAAQ,GAAG,GAAG,OAAO,CAAC,cAAc,cAAc,CAAC;oBAEzD,GAAG,CAAC,GAAG,CAAC;wBACN,cAAc,EAAE,iBAAiB;wBACjC,qBAAqB,EAAE,yBAAyB,QAAQ,GAAG;wBAC3D,gBAAgB,EAAE,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE;qBAC9C,CAAC,CAAC;oBAEH,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACnB,OAAO;gBACT,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;oBAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC;gBACxE,CAAC;YACH,CAAC;YAGD,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACrB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,UAAU,EAAE,GAAG;oBACf,OAAO,EAAE,wBAAwB;oBACjC,KAAK,EAAE,uBAAuB;iBAC/B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAcK,AAAN,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;QAC1C,OAAO,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;IAC5D,CAAC;IAoBK,AAAN,KAAK,CAAC,OAAO,CAA6B,EAAU;QAClD,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IAwBK,AAAN,KAAK,CAAC,MAAM,CACkB,EAAU,EAC9B,gBAAkC,EAC/B,GAAQ;QAEnB,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5E,CAAC;IAmBK,AAAN,KAAK,CAAC,MAAM,CAA6B,EAAU;QACjD,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACtC,OAAO,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACrD,CAAC;CACF,CAAA;AA9RY,gDAAkB;AAsBvB;IAjBL,IAAA,aAAI,GAAE;IACN,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IACjD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,8BAA8B;QAC3C,IAAI,EAAE,0BAAQ;KACf,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,yCAAyC;KACvD,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,MAAM;QAC1B,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,qBAAqB;KACnC,CAAC;IAEC,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,gBAAO,GAAE,CAAA;;qCADgB,qCAAgB;;gDAI3C;AAcK;IAZL,IAAA,YAAG,GAAE;IACL,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;IAC7D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iCAAiC;KAC/C,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,IAAI;QACxB,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,sBAAsB;KACpC,CAAC;IAEC,WAAA,IAAA,0BAAQ,GAAE,CAAA;IACV,WAAA,IAAA,gBAAO,GAAE,CAAA;;;;iDAKX;AAcK;IAZL,IAAA,YAAG,EAAC,OAAO,CAAC;IACZ,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;IACnD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,2CAA2C;KACzD,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,IAAI;QACxB,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,2BAA2B;KACzC,CAAC;;;;kDAGD;AAgBK;IAdL,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC;IACvD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,6CAA6C,EAAE,CAAC;IACvG,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,0CAA0C;QACvD,IAAI,EAAE,CAAC,0BAAQ,CAAC;KACjB,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,IAAI;QACxB,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,0BAA0B;KACxC,CAAC;IACsB,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;;;;0DAEpC;AAgBK;IAdL,IAAA,YAAG,EAAC,+BAA+B,CAAC;IACpC,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACxD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACpE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iCAAiC;QAC9C,IAAI,EAAE,CAAC,0BAAQ,CAAC;KACjB,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,IAAI;QACxB,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,8BAA8B;KAC5C,CAAC;IACuB,WAAA,IAAA,cAAK,EAAC,eAAe,EAAE,sBAAa,CAAC,CAAA;;;;2DAE7D;AAoBK;IAlBL,IAAA,YAAG,EAAC,0BAA0B,CAAC;IAC/B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IAC1D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IAClE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,gCAAgC;QAC7C,IAAI,EAAE,0BAAQ;KACf,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,mBAAmB;KACjC,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,IAAI;QACxB,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,0BAA0B;KACxC,CAAC;IACyB,WAAA,IAAA,cAAK,EAAC,eAAe,CAAC,CAAA;;;;6DAEhD;AAmBK;IAjBL,IAAA,YAAG,EAAC,SAAS,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IAC9D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACrD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,2CAA2C;KACzD,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,mBAAmB;KACjC,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,IAAI;QACxB,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,uBAAuB;KACrC,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,YAAG,GAAE,CAAA;;;;qDAmDP;AAcK;IAZL,IAAA,aAAI,EAAC,sBAAsB,CAAC;IAC5B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,6CAA6C,EAAE,CAAC;IACxE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,qCAAqC;KACnD,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,MAAM;QAC1B,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,QAAQ;QACtB,WAAW,EAAE,4BAA4B;KAC1C,CAAC;;;;4DAID;AAoBK;IAlBL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;IAC9C,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACrD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,gCAAgC;QAC7C,IAAI,EAAE,0BAAQ;KACf,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,mBAAmB;KACjC,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,IAAI;QACxB,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,wBAAwB;KACtC,CAAC;IACa,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;iDAExC;AAwBK;IAtBL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;IAC3C,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACrD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,8BAA8B;QAC3C,IAAI,EAAE,0BAAQ;KACf,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,mBAAmB;KACjC,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,yCAAyC;KACvD,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,MAAM;QAC1B,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,iBAAiB;KAC/B,CAAC;IAEC,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;IAC1B,WAAA,IAAA,aAAI,GAAE,CAAA;IACN,WAAA,IAAA,gBAAO,GAAE,CAAA;;6CADgB,qCAAgB;;gDAI3C;AAmBK;IAjBL,IAAA,eAAM,EAAC,KAAK,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;IAC3C,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACrD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,8BAA8B;KAC5C,CAAC;IACD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,mBAAmB;KACjC,CAAC;IACD,IAAA,yBAAK,EAAC;QACL,MAAM,EAAE,gCAAW,CAAC,MAAM;QAC1B,MAAM,EAAE,gCAAW,CAAC,kBAAkB;QACtC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,iBAAiB;KAC/B,CAAC;IACY,WAAA,IAAA,cAAK,EAAC,IAAI,EAAE,sBAAa,CAAC,CAAA;;;;gDAGvC;6BA7RU,kBAAkB;IAJ9B,IAAA,iBAAO,EAAC,UAAU,CAAC;IACnB,IAAA,mBAAU,EAAC,UAAU,CAAC;IACtB,IAAA,kBAAS,EAAC,6BAAY,CAAC;IACvB,IAAA,uBAAa,EAAC,UAAU,CAAC;qCAIsB,kCAAe;GAHlD,kBAAkB,CA8R9B"}