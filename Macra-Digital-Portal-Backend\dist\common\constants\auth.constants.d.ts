export declare enum TwoFactorAction {
    LOGIN = "login",
    RESET = "reset",
    VERIFY = "verify",
    REGISTER = "register"
}
export declare const AuthConstants: {
    readonly STAFF_ROLES: readonly ["administrator", "administrator", "staff", "moderator", "manager"];
    readonly CUSTOMER: "customer";
    readonly ADMINISTRATOR: "administrator";
    readonly PASSWORD_HASH_ROUNDS: 12;
    readonly TWO_FACTOR: {
        readonly SECRET_LENGTH: 16;
        readonly TOKEN_EXPIRY_MINUTES: 5;
        readonly CODE_HASH_ROUNDS: 8;
        readonly ISSUER_NAME: "MACRA Digital Portal";
        readonly ONE_HOUR_MS: number;
    };
    readonly URL_PATTERNS: {
        readonly CUSTOMER_PREFIX: "customer/auth";
        readonly STAFF_PREFIX: "auth";
        readonly RESET_REDIRECT: "reset-password";
        readonly VERIFY_REDIRECT: "verify-2fa";
        readonly EMAIL_VERIFY_REDIRECT: "verify-email";
        readonly LOGIN_PATH: "/auth/login";
    };
    readonly EMAIL_ATTACHMENT: {
        readonly LOGO_FILENAME: "macra-logo.png";
        readonly LOGO_CID: "logo@macra";
    };
};
export declare const EmailTemplates: {
    readonly TWO_FACTOR: "2fa";
    readonly LOGIN_ALERT: "login-alert";
    readonly RESET: "reset";
    readonly REGISTER: "2fa";
};
export declare const EmailSubjects: {
    readonly VERIFY_OTP: "Verify OTP - MACRA Digital Portal";
    readonly PASSWORD_RESET: "Password Reset - MACRA Digital Portal";
    readonly LOGIN_DETAILS: "Login Details - MACRA Digital Portal";
    readonly TWO_FACTOR_SETUP: "Two-Factor Authentication Setup - MACRA Digital Portal";
    readonly REGISTER_EMAIL: "Verify Email - MACRA Digital Portal";
    readonly WELCOME_EMAIL: "Welcome to MACRA Digital Portal";
    readonly RECOVER_ACCOUNT: "Account Reactivation - MACRA Digital Portal";
};
export declare const TwoFactorMessages: {
    readonly reset: "You are receiving this email because we received a password reset request for your account.";
    readonly login: "You are receiving this email because we received a login request for your account.";
    readonly verify: "You are receiving this email because we received a request to enable two-factor authentication for your account.";
    readonly register: "Thank you for registering with MACRA Digital Portal. Please click the link below to verify your email address and activate your account.";
};
export declare const AuthMessages: {
    readonly INVALID_CREDENTIALS: "Invalid email or password";
    readonly ACCOUNT_INACTIVE: "Account is not active";
    readonly USER_NOT_FOUND: "User not found";
    readonly INVALID_VERIFICATION_LINK: "Invalid verification link!";
    readonly EXPIRED_VERIFICATION_LINK: "Expired verification link!";
    readonly INVALID_VERIFICATION_CODE: "Invalid verification code";
    readonly INVALID_RESET_CODE: "Invalid reset code";
    readonly PASSWORD_SAME_AS_CURRENT: "New password cannot be the same as the current password";
    readonly TWO_FACTOR_ALREADY_ENABLED: "Two-factor authentication is already enabled for {email}.\n\n Redirecting to login..";
    readonly TWO_FACTOR_CODE_SENT: "2FA code has been sent";
    readonly PASSWORD_RESET_SUCCESS: "Password reset successfully for {email}! Please login with your new password.";
    readonly TWO_FACTOR_SETUP_SUCCESS: "Two factor authentication initiation for {email} successful! Please check your email for the verification link.";
    readonly OTP_VERIFIED: "OTP verified successfully";
    readonly TWO_FACTOR_ENABLED: "Two-factor authentication enabled for {email}!";
    readonly PASSWORD_RESET_EMAIL_SENT: "If the email exists, a password reset link has been sent.";
    readonly LOGIN_NOTIFICATION_MESSAGE: "We detected a new login to your MACRA Digital Portal account. If this was you, no action is needed.";
    readonly TWO_FACTOR_ENABLED_MESSAGE: "Two-factor authentication has been successfully enabled for your account. Please login to access your account";
    readonly WELCOME_MESSAGE: "Welcome to MACRA Digital Portal! Your email has been successfully verified and your account is now active. You can now log in to access our services.";
    readonly RECOVER_ACCOUNT_MESSAGE: "Your account has been successfully reactivated. Welcome back!";
};
export declare class AuthUtils {
    static checkUserType(roles: Array<{
        name: string;
    }> | undefined, userType: string): boolean;
    static getUrlPrefix(roles: Array<{
        name: string;
    }> | undefined): string;
    static getRedirectUrl(action: TwoFactorAction): string;
    static formatMessage(template: string, replacements: Record<string, string>): string;
    static getCurrentYear(): number;
    static createExpiryDate(minutes?: number): Date;
    static requires2FA(twoFactorEnabled: boolean, lastLogin: Date | null | undefined): boolean;
}
