{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Y8JckVUlWFoRt22g0sJwhPNGOheToeZKXRmo3KpNRec=", "__NEXT_PREVIEW_MODE_ID": "f7cc31b7012c66167af67b21682f634f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "255555457a8cfb7b1388a03f25d79e54361a9caf6ebfa862faf99bf1378363a5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c699978523b10638966daf2c651ec65104e27f56acf8ba1547f33f33977fcb13"}}}, "sortedMiddleware": ["/"], "functions": {}}