{"version": 3, "file": "license-types.seeder.js", "sourceRoot": "", "sources": ["../../../src/database/seeders/license-types.seeder.ts"], "names": [], "mappings": ";;AACA,8EAAmE;AAMnE,MAAqB,kBAAkB;IAC9B,KAAK,CAAC,GAAG,CAAC,UAAsB;QACrC,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,mCAAY,CAAC,CAAC;QAG1D,MAAM,aAAa,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG;YACnB;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,yHAAyH;aACvI;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,6FAA6F;aAC3G;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,+EAA+E;aAC7F;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,yDAAyD;aACvE;YACD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,iEAAiE;aAC/E;SACF,CAAC;QAEF,KAAK,MAAM,eAAe,IAAI,YAAY,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACvD,MAAM,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;CACF;AA3CD,qCA2CC"}