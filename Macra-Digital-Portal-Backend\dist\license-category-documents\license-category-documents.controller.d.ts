import { PaginateQuery } from 'nestjs-paginate';
import { LicenseCategoryDocumentsService } from './license-category-documents.service';
import { CreateLicenseCategoryDocumentDto } from '../dto/license-category-documents/create-license-category-document.dto';
import { UpdateLicenseCategoryDocumentDto } from '../dto/license-category-documents/update-license-category-document.dto';
import { LicenseCategoryDocument } from '../entities/license-category-document.entity';
export declare class LicenseCategoryDocumentsController {
    private readonly licenseCategoryDocumentsService;
    constructor(licenseCategoryDocumentsService: LicenseCategoryDocumentsService);
    create(createLicenseCategoryDocumentDto: CreateLicenseCategoryDocumentDto, req: any): Promise<LicenseCategoryDocument>;
    findAll(query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<LicenseCategoryDocument>>;
    findOne(id: string): Promise<LicenseCategoryDocument>;
    findByLicenseCategory(licenseCategoryId: string): Promise<LicenseCategoryDocument[]>;
    update(id: string, updateLicenseCategoryDocumentDto: UpdateLicenseCategoryDocumentDto, req: any): Promise<LicenseCategoryDocument>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
