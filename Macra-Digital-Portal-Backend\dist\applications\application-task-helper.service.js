"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationTaskHelperService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const applications_entity_1 = require("../entities/applications.entity");
const tasks_service_1 = require("../tasks/tasks.service");
const notification_helper_service_1 = require("../notifications/notification-helper.service");
const tasks_entity_1 = require("../entities/tasks.entity");
let ApplicationTaskHelperService = class ApplicationTaskHelperService {
    constructor(applicationsRepository, tasksService, notificationHelper) {
        this.applicationsRepository = applicationsRepository;
        this.tasksService = tasksService;
        this.notificationHelper = notificationHelper;
    }
    async handleApplicationSubmission(applicationId, previousStatus, newStatus, updatedBy) {
        if (newStatus == 'pass_evaluation') {
            this.tasksService.closeAllTasks(applicationId, 'application', updatedBy, 'Application passed evaluation');
        }
        if (newStatus == 'approved') {
            this.tasksService.closeAllTasks(applicationId, 'application', updatedBy, 'Application approved');
        }
        if (newStatus === 'submitted') {
            const taskExists = await this.taskExistsForApplication(applicationId);
            if (taskExists) {
                return;
            }
            const application = await this.applicationsRepository.findOne({
                where: { application_id: applicationId },
                relations: ['applicant', 'license_category', 'license_category.license_type'],
            });
            if (!application) {
                return;
            }
            const applicantName = application.applicant ? application.applicant.name : 'Unknown Applicant';
            const licenseTypeName = application.license_category?.license_type?.name || 'Unknown License Type';
            const taskData = {
                title: `Review Application - ${application.application_number}`,
                description: `Application submitted by ${applicantName} for ${licenseTypeName} license. Please review and process this application.`,
                task_type: tasks_entity_1.TaskType.APPLICATION,
                priority: tasks_entity_1.TaskPriority.MEDIUM,
                status: tasks_entity_1.TaskStatus.PENDING,
                entity_type: 'application',
                entity_id: application.application_id,
            };
            await this.tasksService.create(taskData, updatedBy);
            if (application.applicant) {
                try {
                    await this.notificationHelper.notifyApplicationStatus(application.application_id, application.applicant_id, application.applicant.email, application.applicant.phone, application.application_number, 'submitted', updatedBy, application.applicant.name, licenseTypeName);
                }
                catch (notificationError) { }
            }
        }
    }
    async taskExistsForApplication(applicationId) {
        try {
            const existingTask = await this.tasksService.findTaskForApplication(applicationId);
            const exists = !!existingTask;
            return exists;
        }
        catch (error) {
            return false;
        }
    }
    async createTaskWithDuplicateCheck(applicationId, previousStatus, newStatus, updatedBy) {
        if (newStatus === 'submitted' && previousStatus !== 'submitted') {
            const taskExists = await this.taskExistsForApplication(applicationId);
            if (taskExists) {
                console.log(`ℹ️ Task already exists for application: ${applicationId}, skipping creation`);
                return;
            }
            await this.handleApplicationSubmission(applicationId, previousStatus, newStatus, updatedBy);
        }
    }
};
exports.ApplicationTaskHelperService = ApplicationTaskHelperService;
exports.ApplicationTaskHelperService = ApplicationTaskHelperService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(applications_entity_1.Applications)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        tasks_service_1.TasksService,
        notification_helper_service_1.NotificationHelperService])
], ApplicationTaskHelperService);
//# sourceMappingURL=application-task-helper.service.js.map