{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/consumerAffairsService.ts"], "sourcesContent": ["import { apiClient } from '@/lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { PaginatedResponse, PaginateQuery, BaseEntity, UserReference } from '@/types';\r\n\r\n// Types following backend entity structure\r\nexport interface ConsumerAffairsComplaint extends BaseEntity {\r\n  complaint_id: string;\r\n  complaint_number: string;\r\n  complainant_id: string;\r\n  title: string;\r\n  description: string;\r\n  category: ComplaintCategory;\r\n  status: ComplaintStatus;\r\n  priority: ComplaintPriority;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n  deleted_at?: string;\r\n\r\n  // Related data\r\n  complainant?: UserReference;\r\n  assignee?: UserReference;\r\n\r\n  attachments?: ConsumerAffairsComplaintAttachment[];\r\n  status_history?: ConsumerAffairsComplaintStatusHistory[];\r\n}\r\n\r\nexport interface ConsumerAffairsComplaintAttachment {\r\n  attachment_id: string;\r\n  complaint_id: string;\r\n  file_name: string;\r\n  file_type: string;\r\n  file_size: number;\r\n  file_path: string;\r\n  uploaded_at: string;\r\n  uploaded_by: string;\r\n}\r\n\r\nexport interface ConsumerAffairsComplaintStatusHistory {\r\n  history_id: string;\r\n  complaint_id: string;\r\n  status: ComplaintStatus;\r\n  comment?: string;\r\n  created_at: string;\r\n  created_by: string;\r\n}\r\n\r\n// Enums matching backend\r\nexport enum ComplaintCategory {\r\n  BILLING_CHARGES = 'Billing & Charges',\r\n  SERVICE_QUALITY = 'Service Quality',\r\n  NETWORK_ISSUES = 'Network Issues',\r\n  CUSTOMER_SERVICE = 'Customer Service',\r\n  CONTRACT_DISPUTES = 'Contract Disputes',\r\n  ACCESSIBILITY = 'Accessibility',\r\n  FRAUD_SCAMS = 'Fraud & Scams',\r\n  OTHER = 'Other',\r\n}\r\n\r\nexport enum ComplaintStatus {\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  INVESTIGATING = 'investigating',\r\n  RESOLVED = 'resolved',\r\n  CLOSED = 'closed',\r\n}\r\n\r\nexport enum ComplaintPriority {\r\n  LOW = 'low',\r\n  MEDIUM = 'medium',\r\n  HIGH = 'high',\r\n  URGENT = 'urgent',\r\n}\r\n\r\nexport interface CreateConsumerAffairsComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: ComplaintCategory;\r\n  priority?: ComplaintPriority;\r\n  attachments?: File[];\r\n}\r\n\r\nexport interface UpdateConsumerAffairsComplaintData {\r\n  title?: string;\r\n  description?: string;\r\n  category?: ComplaintCategory;\r\n  status?: ComplaintStatus;\r\n  priority?: ComplaintPriority;\r\n  assigned_to?: string;\r\n  resolution?: string;\r\n  internal_notes?: string;\r\n  resolved_at?: string;\r\n}\r\n\r\n// Use centralized pagination types from @/types\r\n\r\nexport type ConsumerAffairsComplaintsResponse = PaginatedResponse<ConsumerAffairsComplaint>;\r\n\r\nexport const consumerAffairsService = {\r\n\r\n  // Create new complaint\r\n  async createComplaint(data: CreateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {\r\n    try {\r\n      console.log('🔄 Creating consumer affairs complaint:', {\r\n        title: data.title,\r\n        category: data.category,\r\n        hasAttachments: data.attachments && data.attachments.length > 0\r\n      });\r\n\r\n      const formData = new FormData();\r\n      formData.append('title', data.title);\r\n      formData.append('description', data.description);\r\n      formData.append('category', data.category);\r\n\r\n      if (data.priority) {\r\n        formData.append('priority', data.priority);\r\n      }\r\n\r\n      // Add attachments if provided\r\n      if (data.attachments && data.attachments.length > 0) {\r\n        data.attachments.forEach((file) => {\r\n          formData.append('attachments', file);\r\n        });\r\n      }\r\n\r\n      const response = await apiClient.post('/consumer-affairs-complaints', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get all complaints with pagination\r\n  async getComplaints(query: PaginateQuery = {}): Promise<ConsumerAffairsComplaintsResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await apiClient.get(`/consumer-affairs-complaints?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get complaint by ID\r\n  async getComplaint(id: string): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.get(`/consumer-affairs-complaints/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get complaint by ID (alias for consistency)\r\n  async getComplaintById(id: string): Promise<ConsumerAffairsComplaint> {\r\n    return this.getComplaint(id);\r\n  },\r\n\r\n  // Update complaint\r\n  async updateComplaint(id: string, data: UpdateConsumerAffairsComplaintData): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.put(`/consumer-affairs-complaints/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete complaint\r\n  async deleteComplaint(id: string): Promise<void> {\r\n    await apiClient.delete(`/consumer-affairs-complaints/${id}`);\r\n  },\r\n\r\n  // Update complaint status (for staff)\r\n  async updateComplaintStatus(id: string, status: ComplaintStatus, comment?: string): Promise<ConsumerAffairsComplaint> {\r\n    const response = await apiClient.put(`/consumer-affairs-complaints/${id}/status`, {\r\n      status,\r\n      comment\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Add attachment to complaint\r\n  async addAttachment(id: string, file: File): Promise<ConsumerAffairsComplaintAttachment> {\r\n    const formData = new FormData();\r\n    formData.append('files', file);\r\n\r\n    const response = await apiClient.post(`/consumer-affairs-complaints/${id}/attachments`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Remove attachment from complaint\r\n  async removeAttachment(complaintId: string, attachmentId: string): Promise<void> {\r\n    await apiClient.delete(`/consumer-affairs-complaints/${complaintId}/attachments/${attachmentId}`);\r\n  },\r\n\r\n\r\n\r\n  // Helper methods\r\n  getStatusColor(status: string): string {\r\n    switch (status?.toLowerCase()) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getPriorityColor(priority: string): string {\r\n    switch (priority?.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  },\r\n\r\n  getStatusOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'submitted', label: 'Submitted' },\r\n      { value: 'under_review', label: 'Under Review' },\r\n      { value: 'investigating', label: 'Investigating' },\r\n      { value: 'resolved', label: 'Resolved' },\r\n      { value: 'closed', label: 'Closed' }\r\n    ];\r\n  },\r\n\r\n  getCategoryOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'Billing & Charges', label: 'Billing & Charges' },\r\n      { value: 'Service Quality', label: 'Service Quality' },\r\n      { value: 'Network Issues', label: 'Network Issues' },\r\n      { value: 'Customer Service', label: 'Customer Service' },\r\n      { value: 'Contract Disputes', label: 'Contract Disputes' },\r\n      { value: 'Accessibility', label: 'Accessibility' },\r\n      { value: 'Fraud & Scams', label: 'Fraud & Scams' },\r\n      { value: 'Other', label: 'Other' }\r\n    ];\r\n  },\r\n\r\n  getPriorityOptions(): Array<{ value: string; label: string }> {\r\n    return [\r\n      { value: 'low', label: 'Low' },\r\n      { value: 'medium', label: 'Medium' },\r\n      { value: 'high', label: 'High' },\r\n      { value: 'urgent', label: 'Urgent' }\r\n    ];\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAgDO,IAAA,AAAK,2CAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,yCAAA;;;;;;WAAA;;AAQL,IAAA,AAAK,2CAAA;;;;;WAAA;;AA+BL,MAAM,yBAAyB;IAEpC,uBAAuB;IACvB,MAAM,iBAAgB,IAAwC;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C;gBACrD,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG;YAChE;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK;YACnC,SAAS,MAAM,CAAC,eAAe,KAAK,WAAW;YAC/C,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAEzC,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,MAAM,CAAC,YAAY,KAAK,QAAQ;YAC3C;YAEA,8BAA8B;YAC9B,IAAI,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,GAAG;gBACnD,KAAK,WAAW,CAAC,OAAO,CAAC,CAAC;oBACxB,SAAS,MAAM,CAAC,eAAe;gBACjC;YACF;YAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC,UAAU;gBAC9E,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,MAAM,eAAc,QAAuB,CAAC,CAAC;QAC3C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,OAAO,QAAQ,IAAI;QACxF,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACzE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8CAA8C;IAC9C,MAAM,kBAAiB,EAAU;QAC/B,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAwC;QACxE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC3E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,6BAA6B,EAAE,IAAI;IAC7D;IAEA,sCAAsC;IACtC,MAAM,uBAAsB,EAAU,EAAE,MAAuB,EAAE,OAAgB;QAC/E,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,GAAG,OAAO,CAAC,EAAE;YAChF;YACA;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU,EAAE,IAAU;QACxC,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,6BAA6B,EAAE,GAAG,YAAY,CAAC,EAAE,UAAU;YAChG,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,mCAAmC;IACnC,MAAM,kBAAiB,WAAmB,EAAE,YAAoB;QAC9D,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,cAAc;IAClG;IAIA,iBAAiB;IACjB,gBAAe,MAAc;QAC3B,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,kBAAiB,QAAgB;QAC/B,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAa,OAAO;YAAY;YACzC;gBAAE,OAAO;gBAAgB,OAAO;YAAe;YAC/C;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAmB,OAAO;YAAkB;YACrD;gBAAE,OAAO;gBAAkB,OAAO;YAAiB;YACnD;gBAAE,OAAO;gBAAoB,OAAO;YAAmB;YACvD;gBAAE,OAAO;gBAAqB,OAAO;YAAoB;YACzD;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAiB,OAAO;YAAgB;YACjD;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;IAEA;QACE,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAQ,OAAO;YAAO;YAC/B;gBAAE,OAAO;gBAAU,OAAO;YAAS;SACpC;IACH;AACF", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/consumer-affairs/index.ts"], "sourcesContent": ["// Consumer Affairs Services\r\nexport * from './consumerAffairsService';\r\nexport { consumerAffairsService } from './consumerAffairsService';\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;AAC5B", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/AssignModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { taskService } from '@/services/task-assignment';\r\nimport { Task, TaskType, TaskPriority, TaskStatus, CreateTaskDto } from '@/types';\r\n\r\ninterface Officer {\r\n  user_id: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  department?: {\r\n    department_id: string;\r\n    name: string;\r\n    code: string;\r\n  } | string; // Can be either a department object or string for backward compatibility\r\n}\r\n\r\ninterface AssignModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  itemId: string | null;\r\n  itemType: string;\r\n  itemTitle?: string;\r\n  onAssignSuccess?: () => void;\r\n  // Reassignment mode props\r\n  task?: Task | null; // For reassignment mode\r\n  onReassignSuccess?: () => void;\r\n}\r\n\r\nconst AssignModal: React.FC<AssignModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  itemId,\r\n  itemType,\r\n  itemTitle,\r\n  onAssignSuccess,\r\n  task,\r\n  onReassignSuccess\r\n}) => {\r\n  const { showSuccess, showError } = useToast();\r\n  const [officers, setOfficers] = useState<Officer[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [assigning, setAssigning] = useState(false);\r\n  const [selectedOfficer, setSelectedOfficer] = useState<string>('');\r\n  const [comment, setComment] = useState('');\r\n  const [dueDate, setDueDate] = useState<string>('');\r\n  const [loadTask, setTask] = useState<Task | null>(null);\r\n  const [isReassignMode, setIsReassignMode] = useState<boolean>(false);\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      fetchOfficers();\r\n      fetchExistingTasks();\r\n      // For reassignment, pre-select the current assignee\r\n      setSelectedOfficer(isReassignMode ? (loadTask?.assigned_to || '') : '');\r\n      setComment('');\r\n      // For reassignment, pre-fill the current due date if available\r\n      setDueDate(isReassignMode && loadTask?.due_date ? loadTask.due_date.split('T')[0] : '');\r\n    }\r\n  }, [isOpen, isReassignMode, task]);\r\n\r\n\r\n  const fetchExistingTasks = async () => {\r\n    if(task) {\r\n      setTask(task);\r\n      setIsReassignMode(true);\r\n      return\r\n    }\r\n\r\n    if (itemId && itemType === 'application') {\r\n      const loadTask = await taskService.getTaskForApplication(itemId)\r\n      if(loadTask && loadTask.task_id) {\r\n        setTask(loadTask)\r\n        setIsReassignMode(true);\r\n      } else {\r\n        setIsReassignMode(false);\r\n        setTask(null);\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  const fetchOfficers = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await taskService.getOfficers();\r\n      const officersData = response.data || [];\r\n      setOfficers(officersData);\r\n\r\n      if (officersData.length === 0) {\r\n        console.warn('No officers found for task assignment');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching officers:', error);\r\n      setOfficers([]);\r\n      showError('Failed to load officers. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getTaskTypeFromItemType = (itemType: string): TaskType => {\r\n    switch (itemType) {\r\n      case 'application':\r\n        return TaskType.EVALUATION;\r\n      case 'data_breach':\r\n        return TaskType.DATA_BREACH;\r\n      case 'complaint':\r\n        return TaskType.COMPLAINT;\r\n      case 'inspection':\r\n        return TaskType.INSPECTION;\r\n      case 'document_review':\r\n        return TaskType.DOCUMENT_REVIEW;\r\n      case 'task':\r\n        return TaskType.APPLICATION; // Default for existing tasks\r\n      default:\r\n        return TaskType.APPLICATION;\r\n    }\r\n  };\r\n\r\n  const getTaskTitle = (itemType: string, itemTitle?: string): string => {\r\n    const baseTitle = itemTitle || 'Untitled';\r\n    switch (itemType) {\r\n      case 'application':\r\n        return `Application Evaluation: ${baseTitle}`;\r\n      case 'data_breach':\r\n        return `Data Breach Investigation: ${baseTitle}`;\r\n      case 'complaint':\r\n        return `Complaint Review: ${baseTitle}`;\r\n      case 'inspection':\r\n        return `Inspection Task: ${baseTitle}`;\r\n      case 'document_review':\r\n        return `Document Review: ${baseTitle}`;\r\n      default:\r\n        return `Task: ${baseTitle}`;\r\n    }\r\n  };\r\n\r\n  const getTaskDescription = (itemType: string, itemTitle?: string): string => {\r\n    const baseTitle = itemTitle || 'item';\r\n    switch (itemType) {\r\n      case 'application':\r\n        return `Evaluate and review application ${baseTitle} for compliance and approval.`;\r\n      case 'data_breach':\r\n        return `Investigate and assess data breach report ${baseTitle} for regulatory compliance.`;\r\n      case 'complaint':\r\n        return `Review and resolve complaint ${baseTitle} according to regulatory procedures.`;\r\n      case 'inspection':\r\n        return `Conduct inspection for ${baseTitle} to ensure regulatory compliance.`;\r\n      case 'document_review':\r\n        return `Review and validate document ${baseTitle} for accuracy and compliance.`;\r\n      default:\r\n        return `Process and review ${baseTitle}.`;\r\n    }\r\n  };\r\n\r\n  const handleAssign = async () => {\r\n    if (!selectedOfficer) {\r\n      showError('Please select an officer');\r\n      return;\r\n    }\r\n\r\n    if (!dueDate) {\r\n      showError('Please select a due date');\r\n      return;\r\n    }\r\n\r\n    if (!isReassignMode && !itemId) {\r\n      showError('Item ID is missing');\r\n      return;\r\n    }\r\n\r\n    setAssigning(true);\r\n\r\n  \r\n    try {\r\n      if (isReassignMode && loadTask) {\r\n        // Reassign existing task\r\n        await taskService.reassignTask(loadTask.task_id, {\r\n          assignedTo: selectedOfficer,\r\n          comment: comment.trim() || undefined,\r\n          due_date: dueDate,\r\n          priority: TaskPriority.MEDIUM\r\n        });\r\n        showSuccess('Task reassigned successfully');\r\n        onReassignSuccess?.();\r\n\r\n      } else {\r\n        const taskData: CreateTaskDto  = {\r\n          task_type: getTaskTypeFromItemType(itemType),\r\n          title: getTaskTitle(itemType, itemTitle),\r\n          description: getTaskDescription(itemType, itemTitle),\r\n          priority: TaskPriority.MEDIUM,\r\n          status: TaskStatus.PENDING,\r\n          entity_type: itemType,\r\n          entity_id: itemId!,\r\n          assigned_to: selectedOfficer,\r\n          due_date: dueDate,\r\n          metadata: {\r\n            comment: comment.trim() || undefined,\r\n            original_item_title: itemTitle,\r\n            assignment_context: 'manual_assignment'\r\n          }\r\n        };\r\n        // For other entity types, create a new task\r\n        await createNewTask(taskData);\r\n        onReassignSuccess?.();\r\n      }\r\n\r\n      onClose();\r\n    } catch (error) {\r\n      console.error(`Error ${isReassignMode ? 'reassigning' : 'creating assignment'} task:`, error);\r\n      showError(`Failed to ${isReassignMode ? 'reassign' : 'assign'} task`);\r\n    } finally {\r\n      setAssigning(false);\r\n    }\r\n  };\r\n\r\n    // Helper function to create a new task\r\n  async function createNewTask(taskData: CreateTaskDto) {\r\n    const createdTask = await taskService.createTask(taskData);\r\n    showSuccess(`Successfully assigned ${itemType.replace('_', ' ')} to officer`);\r\n    onAssignSuccess?.();\r\n  }\r\n  \r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col shadow-2xl\">\r\n        {/* Header - Fixed */}\r\n        <div className=\"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <div className=\"flex-shrink-0\">\r\n              <div className=\"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\">\r\n                <i className={isReassignMode ? \"ri-user-shared-line text-white text-lg\" : \"ri-task-line text-white text-lg\"}></i>\r\n              </div>\r\n            </div>\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n                {isReassignMode ? 'Reassign Task' : `Create Task for ${itemType.replace('_', ' ').toUpperCase()}`}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                {isReassignMode ? 'Transfer task to another officer' : 'Assign this item to an officer for processing'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <button\r\n            type=\"button\"\r\n            onClick={onClose}\r\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n          >\r\n            <i className=\"ri-close-line text-xl\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Content - Scrollable */}\r\n        <div className=\"flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900\">\r\n          {/* Task Details */}\r\n          <div className=\"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\">\r\n            <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center\">\r\n              <i className=\"ri-task-line mr-2\"></i>\r\n              {isReassignMode ? 'Task Details:' : 'Task to be Created:'}\r\n            </h4>\r\n            <p className=\"text-sm text-blue-700 dark:text-blue-300 font-medium\">\r\n              {isReassignMode ? loadTask?.title : getTaskTitle(itemType, itemTitle)}\r\n            </p>\r\n            <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1 break-words whitespace-pre-wrap overflow-wrap-anywhere hyphens-auto\">\r\n              {isReassignMode ? loadTask?.description : getTaskDescription(itemType, itemTitle)}\r\n            </p>\r\n            {isReassignMode && loadTask?.task_number && (\r\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n                Task #{loadTask?.task_number}\r\n              </p>\r\n            )}\r\n            {isReassignMode && loadTask?.assignee && (\r\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\r\n                Currently assigned to: {loadTask.assignee.first_name} {loadTask.assignee.last_name}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Officer Selection */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              {isReassignMode ? 'Reassign to Officer *' : 'Assign to Officer *'}\r\n            </label>\r\n            {loading ? (\r\n              <div className=\"text-center py-4 text-gray-500 dark:text-gray-400\">\r\n                Loading officers...\r\n              </div>\r\n            ) : (\r\n              <select\r\n                value={selectedOfficer}\r\n                onChange={(e) => setSelectedOfficer(e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n              >\r\n                <option value=\"\">Select an officer...</option>\r\n                {officers.map((officer) => (\r\n                  <option key={officer.user_id} value={officer.user_id}>\r\n                    {officer.first_name} {officer.last_name} - {officer.email}\r\n                    {officer.department ? ` (${typeof officer.department === 'string' ? officer.department : officer.department.name})` : ''}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            )}\r\n          </div>\r\n\r\n          {/* Due Date */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Due Date *\r\n            </label>\r\n            <input\r\n              type=\"date\"\r\n              value={dueDate}\r\n              onChange={(e) => setDueDate(e.target.value)}\r\n              min={new Date().toISOString().split('T')[0]} // Prevent past dates\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n            />\r\n          </div>\r\n\r\n          {/* Comment */}\r\n          <div className=\"mb-6\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              {isReassignMode ? 'Reassignment Notes (Optional)' : 'Task Notes (Optional)'}\r\n            </label>\r\n            <textarea\r\n              value={comment}\r\n              onChange={(e) => setComment(e.target.value)}\r\n              placeholder={isReassignMode ? 'Add any notes about this reassignment...' : 'Add any specific instructions or context for this loadTask...'}\r\n              rows={3}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\r\n            />\r\n          </div>\r\n\r\n          {/* Selected Officer Summary */}\r\n          {selectedOfficer && (\r\n            <div className=\"mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\r\n              <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-2\">\r\n                Selected Officer:\r\n              </h4>\r\n              <div className=\"text-sm text-green-700 dark:text-green-200\">\r\n                {(() => {\r\n                  const officer = officers.find(o => o.user_id === selectedOfficer);\r\n                  return officer ? (\r\n                    <>\r\n                      {officer.first_name} {officer.last_name}\r\n                      <br />\r\n                      {officer.email}\r\n                      {officer.department && (\r\n                        <>\r\n                          <br />\r\n                          Department: {typeof officer.department === 'string' ? officer.department : officer.department.name}\r\n                        </>\r\n                      )}\r\n                    </>\r\n                  ) : 'Officer not found';\r\n                })()}\r\n              </div>\r\n              {dueDate && (\r\n                <div className=\"text-sm text-green-700 dark:text-green-200 mt-2\">\r\n                  <strong>Due Date:</strong> {new Date(dueDate).toLocaleDateString()}\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer - Fixed */}\r\n        <div className=\"flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={onClose}\r\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n          >\r\n            Cancel\r\n          </button>\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleAssign}\r\n            disabled={!selectedOfficer || !dueDate || assigning}\r\n            className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\r\n          >\r\n            {assigning ? (\r\n              <>\r\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                {isReassignMode ? 'Reassigning...' : 'Creating Task...'}\r\n              </>\r\n            ) : (\r\n              <>\r\n                <i className={isReassignMode ? \"ri-user-shared-line mr-2\" : \"ri-task-line mr-2\"}></i>\r\n                {isReassignMode ? 'Reassign Task' : 'Create Task'}\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AssignModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AA+BA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,SAAS,EACT,eAAe,EACf,IAAI,EACJ,iBAAiB,EAClB;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,UAAU,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;YACA;YACA,oDAAoD;YACpD,mBAAmB,iBAAkB,UAAU,eAAe,KAAM;YACpE,WAAW;YACX,+DAA+D;YAC/D,WAAW,kBAAkB,UAAU,WAAW,SAAS,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QACtF;IACF,GAAG;QAAC;QAAQ;QAAgB;KAAK;IAGjC,MAAM,qBAAqB;QACzB,IAAG,MAAM;YACP,QAAQ;YACR,kBAAkB;YAClB;QACF;QAEA,IAAI,UAAU,aAAa,eAAe;YACxC,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,qBAAqB,CAAC;YACzD,IAAG,YAAY,SAAS,OAAO,EAAE;gBAC/B,QAAQ;gBACR,kBAAkB;YACpB,OAAO;gBACL,kBAAkB;gBAClB,QAAQ;YACV;QACF;IAEF;IAEA,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;YAC9C,MAAM,eAAe,SAAS,IAAI,IAAI,EAAE;YACxC,YAAY;YAEZ,IAAI,aAAa,MAAM,KAAK,GAAG;gBAC7B,QAAQ,IAAI,CAAC;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,YAAY,EAAE;YACd,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,UAAU;YAC5B,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,WAAW;YAC7B,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,SAAS;YAC3B,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,UAAU;YAC5B,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,eAAe;YACjC,KAAK;gBACH,OAAO,oHAAA,CAAA,WAAQ,CAAC,WAAW,EAAE,6BAA6B;YAC5D;gBACE,OAAO,oHAAA,CAAA,WAAQ,CAAC,WAAW;QAC/B;IACF;IAEA,MAAM,eAAe,CAAC,UAAkB;QACtC,MAAM,YAAY,aAAa;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,wBAAwB,EAAE,WAAW;YAC/C,KAAK;gBACH,OAAO,CAAC,2BAA2B,EAAE,WAAW;YAClD,KAAK;gBACH,OAAO,CAAC,kBAAkB,EAAE,WAAW;YACzC,KAAK;gBACH,OAAO,CAAC,iBAAiB,EAAE,WAAW;YACxC,KAAK;gBACH,OAAO,CAAC,iBAAiB,EAAE,WAAW;YACxC;gBACE,OAAO,CAAC,MAAM,EAAE,WAAW;QAC/B;IACF;IAEA,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,MAAM,YAAY,aAAa;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,gCAAgC,EAAE,UAAU,6BAA6B,CAAC;YACpF,KAAK;gBACH,OAAO,CAAC,0CAA0C,EAAE,UAAU,2BAA2B,CAAC;YAC5F,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,UAAU,oCAAoC,CAAC;YACxF,KAAK;gBACH,OAAO,CAAC,uBAAuB,EAAE,UAAU,iCAAiC,CAAC;YAC/E,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,UAAU,6BAA6B,CAAC;YACjF;gBACE,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,CAAC;QAC7C;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,iBAAiB;YACpB,UAAU;YACV;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,UAAU;YACV;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC,QAAQ;YAC9B,UAAU;YACV;QACF;QAEA,aAAa;QAGb,IAAI;YACF,IAAI,kBAAkB,UAAU;gBAC9B,yBAAyB;gBACzB,MAAM,qIAAA,CAAA,cAAW,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;oBAC/C,YAAY;oBACZ,SAAS,QAAQ,IAAI,MAAM;oBAC3B,UAAU;oBACV,UAAU,oHAAA,CAAA,eAAY,CAAC,MAAM;gBAC/B;gBACA,YAAY;gBACZ;YAEF,OAAO;gBACL,MAAM,WAA2B;oBAC/B,WAAW,wBAAwB;oBACnC,OAAO,aAAa,UAAU;oBAC9B,aAAa,mBAAmB,UAAU;oBAC1C,UAAU,oHAAA,CAAA,eAAY,CAAC,MAAM;oBAC7B,QAAQ,oHAAA,CAAA,aAAU,CAAC,OAAO;oBAC1B,aAAa;oBACb,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,UAAU;wBACR,SAAS,QAAQ,IAAI,MAAM;wBAC3B,qBAAqB;wBACrB,oBAAoB;oBACtB;gBACF;gBACA,4CAA4C;gBAC5C,MAAM,cAAc;gBACpB;YACF;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE,iBAAiB,gBAAgB,sBAAsB,MAAM,CAAC,EAAE;YACvF,UAAU,CAAC,UAAU,EAAE,iBAAiB,aAAa,SAAS,KAAK,CAAC;QACtE,SAAU;YACR,aAAa;QACf;IACF;IAEE,uCAAuC;IACzC,eAAe,cAAc,QAAuB;QAClD,MAAM,cAAc,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QACjD,YAAY,CAAC,sBAAsB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;QAC5E;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAW,iBAAiB,2CAA2C;;;;;;;;;;;;;;;;8CAG9E,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,iBAAiB,kBAAkB,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,IAAI;;;;;;sDAEnG,8OAAC;4CAAE,WAAU;sDACV,iBAAiB,qCAAqC;;;;;;;;;;;;;;;;;;sCAI7D,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAE,WAAU;;;;;;wCACZ,iBAAiB,kBAAkB;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CACV,iBAAiB,UAAU,QAAQ,aAAa,UAAU;;;;;;8CAE7D,8OAAC;oCAAE,WAAU;8CACV,iBAAiB,UAAU,cAAc,mBAAmB,UAAU;;;;;;gCAExE,kBAAkB,UAAU,6BAC3B,8OAAC;oCAAE,WAAU;;wCAAgD;wCACpD,UAAU;;;;;;;gCAGpB,kBAAkB,UAAU,0BAC3B,8OAAC;oCAAE,WAAU;;wCAAgD;wCACnC,SAAS,QAAQ,CAAC,UAAU;wCAAC;wCAAE,SAAS,QAAQ,CAAC,SAAS;;;;;;;;;;;;;sCAMxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CACd,iBAAiB,0BAA0B;;;;;;gCAE7C,wBACC,8OAAC;oCAAI,WAAU;8CAAoD;;;;;6FAInE,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCAClD,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAA6B,OAAO,QAAQ,OAAO;;oDACjD,QAAQ,UAAU;oDAAC;oDAAE,QAAQ,SAAS;oDAAC;oDAAI,QAAQ,KAAK;oDACxD,QAAQ,UAAU,GAAG,CAAC,EAAE,EAAE,OAAO,QAAQ,UAAU,KAAK,WAAW,QAAQ,UAAU,GAAG,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG;;+CAF3G,QAAQ,OAAO;;;;;;;;;;;;;;;;;sCAUpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC3C,WAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CACd,iBAAiB,kCAAkC;;;;;;8CAEtD,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,aAAa,iBAAiB,6CAA6C;oCAC3E,MAAM;oCACN,WAAU;;;;;;;;;;;;wBAKb,iCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAI,WAAU;8CACZ,CAAC;wCACA,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;wCACjD,OAAO,wBACL;;gDACG,QAAQ,UAAU;gDAAC;gDAAE,QAAQ,SAAS;8DACvC,8OAAC;;;;;gDACA,QAAQ,KAAK;gDACb,QAAQ,UAAU,kBACjB;;sEACE,8OAAC;;;;;wDAAK;wDACO,OAAO,QAAQ,UAAU,KAAK,WAAW,QAAQ,UAAU,GAAG,QAAQ,UAAU,CAAC,IAAI;;;;2DAItG;oCACN,CAAC;;;;;;gCAEF,yBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAO;;;;;;wCAAkB;wCAAE,IAAI,KAAK,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;8BAQ1E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,MAAK;4BACL,SAAS;4BACT,UAAU,CAAC,mBAAmB,CAAC,WAAW;4BAC1C,WAAU;sCAET,0BACC;;kDACE,8OAAC;wCAAI,WAAU;;;;;;oCACd,iBAAiB,mBAAmB;;6DAGvC;;kDACE,8OAAC;wCAAE,WAAW,iBAAiB,6BAA6B;;;;;;oCAC3D,iBAAiB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;uCAEe", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/AssignButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport AssignModal from './AssignModal';\r\n\r\ninterface AssignButtonProps {\r\n  itemId: string;\r\n  itemType: string;\r\n  itemTitle?: string;\r\n  isAssigned?: boolean;\r\n  onAssignSuccess?: () => void;\r\n  className?: string;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  variant?: 'primary' | 'secondary' | 'success';\r\n  disabled?: boolean;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst AssignButton: React.FC<AssignButtonProps> = ({\r\n  itemId,\r\n  itemType,\r\n  isAssigned = false,\r\n  itemTitle,\r\n  onAssignSuccess,\r\n  className = '',\r\n  size = 'sm',\r\n  variant = 'success',\r\n  disabled = false,\r\n  children\r\n}) => {\r\n  const [showAssignModal, setShowAssignModal] = useState(false);\r\n\r\n  const handleAssignClick = () => {\r\n    setShowAssignModal(true);\r\n  };\r\n\r\n  const handleCloseModal = () => {\r\n    setShowAssignModal(false);\r\n  };\r\n\r\n  const handleAssignSuccess = () => {\r\n    console.log(\"===0\")\r\n\r\n    setShowAssignModal(false);\r\n    onAssignSuccess?.();\r\n    console.log(\"===1\")\r\n\r\n  };\r\n\r\n  // Size classes\r\n  const sizeClasses = {\r\n    sm: 'px-3 py-1 text-xs',\r\n    md: 'px-4 py-2 text-sm',\r\n    lg: 'px-6 py-3 text-base'\r\n  };\r\n\r\n  // Variant classes\r\n  const variantClasses = {\r\n    primary: 'text-white bg-primary hover:bg-primary-dark focus:ring-primary',\r\n    secondary: 'text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500',\r\n    success: 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500'\r\n  };\r\n\r\n  // Base classes\r\n  const baseClasses = 'inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed';\r\n\r\n  // Combine all classes\r\n  const buttonClasses = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;\r\n\r\n  return (\r\n    <>\r\n      <button\r\n        type=\"button\"\r\n        onClick={handleAssignClick}\r\n        disabled={disabled}\r\n        className={buttonClasses}\r\n        title={`Create task for ${itemType.replace('_', ' ')} assignment`}\r\n      >\r\n        <i className=\"ri-task-line mr-1\"></i>\r\n        {children || (isAssigned ? 'Reassign' : 'Assign')}\r\n      </button>\r\n\r\n      <AssignModal\r\n        isOpen={showAssignModal}\r\n        onClose={handleCloseModal}\r\n        itemId={itemId}\r\n        itemType={itemType}\r\n        itemTitle={itemTitle}\r\n        onAssignSuccess={handleAssignSuccess}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AssignButton;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAkBA,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,QAAQ,EACR,aAAa,KAAK,EAClB,SAAS,EACT,eAAe,EACf,YAAY,EAAE,EACd,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,QAAQ,EACT;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,oBAAoB;QACxB,mBAAmB;IACrB;IAEA,MAAM,mBAAmB;QACvB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB;QAC1B,QAAQ,GAAG,CAAC;QAEZ,mBAAmB;QACnB;QACA,QAAQ,GAAG,CAAC;IAEd;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,eAAe;IACf,MAAM,cAAc;IAEpB,sBAAsB;IACtB,MAAM,gBAAgB,GAAG,YAAY,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;IAEnG,qBACE;;0BACE,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,OAAO,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,WAAW,CAAC;;kCAEjE,8OAAC;wBAAE,WAAU;;;;;;oBACZ,YAAY,CAAC,aAAa,aAAa,QAAQ;;;;;;;0BAGlD,8OAAC,2IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,iBAAiB;;;;;;;;AAIzB;uCAEe", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/consumer-affairs/ConsumerAffairsViewModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { consumerAffairsService, ConsumerAffairsComplaint } from '@/services/consumer-affairs';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport Loader from '@/components/Loader';\r\n\r\ninterface ConsumerAffairsViewModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  complaintId: string | null;\r\n  onUpdate?: () => void;\r\n}\r\n\r\nconst ConsumerAffairsViewModal: React.FC<ConsumerAffairsViewModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  complaintId,\r\n  onUpdate\r\n}) => {\r\n  const { showSuccess, showError } = useToast();\r\n  const [complaint, setComplaint] = useState<ConsumerAffairsComplaint | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (isOpen && complaintId) {\r\n      fetchComplaintDetails();\r\n    }\r\n  }, [isOpen, complaintId]);\r\n\r\n  const fetchComplaintDetails = async () => {\r\n    if (!complaintId) return;\r\n    \r\n    setLoading(true);\r\n    setError(null);\r\n    \r\n    try {\r\n      const response = await consumerAffairsService.getComplaintById(complaintId);\r\n      setComplaint(response);\r\n    } catch (err: unknown) {\r\n      console.error('Error fetching complaint details:', err);\r\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\r\n      setError(`Failed to load complaint details: ${errorMessage}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status?.toLowerCase()) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority?.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\r\n      <div className=\"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800\">\r\n        <div className=\"mt-3\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n              Consumer Affairs Complaint Details\r\n            </h3>\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Content */}\r\n          {loading ? (\r\n            <div className=\"py-8\">\r\n              <Loader message=\"Loading complaint details...\" />\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4\">\r\n              <div className=\"flex\">\r\n                <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\r\n                <p className=\"text-red-700 dark:text-red-200\">{error}</p>\r\n              </div>\r\n            </div>\r\n          ) : complaint ? (\r\n            <div className=\"space-y-6\">\r\n              {/* Basic Information */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Complaint Number\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {complaint.complaint_number}\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Status\r\n                  </h4>\r\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(complaint.status)}`}>\r\n                    {complaint.status?.replace('_', ' ').toUpperCase()}\r\n                  </span>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Category\r\n                  </h4>\r\n                  <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\r\n                    {complaint.category}\r\n                  </span>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Priority\r\n                  </h4>\r\n                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(complaint.priority)}`}>\r\n                    {complaint.priority?.toUpperCase() || 'MEDIUM'}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Title and Description */}\r\n              <div>\r\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                  Title\r\n                </h4>\r\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                  {complaint.title}\r\n                </p>\r\n              </div>\r\n\r\n              <div>\r\n                <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                  Description\r\n                </h4>\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap\">\r\n                    {complaint.description}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Complainant Information */}\r\n              {complaint.complainant && (\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Complainant\r\n                  </h4>\r\n                  <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                      <strong>Name:</strong> {complaint.complainant.first_name} {complaint.complainant.last_name}\r\n                    </p>\r\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                      <strong>Email:</strong> {complaint.complainant.email}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Assignment Information */}\r\n              {complaint.assignee && (\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Assigned To\r\n                  </h4>\r\n                  <div className=\"bg-green-50 dark:bg-green-900 rounded-lg p-4\">\r\n                    <p className=\"text-sm text-green-700 dark:text-green-200\">\r\n                      <strong>Officer:</strong> {complaint.assignee.first_name} {complaint.assignee.last_name}\r\n                    </p>\r\n                    <p className=\"text-sm text-green-700 dark:text-green-200\">\r\n                      <strong>Email:</strong> {complaint.assignee.email}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Resolution */}\r\n              {complaint.resolution && (\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Resolution\r\n                  </h4>\r\n                  <div className=\"bg-green-50 dark:bg-green-900 rounded-lg p-4\">\r\n                    <p className=\"text-sm text-green-700 dark:text-green-200 whitespace-pre-wrap\">\r\n                      {complaint.resolution}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Internal Notes */}\r\n              {complaint.internal_notes && (\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Internal Notes\r\n                  </h4>\r\n                  <div className=\"bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4\">\r\n                    <p className=\"text-sm text-yellow-700 dark:text-yellow-200 whitespace-pre-wrap\">\r\n                      {complaint.internal_notes}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Timestamps */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Submitted\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {new Date(complaint.created_at).toLocaleString()}\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                    Last Updated\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                    {new Date(complaint.updated_at).toLocaleString()}\r\n                  </p>\r\n                </div>\r\n                {complaint.resolved_at && (\r\n                  <div>\r\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                      Resolved\r\n                    </h4>\r\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                      {new Date(complaint.resolved_at).toLocaleString()}\r\n                    </p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center py-8\">\r\n              <p className=\"text-gray-500 dark:text-gray-400\">No complaint data available</p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Actions */}\r\n          <div className=\"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n            >\r\n              Close\r\n            </button>\r\n            {complaint && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  // For now, just show a message. In the future, this could navigate to an evaluation page\r\n                  showSuccess('Complaint evaluation feature coming soon');\r\n                }}\r\n                className=\"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n              >\r\n                <i className=\"ri-clipboard-line mr-2\"></i>\r\n                Evaluate\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ConsumerAffairsViewModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAcA,MAAM,2BAAoE,CAAC,EACzE,MAAM,EACN,OAAO,EACP,WAAW,EACX,QAAQ,EACT;IACC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,aAAa;YACzB;QACF;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,wBAAwB;QAC5B,IAAI,CAAC,aAAa;QAElB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,gKAAA,CAAA,yBAAsB,CAAC,gBAAgB,CAAC;YAC/D,aAAa;QACf,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS,CAAC,kCAAkC,EAAE,cAAc;QAC9D,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuD;;;;;;0CAGrE,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;oBAKhB,wBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;mEAEhB,sBACF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;;;;;8CACb,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;;;;;mEAGjD,0BACF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,UAAU,gBAAgB;;;;;;;;;;;;kDAG/B,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,UAAU,MAAM,GAAG;0DAC5G,UAAU,MAAM,EAAE,QAAQ,KAAK,KAAK;;;;;;;;;;;;kDAGzC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAK,WAAU;0DACb,UAAU,QAAQ;;;;;;;;;;;;kDAGvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,UAAU,QAAQ,GAAG;0DAChH,UAAU,QAAQ,EAAE,iBAAiB;;;;;;;;;;;;;;;;;;0CAM5C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAE,WAAU;kDACV,UAAU,KAAK;;;;;;;;;;;;0CAIpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDACV,UAAU,WAAW;;;;;;;;;;;;;;;;;4BAM3B,UAAU,WAAW,kBACpB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAc;oDAAE,UAAU,WAAW,CAAC,UAAU;oDAAC;oDAAE,UAAU,WAAW,CAAC,SAAS;;;;;;;0DAE5F,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAe;oDAAE,UAAU,WAAW,CAAC,KAAK;;;;;;;;;;;;;;;;;;;4BAO3D,UAAU,QAAQ,kBACjB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAiB;oDAAE,UAAU,QAAQ,CAAC,UAAU;oDAAC;oDAAE,UAAU,QAAQ,CAAC,SAAS;;;;;;;0DAEzF,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;kEAAO;;;;;;oDAAe;oDAAE,UAAU,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;;;4BAOxD,UAAU,UAAU,kBACnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDACV,UAAU,UAAU;;;;;;;;;;;;;;;;;4BAO5B,UAAU,cAAc,kBACvB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDACV,UAAU,cAAc;;;;;;;;;;;;;;;;;0CAOjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,UAAU,UAAU,EAAE,cAAc;;;;;;;;;;;;kDAGlD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,UAAU,UAAU,EAAE,cAAc;;;;;;;;;;;;oCAGjD,UAAU,WAAW,kBACpB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,UAAU,WAAW,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;iFAOzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;kCAKpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;4BAGA,2BACC,8OAAC;gCACC,MAAK;gCACL,SAAS;oCACP,yFAAyF;oCACzF,YAAY;gCACd;gCACA,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D;uCAEe", "debugId": null}}, {"offset": {"line": 1623, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/consumer-affairs/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { consumerAffairsService, ConsumerAffairsComplaint } from '@/services/consumer-affairs';\r\nimport Loader from '@/components/Loader';\r\nimport AssignButton from '@/components/common/AssignButton';\r\nimport ConsumerAffairsViewModal from '@/components/consumer-affairs/ConsumerAffairsViewModal';\r\n\r\nconst ConsumerAffairsPage: React.FC = () => {\r\n  const { isAuthenticated } = useAuth();\r\n  const [complaints, setComplaints] = useState<ConsumerAffairsComplaint[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string>('');\r\n  const [selectedComplaintId, setSelectedComplaintId] = useState<string | null>(null);\r\n  const [showViewModal, setShowViewModal] = useState(false);\r\n  const [filter, setFilter] = useState({\r\n    status: '',\r\n    category: '',\r\n    priority: '',\r\n    search: ''\r\n  });\r\n\r\n  const fetchComplaints = useCallback(async () => {\r\n    if (!isAuthenticated) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      console.log('🔄 Fetching all consumer affairs complaints for staff...');\r\n\r\n      // For staff, fetch all complaints (not filtered by user)\r\n      const response = await consumerAffairsService.getComplaints({\r\n        limit: 100,\r\n        ...filter\r\n      });\r\n\r\n      console.log('✅ Consumer Affairs complaints fetched:', response);\r\n\r\n      if (Array.isArray(response.data)) {\r\n        setComplaints(response.data);\r\n      } else {\r\n        setComplaints([]);\r\n      }\r\n    } catch (err: unknown) {\r\n      console.error('❌ Error fetching complaints:', err);\r\n      if (err instanceof Error) {\r\n        setError(`Failed to load complaints: ${err.message}`);\r\n      } else {\r\n        setError('Failed to load complaints: Unknown error');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [isAuthenticated, filter]);\r\n\r\n  // Fetch complaints\r\n  useEffect(() => {\r\n    fetchComplaints();\r\n  }, [fetchComplaints]);\r\n\r\n  const handleViewComplaint = (complaintId: string) => {\r\n    setSelectedComplaintId(complaintId);\r\n    setShowViewModal(true);\r\n  };\r\n\r\n  const handleCloseViewModal = () => {\r\n    setShowViewModal(false);\r\n    setSelectedComplaintId(null);\r\n  };\r\n\r\n  const handleAssignSuccess = () => {\r\n    // Refresh the complaints list when assignment is successful\r\n    fetchComplaints();\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status?.toLowerCase()) {\r\n      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\r\n      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority?.toLowerCase()) {\r\n      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\r\n      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';\r\n      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\r\n      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';\r\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"p-6 min-h-full bg-gray-50 dark:bg-gray-900\">\r\n        <Loader message=\"Loading consumer affairs complaints...\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-6 min-h-full bg-gray-50 dark:bg-gray-900\">\r\n      {/* Header */}\r\n      <div className=\"mb-6\">\r\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">\r\n          Consumer Affairs Management\r\n        </h1>\r\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\r\n          Manage and respond to customer complaints and service issues\r\n        </p>\r\n      </div>\r\n\r\n      {/* Statistics Cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-file-list-line text-2xl text-blue-600\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Complaints</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{complaints.length}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-time-line text-2xl text-yellow-600\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Submitted</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                {complaints.filter(c => c.status === 'submitted').length}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-progress-line text-2xl text-blue-600\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Under Review</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                {complaints.filter(c => c.status === 'under_review').length}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-check-line text-2xl text-green-600\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Resolved</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                {complaints.filter(c => c.status === 'resolved').length}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Search\r\n            </label>\r\n            <input\r\n              type=\"text\"\r\n              placeholder=\"Search complaints...\"\r\n              value={filter.search}\r\n              onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\r\n            />\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Status\r\n            </label>\r\n            <select\r\n              value={filter.status}\r\n              onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value }))}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\r\n              aria-label=\"Filter by status\"\r\n              title=\"Filter complaints by status\"\r\n            >\r\n              <option value=\"\">All Statuses</option>\r\n              <option value=\"submitted\">Submitted</option>\r\n              <option value=\"under_review\">Under Review</option>\r\n              <option value=\"investigating\">Investigating</option>\r\n              <option value=\"resolved\">Resolved</option>\r\n              <option value=\"closed\">Closed</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Category\r\n            </label>\r\n            <select\r\n              value={filter.category}\r\n              onChange={(e) => setFilter(prev => ({ ...prev, category: e.target.value }))}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\r\n              aria-label=\"Filter by category\"\r\n              title=\"Filter complaints by category\"\r\n            >\r\n              <option value=\"\">All Categories</option>\r\n              <option value=\"Billing & Charges\">Billing & Charges</option>\r\n              <option value=\"Service Quality\">Service Quality</option>\r\n              <option value=\"Network Issues\">Network Issues</option>\r\n              <option value=\"Customer Service\">Customer Service</option>\r\n              <option value=\"Other\">Other</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div>\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Priority\r\n            </label>\r\n            <select\r\n              value={filter.priority}\r\n              onChange={(e) => setFilter(prev => ({ ...prev, priority: e.target.value }))}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100\"\r\n              aria-label=\"Filter by priority\"\r\n              title=\"Filter complaints by priority\"\r\n            >\r\n              <option value=\"\">All Priorities</option>\r\n              <option value=\"low\">Low</option>\r\n              <option value=\"medium\">Medium</option>\r\n              <option value=\"high\">High</option>\r\n              <option value=\"urgent\">Urgent</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Error Message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6\">\r\n          <div className=\"flex\">\r\n            <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\r\n            <p className=\"text-red-700 dark:text-red-200\">{error}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Complaints Table */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\r\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n            Consumer Affairs Complaints ({complaints.length})\r\n          </h3>\r\n        </div>\r\n\r\n        {complaints.length === 0 ? (\r\n          <div className=\"text-center py-12\">\r\n            <i className=\"ri-file-search-line text-4xl text-gray-400 mb-4\"></i>\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">No complaints found</h3>\r\n            <p className=\"text-gray-500 dark:text-gray-400\">\r\n              No consumer affairs complaints have been submitted yet.\r\n            </p>\r\n          </div>\r\n        ) : (\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n              <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n                <tr>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Complaint\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Category\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Status\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Priority\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Submitted\r\n                  </th>\r\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Actions\r\n                  </th>\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                {complaints.map((complaint) => (\r\n                  <tr key={complaint.complaint_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      <div>\r\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                          {complaint.title}\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\">\r\n                          {complaint.description}\r\n                        </div>\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\r\n                        {complaint.category}\r\n                      </span>\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(complaint.status)}`}>\r\n                        {complaint.status?.replace('_', ' ').toUpperCase()}\r\n                      </span>\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(complaint.priority)}`}>\r\n                        {complaint.priority?.toUpperCase() || 'MEDIUM'}\r\n                      </span>\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                      {new Date(complaint.created_at).toLocaleDateString()}\r\n                    </td>\r\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\r\n                      <div className=\"flex items-center space-x-2\">\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => handleViewComplaint(complaint.complaint_id)}\r\n                          className=\"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\r\n                          title=\"View complaint details\"\r\n                        >\r\n                          <i className=\"ri-eye-line mr-1\"></i>\r\n                          View\r\n                        </button>\r\n                        <AssignButton\r\n                          itemId={complaint.complaint_id}\r\n                          itemType=\"complaint\"\r\n                          itemTitle={complaint.title}\r\n                          onAssignSuccess={handleAssignSuccess}\r\n                        />\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* View Modal */}\r\n      <ConsumerAffairsViewModal\r\n        isOpen={showViewModal}\r\n        onClose={handleCloseViewModal}\r\n        complaintId={selectedComplaintId}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ConsumerAffairsPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,sBAAgC;IACpC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,QAAQ;QACR,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,WAAW;YACX,QAAQ,GAAG,CAAC;YAEZ,yDAAyD;YACzD,MAAM,WAAW,MAAM,gKAAA,CAAA,yBAAsB,CAAC,aAAa,CAAC;gBAC1D,OAAO;gBACP,GAAG,MAAM;YACX;YAEA,QAAQ,GAAG,CAAC,0CAA0C;YAEtD,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAChC,cAAc,SAAS,IAAI;YAC7B,OAAO;gBACL,cAAc,EAAE;YAClB;QACF,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,eAAe,OAAO;gBACxB,SAAS,CAAC,2BAA2B,EAAE,IAAI,OAAO,EAAE;YACtD,OAAO;gBACL,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,sBAAsB,CAAC;QAC3B,uBAAuB;QACvB,iBAAiB;IACnB;IAEA,MAAM,uBAAuB;QAC3B,iBAAiB;QACjB,uBAAuB;IACzB;IAEA,MAAM,sBAAsB;QAC1B,4DAA4D;QAC5D;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ;YACd,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,UAAU;YAChB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,4HAAA,CAAA,UAAM;gBAAC,SAAQ;;;;;;;;;;;IAGtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;;;;;;;0BAMvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAA2D,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAK/F,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,gBAAgB,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMnE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDACV,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO,OAAO,MAAM;oCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAU;;;;;;;;;;;;sCAId,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,OAAO,OAAO,MAAM;oCACpB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAe;;;;;;sDAC7B,8OAAC;4CAAO,OAAM;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAI3B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,OAAO,OAAO,QAAQ;oCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACzE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAoB;;;;;;sDAClC,8OAAC;4CAAO,OAAM;sDAAkB;;;;;;sDAChC,8OAAC;4CAAO,OAAM;sDAAiB;;;;;;sDAC/B,8OAAC;4CAAO,OAAM;sDAAmB;;;;;;sDACjC,8OAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;sCAI1B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,8OAAC;oCACC,OAAO,OAAO,QAAQ;oCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACzE,WAAU;oCACV,cAAW;oCACX,OAAM;;sDAEN,8OAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,8OAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,8OAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,8OAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO9B,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;;;;;;0BAMrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;gCAAuD;gCACrC,WAAW,MAAM;gCAAC;;;;;;;;;;;;oBAInD,WAAW,MAAM,KAAK,kBACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;iFAKlD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCAAM,WAAU;8CACf,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,8OAAC;gDAAG,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAKtH,8OAAC;oCAAM,WAAU;8CACd,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC;4CAAgC,WAAU;;8DACzC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EACZ,UAAU,KAAK;;;;;;0EAElB,8OAAC;gEAAI,WAAU;0EACZ,UAAU,WAAW;;;;;;;;;;;;;;;;;8DAI5B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAU;kEACb,UAAU,QAAQ;;;;;;;;;;;8DAGvB,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,UAAU,MAAM,GAAG;kEAC5G,UAAU,MAAM,EAAE,QAAQ,KAAK,KAAK;;;;;;;;;;;8DAGzC,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAK,WAAW,CAAC,yDAAyD,EAAE,iBAAiB,UAAU,QAAQ,GAAG;kEAChH,UAAU,QAAQ,EAAE,iBAAiB;;;;;;;;;;;8DAG1C,8OAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,UAAU,UAAU,EAAE,kBAAkB;;;;;;8DAEpD,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,oBAAoB,UAAU,YAAY;gEACzD,WAAU;gEACV,OAAM;;kFAEN,8OAAC;wEAAE,WAAU;;;;;;oEAAuB;;;;;;;0EAGtC,8OAAC,4IAAA,CAAA,UAAY;gEACX,QAAQ,UAAU,YAAY;gEAC9B,UAAS;gEACT,WAAW,UAAU,KAAK;gEAC1B,iBAAiB;;;;;;;;;;;;;;;;;;2CA5ChB,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAyD3C,8OAAC,qKAAA,CAAA,UAAwB;gBACvB,QAAQ;gBACR,SAAS;gBACT,aAAa;;;;;;;;;;;;AAIrB;uCAEe", "debugId": null}}]}