'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { authService } from '@/services/auth.service';
import { XCircleIcon } from '@heroicons/react/24/solid';
import { useAuth } from '@/contexts/AuthContext';
import PageTransition from '@/components/auth/PageTransition';
import Cookies from 'js-cookie';
import { User } from '@/types';

export default function CustomerSetupTwoFactorPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [secret, setSecret] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loadingMessage, setLoadingMessage] = useState('Initializing 2FA setup...');
  const [alreadyEnabled, setAlreadyEnabled] = useState(false);
  const [setUpComplete, setSetUpComplete] = useState(false);
  const [loading, setLoading] = useState(true);
  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);
  const [dynamicMessages, setDynamicMessages] = useState<string[]>(['Initializing 2FA setup...']);
  const [isClient, setIsClient] = useState(false);
  const [userId, setUserId] = useState(JSON.parse(Cookies.get('auth_user') || '').user_id || '');

  const redirectGeneral = () => router.replace('/customer'); // Middleware takes care of routing
  const handleRedirectLogin = () => {
    setLoading(true);
    sessionStorage.removeItem('2fa_setup_user');
    sessionStorage.removeItem('2fa_verify_url');
    sessionStorage.removeItem('2fa_setup_in_progress');
    Cookies.remove('2fa_setup_user');
    Cookies.remove('2fa_verify_url');
    Cookies.remove('2fa_setup_in_progress');
    setDynamicMessages(['Removing saved session data...', 'Cancelling 2FA setup...', 'Almost there...']);
    setLoadingMessage('Redirecting to login...')
    redirectGeneral(); //middleware will redirect to /customer/auth/login
  };
  const initiate2FA = async (currentUser: User) => {
    sessionStorage.setItem('2fa_setup_in_progress', 'true');
    Cookies.set('2fa_setup_in_progress', 'true', { expires: 0.01 });

    try {
      setDynamicMessages(['Account needs secure authentication', 'Initializing 2FA setup...', 'Almost there...']);
      const {qr_code_data_url, secret, message} = await authService.setupTwoFactorAuth({
        user_id: currentUser.user_id,
      });

      setQrCodeUrl(qr_code_data_url);
      setSecret(secret);
      setSuccess(message || '2FA setup successful');
      setSetUpComplete(true);

      const verifyUrl =  `/customer/auth/verify-2fa` +
        `?i=${currentUser.user_id}` +
        `&unique=${secret}` +
        `&c=`;
      sessionStorage.setItem('2fa_verify_url', verifyUrl);
      Cookies.set('2fa_verify_url', verifyUrl, { expires: 0.01 });
      setLoading(false);
      setSuccess('An OTP has been sent to your email. Please confirm below to continue');
      return;
    } catch (err: any) {
      const msg: string =
        err?.response?.data?.message ||
        err?.message ||
        'Failed to initiate 2FA setup. Redirecting...';

      const isEnabled = msg.toLowerCase().includes('enabled');
      const isInitiated = msg.toLowerCase().includes('initiation');

      setAlreadyEnabled(isEnabled);
      setSetUpComplete(isInitiated);

      if (isEnabled) {
        setSuccess(msg);
      } else {
        setError(msg);
      }
    } finally {
      setLoading(false);
      sessionStorage.removeItem('2fa_setup_in_progress');
      Cookies.remove('2fa_setup_in_progress');
      sessionStorage.removeItem('2fa_verify_url');
      Cookies.remove('2fa_verify_url');
    }
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (authLoading || !isClient) return;

    const setupUser: string | null = Cookies.get('2fa_setup_user') || sessionStorage.getItem('2fa_setup_user') || Cookies.get('auth_user') || sessionStorage.getItem('auth_user') || null;

    let currentUser: User | null = (setupUser) ? JSON.parse(setupUser) : null;

    if (!currentUser) {
      setUnauthorizedAccess(true);
      setLoading(false);
      setError('Your session has expired or is invalid. Please login again to continue.');
      return;
    }
    if (isClient && !setUpComplete) {
      console.log('Is Client. Now initiating 2FA setup...');
      initiate2FA(currentUser);
    } else {return;}

  }, [authLoading, user, isClient, setUpComplete]);

  if (!isClient || loading) {

    return (
      <PageTransition
        isLoading={true}
        loadingMessage={loadingMessage || (!isClient ? 'Loading...' : 'Processing your request')}
        loadingSubmessage="Please wait.."
        dynamicMessages={dynamicMessages}
        showProgress={loading}
      >
        <div />
      </PageTransition>
    );
  }

  const getPageTitle = () => {
    if (setUpComplete && !alreadyEnabled) {
      return '✅ 2FA Setup Complete!';
    }
    if (alreadyEnabled) {
      return '✅ 2FA Already Enabled';
    }
    return '🔐 Secure Your Account';
  };

  const getPageSubtitle = () => {
    if (setUpComplete && !alreadyEnabled) {
      return 'Your account is now protected with two-factor authentication';
    }
    if (alreadyEnabled) {
      return 'Your account is already secured with two-factor authentication';
    }
    return 'Enable two-factor authentication to protect your account with an extra layer of security';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-blue-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>

      <div className="relative flex flex-col justify-center py-12 sm:px-6 lg:px-8 min-h-screen">
        {/* Header Section */}
        <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
          <div className="flex justify-center">
            <div className="relative">
              <Image
                src="/images/macra-logo.png"
                alt="MACRA Logo"
                width={50}
                height={50}
                className="mx-auto h-16 w-auto drop-shadow-lg animate-fadeLoop"
              />
              <div className="absolute -inset-2 rounded-full opacity-20 blur-lg animate-pulse"></div>
            </div>
          </div>

          <div className="mt-6">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mb-3 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 animate-slideInFromTop animate-delay-100">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              Two Factor Authentication
            </div>

            <h2 className="text-3xl font-extrabold text-gray-900 dark:text-white animate-slideInFromTop animate-delay-200">
              {getPageTitle()}
            </h2>

            <p className="mt-3 text-sm text-gray-600 dark:text-gray-400 animate-slideInFromTop animate-delay-300">
              {getPageSubtitle()}
            </p>
          </div>
        </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10">
          {error && !alreadyEnabled && (
            <div className="flex flex-col items-center justify-center">
              <div className="w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                <XCircleIcon className="w-10 h-10 animate-pulse shadow-md text-red-600 dark:text-red-300" />
              </div>
              <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center">
                {error}
              </div>
              {unauthorizedAccess && (
                <div className="mt-4">
                  <button
                    onClick={handleRedirectLogin}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    Go to Login
                  </button>
                </div>
              )}
            </div>
          )}

          {(success || alreadyEnabled || setUpComplete) && (
            <div className="flex flex-col items-center justify-center text-center text-gray-600 dark:text-gray-400">
              <div className="w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md">
                <svg
                  className="w-8 h-8 text-green-600 dark:text-green-300"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                {success}
                {alreadyEnabled && (
                  <p className="text-sm text-gray-400 dark:text-gray-200 p-2">
                    Two-Factor Authentication is already enabled. Please contact support if you need to reset it.
                  </p>
                )}
                {setUpComplete && (
                  <p className="text-sm text-gray-400 dark:text-gray-200 p-2">
                    This link is valid for 5 minutes and can only be used once.
                  </p>
                )}
              </div>
            </div>
          )}

          {/* QR Code Display - Enhanced */}
          {qrCodeUrl && setUpComplete && !alreadyEnabled && (
            <div className="mt-6 text-center animate-fadeIn">
              <div className="bg-white dark:bg-gray-700 shadow-lg p-6 rounded-lg border border-gray-200 dark:border-gray-600 inline-block">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Scan QR Code</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Use your authenticator app</p>
                </div>
                <Image
                  src={qrCodeUrl}
                  alt="2FA QR Code"
                  width={200}
                  height={200}
                  className="rounded-lg"
                />
              </div>

              {secret && (
                <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                  <p className="font-medium text-gray-900 dark:text-white mb-2">Manual Setup Key:</p>
                  <p className="font-mono text-sm break-all text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-900 p-3 rounded border">
                    {secret}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    Enter this key manually if you can't scan the QR code
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Button for OTP verification redirect to verify-2fa after setup complete */}
          {setUpComplete && (
            <div className="mt-6">
              <button
                onClick={() => router.replace(`/customer/auth/verify-2fa?i=${encodeURIComponent(userId)}&unique=${encodeURIComponent(secret)}&c=`)}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                Verify OTP
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  </div>
  );
}
