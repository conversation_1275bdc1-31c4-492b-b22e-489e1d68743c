"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const applications_controller_1 = require("./applications.controller");
const applications_service_1 = require("./applications.service");
const application_task_helper_service_1 = require("./application-task-helper.service");
const applications_entity_1 = require("../entities/applications.entity");
const application_status_history_entity_1 = require("../entities/application-status-history.entity");
const user_entity_1 = require("../entities/user.entity");
const tasks_module_1 = require("../tasks/tasks.module");
const notifications_module_1 = require("../notifications/notifications.module");
const licenses_module_1 = require("../licenses/licenses.module");
const activity_notes_module_1 = require("../activity-notes/activity-notes.module");
let ApplicationsModule = class ApplicationsModule {
};
exports.ApplicationsModule = ApplicationsModule;
exports.ApplicationsModule = ApplicationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([applications_entity_1.Applications, application_status_history_entity_1.ApplicationStatusHistory, user_entity_1.User]),
            (0, common_1.forwardRef)(() => tasks_module_1.TasksModule),
            (0, common_1.forwardRef)(() => licenses_module_1.LicensesModule),
            (0, common_1.forwardRef)(() => activity_notes_module_1.ActivityNotesModule),
            notifications_module_1.NotificationsModule
        ],
        controllers: [applications_controller_1.ApplicationsController],
        providers: [applications_service_1.ApplicationsService, application_task_helper_service_1.ApplicationTaskHelperService],
        exports: [applications_service_1.ApplicationsService, application_task_helper_service_1.ApplicationTaskHelperService],
    })
], ApplicationsModule);
//# sourceMappingURL=applications.module.js.map