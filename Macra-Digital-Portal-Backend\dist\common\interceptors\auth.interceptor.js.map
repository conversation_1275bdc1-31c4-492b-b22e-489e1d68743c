{"version": 3, "file": "auth.interceptor.js", "sourceRoot": "", "sources": ["../../../src/common/interceptors/auth.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAOwB;AACxB,+BAA8C;AAC9C,8CAAiD;AACjD,+EAA0E;AAC1E,0EAA6E;AAGtE,IAAM,eAAe,uBAArB,MAAM,eAAe;IAG1B,YAA6B,iBAAoC;QAApC,sBAAiB,GAAjB,iBAAiB,CAAmB;QAFhD,WAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;IAES,CAAC;IAErE,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QAGtD,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACvE,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAG7B,MAAM,cAAc,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC9C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAG1B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAElC,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,KAAK,EAAE,YAAY,EAAE,EAAE;YAEzB,IAAI,cAAc,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBAExC,YAAY,CAAC,KAAK,IAAI,EAAE;oBACtB,IAAI,CAAC;wBACH,MAAM,IAAI,CAAC,YAAY,CACrB,GAAG,EACH,IAAI,EAAE,OAAO,EACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,gCAAW,CAAC,OAAO,EACnB,SAAS,EACT;4BACE,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;4BACpC,MAAM;4BACN,GAAG;4BACH,UAAU,EAAE,QAAQ,CAAC,UAAU;yBAChC,CACF,CAAC;oBACJ,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,UAAU,CAAC,CAAC;oBACpE,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,EACF,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;YAEnB,IAAI,cAAc,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBAExC,YAAY,CAAC,KAAK,IAAI,EAAE;oBACtB,IAAI,CAAC;wBACH,MAAM,IAAI,CAAC,YAAY,CACrB,GAAG,EACH,IAAI,EAAE,OAAO,EACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,gCAAW,CAAC,OAAO,EACnB,KAAK,CAAC,OAAO,EACb;4BACE,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;4BACpC,MAAM;4BACN,GAAG;4BACH,UAAU,EAAE,KAAK,CAAC,MAAM,IAAI,GAAG;4BAC/B,UAAU,EAAE,KAAK,CAAC,KAAK;yBACxB,CACF,CAAC;oBACJ,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,UAAU,CAAC,CAAC;oBACpE,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAGD,IAAI,KAAK,YAAY,8BAAqB,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,SAAS,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/E,CAAC;YAED,OAAO,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,GAAW,EACX,MAAe,EACf,SAAkB,EAClB,SAAkB,EAClB,SAAkB,EAClB,SAAsB,gCAAW,CAAC,OAAO,EACzC,YAAqB,EACrB,QAA8B;QAE9B,IAAI,CAAC;YACH,IAAI,MAAmB,CAAC;YACxB,IAAI,WAAmB,CAAC;YAGxB,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3B,MAAM,GAAG,gCAAW,CAAC,KAAK,CAAC;gBAC3B,WAAW,GAAG,oBAAoB,CAAC;YACrC,CAAC;iBAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACnC,MAAM,GAAG,gCAAW,CAAC,MAAM,CAAC;gBAC5B,WAAW,GAAG,aAAa,CAAC;YAC9B,CAAC;iBAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACrC,MAAM,GAAG,gCAAW,CAAC,MAAM,CAAC;gBAC5B,WAAW,GAAG,2BAA2B,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,gCAAW,CAAC,IAAI,CAAC;gBAC1B,WAAW,GAAG,uBAAuB,CAAC;YACxC,CAAC;YAED,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CACvC,MAAM,EACN,MAAM,EACN,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,YAAY,EACZ,QAAQ,CACT,CAAC;QACJ,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,UAAU,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,OAAY;QAC9B,OAAO,CACL,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAClC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC;YAC5B,OAAO,CAAC,UAAU,EAAE,aAAa;YACjC,OAAO,CAAC,MAAM,EAAE,aAAa;YAC7B,OAAO,CAAC,EAAE;YACV,SAAS,CACV,CAAC;IACJ,CAAC;IAEO,kBAAkB,CAAC,QAAa;QACtC,IAAI,CAAC;YAEH,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;gBACpE,OAAO;YACT,CAAC;YAGD,QAAQ,CAAC,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;YACxD,QAAQ,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;YAC9C,QAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;YAGxD,QAAQ,CAAC,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAG3C,QAAQ,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;CACF,CAAA;AAvKY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAIqC,uCAAiB;GAHtD,eAAe,CAuK3B"}