{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/LoadingState.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Image from 'next/image';\r\n\r\ninterface LoadingStateProps {\r\n  message?: string;\r\n  submessage?: string;\r\n  showProgress?: boolean;\r\n  progress?: number;\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  dynamicMessages?: string[];\r\n  messageInterval?: number;\r\n}\r\n\r\nconst LoadingState: React.FC<LoadingStateProps> = ({\r\n  message = 'Loading...',\r\n  submessage,\r\n  showProgress = false,\r\n  progress = 0,\r\n  size = 'md',\r\n  className = '',\r\n  dynamicMessages = [],\r\n  messageInterval = 2000\r\n}) => {\r\n  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);\r\n  const [displayMessage, setDisplayMessage] = useState(message);\r\n\r\n  // Handle dynamic message rotation\r\n  useEffect(() => {\r\n    if (dynamicMessages.length > 0) {\r\n      const interval = setInterval(() => {\r\n        setCurrentMessageIndex((prev) => (prev + 1) % dynamicMessages.length);\r\n      }, messageInterval);\r\n\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [dynamicMessages, messageInterval]);\r\n\r\n  // Update display message when dynamic messages change\r\n  useEffect(() => {\r\n    if (dynamicMessages.length > 0) {\r\n      setDisplayMessage(dynamicMessages[currentMessageIndex]);\r\n    } else {\r\n      setDisplayMessage(message);\r\n    }\r\n  }, [currentMessageIndex, dynamicMessages, message]);\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return {\r\n          container: 'w-12 h-12',\r\n          logo: 'h-6 w-6',\r\n          text: 'text-sm'\r\n        };\r\n      case 'lg':\r\n        return {\r\n          container: 'w-24 h-24',\r\n          logo: 'h-12 w-12',\r\n          text: 'text-lg'\r\n        };\r\n      case 'md':\r\n      default:\r\n        return {\r\n          container: 'w-20 h-20',\r\n          logo: 'h-10 w-10',\r\n          text: 'text-base'\r\n        };\r\n    }\r\n  };\r\n\r\n  const sizeClasses = getSizeClasses();\r\n\r\n  return (\r\n    <div className={`text-center ${className}`}>\r\n      {/* Loading Spinner with Logo */}\r\n      <div className={`relative ${sizeClasses.container} mx-auto`}>\r\n        {/* Animated Spinner */}\r\n        <svg\r\n          className=\"absolute inset-0 animate-spin\"\r\n          viewBox=\"0 0 50 50\"\r\n          fill=\"none\"\r\n        >\r\n          <defs>\r\n            <linearGradient id=\"fadeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\r\n              <stop offset=\"0%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n              <stop offset=\"20%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n              <stop offset=\"80%\" stopColor=\"#dc2626\" stopOpacity=\"1\" />\r\n              <stop offset=\"100%\" stopColor=\"#dc2626\" stopOpacity=\"0\" />\r\n            </linearGradient>\r\n          </defs>\r\n\r\n          <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"rgba(255, 255, 255, 0.1)\"\r\n            strokeWidth=\"2\"\r\n          />\r\n          <circle\r\n            cx=\"25\"\r\n            cy=\"25\"\r\n            r=\"20\"\r\n            stroke=\"url(#fadeGradient)\"\r\n            strokeWidth=\"2\"\r\n            strokeDasharray=\"70\"\r\n            strokeDashoffset=\"10\"\r\n            fill=\"none\"\r\n          />\r\n        </svg>\r\n\r\n        {/* MACRA Logo */}\r\n        <Image\r\n          src=\"/images/macra-logo.png\"\r\n          alt=\"MACRA Logo\"\r\n          width={40}\r\n          height={40}\r\n          className={`object-contain absolute inset-0 ${sizeClasses.logo} m-auto animate-pulse`}\r\n          priority\r\n        />\r\n      </div>\r\n\r\n      {/* Progress Bar */}\r\n      {showProgress && (\r\n        <div className=\"mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n          <div\r\n            className=\"bg-red-600 h-2 rounded-full transition-all duration-300 ease-out\"\r\n            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Loading Message */}\r\n      <div className=\"mt-4 space-y-1\">\r\n        <p className={`text-gray-600 dark:text-gray-400 font-medium ${sizeClasses.text} transition-opacity duration-300`}>\r\n          {displayMessage}\r\n        </p>\r\n        {submessage && (\r\n          <p className=\"text-sm text-gray-500 dark:text-gray-500\">\r\n            {submessage}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoadingState;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBA,MAAM,eAA4C,CAAC,EACjD,UAAU,YAAY,EACtB,UAAU,EACV,eAAe,KAAK,EACpB,WAAW,CAAC,EACZ,OAAO,IAAI,EACX,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,kBAAkB,IAAI,EACvB;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,WAAW,YAAY;gBAC3B,uBAAuB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,gBAAgB,MAAM;YACtE,GAAG;YAEH,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAiB;KAAgB;IAErC,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,kBAAkB,eAAe,CAAC,oBAAoB;QACxD,OAAO;YACL,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAqB;QAAiB;KAAQ;IAElD,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;YACF,KAAK;YACL;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,MAAM;gBACR;QACJ;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QAAI,WAAW,CAAC,YAAY,EAAE,WAAW;;0BAExC,8OAAC;gBAAI,WAAW,CAAC,SAAS,EAAE,YAAY,SAAS,CAAC,QAAQ,CAAC;;kCAEzD,8OAAC;wBACC,WAAU;wBACV,SAAQ;wBACR,MAAK;;0CAEL,8OAAC;0CACC,cAAA,8OAAC;oCAAe,IAAG;oCAAe,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC7D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;4CAAU,aAAY;;;;;;sDAClD,8OAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,8OAAC;4CAAK,QAAO;4CAAM,WAAU;4CAAU,aAAY;;;;;;sDACnD,8OAAC;4CAAK,QAAO;4CAAO,WAAU;4CAAU,aAAY;;;;;;;;;;;;;;;;;0CAIxD,8OAAC;gCACC,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;;;;;;0CAEd,8OAAC;gCACC,IAAG;gCACH,IAAG;gCACH,GAAE;gCACF,QAAO;gCACP,aAAY;gCACZ,iBAAgB;gCAChB,kBAAiB;gCACjB,MAAK;;;;;;;;;;;;kCAKT,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAW,CAAC,gCAAgC,EAAE,YAAY,IAAI,CAAC,qBAAqB,CAAC;wBACrF,QAAQ;;;;;;;;;;;;YAKX,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAMjE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAW,CAAC,6CAA6C,EAAE,YAAY,IAAI,CAAC,gCAAgC,CAAC;kCAC7G;;;;;;oBAEF,4BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/auth/PageTransition.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport LoadingState from './LoadingState';\r\n\r\ninterface PageTransitionProps {\r\n  children: React.ReactNode;\r\n  isLoading?: boolean;\r\n  loadingMessage?: string;\r\n  loadingSubmessage?: string;\r\n  redirectTo?: string;\r\n  redirectDelay?: number;\r\n  showProgress?: boolean;\r\n  dynamicMessages?: string[];\r\n  className?: string;\r\n}\r\n\r\nconst PageTransition: React.FC<PageTransitionProps> = ({\r\n  children,\r\n  isLoading = false,\r\n  loadingMessage = 'Loading...',\r\n  loadingSubmessage,\r\n  redirectTo,\r\n  redirectDelay = 2000,\r\n  showProgress = false,\r\n  dynamicMessages = [],\r\n  className = ''\r\n}) => {\r\n  const router = useRouter();\r\n  const [progress, setProgress] = useState(0);\r\n  const [isRedirecting, setIsRedirecting] = useState(false);\r\n\r\n  // Handle redirect with progress\r\n  useEffect(() => {\r\n    if (redirectTo && !isRedirecting) {\r\n      setIsRedirecting(true);\r\n      \r\n      if (showProgress) {\r\n        const progressInterval = setInterval(() => {\r\n          setProgress((prev) => {\r\n            if (prev >= 100) {\r\n              clearInterval(progressInterval);\r\n              return 100;\r\n            }\r\n            return prev + (100 / (redirectDelay / 100));\r\n          });\r\n        }, 100);\r\n\r\n        const redirectTimeout = setTimeout(() => {\r\n          router.push(redirectTo);\r\n        }, redirectDelay);\r\n\r\n        return () => {\r\n          clearInterval(progressInterval);\r\n          clearTimeout(redirectTimeout);\r\n        };\r\n      } else {\r\n        const redirectTimeout = setTimeout(() => {\r\n          router.push(redirectTo);\r\n        }, redirectDelay);\r\n\r\n        return () => clearTimeout(redirectTimeout);\r\n      }\r\n    }\r\n  }, [redirectTo, redirectDelay, router, showProgress, isRedirecting]);\r\n\r\n  if (isLoading || isRedirecting) {\r\n    return (\r\n      <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${className}`}>\r\n        <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\r\n          <LoadingState\r\n            message={loadingMessage}\r\n            submessage={loadingSubmessage}\r\n            showProgress={showProgress}\r\n            progress={progress}\r\n            size=\"lg\"\r\n            dynamicMessages={dynamicMessages}\r\n          />\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`transition-all duration-300 ease-in-out ${className}`}>\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageTransition;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAkBA,MAAM,iBAAgD,CAAC,EACrD,QAAQ,EACR,YAAY,KAAK,EACjB,iBAAiB,YAAY,EAC7B,iBAAiB,EACjB,UAAU,EACV,gBAAgB,IAAI,EACpB,eAAe,KAAK,EACpB,kBAAkB,EAAE,EACpB,YAAY,EAAE,EACf;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,CAAC,eAAe;YAChC,iBAAiB;YAEjB,IAAI,cAAc;gBAChB,MAAM,mBAAmB,YAAY;oBACnC,YAAY,CAAC;wBACX,IAAI,QAAQ,KAAK;4BACf,cAAc;4BACd,OAAO;wBACT;wBACA,OAAO,OAAQ,MAAM,CAAC,gBAAgB,GAAG;oBAC3C;gBACF,GAAG;gBAEH,MAAM,kBAAkB,WAAW;oBACjC,OAAO,IAAI,CAAC;gBACd,GAAG;gBAEH,OAAO;oBACL,cAAc;oBACd,aAAa;gBACf;YACF,OAAO;gBACL,MAAM,kBAAkB,WAAW;oBACjC,OAAO,IAAI,CAAC;gBACd,GAAG;gBAEH,OAAO,IAAM,aAAa;YAC5B;QACF;IACF,GAAG;QAAC;QAAY;QAAe;QAAQ;QAAc;KAAc;IAEnE,IAAI,aAAa,eAAe;QAC9B,qBACE,8OAAC;YAAI,WAAW,CAAC,0EAA0E,EAAE,WAAW;sBACtG,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAA,CAAA,UAAY;oBACX,SAAS;oBACT,YAAY;oBACZ,cAAc;oBACd,UAAU;oBACV,MAAK;oBACL,iBAAiB;;;;;;;;;;;;;;;;IAK3B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,wCAAwC,EAAE,WAAW;kBACnE;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/auth/signup/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport PageTransition from '@/components/auth/PageTransition';\r\n\r\nexport default function SignupPage() {\r\n  const [formData, setFormData] = useState({\r\n    first_name: '',\r\n    last_name: '',\r\n    email: '',\r\n    phone: '',\r\n    organization: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n  });\r\n  const [acceptTerms, setAcceptTerms] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [loadingMessage, setLoadingMessage] = useState('');\r\n  const [showEmailVerificationMessage, setShowEmailVerificationMessage] = useState(false);\r\n\r\n  const { register } = useAuth();\r\n  const router = useRouter();\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setFormData({\r\n      ...formData,\r\n      [e.target.name]: e.target.value,\r\n    });\r\n    // Clear errors when user starts typing\r\n    if (error) setError('');\r\n    if (success) setSuccess('');\r\n  };\r\n\r\n  // Password validation function\r\n  const validatePassword = (password: string): string[] => {\r\n    const errors = [];\r\n    if (password.length < 8) errors.push('At least 8 characters long');\r\n    // if (!/[A-Z]/.test(password)) errors.push('At least one uppercase letter');\r\n    // if (!/[a-z]/.test(password)) errors.push('At least one lowercase letter');\r\n    // if (!/[\\d\\W]/.test(password)) errors.push('At least one number or special character');\r\n    return errors;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    // Step 1: Form validation (before API call)\r\n    setLoadingMessage('Validating your information...');\r\n\r\n    // Validate password\r\n    const passwordErrors = validatePassword(formData.password);\r\n    if (passwordErrors.length > 0) {\r\n      setError(`Password must have: ${passwordErrors.join(', ')}`);\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    if (formData.password !== formData.confirmPassword) {\r\n      setError('Passwords do not match');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    if (!acceptTerms) {\r\n      setError('Please accept the Terms of Service and Privacy Policy');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    // Validate phone number format\r\n    const phoneRegex = /^\\+?\\d{10,15}$/;\r\n    if (!phoneRegex.test(formData.phone)) {\r\n      setError('Please enter a valid phone number (10-15 digits, optionally starting with +)');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Step 2: Preparing data for submission\r\n      setLoadingMessage('Preparing account data...');\r\n      await new Promise(resolve => setTimeout(resolve, 500)); // Brief pause for UX\r\n\r\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n      const { confirmPassword, ...registerData } = formData;\r\n      console.log('Submitting registration data:', registerData);\r\n\r\n      // Step 3: Checking email availability\r\n      setLoadingMessage('Checking email availability...');\r\n      await new Promise(resolve => setTimeout(resolve, 300));\r\n\r\n      // Step 4: Submitting to server\r\n      setLoadingMessage('Creating your account...');\r\n      const result = await register(registerData);\r\n\r\n      // Step 5: Account created successfully\r\n      setLoadingMessage('Account created successfully!');\r\n      await new Promise(resolve => setTimeout(resolve, 800));\r\n\r\n      // Step 6: Preparing email verification\r\n      setLoadingMessage('Preparing email verification...');\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n      setSuccess(result.message || 'Account created successfully! Please check your email to verify your account before logging in.');\r\n      setShowEmailVerificationMessage(true);\r\n\r\n      setLoadingMessage('You will be redirected to login shortly...');\r\n      setLoading(true);\r\n      setTimeout(() => {\r\n        router.replace('/customer');\r\n      }, 3000); // middleware will redirect\r\n\r\n    } catch (err: unknown) {\r\n      console.error('Registration error:', err);\r\n\r\n      // Update loading message to indicate error processing\r\n      setLoadingMessage('Processing error...');\r\n      await new Promise(resolve => setTimeout(resolve, 300));\r\n\r\n      let errorMessage = 'Registration failed. Please try again.';\r\n\r\n      // Type guard to check if error has response property (like Axios errors)\r\n      const isAxiosError = (error: unknown): error is {\r\n        response?: {\r\n          status?: number;\r\n          data?: { message?: string };\r\n        };\r\n      } => {\r\n        return typeof error === 'object' && error !== null && 'response' in error;\r\n      };\r\n\r\n      // Type guard to check if error has message property\r\n      const hasMessage = (error: unknown): error is { message: string } => {\r\n        return typeof error === 'object' && error !== null && 'message' in error &&\r\n               typeof (error as { message: unknown }).message === 'string';\r\n      };\r\n\r\n      // Handle specific error cases based on status code\r\n      if (isAxiosError(err)) {\r\n        if (err.response?.status === 409) {\r\n          setLoadingMessage('Email already exists...');\r\n          errorMessage = 'An account with this email address already exists. Please use a different email or try logging in.';\r\n        } else if (err.response?.status === 422) {\r\n          setLoadingMessage('Validation failed...');\r\n          errorMessage = 'An account with this email address already exists. Please use a different email or try logging in.';\r\n        } else if (err.response?.status === 400) {\r\n          setLoadingMessage('Invalid data provided...');\r\n          // For 400 errors, try to get the specific message from the server\r\n          const serverMessage = err.response?.data?.message || '';\r\n          if (serverMessage.toLowerCase().includes('email') &&\r\n              (serverMessage.toLowerCase().includes('exists') ||\r\n               serverMessage.toLowerCase().includes('already'))) {\r\n            errorMessage = 'An account with this email address already exists. Please use a different email or try logging in.';\r\n          } else if (serverMessage) {\r\n            errorMessage = serverMessage;\r\n          } else {\r\n            errorMessage = 'Please check your information and try again.';\r\n          }\r\n        } else if (err.response?.status === 500) {\r\n          setLoadingMessage('Server error occurred...');\r\n          errorMessage = 'Server error occurred. Please try again later.';\r\n        } else if (err.response?.status === 429) {\r\n          setLoadingMessage('Too many requests...');\r\n          errorMessage = 'Too many registration attempts. Please wait a moment and try again.';\r\n        } else if (err.response?.data?.message) {\r\n          setLoadingMessage('Server responded with error...');\r\n          // Use the server's error message if available\r\n          const serverMessage = err.response.data.message;\r\n          if (serverMessage.toLowerCase().includes('email') &&\r\n              (serverMessage.toLowerCase().includes('exists') ||\r\n               serverMessage.toLowerCase().includes('already') ||\r\n               serverMessage.toLowerCase().includes('duplicate') ||\r\n               serverMessage.toLowerCase().includes('taken'))) {\r\n            errorMessage = 'An account with this email address already exists. Please use a different email or try logging in.';\r\n          } else {\r\n            errorMessage = serverMessage;\r\n          }\r\n        }\r\n      } else if (hasMessage(err)) {\r\n        setLoadingMessage('Connection error...');\r\n        // Fallback to the error message if no response data\r\n        errorMessage = err.message;\r\n      }\r\n\r\n      // Brief pause before showing error\r\n      await new Promise(resolve => setTimeout(resolve, 500));\r\n      setError(errorMessage);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Show loading state with PageTransition during registration and email verification\r\n  if (loading || showEmailVerificationMessage) {\r\n    const dynamicMessages = showEmailVerificationMessage ? [\r\n      'Account created successfully!'\r\n    ] : [\r\n      'Creating your account...',\r\n    ];\r\n    return (\r\n      <PageTransition\r\n        isLoading={true}\r\n        loadingMessage={loadingMessage || 'Processing your registration...'}\r\n        loadingSubmessage={showEmailVerificationMessage ?\r\n          'A verification email has been sent to your email address. Please check your inbox and click the verification link to activate your account.' :\r\n          'Please wait while we process your registration and set up your account'\r\n        }\r\n        dynamicMessages={dynamicMessages}\r\n        showProgress={true}\r\n      >\r\n        <div />\r\n      </PageTransition>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <style jsx>{`\r\n        /* Custom scrollbar styles */\r\n        .custom-scrollbar {\r\n          scroll-behavior: smooth;\r\n        }\r\n        .custom-scrollbar::-webkit-scrollbar {\r\n          width: 8px;\r\n        }\r\n        .custom-scrollbar::-webkit-scrollbar-track {\r\n          background: #f1f1f1;\r\n          border-radius: 4px;\r\n        }\r\n        .custom-scrollbar::-webkit-scrollbar-thumb {\r\n          background: #dc2626;\r\n          border-radius: 4px;\r\n          transition: background-color 0.2s ease;\r\n        }\r\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n          background: #b91c1c;\r\n        }\r\n        .dark .custom-scrollbar::-webkit-scrollbar-track {\r\n          background: #374151;\r\n        }\r\n        .dark .custom-scrollbar::-webkit-scrollbar-thumb {\r\n          background: #ef4444;\r\n        }\r\n        .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n          background: #dc2626;\r\n        }\r\n        \r\n        /* Smooth focus scrolling */\r\n        input:focus, textarea:focus, select:focus {\r\n          scroll-margin-top: 2rem;\r\n        }\r\n        \r\n        /* Form input shared styles */\r\n        .signup-input {\r\n          appearance: none;\r\n          display: block;\r\n          width: 100%;\r\n          padding: 12px 16px;\r\n          border: 2px solid #d1d5db;\r\n          border-radius: 0.375rem;\r\n          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\r\n          background-color: #f9fafb;\r\n          color: #111827;\r\n          transition: all 0.2s ease;\r\n        }\r\n        \r\n        .signup-input:hover {\r\n          background-color: #ffffff;\r\n        }\r\n        \r\n        .signup-input:focus {\r\n          outline: none;\r\n          border-color: #dc2626;\r\n          box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);\r\n        }\r\n        \r\n        .dark .signup-input {\r\n          border-color: #4b5563;\r\n          background-color: #374151;\r\n          color: #f9fafb;\r\n        }\r\n        \r\n        .dark .signup-input:hover {\r\n          background-color: #4b5563;\r\n        }\r\n        \r\n        .dark .signup-input::placeholder {\r\n          color: #9ca3af;\r\n        }\r\n        \r\n        /* Responsive adjustments */\r\n        @media (max-width: 640px) {\r\n          .signup-container {\r\n            padding: 1rem;\r\n          }\r\n        }\r\n      `}</style>\r\n      <div className=\"min-h-screen max-h-screen overflow-y-auto bg-gray-50 dark:bg-gray-900 custom-scrollbar\">\r\n        <div className=\"flex flex-col justify-center py-6 sm:py-12 px-4 sm:px-6 lg:px-8 min-h-full signup-container\">\r\n          <div className=\"sm:mx-auto sm:w-full sm:max-w-2xl\">\r\n            <div className=\"flex justify-center\">\r\n              <Image\r\n                src=\"/images/macra-logo.png\"\r\n                alt=\"MACRA Logo\"\r\n                width={64}\r\n                height={64}\r\n                className=\"h-16 w-auto\"\r\n              />\r\n            </div>\r\n            <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100\">\r\n              Create your account\r\n            </h2>\r\n            <p className=\"mt-2 text-center text-sm text-gray-600 dark:text-gray-400\">\r\n              Or{' '}\r\n              <Link href=\"/customer/auth/login\" className=\"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300\">\r\n                sign in to your existing account\r\n              </Link>\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"mt-6 sm:mt-8 sm:mx-auto sm:w-full sm:max-w-2xl\">\r\n            <div className=\"bg-white dark:bg-gray-800 py-6 sm:py-8 px-4 sm:px-10 shadow sm:rounded-lg border border-gray-200 dark:border-gray-700\">\r\n          {error && (\r\n            <div className=\"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md relative\">\r\n              <div className=\"flex justify-between items-start\">\r\n                <span className=\"flex-1\">{error}</span>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setError('')}\r\n                  className=\"ml-2 text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none\"\r\n                  aria-label=\"Close error message\"\r\n                >\r\n                  <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {success && (\r\n            <div className=\"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-md relative\">\r\n              <div className=\"flex justify-between items-start\">\r\n                <span className=\"flex-1\">{success}</span>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => setSuccess('')}\r\n                  className=\"ml-2 text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none\"\r\n                  aria-label=\"Close success message\"\r\n                >\r\n                  <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <form className=\"space-y-4 sm:space-y-6\" onSubmit={handleSubmit}>\r\n            {/* Name Fields - Side by side */}\r\n            <div className=\"grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6\">\r\n              <div>\r\n                <label htmlFor=\"first_name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  First name\r\n                </label>\r\n                <div className=\"mt-1\">\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"first_name\"\r\n                    id=\"first_name\"\r\n                    autoComplete=\"given-name\"\r\n                    required\r\n                    value={formData.first_name}\r\n                    onChange={handleChange}\r\n                    className=\"appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label htmlFor=\"last_name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Last name\r\n                </label>\r\n                <div className=\"mt-1\">\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"last_name\"\r\n                    id=\"last_name\"\r\n                    autoComplete=\"family-name\"\r\n                    required\r\n                    value={formData.last_name}\r\n                    onChange={handleChange}\r\n                    className=\"appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Email and Phone - Side by side */}\r\n            <div className=\"grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6\">\r\n              <div>\r\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Email address\r\n                </label>\r\n                <div className=\"mt-1\">\r\n                  <input\r\n                    id=\"email\"\r\n                    name=\"email\"\r\n                    type=\"email\"\r\n                    autoComplete=\"email\"\r\n                    required\r\n                    value={formData.email}\r\n                    onChange={handleChange}\r\n                    className=\"appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Phone Number\r\n                </label>\r\n                <div className=\"mt-1\">\r\n                  <input\r\n                    id=\"phone\"\r\n                    name=\"phone\"\r\n                    type=\"tel\"\r\n                    required\r\n                    value={formData.phone}\r\n                    onChange={handleChange}\r\n                    placeholder=\"+265123456789\"\r\n                    className=\"appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Organization - Full width */}\r\n            <div>\r\n              <label htmlFor=\"organization\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Organization (Optional)\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"organization\"\r\n                  name=\"organization\"\r\n                  type=\"text\"\r\n                  value={formData.organization}\r\n                  onChange={handleChange}\r\n                  className=\"appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Password and Confirm Password - Side by side */}\r\n            <div className=\"grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6\">\r\n              <div>\r\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Password\r\n                </label>\r\n                <div className=\"mt-1\">\r\n                  <input\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    type=\"password\"\r\n                    autoComplete=\"new-password\"\r\n                    required\r\n                    value={formData.password}\r\n                    onChange={handleChange}\r\n                    className=\"appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Confirm password\r\n                </label>\r\n                <div className=\"mt-1\">\r\n                  <input\r\n                    id=\"confirmPassword\"\r\n                    name=\"confirmPassword\"\r\n                    type=\"password\"\r\n                    autoComplete=\"new-password\"\r\n                    required\r\n                    value={formData.confirmPassword}\r\n                    onChange={handleChange}\r\n                    className=\"appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Password Requirements - Below password fields */}\r\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 -mt-2\">\r\n              Password must contain at least 8 characters with uppercase, lowercase, and number/special character.\r\n            </div>\r\n\r\n            <div className=\"flex items-center\">\r\n              <input\r\n                id=\"terms\"\r\n                name=\"terms\"\r\n                type=\"checkbox\"\r\n                required\r\n                checked={acceptTerms}\r\n                onChange={(e) => setAcceptTerms(e.target.checked)}\r\n                className=\"h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700\"\r\n              />\r\n              <label htmlFor=\"terms\" className=\"ml-2 block text-sm text-gray-900 dark:text-gray-100\">\r\n                I agree to the{' '}\r\n                <a href=\"#\" className=\"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300\">\r\n                  Terms of Service\r\n                </a>{' '}\r\n                and{' '}\r\n                <a href=\"#\" className=\"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300\">\r\n                  Privacy Policy\r\n                </a>\r\n              </label>\r\n            </div>\r\n\r\n            <div>\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className=\"w-full flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? (\r\n                  <span className=\"flex items-center\">\r\n                    <svg className=\"animate-spin -ml-1 mr-3 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                    </svg>\r\n                    {loadingMessage || 'Processing...'}\r\n                  </span>\r\n                ) : 'Create account'}\r\n              </button>\r\n            </div>\r\n          </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,YAAY;QACZ,WAAW;QACX,OAAO;QACP,OAAO;QACP,cAAc;QACd,UAAU;QACV,iBAAiB;IACnB;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,8BAA8B,gCAAgC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjF,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;QACA,uCAAuC;QACvC,IAAI,OAAO,SAAS;QACpB,IAAI,SAAS,WAAW;IAC1B;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,EAAE;QACjB,IAAI,SAAS,MAAM,GAAG,GAAG,OAAO,IAAI,CAAC;QACrC,6EAA6E;QAC7E,6EAA6E;QAC7E,yFAAyF;QACzF,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,4CAA4C;QAC5C,kBAAkB;QAElB,oBAAoB;QACpB,MAAM,iBAAiB,iBAAiB,SAAS,QAAQ;QACzD,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,SAAS,CAAC,oBAAoB,EAAE,eAAe,IAAI,CAAC,OAAO;YAC3D,WAAW;YACX;QACF;QAEA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI,CAAC,aAAa;YAChB,SAAS;YACT,WAAW;YACX;QACF;QAEA,+BAA+B;QAC/B,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,GAAG;YACpC,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,wCAAwC;YACxC,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,OAAO,qBAAqB;YAE7E,6DAA6D;YAC7D,MAAM,EAAE,eAAe,EAAE,GAAG,cAAc,GAAG;YAC7C,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,sCAAsC;YACtC,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,+BAA+B;YAC/B,kBAAkB;YAClB,MAAM,SAAS,MAAM,SAAS;YAE9B,uCAAuC;YACvC,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,uCAAuC;YACvC,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,WAAW,OAAO,OAAO,IAAI;YAC7B,gCAAgC;YAEhC,kBAAkB;YAClB,WAAW;YACX,WAAW;gBACT,OAAO,OAAO,CAAC;YACjB,GAAG,OAAO,2BAA2B;QAEvC,EAAE,OAAO,KAAc;YACrB,QAAQ,KAAK,CAAC,uBAAuB;YAErC,sDAAsD;YACtD,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAI,eAAe;YAEnB,yEAAyE;YACzE,MAAM,eAAe,CAAC;gBAMpB,OAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,cAAc;YACtE;YAEA,oDAAoD;YACpD,MAAM,aAAa,CAAC;gBAClB,OAAO,OAAO,UAAU,YAAY,UAAU,QAAQ,aAAa,SAC5D,OAAO,AAAC,MAA+B,OAAO,KAAK;YAC5D;YAEA,mDAAmD;YACnD,IAAI,aAAa,MAAM;gBACrB,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBAChC,kBAAkB;oBAClB,eAAe;gBACjB,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBACvC,kBAAkB;oBAClB,eAAe;gBACjB,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBACvC,kBAAkB;oBAClB,kEAAkE;oBAClE,MAAM,gBAAgB,IAAI,QAAQ,EAAE,MAAM,WAAW;oBACrD,IAAI,cAAc,WAAW,GAAG,QAAQ,CAAC,YACrC,CAAC,cAAc,WAAW,GAAG,QAAQ,CAAC,aACrC,cAAc,WAAW,GAAG,QAAQ,CAAC,UAAU,GAAG;wBACrD,eAAe;oBACjB,OAAO,IAAI,eAAe;wBACxB,eAAe;oBACjB,OAAO;wBACL,eAAe;oBACjB;gBACF,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBACvC,kBAAkB;oBAClB,eAAe;gBACjB,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;oBACvC,kBAAkB;oBAClB,eAAe;gBACjB,OAAO,IAAI,IAAI,QAAQ,EAAE,MAAM,SAAS;oBACtC,kBAAkB;oBAClB,8CAA8C;oBAC9C,MAAM,gBAAgB,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO;oBAC/C,IAAI,cAAc,WAAW,GAAG,QAAQ,CAAC,YACrC,CAAC,cAAc,WAAW,GAAG,QAAQ,CAAC,aACrC,cAAc,WAAW,GAAG,QAAQ,CAAC,cACrC,cAAc,WAAW,GAAG,QAAQ,CAAC,gBACrC,cAAc,WAAW,GAAG,QAAQ,CAAC,QAAQ,GAAG;wBACnD,eAAe;oBACjB,OAAO;wBACL,eAAe;oBACjB;gBACF;YACF,OAAO,IAAI,WAAW,MAAM;gBAC1B,kBAAkB;gBAClB,oDAAoD;gBACpD,eAAe,IAAI,OAAO;YAC5B;YAEA,mCAAmC;YACnC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,oFAAoF;IACpF,IAAI,WAAW,8BAA8B;QAC3C,MAAM,kBAAkB,+BAA+B;YACrD;SACD,GAAG;YACF;SACD;QACD,qBACE,8OAAC,4IAAA,CAAA,UAAc;YACb,WAAW;YACX,gBAAgB,kBAAkB;YAClC,mBAAmB,+BACjB,gJACA;YAEF,iBAAiB;YACjB,cAAc;sBAEd,cAAA,8OAAC;;;;;;;;;;IAGP;IAEA,qBACE;;;;;;0BAiFE,8OAAC;0DAAc;0BACb,cAAA,8OAAC;8DAAc;;sCACb,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,8OAAC;8EAAa;8CAA4E;;;;;;8CAG1F,8OAAC;8EAAY;;wCAA4D;wCACpE;sDACH,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAuB,WAAU;sDAAwF;;;;;;;;;;;;;;;;;;sCAMxI,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;oCAChB,uBACC,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAc;;8DACb,8OAAC;8FAAe;8DAAU;;;;;;8DAC1B,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,SAAS;oDAExB,cAAW;8FADD;8DAGV,cAAA,8OAAC;wDAAwB,MAAK;wDAAe,SAAQ;kGAAtC;kEACb,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAOlP,yBACC,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAc;;8DACb,8OAAC;8FAAe;8DAAU;;;;;;8DAC1B,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,WAAW;oDAE1B,cAAW;8FADD;8DAGV,cAAA,8OAAC;wDAAwB,MAAK;wDAAe,SAAQ;kGAAtC;kEACb,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOnP,8OAAC;wCAAwC,UAAU;kFAAnC;;0DAEd,8OAAC;0FAAc;;kEACb,8OAAC;;;0EACC,8OAAC;gEAAM,SAAQ;0GAAuB;0EAA6D;;;;;;0EAGnG,8OAAC;0GAAc;0EACb,cAAA,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,IAAG;oEACH,cAAa;oEACb,QAAQ;oEACR,OAAO,SAAS,UAAU;oEAC1B,UAAU;8GACA;;;;;;;;;;;;;;;;;kEAKhB,8OAAC;;;0EACC,8OAAC;gEAAM,SAAQ;0GAAsB;0EAA6D;;;;;;0EAGlG,8OAAC;0GAAc;0EACb,cAAA,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,IAAG;oEACH,cAAa;oEACb,QAAQ;oEACR,OAAO,SAAS,SAAS;oEACzB,UAAU;8GACA;;;;;;;;;;;;;;;;;;;;;;;0DAOlB,8OAAC;0FAAc;;kEACb,8OAAC;;;0EACC,8OAAC;gEAAM,SAAQ;0GAAkB;0EAA6D;;;;;;0EAG9F,8OAAC;0GAAc;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,cAAa;oEACb,QAAQ;oEACR,OAAO,SAAS,KAAK;oEACrB,UAAU;8GACA;;;;;;;;;;;;;;;;;kEAKhB,8OAAC;;;0EACC,8OAAC;gEAAM,SAAQ;0GAAkB;0EAA6D;;;;;;0EAG9F,8OAAC;0GAAc;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,QAAQ;oEACR,OAAO,SAAS,KAAK;oEACrB,UAAU;oEACV,aAAY;8GACF;;;;;;;;;;;;;;;;;;;;;;;0DAOlB,8OAAC;;;kEACC,8OAAC;wDAAM,SAAQ;kGAAyB;kEAA6D;;;;;;kEAGrG,8OAAC;kGAAc;kEACb,cAAA,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,YAAY;4DAC5B,UAAU;sGACA;;;;;;;;;;;;;;;;;0DAMhB,8OAAC;0FAAc;;kEACb,8OAAC;;;0EACC,8OAAC;gEAAM,SAAQ;0GAAqB;0EAA6D;;;;;;0EAGjG,8OAAC;0GAAc;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,cAAa;oEACb,QAAQ;oEACR,OAAO,SAAS,QAAQ;oEACxB,UAAU;8GACA;;;;;;;;;;;;;;;;;kEAKhB,8OAAC;;;0EACC,8OAAC;gEAAM,SAAQ;0GAA4B;0EAA6D;;;;;;0EAGxG,8OAAC;0GAAc;0EACb,cAAA,8OAAC;oEACC,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,cAAa;oEACb,QAAQ;oEACR,OAAO,SAAS,eAAe;oEAC/B,UAAU;8GACA;;;;;;;;;;;;;;;;;;;;;;;0DAOlB,8OAAC;0FAAc;0DAAiD;;;;;;0DAIhE,8OAAC;0FAAc;;kEACb,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,QAAQ;wDACR,SAAS;wDACT,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,OAAO;kGACtC;;;;;;kEAEZ,8OAAC;wDAAM,SAAQ;kGAAkB;;4DAAsD;4DACtE;0EACf,8OAAC;gEAAE,MAAK;0GAAc;0EAAwF;;;;;;4DAEzG;4DAAI;4DACL;0EACJ,8OAAC;gEAAE,MAAK;0GAAc;0EAAwF;;;;;;;;;;;;;;;;;;0DAMlH,8OAAC;;0DACC,cAAA,8OAAC;oDACC,MAAK;oDACL,UAAU;8FACA;8DAET,wBACC,8OAAC;kGAAe;;0EACd,8OAAC;gEAA2D,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;0GAApG;;kFACb,8OAAC;wEAA8B,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAK,QAAO;wEAAe,aAAY;kHAAtE;;;;;;kFAClB,8OAAC;wEAA4B,MAAK;wEAAe,GAAE;kHAAnC;;;;;;;;;;;;4DAEjB,kBAAkB;;;;;;+DAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB", "debugId": null}}]}