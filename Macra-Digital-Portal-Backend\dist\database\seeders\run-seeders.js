#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runSeeding = runSeeding;
require("reflect-metadata");
const dotenv_1 = require("dotenv");
const seeder_config_1 = require("./seeder.config");
const main_seeder_1 = __importDefault(require("./main.seeder"));
const fix_address_foreign_keys_seeder_1 = __importDefault(require("./fix-address-foreign-keys.seeder"));
(0, dotenv_1.config)();
async function runSeeding() {
    let dataSource;
    try {
        dataSource = (0, seeder_config_1.createSeederDataSource)();
        console.log('📡 Connecting to database...');
        await dataSource.initialize();
        console.log('✅ Database connection established');
        console.log('\n🔧 Fixing foreign key constraints...');
        const fixForeignKeysSeeder = new fix_address_foreign_keys_seeder_1.default();
        await fixForeignKeysSeeder.run(dataSource);
        console.log('\n🚀 Running seeders...');
        const mainSeeder = new main_seeder_1.default();
        await mainSeeder.run(dataSource);
        console.log('\n🎉 All seeders completed successfully!');
    }
    catch (error) {
        console.error('\n❌ Seeding failed:', error);
        process.exit(1);
    }
    finally {
        if (dataSource && dataSource.isInitialized) {
            console.log('\n📡 Closing database connection...');
            await dataSource.destroy();
            console.log('✅ Database connection closed');
        }
    }
}
runSeeding();
//# sourceMappingURL=run-seeders.js.map