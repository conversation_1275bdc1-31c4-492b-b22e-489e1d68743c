{"version": 3, "file": "license-category-documents.seeder.js", "sourceRoot": "", "sources": ["../../../src/database/seeders/license-category-documents.seeder.ts"], "names": [], "mappings": ";;AACA,oFAAkF;AAClF,6CAA0E;AAM1E,MAAqB,8BAA8B;IAC1C,KAAK,CAAC,GAAG,CAAC,UAAsB;QACrC,MAAM,kBAAkB,GAAG,UAAU,CAAC,aAAa,CAAC,kCAAuB,CAAC,CAAC;QAC7E,MAAM,kBAAkB,GAAG,UAAU,CAAC,aAAa,CAAC,4BAAiB,CAAC,CAAC;QAGvE,MAAM,aAAa,GAAG,MAAM,kBAAkB,CAAC,KAAK,EAAE,CAAC;QACvD,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC;YAC/C,SAAS,EAAE,CAAC,cAAc,CAAC;SAC5B,CAAC,CAAC;QAEH,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,KAAK,CAAC,0EAA0E,CAAC,CAAC;YAC1F,OAAO;QACT,CAAC;QAGD,MAAM,iBAAiB,GAAG;YACxB,eAAe;YACf,kBAAkB;YAClB,iBAAiB;YACjB,iCAAiC;YACjC,6DAA6D;YAC7D,kBAAkB;YAClB,mCAAmC;YACnC,iDAAiD;YACjD,oDAAoD;YACpD,kCAAkC;YAClC,mEAAmE;YACnE,sCAAsC;YACtC,4BAA4B;YAC5B,8BAA8B;YAC9B,yBAAyB;YACzB,gCAAgC;YAChC,iCAAiC;YACjC,+CAA+C;SAChD,CAAC;QAGF,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;YAClC,MAAM,IAAI,GAAG,IAAA,gDAAwB,EAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,YAAY,IAAI,iBAAiB,CAAC;YAC9G,KAAK,MAAM,YAAY,IAAI,IAAI,EAAE,CAAC;gBAChC,MAAM,QAAQ,GAAG,kBAAkB,CAAC,MAAM,CAAC;oBACzC,mBAAmB,EAAE,QAAQ,CAAC,mBAAmB;oBACjD,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,IAAI;iBAClB,CAAC,CAAC;gBACH,MAAM,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAxDD,iDAwDC"}