'use client';

import React, { useState, useEffect } from 'react';
import OTPInput from './OTPInput';
import { OTPVerificationProps } from '@/types/user';


const OTPVerification: React.FC<OTPVerificationProps> = ({
  title = 'Enter Verification Code',
  description = 'Please enter the verification code sent to your email',
  value,
  onChange,
  onSubmit,
  error,
  loading = false,
  length = 6,
  submitText = 'Verify',
  loadingText = 'Verifying...',
  autoSubmit = false,
  showSubmitButton = true,
  className = '',
  autoFocus = true,
  numericOnly = true,
  children,
}) => {
  const [isComplete, setIsComplete] = useState(false);

  // Check if OTP is complete
  useEffect(() => {
    const complete = value.length === length;
    setIsComplete(complete);

    // Auto-submit if enabled and OTP is complete
    if (autoSubmit && complete && !loading) {
      onSubmit(value);
    }
  }, [value, length, autoSubmit, loading, onSubmit]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (value.length === length && !loading) {
      onSubmit(value);
    }
  };

  const handleOTPComplete = (otpValue: string) => {
    if (autoSubmit && !loading) {
      onSubmit(otpValue);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {description}
        </p>
      </div>

      {/* Additional content */}
      {children}

      {/* OTP Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="flex flex-col items-center space-y-4">
          {/* OTP Input */}
          <OTPInput
            length={length}
            value={value}
            onChange={onChange}
            onComplete={handleOTPComplete}
            disabled={loading}
            autoFocus={autoFocus}
            hasError={!!error}
            numericOnly={numericOnly}
          />

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg text-sm text-center max-w-md">
              {error}
            </div>
          )}

          {/* Submit Button */}
          {showSubmitButton && (
            <button
              type="submit"
              disabled={!isComplete || loading}
              className="w-full max-w-xs flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {loading ? loadingText : submitText}
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default OTPVerification;
