"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerInvoicesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const invoices_service_1 = require("./invoices.service");
const invoice_pdf_service_1 = require("./invoice-pdf.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const roles_guard_1 = require("../common/guards/roles.guard");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
let CustomerInvoicesController = class CustomerInvoicesController {
    constructor(invoicesService, invoicePdfService) {
        this.invoicesService = invoicesService;
        this.invoicePdfService = invoicePdfService;
    }
    async getCustomerInvoices(status, page, limit, search, req) {
        const userId = req.user.user_id;
        return this.invoicesService.getInvoicesByCustomer(userId, {
            status,
            page: page || 1,
            limit: limit || 10,
            search,
        });
    }
    async getCustomerInvoiceStatistics(req) {
        const userId = req.user.user_id;
        return this.invoicesService.getInvoiceStatisticsByCustomer(userId);
    }
    async getCustomerInvoice(id, req) {
        const userId = req.user.user_id;
        console.log(`🔍 Getting invoice ${id} for customer: ${userId}`);
        return this.invoicesService.getInvoiceByCustomer(id, userId);
    }
    async getCustomerApplicationInvoices(applicationId, req) {
        const userId = req.user.user_id;
        console.log(`🔍 Getting invoices for application ${applicationId} by customer: ${userId}`);
        return this.invoicesService.getApplicationInvoicesByCustomer(applicationId, userId);
    }
    async getCustomerApplicationInvoiceStatus(applicationId, req) {
        const userId = req.user.user_id;
        console.log(`🔍 Getting invoice status for application ${applicationId} by customer: ${userId}`);
        return this.invoicesService.getApplicationInvoiceStatusByCustomer(applicationId, userId);
    }
    async downloadCustomerInvoice(id, req, res) {
        const userId = req.user.user_id;
        console.log(`📄 Downloading invoice PDF ${id} for customer: ${userId}`);
        try {
            const invoice = await this.invoicesService.getInvoiceByCustomer(id, userId);
            let application = null;
            if (invoice.entity_type === 'application' && invoice.entity_id) {
                try {
                    const applicationDetails = await this.invoicesService.getApplicationDetailsForInvoice(invoice.entity_id);
                    application = applicationDetails.application;
                }
                catch (err) {
                    console.warn(`⚠️ Could not fetch application details for invoice ${id}:`, err);
                }
            }
            const pdfBuffer = await this.invoicePdfService.generateInvoicePdf(invoice, application);
            const contentType = pdfBuffer.toString().startsWith('<!DOCTYPE html') ? 'text/html' : 'application/pdf';
            const fileExtension = contentType === 'text/html' ? 'html' : 'pdf';
            res.set({
                'Content-Type': contentType,
                'Content-Disposition': `attachment; filename="invoice-${invoice.invoice_number}.${fileExtension}"`,
                'Content-Length': pdfBuffer.length,
            });
            res.send(pdfBuffer);
        }
        catch (error) {
            throw error;
        }
    }
};
exports.CustomerInvoicesController = CustomerInvoicesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer invoices (filtered by user applications)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer invoices retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: 'Filter by invoice status' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Search term' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerInvoice',
        description: 'Viewed customer invoices',
    }),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('search')),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number, String, Object]),
    __metadata("design:returntype", Promise)
], CustomerInvoicesController.prototype, "getCustomerInvoices", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer invoice statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer invoice statistics retrieved successfully' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerInvoice',
        description: 'Viewed customer invoice statistics',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CustomerInvoicesController.prototype, "getCustomerInvoiceStatistics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get specific customer invoice' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer invoice retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invoice not found or not accessible' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Invoice ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerInvoice',
        description: 'Viewed specific customer invoice',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerInvoicesController.prototype, "getCustomerInvoice", null);
__decorate([
    (0, common_1.Get)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get invoices for specific customer application' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application invoices retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found or not accessible' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('customer'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerInvoice',
        description: 'Viewed customer application invoices',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerInvoicesController.prototype, "getCustomerApplicationInvoices", null);
__decorate([
    (0, common_1.Get)('application/:applicationId/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Get invoice status for customer application' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application invoice status retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('customer'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerInvoice',
        description: 'Viewed customer application invoice status',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerInvoicesController.prototype, "getCustomerApplicationInvoiceStatus", null);
__decorate([
    (0, common_1.Get)(':id/download'),
    (0, swagger_1.ApiOperation)({ summary: 'Download customer invoice PDF' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoice PDF downloaded successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invoice not found or not accessible' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Invoice ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'CustomerInvoice',
        description: 'Downloaded customer invoice PDF',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], CustomerInvoicesController.prototype, "downloadCustomerInvoice", null);
exports.CustomerInvoicesController = CustomerInvoicesController = __decorate([
    (0, swagger_1.ApiTags)('Customer Invoices'),
    (0, common_1.Controller)('customer-invoices'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [invoices_service_1.InvoicesService,
        invoice_pdf_service_1.InvoicePdfService])
], CustomerInvoicesController);
//# sourceMappingURL=customer-invoices.controller.js.map