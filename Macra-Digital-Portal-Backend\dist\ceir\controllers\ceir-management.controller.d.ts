import { CeirCertificationBodiesService } from '../services/ceir-certification-bodies.service';
import { CeirEquipmentCategoriesService } from '../services/ceir-equipment-categories.service';
import { CeirTechnicalStandardsService } from '../services/ceir-technical-standards.service';
import { CreateCeirCertificationBodyDto, UpdateCeirCertificationBodyDto } from '../dto/ceir-certification-bodies';
import { CreateCeirEquipmentCategoryDto, UpdateCeirEquipmentCategoryDto } from '../dto/ceir-equipment-categories';
import { CreateCeirTechnicalStandardDto, UpdateCeirTechnicalStandardDto } from '../dto/ceir-technical-standards';
import { CeirCertificationBodies } from '../entities/ceir-certification-bodies.entity';
import { CeirEquipmentTypeCategories } from '../entities/ceir-equipment-type-categories.entity';
import { CeirTechnicalStandards } from '../entities/ceir-technical-standards.entity';
export declare class CeirManagementController {
    private readonly ceirCertificationBodiesService;
    private readonly ceirEquipmentCategoriesService;
    private readonly ceirTechnicalStandardsService;
    constructor(ceirCertificationBodiesService: CeirCertificationBodiesService, ceirEquipmentCategoriesService: CeirEquipmentCategoriesService, ceirTechnicalStandardsService: CeirTechnicalStandardsService);
    createCertificationBody(createDto: CreateCeirCertificationBodyDto, req: any): Promise<CeirCertificationBodies>;
    findAllCertificationBodies(active?: boolean): Promise<CeirCertificationBodies[]>;
    getCertificationBodiesStatistics(): Promise<{
        total: number;
        active: number;
        inactive: number;
        byType: Record<string, number>;
        byCountry: Record<string, number>;
        byAccreditationStatus: Record<string, number>;
        macraRecognized: number;
        ceirCertifiers: number;
        expiringAccreditations: number;
    }>;
    findMacraRecognizedBodies(): Promise<CeirCertificationBodies[]>;
    findCeirCertifiers(): Promise<CeirCertificationBodies[]>;
    findExpiringAccreditations(days?: number): Promise<CeirCertificationBodies[]>;
    findOneCertificationBody(id: string): Promise<CeirCertificationBodies>;
    updateCertificationBody(id: string, updateDto: UpdateCeirCertificationBodyDto, req: any): Promise<CeirCertificationBodies>;
    removeCertificationBody(id: string): Promise<void>;
    createEquipmentCategory(createDto: CreateCeirEquipmentCategoryDto, req: any): Promise<CeirEquipmentTypeCategories>;
    findAllEquipmentCategories(active?: boolean): Promise<CeirEquipmentTypeCategories[]>;
    getEquipmentCategoriesStatistics(): Promise<{
        total: number;
        active: number;
        inactive: number;
        byType: Record<string, number>;
        requiresSar: number;
        requiresEmc: number;
        requiresRf: number;
    }>;
    findEquipmentCategoryByType(categoryType: string): Promise<CeirEquipmentTypeCategories>;
    findEquipmentCategoryByCeirCode(ceirCode: string): Promise<CeirEquipmentTypeCategories>;
    findOneEquipmentCategory(id: string): Promise<CeirEquipmentTypeCategories>;
    updateEquipmentCategory(id: string, updateDto: UpdateCeirEquipmentCategoryDto, req: any): Promise<CeirEquipmentTypeCategories>;
    removeEquipmentCategory(id: string): Promise<void>;
    createTechnicalStandard(createDto: CreateCeirTechnicalStandardDto, req: any): Promise<CeirTechnicalStandards>;
    findAllTechnicalStandards(active?: boolean): Promise<CeirTechnicalStandards[]>;
    getTechnicalStandardsStatistics(): Promise<{
        total: number;
        active: number;
        inactive: number;
        byType: Record<string, number>;
        byOrganization: Record<string, number>;
        byStatus: Record<string, number>;
        mandatory: number;
        expiring: number;
    }>;
    findTechnicalStandardByReference(reference: string): Promise<CeirTechnicalStandards>;
    findOneTechnicalStandard(id: string): Promise<CeirTechnicalStandards>;
    updateTechnicalStandard(id: string, updateDto: UpdateCeirTechnicalStandardDto, req: any): Promise<CeirTechnicalStandards>;
    removeTechnicalStandard(id: string): Promise<void>;
}
