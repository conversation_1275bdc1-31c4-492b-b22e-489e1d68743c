# User Management Guide (Admin Only)

## Overview

This section is only available to MACRA administrators and authorized staff members. Regular users (customers) cannot access these features.

### Who Can Manage Users?
- **System Administrators** - Full access to all user management features
- **Senior Officers** - Limited access to view user information
- **IT Support Staff** - Technical user management capabilities

## Managing User Accounts

### Viewing All Users
**What You'll See:**
- **User List** - Table showing all registered users
- **Search Function** - Find users by name, email, or phone number
- **Filter Options** - Filter by user type, status, or registration date
- **User Details** - Click on any user to see their full profile

**Screenshot:** `[USER_LIST_SCREENSHOT]`

### Creating New User Accounts

**When to Create Accounts:**
- New MACRA staff members
- External consultants or partners
- Special access accounts for government officials
- Test accounts for system testing

**How to Create a New User:**
1. Click **"Add New User"** button
2. Fill in the required information:
   - **Personal Details**: Name, email, phone number
   - **Account Type**: Select appropriate role (Staff, Officer, Admin, etc.)
   - **Department**: Choose the user's department
   - **Access Level**: Set permissions based on their role
3. Click **"Create Account"** button
4. The new user will receive an email with login instructions

**Screenshot:** `[ADD_USER_FORM_SCREENSHOT]`

### Managing Existing Users

**Updating User Information:**
1. Find the user in the list
2. Click **"Edit"** or **"View Details"**
3. Update necessary information
4. Save changes

**Screenshot:** `[EDIT_USER_SCREENSHOT]`

**Deactivating User Accounts:**
1. Locate the user account
2. Click **"Deactivate"** or change status to "Inactive"
3. Confirm the action
4. The user will no longer be able to login

**Reactivating User Accounts:**
1. Find the inactive user
2. Click **"Activate"** or change status to "Active"
3. The user can now login again

**Resetting User Passwords:**
1. Go to the user's profile
2. Click **"Reset Password"**
3. Choose to either:
   - Generate a temporary password
   - Send password reset email to user
4. Inform the user of the password reset

## Important Guidelines for Admins

### Security Best Practices
- 🔐 **Regular Reviews** - Review user accounts monthly
- 🚫 **Remove Unused Accounts** - Deactivate accounts of former staff
- 👥 **Appropriate Access** - Only give necessary permissions
- 📝 **Document Changes** - Keep records of account modifications
- 🔄 **Regular Backups** - Ensure user data is backed up

### User Account Policies
- **Staff Accounts** - Must use @macra.mw email addresses
- **Customer Accounts** - Can use any valid email address
- **Password Requirements** - Enforce strong password policies
- **Account Expiry** - Set expiry dates for temporary accounts
- **Two-Factor Authentication** - Enable for all staff accounts

---

*This guide is for MACRA administrators only. For general account help, see the Authentication Guide.*
