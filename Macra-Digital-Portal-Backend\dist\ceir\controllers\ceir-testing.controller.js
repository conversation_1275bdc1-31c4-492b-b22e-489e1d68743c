"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CeirTestingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const audit_interceptor_1 = require("../../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../../entities/audit-trail.entity");
const ceir_equipment_specifications_service_1 = require("../services/ceir-equipment-specifications.service");
const ceir_test_reports_service_1 = require("../services/ceir-test-reports.service");
const ceir_equipment_specifications_1 = require("../dto/ceir-equipment-specifications");
const ceir_test_reports_1 = require("../dto/ceir-test-reports");
const ceir_equipment_specifications_entity_1 = require("../entities/ceir-equipment-specifications.entity");
const ceir_test_reports_entity_1 = require("../entities/ceir-test-reports.entity");
let CeirTestingController = class CeirTestingController {
    constructor(ceirEquipmentSpecificationsService, ceirTestReportsService) {
        this.ceirEquipmentSpecificationsService = ceirEquipmentSpecificationsService;
        this.ceirTestReportsService = ceirTestReportsService;
    }
    async createEquipmentSpecification(createDto, req) {
        return this.ceirEquipmentSpecificationsService.create(createDto, req.user?.userId);
    }
    async findAllEquipmentSpecifications(active) {
        if (active === true) {
            return this.ceirEquipmentSpecificationsService.findAllActive();
        }
        return this.ceirEquipmentSpecificationsService.findAll();
    }
    async findSpecificationsByDevice(deviceId) {
        return this.ceirEquipmentSpecificationsService.findByDevice(deviceId);
    }
    async findSpecificationsByCategory(categoryId) {
        return this.ceirEquipmentSpecificationsService.findByCategory(categoryId);
    }
    async findSpecificationsByFrequency(frequency) {
        return this.ceirEquipmentSpecificationsService.findByFrequency(frequency);
    }
    async findOneEquipmentSpecification(id) {
        return this.ceirEquipmentSpecificationsService.findOne(id);
    }
    async updateEquipmentSpecification(id, updateDto, req) {
        return this.ceirEquipmentSpecificationsService.update(id, updateDto, req.user?.userId);
    }
    async removeEquipmentSpecification(id) {
        return this.ceirEquipmentSpecificationsService.remove(id);
    }
    async createTestReport(createDto, req) {
        return this.ceirTestReportsService.create(createDto, req.user?.userId);
    }
    async findAllTestReports(valid) {
        if (valid === true) {
            return this.ceirTestReportsService.findAllValid();
        }
        return this.ceirTestReportsService.findAll();
    }
    async getTestReportsStatistics() {
        return this.ceirTestReportsService.getStatistics();
    }
    async findTestReportsByDevice(deviceId) {
        return this.ceirTestReportsService.findByDevice(deviceId);
    }
    async findTestReportsByCertificationBody(bodyId) {
        return this.ceirTestReportsService.findByCertificationBody(bodyId);
    }
    async findExpiringTestReports(days) {
        return this.ceirTestReportsService.findExpiring(days || 30);
    }
    async findOneTestReport(id) {
        return this.ceirTestReportsService.findOne(id);
    }
    async updateTestReport(id, updateDto, req) {
        return this.ceirTestReportsService.update(id, updateDto, req.user?.userId);
    }
    async removeTestReport(id) {
        return this.ceirTestReportsService.remove(id);
    }
    async approveTestReport(id, approvedBy, req) {
        return this.ceirTestReportsService.approve(id, req.user.userId, approvedBy);
    }
    async rejectTestReport(id, req) {
        return this.ceirTestReportsService.reject(id, req.user.userId);
    }
    async validateTestReport(id, req) {
        return this.ceirTestReportsService.validate(id, req.user.userId);
    }
};
exports.CeirTestingController = CeirTestingController;
__decorate([
    (0, common_1.Post)('equipment-specifications'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new CEIR equipment specification' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Equipment specification created successfully',
        type: ceir_equipment_specifications_entity_1.CeirEquipmentSpecifications,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_equipment_specifications_1.CreateCeirEquipmentSpecificationDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentSpecification',
        description: 'Created CEIR equipment specification',
    }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ceir_equipment_specifications_1.CreateCeirEquipmentSpecificationDto, Object]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "createEquipmentSpecification", null);
__decorate([
    (0, common_1.Get)('equipment-specifications'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all CEIR equipment specifications' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of equipment specifications',
        type: [ceir_equipment_specifications_entity_1.CeirEquipmentSpecifications],
    }),
    (0, swagger_1.ApiQuery)({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentSpecification',
        description: 'Viewed CEIR equipment specifications',
    }),
    __param(0, (0, common_1.Query)('active')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Boolean]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findAllEquipmentSpecifications", null);
__decorate([
    (0, common_1.Get)('equipment-specifications/by-device/:deviceId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get equipment specifications by device ID' }),
    (0, swagger_1.ApiParam)({ name: 'deviceId', description: 'Device UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment specifications for device',
        type: [ceir_equipment_specifications_entity_1.CeirEquipmentSpecifications],
    }),
    __param(0, (0, common_1.Param)('deviceId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findSpecificationsByDevice", null);
__decorate([
    (0, common_1.Get)('equipment-specifications/by-category/:categoryId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get equipment specifications by category ID' }),
    (0, swagger_1.ApiParam)({ name: 'categoryId', description: 'Equipment category UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment specifications for category',
        type: [ceir_equipment_specifications_entity_1.CeirEquipmentSpecifications],
    }),
    __param(0, (0, common_1.Param)('categoryId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findSpecificationsByCategory", null);
__decorate([
    (0, common_1.Get)('equipment-specifications/by-frequency/:frequency'),
    (0, swagger_1.ApiOperation)({ summary: 'Get equipment specifications by frequency range' }),
    (0, swagger_1.ApiParam)({ name: 'frequency', description: 'Frequency in MHz' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment specifications supporting frequency',
        type: [ceir_equipment_specifications_entity_1.CeirEquipmentSpecifications],
    }),
    __param(0, (0, common_1.Param)('frequency', common_1.ParseFloatPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findSpecificationsByFrequency", null);
__decorate([
    (0, common_1.Get)('equipment-specifications/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CEIR equipment specification by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Equipment specification UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment specification found',
        type: ceir_equipment_specifications_entity_1.CeirEquipmentSpecifications,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentSpecification',
        description: 'Viewed CEIR equipment specification details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findOneEquipmentSpecification", null);
__decorate([
    (0, common_1.Patch)('equipment-specifications/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update CEIR equipment specification' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Equipment specification UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment specification updated successfully',
        type: ceir_equipment_specifications_entity_1.CeirEquipmentSpecifications,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_equipment_specifications_1.UpdateCeirEquipmentSpecificationDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentSpecification',
        description: 'Updated CEIR equipment specification',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, ceir_equipment_specifications_1.UpdateCeirEquipmentSpecificationDto, Object]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "updateEquipmentSpecification", null);
__decorate([
    (0, common_1.Delete)('equipment-specifications/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete CEIR equipment specification' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Equipment specification UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment specification deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentSpecification',
        description: 'Deleted CEIR equipment specification',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "removeEquipmentSpecification", null);
__decorate([
    (0, common_1.Post)('test-reports'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new CEIR test report' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Test report created successfully',
        type: ceir_test_reports_entity_1.CeirTestReports,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_test_reports_1.CreateCeirTestReportDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTestReport',
        description: 'Created CEIR test report',
    }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ceir_test_reports_1.CreateCeirTestReportDto, Object]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "createTestReport", null);
__decorate([
    (0, common_1.Get)('test-reports'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all CEIR test reports' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of test reports',
        type: [ceir_test_reports_entity_1.CeirTestReports],
    }),
    (0, swagger_1.ApiQuery)({ name: 'valid', required: false, type: Boolean, description: 'Filter by valid status' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTestReport',
        description: 'Viewed CEIR test reports',
    }),
    __param(0, (0, common_1.Query)('valid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Boolean]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findAllTestReports", null);
__decorate([
    (0, common_1.Get)('test-reports/statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CEIR test reports statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Test reports statistics',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTestReport',
        description: 'Viewed test reports statistics',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "getTestReportsStatistics", null);
__decorate([
    (0, common_1.Get)('test-reports/by-device/:deviceId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get test reports by device ID' }),
    (0, swagger_1.ApiParam)({ name: 'deviceId', description: 'Device UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Test reports for device',
        type: [ceir_test_reports_entity_1.CeirTestReports],
    }),
    __param(0, (0, common_1.Param)('deviceId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findTestReportsByDevice", null);
__decorate([
    (0, common_1.Get)('test-reports/by-certification-body/:bodyId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get test reports by certification body ID' }),
    (0, swagger_1.ApiParam)({ name: 'bodyId', description: 'Certification body UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Test reports by certification body',
        type: [ceir_test_reports_entity_1.CeirTestReports],
    }),
    __param(0, (0, common_1.Param)('bodyId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findTestReportsByCertificationBody", null);
__decorate([
    (0, common_1.Get)('test-reports/expiring'),
    (0, swagger_1.ApiOperation)({ summary: 'Get test reports expiring soon' }),
    (0, swagger_1.ApiQuery)({ name: 'days', required: false, type: Number, description: 'Number of days to look ahead (default: 30)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of expiring test reports',
        type: [ceir_test_reports_entity_1.CeirTestReports],
    }),
    __param(0, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findExpiringTestReports", null);
__decorate([
    (0, common_1.Get)('test-reports/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CEIR test report by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Test report UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Test report found',
        type: ceir_test_reports_entity_1.CeirTestReports,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTestReport',
        description: 'Viewed CEIR test report details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "findOneTestReport", null);
__decorate([
    (0, common_1.Patch)('test-reports/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update CEIR test report' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Test report UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Test report updated successfully',
        type: ceir_test_reports_entity_1.CeirTestReports,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_test_reports_1.UpdateCeirTestReportDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTestReport',
        description: 'Updated CEIR test report',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, ceir_test_reports_1.UpdateCeirTestReportDto, Object]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "updateTestReport", null);
__decorate([
    (0, common_1.Delete)('test-reports/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete CEIR test report' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Test report UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Test report deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTestReport',
        description: 'Deleted CEIR test report',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "removeTestReport", null);
__decorate([
    (0, common_1.Patch)('test-reports/:id/approve'),
    (0, swagger_1.ApiOperation)({ summary: 'Approve test report' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Test report UUID' }),
    (0, swagger_1.ApiQuery)({ name: 'approvedBy', description: 'Name of approver' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Test report approved successfully',
        type: ceir_test_reports_entity_1.CeirTestReports,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTestReport',
        description: 'Approved CEIR test report',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('approvedBy')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "approveTestReport", null);
__decorate([
    (0, common_1.Patch)('test-reports/:id/reject'),
    (0, swagger_1.ApiOperation)({ summary: 'Reject test report' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Test report UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Test report rejected successfully',
        type: ceir_test_reports_entity_1.CeirTestReports,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTestReport',
        description: 'Rejected CEIR test report',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "rejectTestReport", null);
__decorate([
    (0, common_1.Patch)('test-reports/:id/validate'),
    (0, swagger_1.ApiOperation)({ summary: 'Validate test report' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Test report UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Test report validated successfully',
        type: ceir_test_reports_entity_1.CeirTestReports,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTestReport',
        description: 'Validated CEIR test report',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CeirTestingController.prototype, "validateTestReport", null);
exports.CeirTestingController = CeirTestingController = __decorate([
    (0, swagger_1.ApiTags)('CEIR Testing & Specifications'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('ceir/testing'),
    __metadata("design:paramtypes", [ceir_equipment_specifications_service_1.CeirEquipmentSpecificationsService,
        ceir_test_reports_service_1.CeirTestReportsService])
], CeirTestingController);
//# sourceMappingURL=ceir-testing.controller.js.map