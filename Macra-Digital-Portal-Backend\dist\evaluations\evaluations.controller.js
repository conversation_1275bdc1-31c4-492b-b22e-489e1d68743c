"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EvaluationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nestjs_paginate_1 = require("nestjs-paginate");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const evaluations_service_1 = require("./evaluations.service");
const create_evaluation_dto_1 = require("../dto/evaluations/create-evaluation.dto");
const update_evaluation_dto_1 = require("../dto/evaluations/update-evaluation.dto");
const evaluations_entity_1 = require("../entities/evaluations.entity");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let EvaluationsController = class EvaluationsController {
    constructor(evaluationsService) {
        this.evaluationsService = evaluationsService;
    }
    async create(createEvaluationDto, req) {
        return this.evaluationsService.create(createEvaluationDto, req.user.userId);
    }
    async findAll(query) {
        return this.evaluationsService.findAll(query);
    }
    async getStats() {
        return this.evaluationsService.getEvaluationStats();
    }
    async healthCheck() {
        try {
            const count = await this.evaluationsService.getEvaluationCount();
            return {
                status: 'healthy',
                database: 'connected',
                evaluationCount: count,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            console.error('Health check failed:', error);
            return {
                status: 'unhealthy',
                database: 'error',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    async findOne(id) {
        return this.evaluationsService.findOne(id);
    }
    async findByApplication(applicationId) {
        try {
            console.log(`📋 EvaluationsController: Getting evaluation for application ${applicationId}`);
            const evaluation = await this.evaluationsService.findByApplication(applicationId);
            if (!evaluation) {
                console.log(`ℹ️ EvaluationsController: No evaluation found for application ${applicationId}`);
                return {
                    success: true,
                    message: 'No evaluation found for this application',
                    data: null,
                    meta: {
                        applicationId,
                        evaluationExists: false
                    }
                };
            }
            console.log(`✅ EvaluationsController: Found evaluation ${evaluation.evaluation_id}`);
            return {
                success: true,
                message: 'Evaluation retrieved successfully',
                data: evaluation,
                meta: {
                    applicationId,
                    evaluationExists: true,
                    evaluationId: evaluation.evaluation_id
                }
            };
        }
        catch (error) {
            console.error(`❌ EvaluationsController: Error getting evaluation for application ${applicationId}:`, error);
            return {
                success: false,
                message: error.message || 'Failed to retrieve evaluation',
                data: null,
                meta: {
                    applicationId,
                    error: error.name || 'UnknownError'
                }
            };
        }
    }
    async findCriteria(id) {
        return this.evaluationsService.findCriteria(id);
    }
    async update(id, updateEvaluationDto, req) {
        return this.evaluationsService.update(id, updateEvaluationDto, req.user.userId);
    }
    async remove(id) {
        return this.evaluationsService.remove(id);
    }
};
exports.EvaluationsController = EvaluationsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new evaluation' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Evaluation created successfully',
        type: evaluations_entity_1.Evaluations,
    }),
    (0, swagger_1.ApiBody)({ type: create_evaluation_dto_1.CreateEvaluationDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Evaluation',
        description: 'Created new evaluation',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_evaluation_dto_1.CreateEvaluationDto, Object]),
    __metadata("design:returntype", Promise)
], EvaluationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all evaluations with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of evaluations retrieved successfully',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, type: String }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EvaluationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get evaluation statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Evaluation statistics retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EvaluationsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Health check for evaluations service' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Service health status',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EvaluationsController.prototype, "healthCheck", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get evaluation by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Evaluation UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Evaluation retrieved successfully',
        type: evaluations_entity_1.Evaluations,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Evaluation not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EvaluationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get evaluation by application ID' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Evaluation retrieved successfully',
        type: evaluations_entity_1.Evaluations,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Evaluation not found' }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EvaluationsController.prototype, "findByApplication", null);
__decorate([
    (0, common_1.Get)(':id/criteria'),
    (0, swagger_1.ApiOperation)({ summary: 'Get evaluation criteria by evaluation ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Evaluation UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Evaluation criteria retrieved successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EvaluationsController.prototype, "findCriteria", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update evaluation' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Evaluation UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Evaluation updated successfully',
        type: evaluations_entity_1.Evaluations,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Evaluation not found' }),
    (0, swagger_1.ApiBody)({ type: update_evaluation_dto_1.UpdateEvaluationDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Evaluation',
        description: 'Updated evaluation',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_evaluation_dto_1.UpdateEvaluationDto, Object]),
    __metadata("design:returntype", Promise)
], EvaluationsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete evaluation' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Evaluation UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Evaluation deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Evaluation not found' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'Evaluation',
        description: 'Deleted evaluation',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], EvaluationsController.prototype, "remove", null);
exports.EvaluationsController = EvaluationsController = __decorate([
    (0, swagger_1.ApiTags)('Evaluations'),
    (0, common_1.Controller)('evaluations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [evaluations_service_1.EvaluationsService])
], EvaluationsController);
//# sourceMappingURL=evaluations.controller.js.map