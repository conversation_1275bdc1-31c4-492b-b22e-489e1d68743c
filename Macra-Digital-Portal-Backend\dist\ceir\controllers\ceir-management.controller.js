"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CeirManagementController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const audit_interceptor_1 = require("../../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../../entities/audit-trail.entity");
const ceir_certification_bodies_service_1 = require("../services/ceir-certification-bodies.service");
const ceir_equipment_categories_service_1 = require("../services/ceir-equipment-categories.service");
const ceir_technical_standards_service_1 = require("../services/ceir-technical-standards.service");
const ceir_certification_bodies_1 = require("../dto/ceir-certification-bodies");
const ceir_equipment_categories_1 = require("../dto/ceir-equipment-categories");
const ceir_technical_standards_1 = require("../dto/ceir-technical-standards");
const ceir_certification_bodies_entity_1 = require("../entities/ceir-certification-bodies.entity");
const ceir_equipment_type_categories_entity_1 = require("../entities/ceir-equipment-type-categories.entity");
const ceir_technical_standards_entity_1 = require("../entities/ceir-technical-standards.entity");
let CeirManagementController = class CeirManagementController {
    constructor(ceirCertificationBodiesService, ceirEquipmentCategoriesService, ceirTechnicalStandardsService) {
        this.ceirCertificationBodiesService = ceirCertificationBodiesService;
        this.ceirEquipmentCategoriesService = ceirEquipmentCategoriesService;
        this.ceirTechnicalStandardsService = ceirTechnicalStandardsService;
    }
    async createCertificationBody(createDto, req) {
        return this.ceirCertificationBodiesService.create(createDto, req.user?.userId);
    }
    async findAllCertificationBodies(active) {
        if (active === true) {
            return this.ceirCertificationBodiesService.findAllActive();
        }
        return this.ceirCertificationBodiesService.findAll();
    }
    async getCertificationBodiesStatistics() {
        return this.ceirCertificationBodiesService.getStatistics();
    }
    async findMacraRecognizedBodies() {
        return this.ceirCertificationBodiesService.findMacraRecognized();
    }
    async findCeirCertifiers() {
        return this.ceirCertificationBodiesService.findCeirCertifiers();
    }
    async findExpiringAccreditations(days) {
        return this.ceirCertificationBodiesService.findExpiringAccreditations(days || 30);
    }
    async findOneCertificationBody(id) {
        return this.ceirCertificationBodiesService.findOne(id);
    }
    async updateCertificationBody(id, updateDto, req) {
        return this.ceirCertificationBodiesService.update(id, updateDto, req.user?.userId);
    }
    async removeCertificationBody(id) {
        return this.ceirCertificationBodiesService.remove(id);
    }
    async createEquipmentCategory(createDto, req) {
        return this.ceirEquipmentCategoriesService.create(createDto, req.user?.userId);
    }
    async findAllEquipmentCategories(active) {
        if (active === true) {
            return this.ceirEquipmentCategoriesService.findAllActive();
        }
        return this.ceirEquipmentCategoriesService.findAll();
    }
    async getEquipmentCategoriesStatistics() {
        return this.ceirEquipmentCategoriesService.getStatistics();
    }
    async findEquipmentCategoryByType(categoryType) {
        return this.ceirEquipmentCategoriesService.findByType(categoryType);
    }
    async findEquipmentCategoryByCeirCode(ceirCode) {
        return this.ceirEquipmentCategoriesService.findByCeirCode(ceirCode);
    }
    async findOneEquipmentCategory(id) {
        return this.ceirEquipmentCategoriesService.findOne(id);
    }
    async updateEquipmentCategory(id, updateDto, req) {
        return this.ceirEquipmentCategoriesService.update(id, updateDto, req.user?.userId);
    }
    async removeEquipmentCategory(id) {
        return this.ceirEquipmentCategoriesService.remove(id);
    }
    async createTechnicalStandard(createDto, req) {
        return this.ceirTechnicalStandardsService.create(createDto, req.user?.userId);
    }
    async findAllTechnicalStandards(active) {
        if (active === true) {
            return this.ceirTechnicalStandardsService.findAllActive();
        }
        return this.ceirTechnicalStandardsService.findAll();
    }
    async getTechnicalStandardsStatistics() {
        return this.ceirTechnicalStandardsService.getStatistics();
    }
    async findTechnicalStandardByReference(reference) {
        return this.ceirTechnicalStandardsService.findByReference(reference);
    }
    async findOneTechnicalStandard(id) {
        return this.ceirTechnicalStandardsService.findOne(id);
    }
    async updateTechnicalStandard(id, updateDto, req) {
        return this.ceirTechnicalStandardsService.update(id, updateDto, req.user?.userId);
    }
    async removeTechnicalStandard(id) {
        return this.ceirTechnicalStandardsService.remove(id);
    }
};
exports.CeirManagementController = CeirManagementController;
__decorate([
    (0, common_1.Post)('certification-bodies'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new CEIR certification body' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Certification body created successfully',
        type: ceir_certification_bodies_entity_1.CeirCertificationBodies,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_certification_bodies_1.CreateCeirCertificationBodyDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirCertificationBody',
        description: 'Created CEIR certification body',
    }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ceir_certification_bodies_1.CreateCeirCertificationBodyDto, Object]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "createCertificationBody", null);
__decorate([
    (0, common_1.Get)('certification-bodies'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all CEIR certification bodies' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of certification bodies',
        type: [ceir_certification_bodies_entity_1.CeirCertificationBodies],
    }),
    (0, swagger_1.ApiQuery)({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirCertificationBody',
        description: 'Viewed CEIR certification bodies',
    }),
    __param(0, (0, common_1.Query)('active')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Boolean]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findAllCertificationBodies", null);
__decorate([
    (0, common_1.Get)('certification-bodies/statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CEIR certification bodies statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Certification bodies statistics',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirCertificationBody',
        description: 'Viewed certification bodies statistics',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "getCertificationBodiesStatistics", null);
__decorate([
    (0, common_1.Get)('certification-bodies/macra-recognized'),
    (0, swagger_1.ApiOperation)({ summary: 'Get MACRA recognized certification bodies' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of MACRA recognized certification bodies',
        type: [ceir_certification_bodies_entity_1.CeirCertificationBodies],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findMacraRecognizedBodies", null);
__decorate([
    (0, common_1.Get)('certification-bodies/ceir-certifiers'),
    (0, swagger_1.ApiOperation)({ summary: 'Get certification bodies that can issue CEIR certificates' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of CEIR certificate issuers',
        type: [ceir_certification_bodies_entity_1.CeirCertificationBodies],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findCeirCertifiers", null);
__decorate([
    (0, common_1.Get)('certification-bodies/expiring-accreditations'),
    (0, swagger_1.ApiOperation)({ summary: 'Get certification bodies with expiring accreditations' }),
    (0, swagger_1.ApiQuery)({ name: 'days', required: false, type: Number, description: 'Number of days to look ahead (default: 30)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of certification bodies with expiring accreditations',
        type: [ceir_certification_bodies_entity_1.CeirCertificationBodies],
    }),
    __param(0, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findExpiringAccreditations", null);
__decorate([
    (0, common_1.Get)('certification-bodies/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CEIR certification body by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Certification body UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Certification body found',
        type: ceir_certification_bodies_entity_1.CeirCertificationBodies,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirCertificationBody',
        description: 'Viewed CEIR certification body details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findOneCertificationBody", null);
__decorate([
    (0, common_1.Patch)('certification-bodies/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update CEIR certification body' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Certification body UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Certification body updated successfully',
        type: ceir_certification_bodies_entity_1.CeirCertificationBodies,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_certification_bodies_1.UpdateCeirCertificationBodyDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirCertificationBody',
        description: 'Updated CEIR certification body',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, ceir_certification_bodies_1.UpdateCeirCertificationBodyDto, Object]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "updateCertificationBody", null);
__decorate([
    (0, common_1.Delete)('certification-bodies/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete CEIR certification body' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Certification body UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Certification body deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirCertificationBody',
        description: 'Deleted CEIR certification body',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "removeCertificationBody", null);
__decorate([
    (0, common_1.Post)('equipment-categories'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new CEIR equipment category' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Equipment category created successfully',
        type: ceir_equipment_type_categories_entity_1.CeirEquipmentTypeCategories,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_equipment_categories_1.CreateCeirEquipmentCategoryDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentCategory',
        description: 'Created CEIR equipment category',
    }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ceir_equipment_categories_1.CreateCeirEquipmentCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "createEquipmentCategory", null);
__decorate([
    (0, common_1.Get)('equipment-categories'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all CEIR equipment categories' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of equipment categories',
        type: [ceir_equipment_type_categories_entity_1.CeirEquipmentTypeCategories],
    }),
    (0, swagger_1.ApiQuery)({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentCategory',
        description: 'Viewed CEIR equipment categories',
    }),
    __param(0, (0, common_1.Query)('active')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Boolean]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findAllEquipmentCategories", null);
__decorate([
    (0, common_1.Get)('equipment-categories/statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CEIR equipment categories statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment categories statistics',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentCategory',
        description: 'Viewed equipment categories statistics',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "getEquipmentCategoriesStatistics", null);
__decorate([
    (0, common_1.Get)('equipment-categories/by-type/:categoryType'),
    (0, swagger_1.ApiOperation)({ summary: 'Get equipment category by type' }),
    (0, swagger_1.ApiParam)({ name: 'categoryType', description: 'Category type to find' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment category found',
        type: ceir_equipment_type_categories_entity_1.CeirEquipmentTypeCategories,
    }),
    __param(0, (0, common_1.Param)('categoryType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findEquipmentCategoryByType", null);
__decorate([
    (0, common_1.Get)('equipment-categories/by-ceir-code/:ceirCode'),
    (0, swagger_1.ApiOperation)({ summary: 'Get equipment category by CEIR code' }),
    (0, swagger_1.ApiParam)({ name: 'ceirCode', description: 'CEIR standard code to find' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment category found',
        type: ceir_equipment_type_categories_entity_1.CeirEquipmentTypeCategories,
    }),
    __param(0, (0, common_1.Param)('ceirCode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findEquipmentCategoryByCeirCode", null);
__decorate([
    (0, common_1.Get)('equipment-categories/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CEIR equipment category by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Equipment category UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment category found',
        type: ceir_equipment_type_categories_entity_1.CeirEquipmentTypeCategories,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentCategory',
        description: 'Viewed CEIR equipment category details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findOneEquipmentCategory", null);
__decorate([
    (0, common_1.Patch)('equipment-categories/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update CEIR equipment category' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Equipment category UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment category updated successfully',
        type: ceir_equipment_type_categories_entity_1.CeirEquipmentTypeCategories,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_equipment_categories_1.UpdateCeirEquipmentCategoryDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentCategory',
        description: 'Updated CEIR equipment category',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, ceir_equipment_categories_1.UpdateCeirEquipmentCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "updateEquipmentCategory", null);
__decorate([
    (0, common_1.Delete)('equipment-categories/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete CEIR equipment category' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Equipment category UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Equipment category deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirEquipmentCategory',
        description: 'Deleted CEIR equipment category',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "removeEquipmentCategory", null);
__decorate([
    (0, common_1.Post)('technical-standards'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new CEIR technical standard' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Technical standard created successfully',
        type: ceir_technical_standards_entity_1.CeirTechnicalStandards,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_technical_standards_1.CreateCeirTechnicalStandardDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTechnicalStandard',
        description: 'Created CEIR technical standard',
    }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ceir_technical_standards_1.CreateCeirTechnicalStandardDto, Object]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "createTechnicalStandard", null);
__decorate([
    (0, common_1.Get)('technical-standards'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all CEIR technical standards' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of technical standards',
        type: [ceir_technical_standards_entity_1.CeirTechnicalStandards],
    }),
    (0, swagger_1.ApiQuery)({ name: 'active', required: false, type: Boolean, description: 'Filter by active status' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTechnicalStandard',
        description: 'Viewed CEIR technical standards',
    }),
    __param(0, (0, common_1.Query)('active')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Boolean]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findAllTechnicalStandards", null);
__decorate([
    (0, common_1.Get)('technical-standards/statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CEIR technical standards statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Technical standards statistics',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTechnicalStandard',
        description: 'Viewed technical standards statistics',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "getTechnicalStandardsStatistics", null);
__decorate([
    (0, common_1.Get)('technical-standards/by-reference/:reference'),
    (0, swagger_1.ApiOperation)({ summary: 'Get technical standard by reference' }),
    (0, swagger_1.ApiParam)({ name: 'reference', description: 'Standard reference to find' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Technical standard found',
        type: ceir_technical_standards_entity_1.CeirTechnicalStandards,
    }),
    __param(0, (0, common_1.Param)('reference')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findTechnicalStandardByReference", null);
__decorate([
    (0, common_1.Get)('technical-standards/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CEIR technical standard by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Technical standard UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Technical standard found',
        type: ceir_technical_standards_entity_1.CeirTechnicalStandards,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTechnicalStandard',
        description: 'Viewed CEIR technical standard details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "findOneTechnicalStandard", null);
__decorate([
    (0, common_1.Patch)('technical-standards/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update CEIR technical standard' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Technical standard UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Technical standard updated successfully',
        type: ceir_technical_standards_entity_1.CeirTechnicalStandards,
    }),
    (0, swagger_1.ApiBody)({ type: ceir_technical_standards_1.UpdateCeirTechnicalStandardDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTechnicalStandard',
        description: 'Updated CEIR technical standard',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, ceir_technical_standards_1.UpdateCeirTechnicalStandardDto, Object]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "updateTechnicalStandard", null);
__decorate([
    (0, common_1.Delete)('technical-standards/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete CEIR technical standard' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Technical standard UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Technical standard deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'CeirTechnicalStandard',
        description: 'Deleted CEIR technical standard',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CeirManagementController.prototype, "removeTechnicalStandard", null);
exports.CeirManagementController = CeirManagementController = __decorate([
    (0, swagger_1.ApiTags)('CEIR Management'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('ceir/management'),
    __metadata("design:paramtypes", [ceir_certification_bodies_service_1.CeirCertificationBodiesService,
        ceir_equipment_categories_service_1.CeirEquipmentCategoriesService,
        ceir_technical_standards_service_1.CeirTechnicalStandardsService])
], CeirManagementController);
//# sourceMappingURL=ceir-management.controller.js.map