(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/services/licenseService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "licenseService": ()=>licenseService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
;
;
const licenseService = {
    // Get all licenses with pagination
    async getLicenses (params) {
        const queryParams = new URLSearchParams();
        if (params === null || params === void 0 ? void 0 : params.page) queryParams.append('page', params.page.toString());
        if (params === null || params === void 0 ? void 0 : params.limit) queryParams.append('limit', params.limit.toString());
        if (params === null || params === void 0 ? void 0 : params.search) queryParams.append('search', params.search);
        if (params === null || params === void 0 ? void 0 : params.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params === null || params === void 0 ? void 0 : params.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        if (params === null || params === void 0 ? void 0 : params.status) queryParams.append('filter.status', params.status);
        if (params === null || params === void 0 ? void 0 : params.licenseType) queryParams.append('filter.licenseType', params.licenseType);
        if (params === null || params === void 0 ? void 0 : params.dateRange) queryParams.append('filter.dateRange', params.dateRange);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/licenses?".concat(queryParams.toString()));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get single license by ID
    async getLicense (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/licenses/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get license by application ID
    async getLicenseByApplication (applicationId) {
        try {
            // Since there's no direct endpoint, we'll get all licenses and filter by application_id
            // In a real implementation, you'd want to add this endpoint to the backend
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/licenses?filter.application_id=".concat(applicationId));
            const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            if (result.data && result.data.length > 0) {
                return result.data[0]; // Return the first (and should be only) license for this application
            }
            return null;
        } catch (error) {
            console.error('Error getting license by application:', error);
            return null;
        }
    },
    // Get licenses by applicant
    async getLicensesByApplicant (applicantId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/licenses/by-applicant/".concat(applicantId));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get license by license number
    async getLicenseByNumber (licenseNumber) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/licenses/by-number/".concat(licenseNumber));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Download license PDF
    async downloadLicensePDF (licenseId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/licenses/".concat(licenseId, "/pdf"), {
                responseType: 'blob',
                headers: {
                    'Accept': 'application/pdf'
                }
            });
            return response.data;
        } catch (error) {
            console.error('Error downloading license PDF:', error);
            throw error;
        }
    },
    // Get license statistics
    async getLicenseStats () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/licenses/stats');
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get licenses expiring soon
    async getExpiringSoon () {
        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/licenses/expiring-soon?days=".concat(days));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new license (admin only)
    async createLicense (data) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/licenses', data);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update license (admin only)
    async updateLicense (id, data) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put("/licenses/".concat(id), data);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Delete license (admin only)
    async deleteLicense (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete("/licenses/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/cacheService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Cache service for API responses to reduce rate limiting
__turbopack_context__.s({
    "CACHE_KEYS": ()=>CACHE_KEYS,
    "CACHE_TTL": ()=>CACHE_TTL,
    "cacheService": ()=>cacheService,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class CacheService {
    /**
   * Set cache item with TTL
   */ set(key, data) {
        let ttl = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : this.defaultTTL;
        const now = Date.now();
        const item = {
            data,
            timestamp: now,
            expiresAt: now + ttl
        };
        this.cache.set(key, item);
        console.log("Cache SET: ".concat(key, " (expires in ").concat(ttl, "ms)"));
    }
    /**
   * Get cache item if not expired
   */ get(key) {
        const item = this.cache.get(key);
        if (!item) {
            console.log("Cache MISS: ".concat(key));
            return null;
        }
        const now = Date.now();
        if (now > item.expiresAt) {
            console.log("Cache EXPIRED: ".concat(key));
            this.cache.delete(key);
            return null;
        }
        console.log("Cache HIT: ".concat(key, " (age: ").concat(now - item.timestamp, "ms)"));
        return item.data;
    }
    /**
   * Check if cache has valid item
   */ has(key) {
        return this.get(key) !== null;
    }
    /**
   * Delete cache item
   */ delete(key) {
        console.log("Cache DELETE: ".concat(key));
        return this.cache.delete(key);
    }
    /**
   * Clear all cache
   */ clear() {
        console.log('Cache CLEAR: All items');
        this.cache.clear();
    }
    /**
   * Get cache stats
   */ getStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
    /**
   * Clean expired items
   */ cleanup() {
        const now = Date.now();
        let cleaned = 0;
        for (const [key, item] of this.cache.entries()){
            if (now > item.expiresAt) {
                this.cache.delete(key);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            console.log("Cache CLEANUP: Removed ".concat(cleaned, " expired items"));
        }
    }
    /**
   * Get or set pattern - fetch data if not cached
   */ async getOrSet(key, fetcher) {
        let ttl = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : this.defaultTTL;
        // Try to get from cache first
        const cached = this.get(key);
        if (cached !== null) {
            return cached;
        }
        // Fetch fresh data
        console.log("Cache FETCH: ".concat(key));
        const data = await fetcher();
        // Store in cache
        this.set(key, data, ttl);
        return data;
    }
    /**
   * Invalidate cache by pattern
   */ invalidatePattern(pattern) {
        const regex = new RegExp(pattern);
        let invalidated = 0;
        for (const key of this.cache.keys()){
            if (regex.test(key)) {
                this.cache.delete(key);
                invalidated++;
            }
        }
        if (invalidated > 0) {
            console.log("Cache INVALIDATE: Removed ".concat(invalidated, " items matching pattern: ").concat(pattern));
        }
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "cache", new Map());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "defaultTTL", 5 * 60 * 1000); // 5 minutes default TTL
    }
}
const cacheService = new CacheService();
const CACHE_KEYS = {
    LICENSE_TYPES: 'license-types',
    LICENSE_CATEGORIES: 'license-categories',
    LICENSE_CATEGORIES_BY_TYPE: (typeId)=>"license-categories-type-".concat(typeId),
    USER_APPLICATIONS: 'user-applications',
    APPLICATION: (id)=>"application-".concat(id)
};
const CACHE_TTL = {
    SHORT: 2 * 60 * 1000,
    MEDIUM: 5 * 60 * 1000,
    LONG: 15 * 60 * 1000,
    VERY_LONG: 60 * 60 * 1000
};
// Auto cleanup every 5 minutes
setInterval(()=>{
    cacheService.cleanup();
}, 5 * 60 * 1000);
const __TURBOPACK__default__export__ = cacheService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/licenseTypeService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "licenseTypeService": ()=>licenseTypeService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/cacheService.ts [app-client] (ecmascript)");
;
;
;
const licenseTypeService = {
    // Get all license types with pagination
    async getLicenseTypes () {
        let query = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
        const params = new URLSearchParams();
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());
        if (query.search) params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach((sort)=>params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach((search)=>params.append('searchBy', search));
        }
        if (query.filter) {
            Object.entries(query.filter).forEach((param)=>{
                let [key, value] = param;
                if (Array.isArray(value)) {
                    value.forEach((v)=>params.append("filter.".concat(key), v));
                } else {
                    params.set("filter.".concat(key), value);
                }
            });
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/license-types?".concat(params.toString()));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get license type by ID
    async getLicenseType (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/license-types/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get license type by ID
    async getLicenseTypeByCode (code) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get("/license-types/by-code/".concat(code));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Create new license type
    async createLicenseType (licenseTypeData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/license-types', licenseTypeData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Update license type
    async updateLicenseType (id, licenseTypeData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put("/license-types/".concat(id), licenseTypeData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Delete license type
    async deleteLicenseType (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete("/license-types/".concat(id));
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
    },
    // Get all license types (simple list for dropdowns) with caching
    async getAllLicenseTypes () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEYS"].LICENSE_TYPES, async ()=>{
            console.log('Fetching license types from API...');
            // Reduce limit to avoid rate limiting
            const response = await this.getLicenseTypes({
                limit: 100
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].LONG // Cache for 15 minutes
        );
    },
    // Get license types for sidebar navigation
    async getNavigationItems () {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/license-types/navigation/sidebar');
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('LicenseTypeService.getNavigationItems error:', error);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/Pagination.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
;
const Pagination = (param)=>{
    let { meta, onPageChange, onPageSizeChange, showFirstLast = true, showPageSizeSelector = true, showInfo = true, maxVisiblePages = 7, pageSizeOptions = [
        10,
        25,
        50,
        100
    ], className = '' } = param;
    const { currentPage, totalPages, totalItems, itemsPerPage } = meta;
    // Don't render if there's only one page or no pages and no additional features
    if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {
        return null;
    }
    // Calculate current items range
    const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);
    // Calculate which pages to show
    const getVisiblePages = ()=>{
        const pages = [];
        // If total pages is less than or equal to maxVisiblePages, show all
        if (totalPages <= maxVisiblePages) {
            for(let i = 1; i <= totalPages; i++){
                pages.push(i);
            }
            return pages;
        }
        // Always show first page
        pages.push(1);
        // Calculate start and end of the visible range around current page
        const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current
        let startPage = Math.max(2, currentPage - sidePages);
        let endPage = Math.min(totalPages - 1, currentPage + sidePages);
        // Adjust if we're near the beginning
        if (currentPage <= sidePages + 2) {
            startPage = 2;
            endPage = Math.min(totalPages - 1, maxVisiblePages - 1);
        }
        // Adjust if we're near the end
        if (currentPage >= totalPages - sidePages - 1) {
            startPage = Math.max(2, totalPages - maxVisiblePages + 2);
            endPage = totalPages - 1;
        }
        // Add ellipsis after first page if needed
        if (startPage > 2) {
            pages.push('...');
        }
        // Add pages in the visible range
        for(let i = startPage; i <= endPage; i++){
            pages.push(i);
        }
        // Add ellipsis before last page if needed
        if (endPage < totalPages - 1) {
            pages.push('...');
        }
        // Always show last page (if it's not already included)
        if (totalPages > 1) {
            pages.push(totalPages);
        }
        return pages;
    };
    const visiblePages = getVisiblePages();
    const handlePageClick = (page)=>{
        if (typeof page === 'number' && page !== currentPage) {
            onPageChange(page);
        }
    };
    const handlePageSizeChange = (event)=>{
        const newPageSize = parseInt(event.target.value);
        if (onPageSizeChange) {
            onPageSizeChange(newPageSize);
        }
    };
    const handlePrevious = ()=>{
        if (currentPage > 1) {
            onPageChange(currentPage - 1);
        }
    };
    const handleNext = ()=>{
        if (currentPage < totalPages) {
            onPageChange(currentPage + 1);
        }
    };
    const handleFirst = ()=>{
        if (currentPage !== 1) {
            onPageChange(1);
        }
    };
    const handleLast = ()=>{
        if (currentPage !== totalPages) {
            onPageChange(totalPages);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ".concat(className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4",
                children: [
                    showInfo && totalItems > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-sm text-gray-700 dark:text-gray-300",
                        children: [
                            "Showing ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: startItem
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 152,
                                columnNumber: 21
                            }, ("TURBOPACK compile-time value", void 0)),
                            " to",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: endItem
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 153,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            " of",
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: totalItems
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            " results"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 151,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    showPageSizeSelector && onPageSizeChange && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                            value: itemsPerPage,
                            onChange: handlePageSizeChange,
                            className: "px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
                            children: pageSizeOptions.map((size)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: size,
                                    children: [
                                        size,
                                        " per page"
                                    ]
                                }, size, true, {
                                    fileName: "[project]/src/components/common/Pagination.tsx",
                                    lineNumber: 167,
                                    columnNumber: 17
                                }, ("TURBOPACK compile-time value", void 0)))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 161,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 160,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Pagination.tsx",
                lineNumber: 148,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            totalPages > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                className: "flex items-center space-x-1",
                "aria-label": "Pagination",
                children: [
                    showFirstLast && currentPage > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleFirst,
                        className: "inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300",
                        "aria-label": "Go to first page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-skip-back-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 186,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 181,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handlePrevious,
                        disabled: currentPage === 1,
                        className: "inline-flex items-center px-2 py-2 text-sm font-medium border ".concat(currentPage === 1 ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300', " ").concat(showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'),
                        "aria-label": "Go to previous page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-left-s-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 201,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 191,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    visiblePages.map((page, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                            children: page === '...' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400",
                                children: "..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 208,
                                columnNumber: 17
                            }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>handlePageClick(page),
                                className: "inline-flex items-center px-4 py-2 text-sm font-medium border ".concat(page === currentPage ? 'text-white bg-red-600 border-red-600 hover:bg-red-700' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'),
                                "aria-label": "Go to page ".concat(page),
                                "aria-current": page === currentPage ? 'page' : undefined,
                                children: page
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Pagination.tsx",
                                lineNumber: 212,
                                columnNumber: 17
                            }, ("TURBOPACK compile-time value", void 0))
                        }, index, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 206,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleNext,
                        disabled: currentPage === totalPages,
                        className: "inline-flex items-center px-2 py-2 text-sm font-medium border ".concat(currentPage === totalPages ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300', " ").concat(showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'),
                        "aria-label": "Go to next page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-right-s-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 239,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 229,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    showFirstLast && currentPage < totalPages && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleLast,
                        className: "inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300",
                        "aria-label": "Go to last page",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-skip-forward-line"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Pagination.tsx",
                            lineNumber: 249,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Pagination.tsx",
                        lineNumber: 244,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Pagination.tsx",
                lineNumber: 178,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/Pagination.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = Pagination;
const __TURBOPACK__default__export__ = Pagination;
var _c;
__turbopack_context__.k.register(_c, "Pagination");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/DataTable.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>DataTable
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Pagination.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function DataTable(param) {
    let { columns, data, loading = false, onQueryChange, searchPlaceholder = "Search...", className = "", emptyStateIcon = "ri-inbox-line", emptyStateMessage = "No data found" } = param;
    _s();
    const [query, setQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        page: 1,
        limit: 10,
        search: '',
        sortBy: []
    });
    const [searchInput, setSearchInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const handleSearch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DataTable.useCallback[handleSearch]": (search)=>{
            try {
                const newQuery = {
                    ...query,
                    search,
                    page: 1
                };
                setQuery(newQuery);
                onQueryChange(newQuery);
            } catch (error) {
                console.error('Error handling search:', error);
            }
        }
    }["DataTable.useCallback[handleSearch]"], [
        query,
        onQueryChange
    ]);
    // Debounced search effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DataTable.useEffect": ()=>{
            const timeoutId = setTimeout({
                "DataTable.useEffect.timeoutId": ()=>{
                    if (searchInput !== query.search) {
                        handleSearch(searchInput);
                    }
                }
            }["DataTable.useEffect.timeoutId"], 300);
            return ({
                "DataTable.useEffect": ()=>clearTimeout(timeoutId)
            })["DataTable.useEffect"];
        }
    }["DataTable.useEffect"], [
        searchInput,
        query.search,
        handleSearch
    ]);
    const handleSort = (columnKey)=>{
        var _query_sortBy;
        const currentSort = (_query_sortBy = query.sortBy) === null || _query_sortBy === void 0 ? void 0 : _query_sortBy.find((sort)=>sort.startsWith(columnKey));
        let newSortBy = [];
        if (!currentSort) {
            newSortBy = [
                "".concat(columnKey, ":ASC")
            ];
        } else if (currentSort.endsWith(':ASC')) {
            newSortBy = [
                "".concat(columnKey, ":DESC")
            ];
        } else {
            newSortBy = [];
        }
        const newQuery = {
            ...query,
            sortBy: newSortBy,
            page: 1
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    const handlePageChange = (page)=>{
        const newQuery = {
            ...query,
            page
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    const getSortDirection = (columnKey)=>{
        var _query_sortBy;
        const currentSort = (_query_sortBy = query.sortBy) === null || _query_sortBy === void 0 ? void 0 : _query_sortBy.find((sort)=>sort.startsWith(columnKey));
        if (!currentSort) return null;
        return currentSort.endsWith(':ASC') ? 'asc' : 'desc';
    };
    // Handle null data case early
    if (!data) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ".concat(className),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-6 text-center text-gray-500 dark:text-gray-400",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 125,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "ml-2",
                            children: "Loading..."
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 126,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/DataTable.tsx",
                    lineNumber: 124,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 123,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/common/DataTable.tsx",
            lineNumber: 122,
            columnNumber: 7
        }, this);
    }
    const handlePageSizeChange = (newPageSize)=>{
        const newQuery = {
            ...query,
            limit: newPageSize,
            page: 1
        };
        setQuery(newQuery);
        onQueryChange(newQuery);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ".concat(className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-6 border-b border-gray-200 dark:border-gray-700",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-search-line text-gray-400 dark:text-gray-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 145,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 144,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                            type: "text",
                            placeholder: searchPlaceholder,
                            value: searchInput,
                            onChange: (e)=>setSearchInput(e.target.value),
                            className: "block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 147,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/DataTable.tsx",
                    lineNumber: 143,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 142,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "overflow-x-auto data-table-container",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                    className: "min-w-full divide-y divide-gray-200 dark:divide-gray-700",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                            className: "bg-gray-50 dark:bg-gray-900",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: columns.map((column)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: "px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ".concat(column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : '', " ").concat(column.className || ''),
                                        onClick: ()=>column.sortable && handleSort(String(column.key)),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center space-x-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: column.label
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                                    lineNumber: 171,
                                                    columnNumber: 21
                                                }, this),
                                                column.sortable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex flex-col",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: "ri-arrow-up-s-line text-xs ".concat(getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400')
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                                            lineNumber: 174,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: "ri-arrow-down-s-line text-xs -mt-1 ".concat(getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400')
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                                            lineNumber: 177,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                                    lineNumber: 173,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                            lineNumber: 170,
                                            columnNumber: 19
                                        }, this)
                                    }, String(column.key), false, {
                                        fileName: "[project]/src/components/common/DataTable.tsx",
                                        lineNumber: 163,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 161,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 160,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                            className: "bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",
                            children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    colSpan: columns.length,
                                    className: "px-6 py-4 text-center text-gray-500 dark:text-gray-400",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/DataTable.tsx",
                                                lineNumber: 192,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "ml-2",
                                                children: "Loading..."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/DataTable.tsx",
                                                lineNumber: 193,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/DataTable.tsx",
                                        lineNumber: 191,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                    lineNumber: 190,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 189,
                                columnNumber: 15
                            }, this) : !(data === null || data === void 0 ? void 0 : data.data) || data.data.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                    colSpan: columns.length,
                                    className: "px-6 py-4 text-center text-gray-500 dark:text-gray-400",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex flex-col items-center justify-center py-8",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                className: "".concat(emptyStateIcon, " text-4xl mb-2")
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/DataTable.tsx",
                                                lineNumber: 201,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                children: emptyStateMessage
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/DataTable.tsx",
                                                lineNumber: 202,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/DataTable.tsx",
                                        lineNumber: 200,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                    lineNumber: 199,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/DataTable.tsx",
                                lineNumber: 198,
                                columnNumber: 15
                            }, this) : data.data.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                    className: "hover:bg-gray-50 dark:hover:bg-gray-700",
                                    children: columns.map((column)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100",
                                            children: column.render ? column.render(item[column.key], item) : String(item[column.key] || '')
                                        }, String(column.key), false, {
                                            fileName: "[project]/src/components/common/DataTable.tsx",
                                            lineNumber: 210,
                                            columnNumber: 21
                                        }, this))
                                }, index, false, {
                                    fileName: "[project]/src/components/common/DataTable.tsx",
                                    lineNumber: 208,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/DataTable.tsx",
                            lineNumber: 187,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/DataTable.tsx",
                    lineNumber: 159,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 158,
                columnNumber: 7
            }, this),
            (data === null || data === void 0 ? void 0 : data.meta) && data.meta.totalItems !== undefined && data.meta.currentPage !== undefined && data.meta.totalPages !== undefined && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Pagination$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                meta: {
                    ...data.meta,
                    totalItems: data.meta.totalItems,
                    currentPage: data.meta.currentPage,
                    totalPages: data.meta.totalPages
                },
                onPageChange: handlePageChange,
                onPageSizeChange: handlePageSizeChange,
                showFirstLast: true,
                showPageSizeSelector: true,
                showInfo: true,
                maxVisiblePages: 7,
                pageSizeOptions: [
                    10,
                    25,
                    50,
                    100
                ]
            }, void 0, false, {
                fileName: "[project]/src/components/common/DataTable.tsx",
                lineNumber: 226,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/DataTable.tsx",
        lineNumber: 140,
        columnNumber: 5
    }, this);
}
_s(DataTable, "zJAC0G83LLWNxtp2e05yoyUevis=");
_c = DataTable;
var _c;
__turbopack_context__.k.register(_c, "DataTable");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/Select.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
;
const Select = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = (param, ref)=>{
    let { label, error, helperText, required = false, options = [], placeholder = 'Select an option...', className = '', containerClassName = '', onChange, id, value, ...props } = param;
    const selectId = id || "select-".concat(Math.random().toString(36).substr(2, 9));
    const baseSelectClasses = "\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  ";
    const selectClasses = error ? "".concat(baseSelectClasses, " border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600") : "".concat(baseSelectClasses, " border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary");
    const handleChange = (e)=>{
        if (onChange) {
            onChange(e.target.value);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-1 ".concat(containerClassName),
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: selectId,
                className: "block text-sm font-medium text-gray-700 dark:text-gray-300",
                children: [
                    label,
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500 ml-1",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 68,
                        columnNumber: 24
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 63,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                        ref: ref,
                        id: selectId,
                        value: value || '',
                        onChange: handleChange,
                        className: "".concat(selectClasses, " ").concat(className),
                        ...props,
                        children: [
                            placeholder && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: "",
                                disabled: true,
                                children: placeholder
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Select.tsx",
                                lineNumber: 82,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            options.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: option.value,
                                    disabled: option.disabled,
                                    children: option.label
                                }, option.value, false, {
                                    fileName: "[project]/src/components/common/Select.tsx",
                                    lineNumber: 88,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-down-s-line text-gray-400 dark:text-gray-500"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Select.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-red-600 dark:text-red-400 flex items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-error-warning-line mr-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 106,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    error
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 105,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            helperText && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-gray-500 dark:text-gray-400",
                children: helperText
            }, void 0, false, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 112,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/Select.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
});
_c1 = Select;
Select.displayName = 'Select';
const __TURBOPACK__default__export__ = Select;
var _c, _c1;
__turbopack_context__.k.register(_c, "Select$forwardRef");
__turbopack_context__.k.register(_c1, "Select");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/certificates/PostalServicesCertificate.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>PostalServicesCertificate
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function PostalServicesCertificate(param) {
    let { license } = param;
    var _license_application_applicant, _license_application_applicant1, _license_application_applicant2, _license_application_applicant3;
    _s();
    const qrCodeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PostalServicesCertificate.useEffect": ()=>{
            // Generate QR code
            if (qrCodeRef.current && "object" !== 'undefined') {
                __turbopack_context__.r("[project]/node_modules/qrcode/lib/browser.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then({
                    "PostalServicesCertificate.useEffect": (QRCode)=>{
                        var _license_application_applicant;
                        const qrData = "MACRA Postal License Verification\nRef: ".concat(license.license_number, "\nLicense No: ").concat(license.license_number, "\nLicensee: ").concat(((_license_application_applicant = license.application.applicant) === null || _license_application_applicant === void 0 ? void 0 : _license_application_applicant.name) || 'N/A', "\nValid: ").concat(license.issue_date, " - ").concat(license.expiry_date, "\nVerify at: https://macra.mw/verify/").concat(license.license_number);
                        QRCode.toCanvas(qrCodeRef.current, qrData, {
                            width: 96,
                            margin: 1,
                            color: {
                                dark: '#000000',
                                light: '#FFFFFF'
                            }
                        }).catch({
                            "PostalServicesCertificate.useEffect": (error)=>{
                                console.error('QR Code generation failed:', error);
                                if (qrCodeRef.current) {
                                    qrCodeRef.current.innerHTML = 'QR Code<br>Error';
                                }
                            }
                        }["PostalServicesCertificate.useEffect"]);
                    }
                }["PostalServicesCertificate.useEffect"]);
            }
        }
    }["PostalServicesCertificate.useEffect"], [
        license
    ]);
    const formatDate = (dateString)=>{
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };
    const getValidityPeriod = ()=>{
        const issueDate = new Date(license.issue_date);
        const expiryDate = new Date(license.expiry_date);
        const years = expiryDate.getFullYear() - issueDate.getFullYear();
        return "".concat(years, " Years (").concat(formatDate(license.issue_date), " - ").concat(formatDate(license.expiry_date), ")");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "jsx-56a8c0722b67b1b" + " " + "postal-certificate",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                id: "56a8c0722b67b1b",
                children: '.postal-certificate.jsx-56a8c0722b67b1b{background:#fff;border:4px solid #dc2626;border-radius:8px;min-height:842px;padding:40px;font-family:Times New Roman,serif;position:relative;overflow:hidden}.inner-border.jsx-56a8c0722b67b1b{border:2px solid #16a34a;min-height:100%;padding:30px;position:relative}.watermark.jsx-56a8c0722b67b1b{opacity:.05;pointer-events:none;z-index:0;text-align:center;flex-direction:column;align-items:center;display:flex;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)rotate(-15deg)}.watermark-text.jsx-56a8c0722b67b1b{color:#dc2626;letter-spacing:3px;font-size:36px;font-weight:700;line-height:1.2}.content-wrapper.jsx-56a8c0722b67b1b{z-index:1;position:relative}.header.jsx-56a8c0722b67b1b{text-align:center;margin-bottom:30px}.logo-section.jsx-56a8c0722b67b1b{margin-bottom:20px}.authority-name.jsx-56a8c0722b67b1b{border:2px solid #dc2626;margin-bottom:20px;padding:12px 30px;display:inline-block}.authority-title.jsx-56a8c0722b67b1b{color:#dc2626;letter-spacing:1px;font-size:16px;font-weight:700}.reference-section.jsx-56a8c0722b67b1b{color:#2563eb;justify-content:space-between;margin-bottom:15px;font-size:14px;display:flex}.certificate-type.jsx-56a8c0722b67b1b{color:#fff;background-color:#16a34a;margin-bottom:10px;padding:10px 20px;font-size:18px;font-weight:700;display:inline-block}.certification-text.jsx-56a8c0722b67b1b{color:#374151;margin-bottom:25px;font-size:14px}.license-details.jsx-56a8c0722b67b1b{margin-bottom:25px;line-height:1.8}.detail-row.jsx-56a8c0722b67b1b{align-items:flex-start;margin-bottom:12px;display:flex}.detail-label.jsx-56a8c0722b67b1b{color:#374151;min-width:200px;font-size:14px;font-weight:600}.detail-colon.jsx-56a8c0722b67b1b{margin:0 10px;font-weight:700}.detail-value.jsx-56a8c0722b67b1b{color:#111827;flex:1;font-size:14px;font-weight:700}.services-section.jsx-56a8c0722b67b1b{margin-bottom:20px}.services-title.jsx-56a8c0722b67b1b{color:#374151;margin-bottom:8px;font-size:14px;font-weight:700}.services-list.jsx-56a8c0722b67b1b{padding-left:20px;list-style:none}.services-list.jsx-56a8c0722b67b1b li.jsx-56a8c0722b67b1b{margin-bottom:4px;font-size:13px;position:relative}.services-list.jsx-56a8c0722b67b1b li.jsx-56a8c0722b67b1b:before{content:"•";color:#16a34a;font-weight:700;position:absolute;left:-15px}.compliance-section.jsx-56a8c0722b67b1b{text-align:justify;margin-bottom:30px;font-size:14px;line-height:1.6}.footer-section.jsx-56a8c0722b67b1b{justify-content:space-between;align-items:flex-end;margin-top:40px;display:flex}.signature-area.jsx-56a8c0722b67b1b{flex:1}.issue-location.jsx-56a8c0722b67b1b{margin-bottom:60px;font-size:14px}.signature-line.jsx-56a8c0722b67b1b{text-align:center;border-top:1px solid #000;max-width:200px;padding-top:8px}.dg-name.jsx-56a8c0722b67b1b{font-size:14px;font-weight:700}.dg-title.jsx-56a8c0722b67b1b{font-size:14px}.qr-section.jsx-56a8c0722b67b1b{text-align:center;flex-shrink:0;margin-left:40px}.qr-code.jsx-56a8c0722b67b1b{color:#6b7280;background:#fff;border:2px solid #9ca3af;justify-content:center;align-items:center;width:100px;height:100px;margin-bottom:8px;font-size:10px;display:flex}.qr-text.jsx-56a8c0722b67b1b{color:#4b5563;max-width:100px;font-size:10px}.security-footer.jsx-56a8c0722b67b1b{color:#fff;text-align:center;background-color:#06b6d4;margin:20px -30px -30px;padding:8px;font-size:12px;font-weight:600}'
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-56a8c0722b67b1b" + " " + "inner-border",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-56a8c0722b67b1b" + " " + "watermark",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-56a8c0722b67b1b" + " " + "watermark-text",
                            children: [
                                "MALAWI COMMUNICATIONS",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {
                                    className: "jsx-56a8c0722b67b1b"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                    lineNumber: 291,
                                    columnNumber: 34
                                }, this),
                                "REGULATORY AUTHORITY"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                            lineNumber: 290,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                        lineNumber: 289,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-56a8c0722b67b1b" + " " + "content-wrapper",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-56a8c0722b67b1b" + " " + "header",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "authority-name",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                            className: "jsx-56a8c0722b67b1b" + " " + "authority-title",
                                            children: "MALAWI COMMUNICATIONS REGULATORY AUTHORITY"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                            lineNumber: 301,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 300,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "reference-section",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b",
                                                children: [
                                                    "Ref: PS - ",
                                                    new Date().getFullYear()
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 308,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b",
                                                children: [
                                                    "License No. ",
                                                    license.license_number
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 309,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 307,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "certificate-type",
                                        children: "POSTAL SERVICES LICENSE"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 313,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "certification-text",
                                        children: "This license authorizes the holder to operate postal services in Malawi"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 316,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                lineNumber: 298,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-56a8c0722b67b1b" + " " + "license-details",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-label",
                                                children: "LICENSEE NAME"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 324,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 325,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-value",
                                                children: ((_license_application_applicant = license.application.applicant) === null || _license_application_applicant === void 0 ? void 0 : _license_application_applicant.name) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 326,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 323,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-label",
                                                children: "BUSINESS REGISTRATION NO."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 330,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 331,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-value",
                                                children: ((_license_application_applicant1 = license.application.applicant) === null || _license_application_applicant1 === void 0 ? void 0 : _license_application_applicant1.business_registration_number) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 332,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 329,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-label",
                                                children: "TAX IDENTIFICATION NO."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 336,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 337,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-value",
                                                children: ((_license_application_applicant2 = license.application.applicant) === null || _license_application_applicant2 === void 0 ? void 0 : _license_application_applicant2.tpin) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 338,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 335,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-label",
                                                children: "REGISTERED ADDRESS"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 342,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 343,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-value",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-56a8c0722b67b1b",
                                                    children: ((_license_application_applicant3 = license.application.applicant) === null || _license_application_applicant3 === void 0 ? void 0 : _license_application_applicant3.name) || 'N/A'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                    lineNumber: 345,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 344,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 341,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-label",
                                                children: "SERVICE COVERAGE AREA"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 351,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 352,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-value",
                                                children: "National"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 353,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 350,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-label",
                                                children: "LICENSE VALIDITY PERIOD"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 357,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 358,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "detail-value",
                                                children: getValidityPeriod()
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 359,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 356,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                lineNumber: 322,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-56a8c0722b67b1b" + " " + "services-section",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "services-title",
                                        children: "AUTHORIZED POSTAL SERVICES:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 365,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "services-list",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "jsx-56a8c0722b67b1b",
                                                children: "Domestic mail collection, processing, and delivery"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 367,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "jsx-56a8c0722b67b1b",
                                                children: "International mail services (inbound and outbound)"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 368,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "jsx-56a8c0722b67b1b",
                                                children: "Express and courier services"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 369,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "jsx-56a8c0722b67b1b",
                                                children: "Parcel and package delivery services"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 370,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "jsx-56a8c0722b67b1b",
                                                children: "Postal financial services (money orders, postal banking)"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 371,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "jsx-56a8c0722b67b1b",
                                                children: "Philatelic services"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 372,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 366,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                lineNumber: 364,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-56a8c0722b67b1b" + " " + "compliance-section",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "jsx-56a8c0722b67b1b",
                                    children: license.conditions || "This license is issued under the Communications Act, 2016, and authorizes the licensee \n                to provide postal services in Malawi subject to compliance with all applicable laws, \n                regulations, and license conditions. The licensee shall maintain adequate infrastructure, \n                qualified personnel, and service standards as prescribed by the Malawi Communications \n                Regulatory Authority. This license is non-transferable and must be renewed before expiration."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                    lineNumber: 378,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                lineNumber: 377,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-56a8c0722b67b1b" + " " + "footer-section",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "signature-area",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "issue-location",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "jsx-56a8c0722b67b1b",
                                                    children: [
                                                        "Issued at Lilongwe, this ",
                                                        formatDate(license.issue_date)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                    lineNumber: 394,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 393,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "signature-line",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "jsx-56a8c0722b67b1b" + " " + "dg-name",
                                                        children: "Daud Suleman"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                        lineNumber: 400,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "jsx-56a8c0722b67b1b" + " " + "dg-title",
                                                        children: "Director General"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                        lineNumber: 401,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 399,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 392,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-56a8c0722b67b1b" + " " + "qr-section",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "qr-code",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    ref: qrCodeRef,
                                                    className: "jsx-56a8c0722b67b1b"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                    lineNumber: 408,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 407,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "jsx-56a8c0722b67b1b" + " " + "qr-text",
                                                children: "Scan for verification"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                                lineNumber: 410,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                        lineNumber: 406,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                lineNumber: 390,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-56a8c0722b67b1b" + " " + "security-footer",
                                children: "This licence is issued without any alterations and remains the property of MACRA"
                            }, void 0, false, {
                                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                                lineNumber: 417,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                        lineNumber: 296,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
                lineNumber: 287,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/certificates/PostalServicesCertificate.tsx",
        lineNumber: 57,
        columnNumber: 5
    }, this);
}
_s(PostalServicesCertificate, "n3XkxOz+52xEjzVIwTG6aG740uY=");
_c = PostalServicesCertificate;
var _c;
__turbopack_context__.k.register(_c, "PostalServicesCertificate");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/certificates/TypeApprovalCertificate.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>TypeApprovalCertificate
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function TypeApprovalCertificate(param) {
    let { license } = param;
    var _license_application_applicant, _license_application_applicant1, _license_application_applicant2, _license_application_applicant3, _license_application_license_category, _license_application_license_category1;
    _s();
    const qrCodeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TypeApprovalCertificate.useEffect": ()=>{
            // Generate QR code
            if (qrCodeRef.current && "object" !== 'undefined') {
                __turbopack_context__.r("[project]/node_modules/qrcode/lib/browser.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then({
                    "TypeApprovalCertificate.useEffect": (QRCode)=>{
                        var _license_application_applicant;
                        const qrData = "MACRA Type Approval Certificate Verification\nRef: ".concat(license.license_number, "\nCertificate No: ").concat(license.license_number, "\nApplicant: ").concat(((_license_application_applicant = license.application.applicant) === null || _license_application_applicant === void 0 ? void 0 : _license_application_applicant.name) || 'N/A', "\nValid: ").concat(license.issue_date, " - ").concat(license.expiry_date, "\nVerify at: https://macra.mw/verify/").concat(license.license_number);
                        QRCode.toCanvas(qrCodeRef.current, qrData, {
                            width: 96,
                            margin: 1,
                            color: {
                                dark: '#000000',
                                light: '#FFFFFF'
                            }
                        }).catch({
                            "TypeApprovalCertificate.useEffect": (error)=>{
                                console.error('QR Code generation failed:', error);
                                if (qrCodeRef.current) {
                                    qrCodeRef.current.innerHTML = 'QR Code<br>Error';
                                }
                            }
                        }["TypeApprovalCertificate.useEffect"]);
                    }
                }["TypeApprovalCertificate.useEffect"]);
            }
        }
    }["TypeApprovalCertificate.useEffect"], [
        license
    ]);
    const formatDate = (dateString)=>{
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };
    const getValidityPeriod = ()=>{
        const issueDate = new Date(license.issue_date);
        const expiryDate = new Date(license.expiry_date);
        const years = expiryDate.getFullYear() - issueDate.getFullYear();
        return "".concat(years, " Years (").concat(formatDate(license.issue_date), " - ").concat(formatDate(license.expiry_date), ")");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "jsx-76b93a70fd8df82a" + " " + "type-approval-certificate",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                id: "76b93a70fd8df82a",
                children: ".type-approval-certificate.jsx-76b93a70fd8df82a{background:#fff;border:4px solid #dc2626;border-radius:8px;min-height:842px;padding:40px;font-family:Times New Roman,serif;position:relative;overflow:hidden}.inner-border.jsx-76b93a70fd8df82a{border:2px solid #16a34a;min-height:100%;padding:30px;position:relative}.watermark.jsx-76b93a70fd8df82a{opacity:.05;pointer-events:none;z-index:0;text-align:center;flex-direction:column;align-items:center;display:flex;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)rotate(-15deg)}.watermark-text.jsx-76b93a70fd8df82a{color:#dc2626;letter-spacing:3px;font-size:36px;font-weight:700;line-height:1.2}.content-wrapper.jsx-76b93a70fd8df82a{z-index:1;position:relative}.header.jsx-76b93a70fd8df82a{text-align:center;margin-bottom:30px}.authority-name.jsx-76b93a70fd8df82a{border:2px solid #dc2626;margin-bottom:20px;padding:12px 30px;display:inline-block}.authority-title.jsx-76b93a70fd8df82a{color:#dc2626;letter-spacing:1px;font-size:16px;font-weight:700}.reference-section.jsx-76b93a70fd8df82a{color:#2563eb;justify-content:space-between;margin-bottom:15px;font-size:14px;display:flex}.certificate-type.jsx-76b93a70fd8df82a{color:#fff;background-color:#7c3aed;margin-bottom:10px;padding:10px 20px;font-size:18px;font-weight:700;display:inline-block}.certification-text.jsx-76b93a70fd8df82a{color:#374151;margin-bottom:25px;font-size:14px}.certificate-details.jsx-76b93a70fd8df82a{margin-bottom:25px;line-height:1.8}.detail-row.jsx-76b93a70fd8df82a{align-items:flex-start;margin-bottom:12px;display:flex}.detail-label.jsx-76b93a70fd8df82a{color:#374151;min-width:200px;font-size:14px;font-weight:600}.detail-colon.jsx-76b93a70fd8df82a{margin:0 10px;font-weight:700}.detail-value.jsx-76b93a70fd8df82a{color:#111827;flex:1;font-size:14px;font-weight:700}.equipment-section.jsx-76b93a70fd8df82a{background-color:#f9fafb;border-left:4px solid #7c3aed;border-radius:6px;margin-bottom:20px;padding:15px}.equipment-title.jsx-76b93a70fd8df82a{color:#374151;margin-bottom:8px;font-size:14px;font-weight:700}.compliance-section.jsx-76b93a70fd8df82a{text-align:justify;margin-bottom:30px;font-size:14px;line-height:1.6}.footer-section.jsx-76b93a70fd8df82a{justify-content:space-between;align-items:flex-end;margin-top:40px;display:flex}.signature-area.jsx-76b93a70fd8df82a{flex:1}.issue-location.jsx-76b93a70fd8df82a{margin-bottom:60px;font-size:14px}.signature-line.jsx-76b93a70fd8df82a{text-align:center;border-top:1px solid #000;max-width:200px;padding-top:8px}.dg-name.jsx-76b93a70fd8df82a{font-size:14px;font-weight:700}.dg-title.jsx-76b93a70fd8df82a{font-size:14px}.qr-section.jsx-76b93a70fd8df82a{text-align:center;flex-shrink:0;margin-left:40px}.qr-code.jsx-76b93a70fd8df82a{color:#6b7280;background:#fff;border:2px solid #9ca3af;justify-content:center;align-items:center;width:100px;height:100px;margin-bottom:8px;font-size:10px;display:flex}.qr-text.jsx-76b93a70fd8df82a{color:#4b5563;max-width:100px;font-size:10px}.security-footer.jsx-76b93a70fd8df82a{color:#fff;text-align:center;background-color:#06b6d4;margin:20px -30px -30px;padding:8px;font-size:12px;font-weight:600}"
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-76b93a70fd8df82a" + " " + "inner-border",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-76b93a70fd8df82a" + " " + "watermark",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-76b93a70fd8df82a" + " " + "watermark-text",
                            children: [
                                "MALAWI COMMUNICATIONS",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {
                                    className: "jsx-76b93a70fd8df82a"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                    lineNumber: 272,
                                    columnNumber: 34
                                }, this),
                                "REGULATORY AUTHORITY"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                            lineNumber: 271,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                        lineNumber: 270,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-76b93a70fd8df82a" + " " + "content-wrapper",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-76b93a70fd8df82a" + " " + "header",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "authority-name",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                            className: "jsx-76b93a70fd8df82a" + " " + "authority-title",
                                            children: "MALAWI COMMUNICATIONS REGULATORY AUTHORITY"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                            lineNumber: 282,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 281,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "reference-section",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a",
                                                children: [
                                                    "Ref: TA - ",
                                                    new Date().getFullYear()
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 289,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a",
                                                children: [
                                                    "Certificate No. ",
                                                    license.license_number
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 290,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 288,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "certificate-type",
                                        children: "TYPE APPROVAL CERTIFICATE"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 294,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "certification-text",
                                        children: "This certificate confirms that the equipment described below has been type approved for use in Malawi"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 297,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                lineNumber: 279,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-76b93a70fd8df82a" + " " + "certificate-details",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-label",
                                                children: "APPLICANT NAME"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 305,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 306,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-value",
                                                children: ((_license_application_applicant = license.application.applicant) === null || _license_application_applicant === void 0 ? void 0 : _license_application_applicant.name) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 307,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 304,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-label",
                                                children: "BUSINESS REGISTRATION NO."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 311,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 312,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-value",
                                                children: ((_license_application_applicant1 = license.application.applicant) === null || _license_application_applicant1 === void 0 ? void 0 : _license_application_applicant1.business_registration_number) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 313,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 310,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-label",
                                                children: "TAX IDENTIFICATION NO."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 317,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 318,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-value",
                                                children: ((_license_application_applicant2 = license.application.applicant) === null || _license_application_applicant2 === void 0 ? void 0 : _license_application_applicant2.tpin) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 319,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 316,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-label",
                                                children: "REGISTERED ADDRESS"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 323,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 324,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-value",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-76b93a70fd8df82a",
                                                    children: ((_license_application_applicant3 = license.application.applicant) === null || _license_application_applicant3 === void 0 ? void 0 : _license_application_applicant3.name) || 'N/A'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                    lineNumber: 326,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 325,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 322,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-label",
                                                children: "CERTIFICATE VALIDITY PERIOD"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 333,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 334,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-value",
                                                children: getValidityPeriod()
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 335,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 332,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                lineNumber: 303,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-76b93a70fd8df82a" + " " + "equipment-section",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "equipment-title",
                                        children: "APPROVED EQUIPMENT/SERVICE DETAILS:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 341,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "certificate-details",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-row",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-label",
                                                        children: "EQUIPMENT TYPE"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 344,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-colon",
                                                        children: ":"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 345,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-value",
                                                        children: ((_license_application_license_category = license.application.license_category) === null || _license_application_license_category === void 0 ? void 0 : _license_application_license_category.name) || 'Standards Compliance Equipment'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 346,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 343,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-row",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-label",
                                                        children: "STANDARDS COMPLIANCE"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 349,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-colon",
                                                        children: ":"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 350,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-value",
                                                        children: "ITU-R, ITU-T, and MACRA Technical Standards"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 351,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 348,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "detail-row",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-label",
                                                        children: "AUTHORIZATION"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 354,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-colon",
                                                        children: ":"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 355,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "detail-value",
                                                        children: ((_license_application_license_category1 = license.application.license_category) === null || _license_application_license_category1 === void 0 ? void 0 : _license_application_license_category1.authorizes) || 'Use of approved equipment in Malawi telecommunications networks'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 356,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 353,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 342,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                lineNumber: 340,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-76b93a70fd8df82a" + " " + "compliance-section",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "jsx-76b93a70fd8df82a",
                                    children: license.conditions || "This certificate is issued under the Communications Act, 2016, and confirms that the equipment \n                described above meets the technical standards and requirements for use in Malawi. The equipment \n                must be used in accordance with the approved specifications and applicable regulations. Any \n                modifications to the equipment will void this certificate. This certificate is non-transferable \n                and must be renewed before expiration."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                    lineNumber: 363,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                lineNumber: 362,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-76b93a70fd8df82a" + " " + "footer-section",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "signature-area",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "issue-location",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "jsx-76b93a70fd8df82a",
                                                    children: [
                                                        "Issued at Lilongwe, this ",
                                                        formatDate(license.issue_date)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                    lineNumber: 379,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 378,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "signature-line",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "dg-name",
                                                        children: "Daud Suleman"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 385,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "jsx-76b93a70fd8df82a" + " " + "dg-title",
                                                        children: "Director General"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                        lineNumber: 386,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 384,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 377,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-76b93a70fd8df82a" + " " + "qr-section",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "qr-code",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    ref: qrCodeRef,
                                                    className: "jsx-76b93a70fd8df82a"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                    lineNumber: 393,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 392,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "jsx-76b93a70fd8df82a" + " " + "qr-text",
                                                children: "Scan for verification"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                                lineNumber: 395,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                        lineNumber: 391,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                lineNumber: 375,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-76b93a70fd8df82a" + " " + "security-footer",
                                children: "This certificate is issued without any alterations and remains the property of MACRA"
                            }, void 0, false, {
                                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                                lineNumber: 402,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                        lineNumber: 277,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
                lineNumber: 268,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/certificates/TypeApprovalCertificate.tsx",
        lineNumber: 57,
        columnNumber: 5
    }, this);
}
_s(TypeApprovalCertificate, "n3XkxOz+52xEjzVIwTG6aG740uY=");
_c = TypeApprovalCertificate;
var _c;
__turbopack_context__.k.register(_c, "TypeApprovalCertificate");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/certificates/StandardsCertificate.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>StandardsCertificate
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function StandardsCertificate(param) {
    let { license } = param;
    var _license_code, _license_application_applicant, _license_application_applicant1, _license_application_applicant2, _license_application_applicant3, _license_application_license_category, _license_application_license_category1;
    _s();
    const qrCodeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "StandardsCertificate.useEffect": ()=>{
            // Generate QR code
            if (qrCodeRef.current && "object" !== 'undefined') {
                __turbopack_context__.r("[project]/node_modules/qrcode/lib/browser.js [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then({
                    "StandardsCertificate.useEffect": (QRCode)=>{
                        var _license_application_applicant, _license_application_license_category;
                        const qrData = "MACRA License Verification\nRef: ".concat(license.license_number, "\nLicense No: ").concat(license.license_number, "\nLicensee: ").concat(((_license_application_applicant = license.application.applicant) === null || _license_application_applicant === void 0 ? void 0 : _license_application_applicant.name) || 'N/A', "\nType: ").concat(((_license_application_license_category = license.application.license_category) === null || _license_application_license_category === void 0 ? void 0 : _license_application_license_category.name) || 'N/A', "\nValid: ").concat(license.issue_date, " - ").concat(license.expiry_date, "\nVerify at: https://macra.mw/verify/").concat(license.license_number);
                        QRCode.toCanvas(qrCodeRef.current, qrData, {
                            width: 96,
                            margin: 1,
                            color: {
                                dark: '#000000',
                                light: '#FFFFFF'
                            }
                        }).catch({
                            "StandardsCertificate.useEffect": (error)=>{
                                console.error('QR Code generation failed:', error);
                                if (qrCodeRef.current) {
                                    qrCodeRef.current.innerHTML = 'QR Code<br>Error';
                                }
                            }
                        }["StandardsCertificate.useEffect"]);
                    }
                }["StandardsCertificate.useEffect"]);
            }
        }
    }["StandardsCertificate.useEffect"], [
        license
    ]);
    const formatDate = (dateString)=>{
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };
    const getValidityPeriod = ()=>{
        const issueDate = new Date(license.issue_date);
        const expiryDate = new Date(license.expiry_date);
        const years = expiryDate.getFullYear() - issueDate.getFullYear();
        return "".concat(years, " Years (").concat(formatDate(license.issue_date), " - ").concat(formatDate(license.expiry_date), ")");
    };
    const getLicenseTypeTitle = ()=>{
        switch(license.code){
            case 'telecommunications':
                return 'TELECOMMUNICATIONS LICENSE';
            case 'broadcasting':
                return 'BROADCASTING LICENSE';
            case 'spectrum_management':
                return 'SPECTRUM MANAGEMENT LICENSE';
            default:
                return 'COMMUNICATIONS LICENSE';
        }
    };
    const getLicenseDescription = ()=>{
        switch(license.code){
            case 'telecommunications':
                return 'This license authorizes the holder to provide telecommunications services in Malawi';
            case 'broadcasting':
                return 'This license authorizes the holder to operate broadcasting services in Malawi';
            case 'spectrum_management':
                return 'This license authorizes the holder to manage radio frequency spectrum in Malawi';
            default:
                return 'This license authorizes the holder to operate communications services in Malawi';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "jsx-8363cb1f1e5421b5" + " " + "standards-certificate",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                id: "8363cb1f1e5421b5",
                children: ".standards-certificate.jsx-8363cb1f1e5421b5{background:#fff;border:4px solid #dc2626;border-radius:8px;min-height:842px;padding:40px;font-family:Times New Roman,serif;position:relative;overflow:hidden}.inner-border.jsx-8363cb1f1e5421b5{border:2px solid #16a34a;min-height:100%;padding:30px;position:relative}.watermark.jsx-8363cb1f1e5421b5{opacity:.05;pointer-events:none;z-index:0;text-align:center;flex-direction:column;align-items:center;display:flex;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)rotate(-15deg)}.watermark-text.jsx-8363cb1f1e5421b5{color:#dc2626;letter-spacing:3px;font-size:36px;font-weight:700;line-height:1.2}.content-wrapper.jsx-8363cb1f1e5421b5{z-index:1;position:relative}.header.jsx-8363cb1f1e5421b5{text-align:center;margin-bottom:30px}.authority-name.jsx-8363cb1f1e5421b5{border:2px solid #dc2626;margin-bottom:20px;padding:12px 30px;display:inline-block}.authority-title.jsx-8363cb1f1e5421b5{color:#dc2626;letter-spacing:1px;font-size:16px;font-weight:700}.reference-section.jsx-8363cb1f1e5421b5{color:#2563eb;justify-content:space-between;margin-bottom:15px;font-size:14px;display:flex}.certificate-type.jsx-8363cb1f1e5421b5{color:#fff;background-color:#2563eb;margin-bottom:10px;padding:10px 20px;font-size:18px;font-weight:700;display:inline-block}.certification-text.jsx-8363cb1f1e5421b5{color:#374151;margin-bottom:25px;font-size:14px}.license-details.jsx-8363cb1f1e5421b5{margin-bottom:25px;line-height:1.8}.detail-row.jsx-8363cb1f1e5421b5{align-items:flex-start;margin-bottom:12px;display:flex}.detail-label.jsx-8363cb1f1e5421b5{color:#374151;min-width:200px;font-size:14px;font-weight:600}.detail-colon.jsx-8363cb1f1e5421b5{margin:0 10px;font-weight:700}.detail-value.jsx-8363cb1f1e5421b5{color:#111827;flex:1;font-size:14px;font-weight:700}.authorization-section.jsx-8363cb1f1e5421b5{background-color:#f9fafb;border-left:4px solid #2563eb;border-radius:6px;margin-bottom:20px;padding:15px}.authorization-title.jsx-8363cb1f1e5421b5{color:#374151;margin-bottom:8px;font-size:14px;font-weight:700}.compliance-section.jsx-8363cb1f1e5421b5{text-align:justify;margin-bottom:30px;font-size:14px;line-height:1.6}.footer-section.jsx-8363cb1f1e5421b5{justify-content:space-between;align-items:flex-end;margin-top:40px;display:flex}.signature-area.jsx-8363cb1f1e5421b5{flex:1}.issue-location.jsx-8363cb1f1e5421b5{margin-bottom:60px;font-size:14px}.signature-line.jsx-8363cb1f1e5421b5{text-align:center;border-top:1px solid #000;max-width:200px;padding-top:8px}.dg-name.jsx-8363cb1f1e5421b5{font-size:14px;font-weight:700}.dg-title.jsx-8363cb1f1e5421b5{font-size:14px}.qr-section.jsx-8363cb1f1e5421b5{text-align:center;flex-shrink:0;margin-left:40px}.qr-code.jsx-8363cb1f1e5421b5{color:#6b7280;background:#fff;border:2px solid #9ca3af;justify-content:center;align-items:center;width:100px;height:100px;margin-bottom:8px;font-size:10px;display:flex}.qr-text.jsx-8363cb1f1e5421b5{color:#4b5563;max-width:100px;font-size:10px}.security-footer.jsx-8363cb1f1e5421b5{color:#fff;text-align:center;background-color:#06b6d4;margin:20px -30px -30px;padding:8px;font-size:12px;font-weight:600}"
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-8363cb1f1e5421b5" + " " + "inner-border",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-8363cb1f1e5421b5" + " " + "watermark",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-8363cb1f1e5421b5" + " " + "watermark-text",
                            children: [
                                "MALAWI COMMUNICATIONS",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("br", {
                                    className: "jsx-8363cb1f1e5421b5"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                    lineNumber: 299,
                                    columnNumber: 34
                                }, this),
                                "REGULATORY AUTHORITY"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                            lineNumber: 298,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                        lineNumber: 297,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-8363cb1f1e5421b5" + " " + "content-wrapper",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-8363cb1f1e5421b5" + " " + "header",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "authority-name",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                            className: "jsx-8363cb1f1e5421b5" + " " + "authority-title",
                                            children: "MALAWI COMMUNICATIONS REGULATORY AUTHORITY"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                            lineNumber: 309,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 308,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "reference-section",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5",
                                                children: [
                                                    "Ref: ",
                                                    ((_license_code = license.code) === null || _license_code === void 0 ? void 0 : _license_code.toUpperCase().substring(0, 2)) || 'GEN',
                                                    " - ",
                                                    new Date().getFullYear()
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 316,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5",
                                                children: [
                                                    "License No. ",
                                                    license.license_number
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 317,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 315,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "certificate-type",
                                        children: getLicenseTypeTitle()
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 321,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "certification-text",
                                        children: getLicenseDescription()
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 324,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                lineNumber: 306,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-8363cb1f1e5421b5" + " " + "license-details",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-label",
                                                children: "LICENSEE NAME"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 332,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 333,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-value",
                                                children: ((_license_application_applicant = license.application.applicant) === null || _license_application_applicant === void 0 ? void 0 : _license_application_applicant.name) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 334,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 331,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-label",
                                                children: "BUSINESS REGISTRATION NO."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 338,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 339,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-value",
                                                children: ((_license_application_applicant1 = license.application.applicant) === null || _license_application_applicant1 === void 0 ? void 0 : _license_application_applicant1.business_registration_number) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 340,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 337,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-label",
                                                children: "TAX IDENTIFICATION NO."
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 344,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 345,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-value",
                                                children: ((_license_application_applicant2 = license.application.applicant) === null || _license_application_applicant2 === void 0 ? void 0 : _license_application_applicant2.tpin) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 346,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 343,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-label",
                                                children: "REGISTERED ADDRESS"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 350,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 351,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-value",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-8363cb1f1e5421b5",
                                                    children: ((_license_application_applicant3 = license.application.applicant) === null || _license_application_applicant3 === void 0 ? void 0 : _license_application_applicant3.name) || 'N/A'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                    lineNumber: 353,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 352,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 349,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-label",
                                                children: "LICENSE CATEGORY"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 360,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 361,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-value",
                                                children: ((_license_application_license_category = license.application.license_category) === null || _license_application_license_category === void 0 ? void 0 : _license_application_license_category.name) || 'N/A'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 362,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 359,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "detail-row",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-label",
                                                children: "LICENSE VALIDITY PERIOD"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 366,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-colon",
                                                children: ":"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 367,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "detail-value",
                                                children: getValidityPeriod()
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 368,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 365,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                lineNumber: 330,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-8363cb1f1e5421b5" + " " + "authorization-section",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "authorization-title",
                                        children: "AUTHORIZATION:"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 374,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "jsx-8363cb1f1e5421b5",
                                        children: ((_license_application_license_category1 = license.application.license_category) === null || _license_application_license_category1 === void 0 ? void 0 : _license_application_license_category1.authorizes) || 'This license authorizes the holder to operate communications services as specified in the license conditions and applicable regulations.'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 375,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                lineNumber: 373,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-8363cb1f1e5421b5" + " " + "compliance-section",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "jsx-8363cb1f1e5421b5",
                                    children: license.conditions || "This license is issued under the Communications Act, 2016, and authorizes the licensee \n                to provide the specified services in Malawi subject to compliance with all applicable laws, \n                regulations, and license conditions. The licensee shall maintain adequate infrastructure, \n                qualified personnel, and service standards as prescribed by the Malawi Communications \n                Regulatory Authority. This license is non-transferable and must be renewed before expiration."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                    lineNumber: 380,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                lineNumber: 379,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-8363cb1f1e5421b5" + " " + "footer-section",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "signature-area",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "issue-location",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "jsx-8363cb1f1e5421b5",
                                                    children: [
                                                        "Issued at Lilongwe, this ",
                                                        formatDate(license.issue_date)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                    lineNumber: 396,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 395,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "signature-line",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "jsx-8363cb1f1e5421b5" + " " + "dg-name",
                                                        children: "Daud Suleman"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                        lineNumber: 402,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "jsx-8363cb1f1e5421b5" + " " + "dg-title",
                                                        children: "Director General"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                        lineNumber: 403,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 401,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 394,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-8363cb1f1e5421b5" + " " + "qr-section",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "qr-code",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    ref: qrCodeRef,
                                                    className: "jsx-8363cb1f1e5421b5"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                    lineNumber: 410,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 409,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "jsx-8363cb1f1e5421b5" + " " + "qr-text",
                                                children: "Scan for verification"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                                lineNumber: 412,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                        lineNumber: 408,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                lineNumber: 392,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-8363cb1f1e5421b5" + " " + "security-footer",
                                children: "This licence is issued without any alterations and remains the property of MACRA"
                            }, void 0, false, {
                                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                                lineNumber: 419,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                        lineNumber: 304,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
                lineNumber: 295,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/certificates/StandardsCertificate.tsx",
        lineNumber: 84,
        columnNumber: 5
    }, this);
}
_s(StandardsCertificate, "n3XkxOz+52xEjzVIwTG6aG740uY=");
_c = StandardsCertificate;
var _c;
__turbopack_context__.k.register(_c, "StandardsCertificate");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/certificates/CertificateModal.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>CertificateModal
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/styled-jsx/style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$certificates$2f$PostalServicesCertificate$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/certificates/PostalServicesCertificate.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$certificates$2f$TypeApprovalCertificate$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/certificates/TypeApprovalCertificate.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$certificates$2f$StandardsCertificate$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/certificates/StandardsCertificate.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
function CertificateModal(param) {
    let { license, isOpen, onClose } = param;
    _s();
    const [isDownloading, setIsDownloading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    if (!isOpen) return null;
    const renderCertificate = ()=>{
        // Determine which certificate to show based on license code
        switch(license.code){
            case 'postal_services':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$certificates$2f$PostalServicesCertificate$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    license: license
                }, void 0, false, {
                    fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                    lineNumber: 26,
                    columnNumber: 16
                }, this);
            case 'standards_compliance':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$certificates$2f$TypeApprovalCertificate$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    license: license
                }, void 0, false, {
                    fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                    lineNumber: 28,
                    columnNumber: 16
                }, this);
            case 'telecommunications':
            case 'broadcasting':
            case 'spectrum_management':
            default:
                // Fallback to standards certificate for other types
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$certificates$2f$StandardsCertificate$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    license: license
                }, void 0, false, {
                    fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                    lineNumber: 34,
                    columnNumber: 16
                }, this);
        }
    };
    const handlePrint = ()=>{
        window.print();
    };
    const handleDownloadPDF = async ()=>{
        try {
            setIsDownloading(true);
            const blob = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseService"].downloadLicensePDF(license.license_id);
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = "".concat(license.license_number, "_certificate.pdf");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].success('Certificate PDF downloaded successfully');
        } catch (error) {
            console.error('Error downloading certificate PDF:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].error('Failed to download certificate PDF');
        } finally{
            setIsDownloading(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "jsx-e38b76609b0d0cbf" + " " + "fixed inset-0 z-50 overflow-y-auto",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-e38b76609b0d0cbf" + " " + "flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        onClick: onClose,
                        className: "jsx-e38b76609b0d0cbf" + " " + "fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
                    }, void 0, false, {
                        fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                        lineNumber: 67,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-e38b76609b0d0cbf" + " " + "inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-e38b76609b0d0cbf" + " " + "flex items-center justify-between mb-4 print:hidden",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "jsx-e38b76609b0d0cbf" + " " + "text-lg font-medium text-gray-900",
                                        children: [
                                            "License Certificate - ",
                                            license.license_number
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                                        lineNumber: 76,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-e38b76609b0d0cbf" + " " + "flex space-x-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: handleDownloadPDF,
                                                disabled: isDownloading,
                                                className: "jsx-e38b76609b0d0cbf" + " " + "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",
                                                children: isDownloading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: "jsx-e38b76609b0d0cbf" + " " + "ri-loader-4-line mr-2 animate-spin"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                                                            lineNumber: 88,
                                                            columnNumber: 21
                                                        }, this),
                                                        "Downloading..."
                                                    ]
                                                }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: "jsx-e38b76609b0d0cbf" + " " + "ri-download-line mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                                                            lineNumber: 93,
                                                            columnNumber: 21
                                                        }, this),
                                                        "Download PDF"
                                                    ]
                                                }, void 0, true)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                                                lineNumber: 80,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: onClose,
                                                className: "jsx-e38b76609b0d0cbf" + " " + "inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "jsx-e38b76609b0d0cbf" + " " + "ri-close-line mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                                                        lineNumber: 103,
                                                        columnNumber: 17
                                                    }, this),
                                                    "Close"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                                                lineNumber: 98,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                                        lineNumber: 79,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                                lineNumber: 75,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-e38b76609b0d0cbf" + " " + "certificate-container",
                                children: renderCertificate()
                            }, void 0, false, {
                                fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                                lineNumber: 110,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/certificates/CertificateModal.tsx",
                lineNumber: 65,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                id: "e38b76609b0d0cbf",
                children: "@media print{body *{visibility:hidden}.certificate-container,.certificate-container *{visibility:visible}.certificate-container{width:100%;position:absolute;top:0;left:0}.print\\\\:hidden{display:none!important}}"
            }, void 0, false, void 0, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/certificates/CertificateModal.tsx",
        lineNumber: 64,
        columnNumber: 5
    }, this);
}
_s(CertificateModal, "I+IY6bHIajfeJNOs4vl6hY3OWII=");
_c = CertificateModal;
var _c;
__turbopack_context__.k.register(_c, "CertificateModal");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/licenses/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>LicensesPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/license.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseTypeService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$DataTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/DataTable.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Select.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$certificates$2f$CertificateModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/certificates/CertificateModal.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
function LicensesPage() {
    _s();
    const [licensesData, setLicensesData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [licenseTypes, setLicenseTypes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [statusFilter, setStatusFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [licenseTypeFilter, setLicenseTypeFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [dateRangeFilter, setDateRangeFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // Certificate modal state
    const [selectedLicense, setSelectedLicense] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isCertificateModalOpen, setIsCertificateModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load licenses function for DataTable
    const loadLicenses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LicensesPage.useCallback[loadLicenses]": async (query)=>{
            try {
                setLoading(true);
                setError(null);
                const params = {
                    page: query.page,
                    limit: query.limit,
                    search: query.search || undefined,
                    sortBy: Array.isArray(query.sortBy) ? query.sortBy[0] : query.sortBy,
                    sortOrder: Array.isArray(query.sortBy) && query.sortBy.length > 1 ? query.sortBy[1] : undefined,
                    status: statusFilter || undefined,
                    licenseType: licenseTypeFilter || undefined,
                    dateRange: dateRangeFilter || undefined
                };
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseService"].getLicenses(params);
                setLicensesData(response);
            } catch (err) {
                setError('Failed to load licenses');
                console.error('Error loading licenses:', err);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].error('Failed to load licenses');
            } finally{
                setLoading(false);
            }
        }
    }["LicensesPage.useCallback[loadLicenses]"], [
        statusFilter,
        licenseTypeFilter,
        dateRangeFilter
    ]);
    // Load license types on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LicensesPage.useEffect": ()=>{
            const fetchInitialData = {
                "LicensesPage.useEffect.fetchInitialData": async ()=>{
                    try {
                        // Fetch license types
                        const typesResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseTypeService"].getAllLicenseTypes();
                        const types = Array.isArray(typesResponse) ? typesResponse : (typesResponse === null || typesResponse === void 0 ? void 0 : typesResponse.data) || [];
                        setLicenseTypes(types);
                    } catch (error) {
                        console.error('Error fetching initial data:', error);
                    }
                }
            }["LicensesPage.useEffect.fetchInitialData"];
            fetchInitialData();
        }
    }["LicensesPage.useEffect"], []);
    // Load licenses on component mount and when filters change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LicensesPage.useEffect": ()=>{
            loadLicenses({
                page: 1,
                limit: 10
            });
        }
    }["LicensesPage.useEffect"], [
        loadLicenses
    ]);
    const getStatusBadge = (status)=>{
        const statusClasses = {
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].ACTIVE]: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].EXPIRED]: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].SUSPENDED]: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].REVOKED]: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
            [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].UNDER_REVIEW]: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
        };
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(statusClasses[status] || statusClasses[__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].ACTIVE]),
            children: status.replace('_', ' ').toUpperCase()
        }, void 0, false, {
            fileName: "[project]/src/app/licenses/page.tsx",
            lineNumber: 86,
            columnNumber: 7
        }, this);
    };
    const handleViewCertificate = (license)=>{
        setSelectedLicense(license);
        setIsCertificateModalOpen(true);
    };
    const handleCloseCertificateModal = ()=>{
        setIsCertificateModalOpen(false);
        setSelectedLicense(null);
    };
    const handleFilterChange = (filterType, value)=>{
        switch(filterType){
            case 'status':
                setStatusFilter(value);
                break;
            case 'licenseType':
                setLicenseTypeFilter(value);
                break;
            case 'dateRange':
                setDateRangeFilter(value);
                break;
        }
        // Reload licenses with new filters
        loadLicenses({
            page: 1,
            limit: 10
        });
    };
    // Define table columns
    const licenseColumns = [
        {
            key: 'license_number',
            label: 'License Number',
            sortable: true,
            render: (value, item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-shrink-0 h-10 w-10",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-award-line text-blue-600 dark:text-blue-400"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/licenses/page.tsx",
                                    lineNumber: 128,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/licenses/page.tsx",
                                lineNumber: 127,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/licenses/page.tsx",
                            lineNumber: 126,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "ml-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                                    children: item.license_number
                                }, void 0, false, {
                                    fileName: "[project]/src/app/licenses/page.tsx",
                                    lineNumber: 132,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-gray-500 dark:text-gray-400",
                                    children: item.code || 'N/A'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/licenses/page.tsx",
                                    lineNumber: 135,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/licenses/page.tsx",
                            lineNumber: 131,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/licenses/page.tsx",
                    lineNumber: 125,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'applicant',
            label: 'Applicant',
            render: (value, item)=>{
                var _item_application_applicant, _item_application_applicant1;
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                            children: ((_item_application_applicant = item.application.applicant) === null || _item_application_applicant === void 0 ? void 0 : _item_application_applicant.name) || 'N/A'
                        }, void 0, false, {
                            fileName: "[project]/src/app/licenses/page.tsx",
                            lineNumber: 147,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm text-gray-500 dark:text-gray-400",
                            children: ((_item_application_applicant1 = item.application.applicant) === null || _item_application_applicant1 === void 0 ? void 0 : _item_application_applicant1.email) || 'N/A'
                        }, void 0, false, {
                            fileName: "[project]/src/app/licenses/page.tsx",
                            lineNumber: 150,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/licenses/page.tsx",
                    lineNumber: 146,
                    columnNumber: 9
                }, this);
            }
        },
        {
            key: 'license_type',
            label: 'License Type',
            render: (value, item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm font-medium text-gray-900 dark:text-gray-100",
                            children: (item === null || item === void 0 ? void 0 : item.description) || 'N/A'
                        }, void 0, false, {
                            fileName: "[project]/src/app/licenses/page.tsx",
                            lineNumber: 161,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm text-gray-500 dark:text-gray-400",
                            children: (item === null || item === void 0 ? void 0 : item.code) || 'N/A'
                        }, void 0, false, {
                            fileName: "[project]/src/app/licenses/page.tsx",
                            lineNumber: 164,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/licenses/page.tsx",
                    lineNumber: 160,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'status',
            label: 'Status',
            sortable: true,
            render: (value, item)=>getStatusBadge(item.status)
        },
        {
            key: 'issue_date',
            label: 'Issue Date',
            sortable: true,
            render: (value, item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm text-gray-500 dark:text-gray-400",
                    children: new Date(item.issue_date).toLocaleDateString()
                }, void 0, false, {
                    fileName: "[project]/src/app/licenses/page.tsx",
                    lineNumber: 181,
                    columnNumber: 9
                }, this)
        },
        {
            key: 'expiry_date',
            label: 'Expiry Date',
            sortable: true,
            render: (value, item)=>{
                const expiryDate = new Date(item.expiry_date);
                const isExpired = expiryDate < new Date();
                const isExpiringSoon = expiryDate < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "text-sm ".concat(isExpired ? 'text-red-600 dark:text-red-400' : isExpiringSoon ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-500 dark:text-gray-400'),
                    children: expiryDate.toLocaleDateString()
                }, void 0, false, {
                    fileName: "[project]/src/app/licenses/page.tsx",
                    lineNumber: 196,
                    columnNumber: 11
                }, this);
            }
        },
        {
            key: 'actions',
            label: 'Actions',
            render: (value, item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-end space-x-2",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: ()=>handleViewCertificate(item),
                        className: "inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-eye-line mr-1"
                            }, void 0, false, {
                                fileName: "[project]/src/app/licenses/page.tsx",
                                lineNumber: 212,
                                columnNumber: 13
                            }, this),
                            "View Certificate"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/licenses/page.tsx",
                        lineNumber: 207,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/licenses/page.tsx",
                    lineNumber: 206,
                    columnNumber: 9
                }, this)
        }
    ];
    const statusOptions = [
        {
            value: '',
            label: 'All Statuses'
        },
        {
            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].ACTIVE,
            label: 'Active'
        },
        {
            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].EXPIRED,
            label: 'Expired'
        },
        {
            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].SUSPENDED,
            label: 'Suspended'
        },
        {
            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].REVOKED,
            label: 'Revoked'
        },
        {
            value: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$license$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LicenseStatus"].UNDER_REVIEW,
            label: 'Under Review'
        }
    ];
    const licenseTypeOptions = [
        {
            value: '',
            label: 'All License Types'
        },
        ...licenseTypes.map((type)=>({
                value: type.license_type_id,
                label: type.name
            }))
    ];
    const dateRangeOptions = [
        {
            value: '',
            label: 'All Time'
        },
        {
            value: 'today',
            label: 'Today'
        },
        {
            value: 'week',
            label: 'This Week'
        },
        {
            value: 'month',
            label: 'This Month'
        },
        {
            value: 'quarter',
            label: 'This Quarter'
        },
        {
            value: 'year',
            label: 'This Year'
        },
        {
            value: 'expiring_30',
            label: 'Expiring in 30 Days'
        },
        {
            value: 'expiring_90',
            label: 'Expiring in 90 Days'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                        className: "text-3xl font-bold text-gray-900 dark:text-gray-100",
                                        children: "Licenses"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/licenses/page.tsx",
                                        lineNumber: 255,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-2 text-sm text-gray-600 dark:text-gray-400",
                                        children: "Manage and view all issued licenses in the system"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/licenses/page.tsx",
                                        lineNumber: 256,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/licenses/page.tsx",
                                lineNumber: 254,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/licenses/page.tsx",
                            lineNumber: 253,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/licenses/page.tsx",
                        lineNumber: 252,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 bg-white dark:bg-gray-800 rounded-lg shadow p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-3 gap-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                            children: "Status"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/licenses/page.tsx",
                                            lineNumber: 269,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            value: statusFilter,
                                            onChange: (value)=>handleFilterChange('status', value),
                                            options: statusOptions,
                                            placeholder: "Filter by status"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/licenses/page.tsx",
                                            lineNumber: 272,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/licenses/page.tsx",
                                    lineNumber: 268,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                            children: "Date Range"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/licenses/page.tsx",
                                            lineNumber: 293,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            value: dateRangeFilter,
                                            onChange: (value)=>handleFilterChange('dateRange', value),
                                            options: dateRangeOptions,
                                            placeholder: "Filter by date range"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/licenses/page.tsx",
                                            lineNumber: 296,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/licenses/page.tsx",
                                    lineNumber: 292,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/licenses/page.tsx",
                            lineNumber: 267,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/licenses/page.tsx",
                        lineNumber: 266,
                        columnNumber: 9
                    }, this),
                    error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex-shrink-0",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "ri-error-warning-line text-red-400"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/licenses/page.tsx",
                                            lineNumber: 312,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/licenses/page.tsx",
                                        lineNumber: 311,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "ml-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "text-sm font-medium text-red-800 dark:text-red-200",
                                                children: "Error loading licenses"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/licenses/page.tsx",
                                                lineNumber: 315,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mt-2 text-sm text-red-700 dark:text-red-300",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: error
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/licenses/page.tsx",
                                                    lineNumber: 319,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/licenses/page.tsx",
                                                lineNumber: 318,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/licenses/page.tsx",
                                        lineNumber: 314,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "ml-auto pl-3",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            onClick: ()=>setError(null),
                                            className: "inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "sr-only",
                                                    children: "Dismiss"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/licenses/page.tsx",
                                                    lineNumber: 328,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                    className: "ri-close-line text-sm"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/licenses/page.tsx",
                                                    lineNumber: 329,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/licenses/page.tsx",
                                            lineNumber: 323,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/licenses/page.tsx",
                                        lineNumber: 322,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/licenses/page.tsx",
                                lineNumber: 310,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/licenses/page.tsx",
                            lineNumber: 309,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/licenses/page.tsx",
                        lineNumber: 308,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$DataTable$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        columns: licenseColumns,
                        data: licensesData,
                        loading: loading,
                        onQueryChange: loadLicenses,
                        searchPlaceholder: "Search licenses by number, applicant, or type...",
                        emptyStateIcon: "ri-award-line",
                        emptyStateMessage: "No licenses found."
                    }, void 0, false, {
                        fileName: "[project]/src/app/licenses/page.tsx",
                        lineNumber: 338,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/licenses/page.tsx",
                lineNumber: 250,
                columnNumber: 7
            }, this),
            selectedLicense && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$certificates$2f$CertificateModal$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                license: selectedLicense,
                isOpen: isCertificateModalOpen,
                onClose: handleCloseCertificateModal
            }, void 0, false, {
                fileName: "[project]/src/app/licenses/page.tsx",
                lineNumber: 351,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/licenses/page.tsx",
        lineNumber: 249,
        columnNumber: 5
    }, this);
}
_s(LicensesPage, "Ev6ypRNTA7YwkpnWrxNTeddycAk=");
_c = LicensesPage;
var _c;
__turbopack_context__.k.register(_c, "LicensesPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_527826dc._.js.map