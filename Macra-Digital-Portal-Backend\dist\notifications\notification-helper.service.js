"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationHelperService = void 0;
const common_1 = require("@nestjs/common");
const notifications_service_1 = require("./notifications.service");
const email_template_service_1 = require("./email-template.service");
const notifications_entity_1 = require("../entities/notifications.entity");
const mailer_1 = require("@nestjs-modules/mailer");
const path_1 = require("path");
const app_module_1 = require("../app.module");
const formatters_1 = require("../common/utils/formatters");
let NotificationHelperService = class NotificationHelperService {
    constructor(notificationsService, emailTemplateService, mailerService) {
        this.notificationsService = notificationsService;
        this.emailTemplateService = emailTemplateService;
        this.mailerService = mailerService;
    }
    async notifyApplicationStatus(applicationId, applicantId, applicantEmail, applicationNumber, status, createdBy, applicantName, licenseType, oldStatus) {
        console.log(`📧 NotificationHelper: Processing application status notification for ${applicationNumber} - ${status}`);
        let emailTemplate;
        if (status === 'submitted') {
            emailTemplate = this.emailTemplateService.generateApplicationSubmittedTemplate({
                applicantName: applicantName || 'Valued Customer',
                applicationNumber,
                licenseType: licenseType || 'License',
                submissionDate: new Date().toLocaleDateString(),
                portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
            });
        }
        else if (status === 'invoice_generated') {
            throw new Error('Invoice generation should use notifyInvoiceGenerated method');
        }
        else {
            emailTemplate = this.emailTemplateService.generateApplicationStatusChangeTemplate({
                applicantName: applicantName || 'Valued Customer',
                applicationNumber,
                licenseType: licenseType || 'License',
                oldStatus: oldStatus || 'previous',
                newStatus: status,
                changeDate: new Date().toLocaleDateString(),
                portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
            });
        }
        const message = `Your application ${applicationNumber} status has been updated to: ${status.toUpperCase()}`;
        const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`;
        try {
            await this.sendEmailNotification({
                recipientId: applicantId,
                recipientEmail: applicantEmail,
                recipientName: applicantName,
                subject: emailTemplate.subject,
                message,
                htmlContent: emailTemplate.html,
                entityType: 'application',
                entityId: applicationId,
                actionUrl,
                recipientType: notifications_entity_1.RecipientType.CUSTOMER,
                createdBy,
                sendEmail: !!applicantEmail,
                createInApp: true,
            });
            console.log(`✅ NotificationHelper: Application status notifications completed for ${applicationNumber}`);
        }
        catch (error) {
            console.error(`❌ NotificationHelper: Failed to send application status notifications:`, error);
            throw error;
        }
    }
    async notifyInvoiceGenerated(applicationId, applicantId, applicantEmail, applicationNumber, invoiceNumber, amount, dueDate, description, createdBy, applicantName, licenseType) {
        console.log(`📧 NotificationHelper: Processing invoice generation notification for ${invoiceNumber}`);
        const emailTemplate = this.emailTemplateService.generateInvoiceGeneratedTemplate({
            applicantName: applicantName || 'Valued Customer',
            applicationNumber,
            licenseType: licenseType || 'License',
            invoiceNumber,
            amount,
            dueDate,
            description,
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
        });
        const message = `Invoice ${invoiceNumber} has been generated for your application ${applicationNumber}. Amount: ${(0, formatters_1.formatAmount)(amount)}`;
        const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`;
        try {
            await this.sendEmailNotification({
                recipientId: applicantId,
                recipientEmail: applicantEmail,
                recipientName: applicantName,
                subject: emailTemplate.subject,
                message,
                htmlContent: emailTemplate.html,
                entityType: 'invoice',
                entityId: invoiceNumber,
                actionUrl,
                recipientType: notifications_entity_1.RecipientType.CUSTOMER,
                createdBy,
                sendEmail: !!applicantEmail,
                createInApp: true,
            });
            console.log(`✅ NotificationHelper: Invoice notifications completed for ${invoiceNumber}`);
        }
        catch (error) {
            console.error(`❌ NotificationHelper: Failed to send invoice notifications:`, error);
            throw error;
        }
    }
    async notifyPaymentConfirmation(invoiceId, clientId, clientEmail, clientName, invoiceNumber, invoiceAmount, paidAmount, paymentDate, processedBy) {
        console.log(`📧 NotificationHelper: Processing payment confirmation notification for ${invoiceNumber}`);
        const emailTemplate = this.emailTemplateService.generatePaymentConfirmationTemplate({
            clientName: clientName || 'Valued Customer',
            invoiceNumber,
            invoiceAmount,
            paidAmount,
            paymentDate,
            processedBy,
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/invoices?invoice_id=${invoiceId}`,
        });
        const message = `Payment confirmed for invoice ${invoiceNumber}. Amount paid:  ${(0, formatters_1.formatAmount)(paidAmount)}`;
        const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/invoices?invoice_id=${invoiceId}`;
        try {
            await this.sendEmailNotification({
                recipientId: clientId,
                recipientEmail: clientEmail,
                recipientName: clientName,
                subject: emailTemplate.subject,
                message,
                htmlContent: emailTemplate.html,
                entityType: 'invoice',
                entityId: invoiceId,
                actionUrl,
                recipientType: notifications_entity_1.RecipientType.CUSTOMER,
                createdBy: processedBy,
                sendEmail: !!clientEmail,
                createInApp: true,
            });
            console.log(`✅ NotificationHelper: Payment confirmation notifications completed for ${invoiceNumber}`);
        }
        catch (error) {
            console.error(`❌ NotificationHelper: Failed to send payment confirmation notifications:`, error);
            throw error;
        }
    }
    async notifyActivityNote(applicationId, applicantId, applicantEmail, applicantName, applicationNumber, licenseType, noteContent, noteType, category, step, createdBy, createdDate, userId, isAdditionalRecipient = false) {
        console.log(`📧 NotificationHelper: Processing activity note notification for application ${applicationNumber}`);
        const emailTemplate = this.emailTemplateService.generateActivityNoteTemplate({
            applicantName: applicantName || 'Valued Customer',
            applicationNumber,
            licenseType: licenseType || 'License',
            noteContent,
            noteType,
            category,
            step,
            createdBy,
            createdDate,
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
        });
        const message = `New update on your application ${applicationNumber}: ${noteContent}`;
        const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`;
        try {
            await this.sendEmailNotification({
                recipientId: applicantId || undefined,
                recipientEmail: applicantEmail,
                recipientName: applicantName,
                subject: emailTemplate.subject,
                message,
                htmlContent: emailTemplate.html,
                entityType: 'application',
                entityId: applicationId,
                actionUrl,
                recipientType: isAdditionalRecipient ? notifications_entity_1.RecipientType.STAFF : notifications_entity_1.RecipientType.CUSTOMER,
                createdBy: userId,
                sendEmail: !!applicantEmail,
                createInApp: !isAdditionalRecipient && !!applicantId,
            });
            if (isAdditionalRecipient) {
                console.log(`✅ Additional email notification sent to: ${applicantEmail}`);
            }
            else {
                console.log(`✅ Primary applicant notification sent to: ${applicantEmail}`);
            }
        }
        catch (error) {
            console.error(`❌ NotificationHelper: Failed to send activity note notifications:`, error);
            throw error;
        }
    }
    async notifyTaskAssignment(taskId, assigneeId, assigneeEmail, taskTitle, taskDescription, createdBy, assigneeName, applicationNumber, applicantName, priority, dueDate) {
        console.log(`📧 NotificationHelper: Processing task assignment notification for ${taskTitle}`);
        const emailTemplate = this.emailTemplateService.generateTaskAssignedTemplate({
            assigneeName: assigneeName || 'Team Member',
            taskTitle,
            taskDescription,
            applicationNumber: applicationNumber || 'N/A',
            applicantName: applicantName || 'N/A',
            priority: priority || 'medium',
            dueDate,
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/tasks?task_id=${taskId}`,
        });
        const message = `You have been assigned a new task: ${taskTitle}. ${taskDescription}`;
        const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/tasks?task_id=${taskId}`;
        try {
            await this.sendEmailNotification({
                recipientId: assigneeId,
                recipientEmail: assigneeEmail,
                recipientName: assigneeName,
                subject: emailTemplate.subject,
                message,
                htmlContent: emailTemplate.html,
                entityType: 'task',
                entityId: taskId,
                actionUrl,
                recipientType: notifications_entity_1.RecipientType.STAFF,
                createdBy,
                sendEmail: !!assigneeEmail,
                createInApp: true,
            });
            console.log(`✅ NotificationHelper: Task assignment notifications completed for ${taskTitle}`);
        }
        catch (error) {
            console.error(`❌ NotificationHelper: Failed to send task assignment notifications:`, error);
            throw error;
        }
    }
    async notifyLicenseExpiry(licenseId, customerId, customerEmail, licenseNumber, expiryDate, daysUntilExpiry, createdBy) {
        console.log(`📧 NotificationHelper: Processing license expiry notification for ${licenseNumber}`);
        const subject = `License ${licenseNumber} Expiry Notice`;
        const message = `Your license ${licenseNumber} will expire in ${daysUntilExpiry} days on ${expiryDate.toDateString()}. Please renew to avoid service interruption.`;
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #e02b20;">License Expiry Notice</h2>
        <p>Dear Valued Customer,</p>
        <p>Your license <strong>${licenseNumber}</strong> will expire in <strong>${daysUntilExpiry} days</strong> on <strong>${expiryDate.toDateString()}</strong>.</p>
        <p>Please renew your license to avoid service interruption.</p>
        <p>Best regards,<br>MACRA Team</p>
      </div>
    `;
        const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?license_id=${licenseId}`;
        try {
            await this.sendEmailNotification({
                recipientId: customerId,
                recipientEmail: customerEmail,
                subject,
                message,
                htmlContent,
                entityType: 'license',
                entityId: licenseId,
                actionUrl,
                recipientType: notifications_entity_1.RecipientType.CUSTOMER,
                createdBy,
                sendEmail: !!customerEmail,
                createInApp: true,
            });
            console.log(`✅ NotificationHelper: License expiry notifications completed for ${licenseNumber}`);
        }
        catch (error) {
            console.error(`❌ NotificationHelper: Failed to send license expiry notifications:`, error);
            throw error;
        }
    }
    async notifyTaskCompletion(taskId, applicationId, applicantId, applicantEmail, assigneeId, assigneeEmail, taskTitle, applicationNumber, outcome, createdBy, applicantName, licenseType, comments, nextSteps) {
        console.log(`📧 NotificationHelper: Processing task completion notification for ${taskTitle}`);
        const emailTemplate = this.emailTemplateService.generateTaskCompletedTemplate({
            applicantName: applicantName || 'Valued Customer',
            taskTitle,
            applicationNumber,
            licenseType: licenseType || 'License',
            completionDate: new Date().toLocaleDateString(),
            outcome,
            comments,
            nextSteps,
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
        });
        const message = `Task "${taskTitle}" for application ${applicationNumber} has been completed with outcome: ${outcome}`;
        const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`;
        if (applicantEmail) {
            try {
                await this.sendEmailNotification({
                    recipientId: applicantId,
                    recipientEmail: applicantEmail,
                    recipientName: applicantName,
                    subject: emailTemplate.subject,
                    message,
                    htmlContent: emailTemplate.html,
                    entityType: 'application',
                    entityId: applicationId,
                    actionUrl,
                    recipientType: notifications_entity_1.RecipientType.CUSTOMER,
                    createdBy,
                    sendEmail: true,
                    createInApp: true,
                });
            }
            catch (error) {
                console.error(`❌ Failed to notify applicant for task completion:`, error);
            }
        }
        if (assigneeEmail && assigneeId !== createdBy) {
            const assigneeMessage = `You have successfully completed task "${taskTitle}" for application ${applicationNumber}`;
            try {
                await this.sendEmailNotification({
                    recipientId: assigneeId,
                    recipientEmail: assigneeEmail,
                    subject: `Task Completed: ${taskTitle}`,
                    message: assigneeMessage,
                    htmlContent: `<p>${assigneeMessage}</p>`,
                    entityType: 'task',
                    entityId: taskId,
                    actionUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/tasks?task_id=${taskId}`,
                    recipientType: notifications_entity_1.RecipientType.STAFF,
                    createdBy,
                    sendEmail: false,
                    createInApp: true,
                });
            }
            catch (error) {
                console.error(`❌ Failed to notify assignee for task completion:`, error);
            }
        }
        console.log(`✅ NotificationHelper: Task completion notifications completed for ${taskTitle}`);
    }
    async notifyLicenseApproval(applicationId, applicantId, applicantEmail, applicationNumber, licenseNumber, licenseType, createdBy, applicantName, expiryDate) {
        console.log(`📧 NotificationHelper: Processing license approval notification for ${licenseNumber}`);
        const emailTemplate = this.emailTemplateService.generateLicenseApprovedTemplate({
            applicantName: applicantName || 'Valued Customer',
            applicationNumber,
            licenseType,
            licenseNumber,
            approvalDate: new Date().toLocaleDateString(),
            expiryDate: expiryDate || 'TBD',
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?license_number=${licenseNumber}`,
        });
        const message = `Congratulations! Your license application ${applicationNumber} has been approved. License Number: ${licenseNumber}`;
        const actionUrl = `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?license_number=${licenseNumber}`;
        try {
            await this.sendEmailNotification({
                recipientId: applicantId,
                recipientEmail: applicantEmail,
                recipientName: applicantName,
                subject: emailTemplate.subject,
                message,
                htmlContent: emailTemplate.html,
                entityType: 'application',
                entityId: applicationId,
                actionUrl,
                recipientType: notifications_entity_1.RecipientType.CUSTOMER,
                createdBy,
                sendEmail: !!applicantEmail,
                createInApp: true,
            });
            console.log(`✅ NotificationHelper: License approval notifications completed for ${licenseNumber}`);
        }
        catch (error) {
            console.error(`❌ NotificationHelper: Failed to send license approval notifications:`, error);
            throw error;
        }
    }
    async sendEmailNotification(params) {
        const { recipientId, recipientEmail, subject, message, htmlContent, entityType, entityId, actionUrl, recipientType = notifications_entity_1.RecipientType.CUSTOMER, createdBy, sendEmail = true, createInApp = true } = params;
        const results = {};
        try {
            if (sendEmail && recipientEmail) {
                console.log(`📧 NotificationHelper: Sending email to ${recipientEmail}`);
                console.log(`📧 Email subject: ${subject}`);
                await this.mailerService.sendMail({
                    to: recipientEmail,
                    subject: subject,
                    html: htmlContent,
                    attachments: [
                        {
                            filename: 'macra-logo.png',
                            path: (0, path_1.join)(app_module_1.assetsDir, 'macra-logo.png'),
                            cid: 'logo@macra',
                        },
                    ],
                });
                console.log(`✅ Email sent successfully to ${recipientEmail}`);
                results.emailNotification = await this.notificationsService.create({
                    type: notifications_entity_1.NotificationType.EMAIL,
                    recipient_type: recipientType,
                    recipient_id: recipientId,
                    recipient_email: recipientEmail,
                    subject,
                    message,
                    html_content: htmlContent,
                    entity_type: entityType,
                    entity_id: entityId,
                    status: notifications_entity_1.NotificationStatus.SENT,
                }, createdBy);
                await this.notificationsService.markAsSent(results.emailNotification.notification_id);
                console.log(`✅ Email notification record created with ID: ${results.emailNotification.notification_id}`);
            }
            return results;
        }
        catch (error) {
            console.error('❌ Failed to send email notification:', error);
            if (sendEmail && recipientEmail) {
                try {
                    results.emailNotification = await this.notificationsService.create({
                        type: notifications_entity_1.NotificationType.EMAIL,
                        recipient_type: recipientType,
                        recipient_id: recipientId,
                        recipient_email: recipientEmail,
                        subject,
                        message,
                        html_content: htmlContent,
                        entity_type: entityType,
                        entity_id: entityId,
                        status: notifications_entity_1.NotificationStatus.FAILED,
                    }, createdBy);
                    await this.notificationsService.markAsFailed(results.emailNotification.notification_id, error.message || 'Failed to send email');
                }
                catch (dbError) {
                    console.error('❌ Failed to create failed notification record:', dbError);
                }
            }
            throw error;
        }
    }
};
exports.NotificationHelperService = NotificationHelperService;
exports.NotificationHelperService = NotificationHelperService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [notifications_service_1.NotificationsService,
        email_template_service_1.EmailTemplateService,
        mailer_1.MailerService])
], NotificationHelperService);
//# sourceMappingURL=notification-helper.service.js.map