{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,KAOjB;QAPiB,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU,GAPiB;;IAQnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,AAAC,GAAgB,OAAd,YAAW,KAAyB,OAAtB,oBAAmB,KAAgD,OAA7C,YAAY,SAAS,kBAAkB,IAAG,KAAa,OAAV;IAEzG,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,CAAA,iBAAA,2BAAA,KAAM,UAAU,IAAG,AAAC,KAAoB,OAAhB,KAAK,UAAU,IAAK;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/types/license.ts"], "sourcesContent": ["import { LicenseCategory } from \".\";\r\nimport { User } from \"./user\";\r\n\r\nexport interface LicenseType {\r\n  license_type_id: string;\r\n  name: string;\r\n  validity?: number;\r\n  code?: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  categories?: LicenseCategory[];\r\n  iconColor?: string;\r\n  icon?: string;\r\n  iconBg?: string;\r\n}\r\n\r\nexport interface StatusAction {\r\n  status: ApplicationStatus;\r\n  label: string;\r\n  icon: string;\r\n  color: string;\r\n  hoverColor: string;\r\n  roles?: string[],\r\n  description: string;\r\n  confirmMessage?: string;\r\n}\r\n\r\n\r\nexport interface Applicant {\r\n  applicant_id: string;\r\n  name: string;\r\n  business_registration_number: string;\r\n  tpin: string;\r\n  website: string;\r\n  email: string;\r\n  phone: string;\r\n  fax?: string;\r\n  level_of_insurance_cover?: string;\r\n  address_id?: string;\r\n  contact_id?: string;\r\n  date_incorporation: string;\r\n  place_incorporation: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface Application extends Record<string, unknown> {\r\n  application_id: string;\r\n  application_number: string;\r\n  applicant_id: string;\r\n  license_category_id: string;\r\n  status: string;\r\n  current_step: number;\r\n  progress_percentage: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  assigned_to?: string;\r\n  assigned_at?: string;\r\n  application_data?: any;\r\n  applicant?: Applicant;\r\n  license_category?:LicenseCategory;\r\n  assignee?:User;\r\n}\r\n\r\nexport enum ApplicationStatus {\r\n  DRAFT = 'draft',\r\n  PENDING = 'pending',\r\n  SUBMITTED = 'submitted',\r\n  UNDER_REVIEW = 'under_review',\r\n  EVALUATION = 'evaluation',\r\n  PENDING_PAYMENT = 'pending_payment',\r\n  APPROVED = 'approved',\r\n  REJECTED = 'rejected',\r\n  WITHDRAWN = 'withdrawn',\r\n  PASS_EVALUATION = 'pass_evaluation',\r\n  WAITING_FOR_APPROVAL = 'waiting_for_approval',\r\n}\r\n\r\nexport interface ApplicationFilters {\r\n  licenseTypeId?: string;\r\n  licenseCategoryId?: string;\r\n  status?: ApplicationStatus | '';\r\n  dateRange?: string;\r\n  search?: string;\r\n}\r\n\r\nexport interface License extends Record<string, unknown> {\r\n  license_id: string;\r\n  license_number: string;\r\n  description?: string;\r\n  application_id: string;\r\n  applicant_id: string;\r\n  license_type_id: string;\r\n  status: LicenseStatus;\r\n  issue_date: string;\r\n  expiry_date: string;\r\n  issued_by: string;\r\n  code?: string;\r\n  conditions?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  application: Application;\r\n  issuer?:User;\r\n}\r\n\r\nexport enum LicenseStatus {\r\n  ACTIVE = 'active',\r\n  EXPIRED = 'expired',\r\n  SUSPENDED = 'suspended',\r\n  REVOKED = 'revoked',\r\n  UNDER_REVIEW = 'under_review',\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAkEO,IAAA,AAAK,2CAAA;;;;;;;;;;;;WAAA;;AAyCL,IAAA,AAAK,uCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/notificationService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { ApplicationStatus } from '../types/license';\r\nimport { AppNotification, NotificationSummary } from '@/types/notification';\r\n\r\n\r\nexport const notificationService = {\r\n  // Get user notifications\r\n  async getUserNotifications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    type?: string;\r\n    status?: 'read' | 'unread';\r\n  }): Promise<NotificationSummary> {\r\n    try {\r\n      const queryParams = new URLSearchParams();\r\n      \r\n      if (params?.page) queryParams.append('page', params.page.toString());\r\n      if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n      if (params?.type) queryParams.append('type', params.type);\r\n      if (params?.status) queryParams.append('status', params.status);\r\n\r\n      const endpoint = `/notifications/my-notifications?${queryParams.toString()}`;\r\n      const response = await apiClient.get(endpoint);\r\n      const data = processApiResponse(response).data;\r\n      \r\n      // Validate response structure\r\n      if (!data || typeof data !== 'object') {\r\n        console.warn('Invalid notification response format:', data);\r\n        return {\r\n          total_count: 0,\r\n          unread_count: 0,\r\n          notifications: []\r\n        };\r\n      }\r\n      \r\n      return {\r\n        total_count: data.length || 0,\r\n        unread_count: data.length || 0,\r\n        notifications: Array.isArray(data) ? data : []\r\n      };\r\n    } catch (error: any) {\r\n      // Convert params to string format for URLSearchParams\r\n      const stringParams: Record<string, string> = {};\r\n      if (params) {\r\n        if (params.page) stringParams.page = params.page.toString();\r\n        if (params.limit) stringParams.limit = params.limit.toString();\r\n        if (params.type) stringParams.type = params.type;\r\n        if (params.status) stringParams.status = params.status;\r\n      }\r\n\r\n      console.error('Error fetching user notifications:', {\r\n        error: error.message || 'Unknown error',\r\n        response: error.response?.data || null,\r\n        status: error.response?.status || null,\r\n        code: error.code || null,\r\n        params,\r\n        endpoint: `/notifications/my-notifications?${new URLSearchParams(stringParams).toString()}`\r\n      });\r\n      \r\n      // Return empty result on error instead of throwing\r\n      return {\r\n        total_count: 0,\r\n        unread_count: 0,\r\n        notifications: []\r\n      };\r\n    }\r\n  },\r\n\r\n  // Mark notification as read\r\n  async markAsRead(notificationId: string): Promise<void> {\r\n    const response = await apiClient.patch(`/notifications/${notificationId}/mark-read`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Mark all notifications as read (implemented client-side by marking each notification individually)\r\n  async markAllAsRead(): Promise<void> {\r\n    // This is handled by the useNotifications hook by calling markAsRead for each unread notification\r\n    // We don't need a separate backend endpoint for this\r\n    throw new Error('markAllAsRead should be handled by useNotifications hook');\r\n  },\r\n\r\n  // Create a status change notification (usually called from backend)\r\n  async createStatusChangeNotification(\r\n    applicationId: string,\r\n    oldStatus: ApplicationStatus,\r\n    newStatus: ApplicationStatus,\r\n    step?: number,\r\n    progressPercentage?: number\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/status-change', {\r\n      application_id: applicationId,\r\n      old_status: oldStatus,\r\n      new_status: newStatus,\r\n      step,\r\n      progress_percentage: progressPercentage\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete notification\r\n  async deleteNotification(notificationId: string): Promise<void> {\r\n    const response = await apiClient.delete(`/notifications/${notificationId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get notification count\r\n  async getNotificationCount(): Promise<{ total: number; unread: number }> {\r\n    const response = await apiClient.get('/notifications/count');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Manual notification trigger for testing (will be removed in production)\r\n  async triggerTestNotification(\r\n    applicationId: string,\r\n    applicationNumber: string,\r\n    licenseCategoryName: string,\r\n    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',\r\n    status: ApplicationStatus\r\n  ): Promise<Notification> {\r\n    const response = await apiClient.post('/notifications/test', {\r\n      application_id: applicationId,\r\n      application_number: applicationNumber,\r\n      license_category_name: licenseCategoryName,\r\n      type,\r\n      status\r\n    });\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Send activity note notification (creates notification which auto-creates activity note)\r\n  async sendActivityNoteNotification(data: {\r\n    applicationId: string;\r\n    message: string;\r\n    noteType?: string;\r\n    category?: string;\r\n    priority?: 'low' | 'normal' | 'high';\r\n    attachmentsCount?: number;\r\n  }): Promise<any> {\r\n    const response = await apiClient.post('/notifications/activity-note', {\r\n      application_id: data.applicationId,\r\n      message: data.message,\r\n      note_type: data.noteType || 'evaluation_comment',\r\n      category: data.category || 'communication',\r\n      priority: data.priority || 'normal',\r\n      attachments_count: data.attachmentsCount || 0,\r\n    });\r\n    return processApiResponse(response);\r\n  }\r\n};\r\n\r\n// Status change message templates\r\nexport const getStatusChangeMessage = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  oldStatus: string,\r\n  newStatus: string,\r\n  step?: number,\r\n  progressPercentage?: number\r\n): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {\r\n  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';\r\n  const stepText = step ? ` - Step ${step}` : '';\r\n  \r\n  const messages = {\r\n    [ApplicationStatus.DRAFT]: {\r\n      title: 'Application Draft Saved',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been saved as a draft. You can continue editing and submit it when ready.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING]: {\r\n      title: 'Application Pending',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is pending initial review. We'll notify you once the review begins.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.SUBMITTED]: {\r\n      title: 'Application Submitted',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.UNDER_REVIEW]: {\r\n      title: 'Application Under Review',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.EVALUATION]: {\r\n      title: 'Application Being Evaluated',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,\r\n      type: 'info' as const\r\n    },\r\n    [ApplicationStatus.PENDING_PAYMENT]: {\r\n      title: 'Payment Required',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been approved for payment. Please complete the payment to proceed with license issuance.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.APPROVED]: {\r\n      title: 'Application Approved! 🎉',\r\n      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,\r\n      type: 'success' as const\r\n    },\r\n    [ApplicationStatus.REJECTED]: {\r\n      title: 'Application Status Update',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,\r\n      type: 'warning' as const\r\n    },\r\n    [ApplicationStatus.WITHDRAWN]: {\r\n      title: 'Application Withdrawn',\r\n      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,\r\n      type: 'info' as const\r\n    }\r\n  };\r\n\r\n  const defaultMessage = {\r\n    title: 'Application Status Update',\r\n    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,\r\n    type: 'info' as const\r\n  };\r\n\r\n  return messages[newStatus as keyof typeof messages] || defaultMessage;\r\n};\r\n\r\n// Helper function to manually trigger notification for testing\r\nexport const createTestNotification = (\r\n  applicationNumber: string,\r\n  licenseCategoryName: string,\r\n  status: ApplicationStatus\r\n): AppNotification => {\r\n  const now = new Date().toISOString();\r\n  const notificationId = `test-${Date.now()}`;\r\n  \r\n  return {\r\n    notification_id: notificationId,\r\n    user_id: 'current-user',\r\n    application_id: `app-${applicationNumber}`,\r\n    application_number: applicationNumber,\r\n    license_category_name: licenseCategoryName,\r\n    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,\r\n    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,\r\n    type: 'status_change',\r\n    status: 'unread',\r\n    priority: 'medium',\r\n    created_at: now,\r\n    metadata: {\r\n      old_status: ApplicationStatus.SUBMITTED,\r\n      new_status: status,\r\n      step: 2,\r\n      progress_percentage: 50\r\n    }\r\n  };\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAIO,MAAM,sBAAsB;IACjC,yBAAyB;IACzB,MAAM,sBAAqB,MAK1B;QACC,IAAI;YACF,MAAM,cAAc,IAAI;YAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;YACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;YACpE,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI;YACxD,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;YAE9D,MAAM,WAAW,AAAC,mCAAyD,OAAvB,YAAY,QAAQ;YACxE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,IAAI;YAE9C,8BAA8B;YAC9B,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;gBACrC,QAAQ,IAAI,CAAC,yCAAyC;gBACtD,OAAO;oBACL,aAAa;oBACb,cAAc;oBACd,eAAe,EAAE;gBACnB;YACF;YAEA,OAAO;gBACL,aAAa,KAAK,MAAM,IAAI;gBAC5B,cAAc,KAAK,MAAM,IAAI;gBAC7B,eAAe,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;YAChD;QACF,EAAE,OAAO,OAAY;gBAYP,iBACF;YAZV,sDAAsD;YACtD,MAAM,eAAuC,CAAC;YAC9C,IAAI,QAAQ;gBACV,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ;gBACzD,IAAI,OAAO,KAAK,EAAE,aAAa,KAAK,GAAG,OAAO,KAAK,CAAC,QAAQ;gBAC5D,IAAI,OAAO,IAAI,EAAE,aAAa,IAAI,GAAG,OAAO,IAAI;gBAChD,IAAI,OAAO,MAAM,EAAE,aAAa,MAAM,GAAG,OAAO,MAAM;YACxD;YAEA,QAAQ,KAAK,CAAC,sCAAsC;gBAClD,OAAO,MAAM,OAAO,IAAI;gBACxB,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,IAAI,KAAI;gBAClC,QAAQ,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,KAAI;gBAClC,MAAM,MAAM,IAAI,IAAI;gBACpB;gBACA,UAAU,AAAC,mCAA+E,OAA7C,IAAI,gBAAgB,cAAc,QAAQ;YACzF;YAEA,mDAAmD;YACnD,OAAO;gBACL,aAAa;gBACb,cAAc;gBACd,eAAe,EAAE;YACnB;QACF;IACF;IAEA,4BAA4B;IAC5B,MAAM,YAAW,cAAsB;QACrC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,KAAK,CAAC,AAAC,kBAAgC,OAAf,gBAAe;QACxE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,qGAAqG;IACrG,MAAM;QACJ,kGAAkG;QAClG,qDAAqD;QACrD,MAAM,IAAI,MAAM;IAClB;IAEA,oEAAoE;IACpE,MAAM,gCACJ,aAAqB,EACrB,SAA4B,EAC5B,SAA4B,EAC5B,IAAa,EACb,kBAA2B;QAE3B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB;YAChB,YAAY;YACZ,YAAY;YACZ;YACA,qBAAqB;QACvB;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,sBAAsB;IACtB,MAAM,oBAAmB,cAAsB;QAC7C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,kBAAgC,OAAf;QAC1D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0EAA0E;IAC1E,MAAM,yBACJ,aAAqB,EACrB,iBAAyB,EACzB,mBAA2B,EAC3B,IAAmF,EACnF,MAAyB;QAEzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;YAC3D,gBAAgB;YAChB,oBAAoB;YACpB,uBAAuB;YACvB;YACA;QACF;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,0FAA0F;IAC1F,MAAM,8BAA6B,IAOlC;QACC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,gCAAgC;YACpE,gBAAgB,KAAK,aAAa;YAClC,SAAS,KAAK,OAAO;YACrB,WAAW,KAAK,QAAQ,IAAI;YAC5B,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ,IAAI;YAC3B,mBAAmB,KAAK,gBAAgB,IAAI;QAC9C;QACA,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA,WACA,WACA,MACA;IAEA,MAAM,eAAe,qBAAqB,AAAC,KAAuB,OAAnB,oBAAmB,iBAAe;IACjF,MAAM,WAAW,OAAO,AAAC,WAAe,OAAL,QAAS;IAE5C,MAAM,WAAW;QACf,CAAC,0HAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC,EAAE;YACzB,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,EAAE;YAC3B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,EAAE;YAChC,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAqE,OAArD,mBAAkB,qCAAkD,OAAf,cAAwB,OAAT,UAAS;YAClI,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkE,OAAlD,mBAAkB,kCAA+C,OAAf,cAAwB,OAAT,UAAS;YAC/H,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,EAAE;YACnC,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,AAAC,yBAA4D,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACxF,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;QACA,CAAC,0HAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC,EAAE;YAC7B,OAAO;YACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAkC,OAAlB,mBAAkB;YACvE,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS,AAAC,QAA2C,OAApC,qBAAoB,kBAAiE,OAAjD,mBAAkB,iCAA2D,OAA5B,UAAU,OAAO,CAAC,KAAK,MAAK;QAClI,MAAM;IACR;IAEA,OAAO,QAAQ,CAAC,UAAmC,IAAI;AACzD;AAGO,MAAM,yBAAyB,CACpC,mBACA,qBACA;IAEA,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,AAAC,QAAkB,OAAX,KAAK,GAAG;IAEvC,OAAO;QACL,iBAAiB;QACjB,SAAS;QACT,gBAAgB,AAAC,OAAwB,OAAlB;QACvB,oBAAoB;QACpB,uBAAuB;QACvB,OAAO,uBAAuB,mBAAmB,qBAAqB,0HAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,KAAK;QAChH,SAAS,uBAAuB,mBAAmB,qBAAqB,0HAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,QAAQ,OAAO;QACpH,MAAM;QACN,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,UAAU;YACR,YAAY,0HAAA,CAAA,oBAAiB,CAAC,SAAS;YACvC,YAAY;YACZ,MAAM;YACN,qBAAqB;QACvB;IACF;AACF", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ReactNode, useEffect } from 'react';\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: ReactNode;\r\n  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';\r\n  showCloseButton?: boolean;\r\n  closeOnOverlayClick?: boolean;\r\n}\r\n\r\nexport default function Modal({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  size = 'md',\r\n  showCloseButton = true,\r\n  closeOnOverlayClick = true,\r\n}: ModalProps) {\r\n  // Handle escape key press\r\n  useEffect(() => {\r\n    const handleEscape = (event: KeyboardEvent) => {\r\n      if (event.key === 'Escape' && isOpen) {\r\n        onClose();\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      document.addEventListener('keydown', handleEscape);\r\n      // Prevent body scroll when modal is open\r\n      document.body.style.overflow = 'hidden';\r\n    }\r\n\r\n    return () => {\r\n      document.removeEventListener('keydown', handleEscape);\r\n      document.body.style.overflow = 'unset';\r\n    };\r\n  }, [isOpen, onClose]);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const getSizeClasses = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'sm:max-w-sm';\r\n      case 'md':\r\n        return 'sm:max-w-md';\r\n      case 'lg':\r\n        return 'sm:max-w-lg';\r\n      case 'xl':\r\n        return 'sm:max-w-xl';\r\n      case '2xl':\r\n        return 'sm:max-w-2xl';\r\n      default:\r\n        return 'sm:max-w-md';\r\n    }\r\n  };\r\n\r\n  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {\r\n    if (closeOnOverlayClick && e.target === e.currentTarget) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4\">\r\n      <div\r\n        className=\"fixed inset-0\"\r\n        onClick={handleOverlayClick}\r\n        aria-hidden=\"true\"\r\n      />\r\n      \r\n      <div className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full ${getSizeClasses()} mx-4 transform transition-all max-h-[90vh] flex flex-col`}>\r\n        {/* Header - Fixed */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            {title}\r\n          </h3>\r\n          {showCloseButton && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1\"\r\n              aria-label=\"Close modal\"\r\n            >\r\n              <i className=\"ri-close-line text-xl\"></i>\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Scrollable Content */}\r\n        <div className=\"flex-1 overflow-hidden\">\r\n          <div className=\"overflow-y-auto h-full\">\r\n            <div className=\"p-6\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAce,SAAS,MAAM,KAQjB;QARiB,EAC5B,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,sBAAsB,IAAI,EACf,GARiB;;IAS5B,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,YAAY,QAAQ;wBACpC;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,yCAAyC;gBACzC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,uBAAuB,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YACvD;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS;gBACT,eAAY;;;;;;0BAGd,6LAAC;gBAAI,WAAW,AAAC,kEAAkF,OAAjB,kBAAiB;;kCAEjG,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,iCACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GA3FwB;KAAA", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationItem.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { Notification } from '@/hooks/useNotifications';\r\n\r\ninterface NotificationItemProps {\r\n  notification: Notification;\r\n  onMarkAsRead: (id: string) => void;\r\n  onNotificationClick?: (notification: Notification) => void;\r\n}\r\n\r\nconst NotificationItem: React.FC<NotificationItemProps> = ({\r\n  notification,\r\n  onMarkAsRead,\r\n  onNotificationClick,\r\n}) => {\r\n  const formatTimeAgo = (dateString: string) => {\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) {\r\n      return 'Just now';\r\n    } else if (diffInSeconds < 3600) {\r\n      const minutes = Math.floor(diffInSeconds / 60);\r\n      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 86400) {\r\n      const hours = Math.floor(diffInSeconds / 3600);\r\n      return `${hours} hour${hours > 1 ? 's' : ''} ago`;\r\n    } else if (diffInSeconds < 604800) {\r\n      const days = Math.floor(diffInSeconds / 86400);\r\n      return `${days} day${days > 1 ? 's' : ''} ago`;\r\n    } else {\r\n      return date.toLocaleDateString();\r\n    }\r\n  };\r\n\r\n  const getNotificationIcon = (type: string) => {\r\n    switch (type.toLowerCase()) {\r\n      case 'email':\r\n        return 'ri-mail-line';\r\n      case 'sms':\r\n        return 'ri-message-3-line';\r\n      case 'in_app':\r\n        return 'ri-notification-3-line';\r\n      case 'application_status':\r\n        return 'ri-file-list-3-line';\r\n      case 'evaluation_assigned':\r\n        return 'ri-user-settings-line';\r\n      case 'payment_due':\r\n        return 'ri-money-dollar-circle-line';\r\n      case 'license_expiry':\r\n        return 'ri-calendar-event-line';\r\n      case 'system_alert':\r\n        return 'ri-alert-line';\r\n      default:\r\n        return 'ri-notification-3-line';\r\n    }\r\n  };\r\n\r\n  const getPriorityColor = (priority: string) => {\r\n    switch (priority.toLowerCase()) {\r\n      case 'urgent':\r\n        return 'text-red-600 dark:text-red-400';\r\n      case 'high':\r\n        return 'text-orange-600 dark:text-orange-400';\r\n      case 'medium':\r\n        return 'text-yellow-600 dark:text-yellow-400';\r\n      case 'low':\r\n        return 'text-green-600 dark:text-green-400';\r\n      default:\r\n        return 'text-gray-600 dark:text-gray-400';\r\n    }\r\n  };\r\n\r\n  const handleItemClick = () => {\r\n    if (!notification.is_read) {\r\n      onMarkAsRead(notification.notification_id);\r\n    }\r\n    if (onNotificationClick) {\r\n      onNotificationClick(notification);\r\n    }\r\n  };\r\n\r\n  const handleMarkAsReadClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onMarkAsRead(notification.notification_id);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors duration-200 ${\r\n        !notification.is_read ? 'bg-blue-50 dark:bg-blue-900/20' : ''\r\n      }`}\r\n      onClick={handleItemClick}\r\n    >\r\n      <div className=\"flex items-start space-x-3\">\r\n        {/* Notification Icon */}\r\n        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${\r\n          !notification.is_read \r\n            ? 'bg-blue-100 dark:bg-blue-900/50' \r\n            : 'bg-gray-100 dark:bg-gray-700'\r\n        }`}>\r\n          <i className={`${getNotificationIcon(notification.type)} text-sm ${\r\n            !notification.is_read \r\n              ? 'text-blue-600 dark:text-blue-400' \r\n              : 'text-gray-600 dark:text-gray-400'\r\n          }`}></i>\r\n        </div>\r\n\r\n        {/* Notification Content */}\r\n        <div className=\"flex-1 min-w-0\">\r\n          <div className=\"flex items-start justify-between\">\r\n            <div className=\"flex-1\">\r\n              <h4 className={`text-sm font-medium ${\r\n                !notification.is_read \r\n                  ? 'text-gray-900 dark:text-gray-100' \r\n                  : 'text-gray-700 dark:text-gray-300'\r\n              }`}>\r\n                {notification.subject}\r\n              </h4>\r\n              <p className={`mt-1 text-sm ${\r\n                !notification.is_read \r\n                  ? 'text-gray-700 dark:text-gray-300' \r\n                  : 'text-gray-500 dark:text-gray-400'\r\n              }`}>\r\n                {notification.message}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Priority Indicator */}\r\n            {notification.priority && notification.priority !== 'medium' && (\r\n              <div className={`flex-shrink-0 ml-2 ${getPriorityColor(notification.priority)}`}>\r\n                <i className=\"ri-flag-line text-xs\"></i>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Metadata */}\r\n          <div className=\"mt-2 flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400\">\r\n              <span>{formatTimeAgo(notification.created_at)}</span>\r\n              {notification.entity_type && (\r\n                <span className=\"capitalize\">{notification.entity_type}</span>\r\n              )}\r\n            </div>\r\n\r\n            {/* Actions */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              {!notification.is_read && (\r\n                <button\r\n                  onClick={handleMarkAsReadClick}\r\n                  className=\"inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/50 hover:text-blue-800 dark:hover:text-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200\"\r\n                  title=\"Mark as read\"\r\n                >\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Mark as Read\r\n                </button>\r\n              )}\r\n              {notification.is_read && (\r\n                <span className=\"text-xs text-green-600 dark:text-green-400 flex items-center\">\r\n                  <i className=\"ri-check-line mr-1\"></i>\r\n                  Read\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Unread Indicator */}\r\n        {!notification.is_read && (\r\n          <div className=\"flex-shrink-0 w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full\"></div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationItem;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,MAAM,mBAAoD;QAAC,EACzD,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACpB;IACC,MAAM,gBAAgB,CAAC;QACrB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI;YACtB,OAAO;QACT,OAAO,IAAI,gBAAgB,MAAM;YAC/B,MAAM,UAAU,KAAK,KAAK,CAAC,gBAAgB;YAC3C,OAAO,AAAC,GAAmB,OAAjB,SAAQ,WAAgC,OAAvB,UAAU,IAAI,MAAM,IAAG;QACpD,OAAO,IAAI,gBAAgB,OAAO;YAChC,MAAM,QAAQ,KAAK,KAAK,CAAC,gBAAgB;YACzC,OAAO,AAAC,GAAe,OAAb,OAAM,SAA4B,OAArB,QAAQ,IAAI,MAAM,IAAG;QAC9C,OAAO,IAAI,gBAAgB,QAAQ;YACjC,MAAM,OAAO,KAAK,KAAK,CAAC,gBAAgB;YACxC,OAAO,AAAC,GAAa,OAAX,MAAK,QAA0B,OAApB,OAAO,IAAI,MAAM,IAAG;QAC3C,OAAO;YACL,OAAO,KAAK,kBAAkB;QAChC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa,OAAO,EAAE;YACzB,aAAa,aAAa,eAAe;QAC3C;QACA,IAAI,qBAAqB;YACvB,oBAAoB;QACtB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,EAAE,eAAe;QACjB,aAAa,aAAa,eAAe;IAC3C;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,2IAEX,OADC,CAAC,aAAa,OAAO,GAAG,mCAAmC;QAE7D,SAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAW,AAAC,uEAIhB,OAHC,CAAC,aAAa,OAAO,GACjB,oCACA;8BAEJ,cAAA,6LAAC;wBAAE,WAAW,AAAC,GACb,OADe,oBAAoB,aAAa,IAAI,GAAE,aAIvD,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;;;;;;;;;;;8BAKR,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAW,AAAC,uBAIf,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;sDAEH,aAAa,OAAO;;;;;;sDAEvB,6LAAC;4CAAE,WAAW,AAAC,gBAId,OAHC,CAAC,aAAa,OAAO,GACjB,qCACA;sDAEH,aAAa,OAAO;;;;;;;;;;;;gCAKxB,aAAa,QAAQ,IAAI,aAAa,QAAQ,KAAK,0BAClD,6LAAC;oCAAI,WAAW,AAAC,sBAA6D,OAAxC,iBAAiB,aAAa,QAAQ;8CAC1E,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAM,cAAc,aAAa,UAAU;;;;;;wCAC3C,aAAa,WAAW,kBACvB,6LAAC;4CAAK,WAAU;sDAAc,aAAa,WAAW;;;;;;;;;;;;8CAK1D,6LAAC;oCAAI,WAAU;;wCACZ,CAAC,aAAa,OAAO,kBACpB,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAM;;8DAEN,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;wCAIzC,aAAa,OAAO,kBACnB,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;gBAS/C,CAAC,aAAa,OAAO,kBACpB,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;KArKM;uCAuKS", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useNotifications.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\nexport interface NotificationCounts {\r\n  total: number;\r\n  unread: number;\r\n}\r\n\r\nexport interface UseNotificationsReturn {\r\n  notifications: AppNotification[];\r\n  unreadCount: number;\r\n  totalCount: number;\r\n  loading: boolean;\r\n  error: string | null;\r\n  fetchNotifications: () => Promise<void>;\r\n  markAsRead: (id: string) => Promise<void>;\r\n  markAllAsRead: () => Promise<void>;\r\n  refreshNotifications: () => Promise<void>;\r\n  getNotificationCounts: () => Promise<void>;\r\n}\r\n\r\nexport const useNotifications = (): UseNotificationsReturn => {\r\n  const [notifications, setNotifications] = useState<AppNotification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [totalCount, setTotalCount] = useState(0);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { user, isAuthenticated } = useAuth();\r\n  const { showError } = useToast();\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      console.log('🔍 Fetching notifications for user:', {\r\n        userId: user.user_id,\r\n        email: user.email\r\n      });\r\n\r\n      const data = await notificationService.getUserNotifications({\r\n        page: 1,\r\n        limit: 50 // Get more notifications for better UX\r\n      });\r\n\r\n      if (data && data.notifications) {\r\n        setNotifications(data.notifications);\r\n        setUnreadCount(data.unread_count);\r\n        setTotalCount(data.total_count);\r\n      } else {\r\n        setNotifications([]);\r\n        setUnreadCount(0);\r\n        setTotalCount(0);\r\n      }\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch notifications';\r\n      setError(errorMessage);\r\n      console.error('Error fetching notifications:', err);\r\n      // Don't show toast error for initial load failures\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [isAuthenticated, user]);\r\n\r\n  const markAsRead = useCallback(async (id: string) => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await notificationService.markAsRead(id);\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notification =>\r\n          notification.notification_id === id\r\n            ? { ...notification, status: 'read', read_at: new Date().toISOString() }\r\n            : notification\r\n        )\r\n      );\r\n\r\n      // Update unread count\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark notification as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark notification as read');\r\n      console.error('Error marking notification as read:', err);\r\n    }\r\n  }, [isAuthenticated, showError]);\r\n\r\n  const markAllAsRead = useCallback(async () => {\r\n    if (!isAuthenticated || notifications.length === 0) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Mark all unread notifications as read\r\n      const unreadNotifications = notifications.filter(n => n.status === 'unread');\r\n\r\n      for (const notification of unreadNotifications) {\r\n        await markAsRead(notification.notification_id);\r\n      }\r\n\r\n      setUnreadCount(0);\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : 'Failed to mark all notifications as read';\r\n      setError(errorMessage);\r\n      showError('Failed to mark all notifications as read');\r\n      console.error('Error marking all notifications as read:', err);\r\n    }\r\n  }, [isAuthenticated, notifications, markAsRead, showError]);\r\n\r\n  const getNotificationCounts = useCallback(async () => {\r\n    if (!isAuthenticated) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const counts = await notificationService.getNotificationCount();\r\n      setUnreadCount(counts.unread);\r\n      setTotalCount(counts.total);\r\n    } catch (err) {\r\n      console.error('Error getting notification counts:', err);\r\n    }\r\n  }, [isAuthenticated]);\r\n\r\n  const refreshNotifications = useCallback(async () => {\r\n    await fetchNotifications();\r\n  }, [fetchNotifications]);\r\n\r\n  // Initial fetch when component mounts and user is authenticated\r\n  useEffect(() => {\r\n    if (isAuthenticated && user) {\r\n      fetchNotifications();\r\n    }\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  // Set up polling for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    if (!isAuthenticated || !user) {\r\n      return;\r\n    }\r\n\r\n    const interval = setInterval(() => {\r\n      fetchNotifications();\r\n    }, 30000); // 30 seconds\r\n\r\n    return () => clearInterval(interval);\r\n  }, [isAuthenticated, user, fetchNotifications]);\r\n\r\n  return {\r\n    notifications,\r\n    unreadCount,\r\n    totalCount,\r\n    loading,\r\n    error,\r\n    fetchNotifications,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n    getNotificationCounts,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;;AALA;;;;;AA0BO,MAAM,mBAAmB;;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAE7B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACrC,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,WAAW;YACX,SAAS;YAET,IAAI;gBACF,QAAQ,GAAG,CAAC,uCAAuC;oBACjD,QAAQ,KAAK,OAAO;oBACpB,OAAO,KAAK,KAAK;gBACnB;gBAEA,MAAM,OAAO,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;oBAC1D,MAAM;oBACN,OAAO,GAAG,uCAAuC;gBACnD;gBAEA,IAAI,QAAQ,KAAK,aAAa,EAAE;oBAC9B,iBAAiB,KAAK,aAAa;oBACnC,eAAe,KAAK,YAAY;oBAChC,cAAc,KAAK,WAAW;gBAChC,OAAO;oBACL,iBAAiB,EAAE;oBACnB,eAAe;oBACf,cAAc;gBAChB;YACF,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,mDAAmD;YACrD,SAAU;gBACR,WAAW;YACb;QACF;2DAAG;QAAC;QAAiB;KAAK;IAE1B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACpC,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,yIAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;gBAErC,qBAAqB;gBACrB;gEAAiB,CAAA,OACf,KAAK,GAAG;wEAAC,CAAA,eACP,aAAa,eAAe,KAAK,KAC7B;oCAAE,GAAG,YAAY;oCAAE,QAAQ;oCAAQ,SAAS,IAAI,OAAO,WAAW;gCAAG,IACrE;;;gBAIR,sBAAsB;gBACtB;gEAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;;YAC5C,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;mDAAG;QAAC;QAAiB;KAAU;IAE/B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAChC,IAAI,CAAC,mBAAmB,cAAc,MAAM,KAAK,GAAG;gBAClD;YACF;YAEA,IAAI;gBACF,wCAAwC;gBACxC,MAAM,sBAAsB,cAAc,MAAM;uFAAC,CAAA,IAAK,EAAE,MAAM,KAAK;;gBAEnE,KAAK,MAAM,gBAAgB,oBAAqB;oBAC9C,MAAM,WAAW,aAAa,eAAe;gBAC/C;gBAEA,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,SAAS;gBACT,UAAU;gBACV,QAAQ,KAAK,CAAC,4CAA4C;YAC5D;QACF;sDAAG;QAAC;QAAiB;QAAe;QAAY;KAAU;IAE1D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE;YACxC,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,IAAI;gBACF,MAAM,SAAS,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB;gBAC7D,eAAe,OAAO,MAAM;gBAC5B,cAAc,OAAO,KAAK;YAC5B,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;8DAAG;QAAC;KAAgB;IAEpB,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACvC,MAAM;QACR;6DAAG;QAAC;KAAmB;IAEvB,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,mBAAmB,MAAM;gBAC3B;YACF;QACF;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B;YACF;YAEA,MAAM,WAAW;uDAAY;oBAC3B;gBACF;sDAAG,QAAQ,aAAa;YAExB;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;QAAiB;QAAM;KAAmB;IAE9C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlJa;;QAMuB,kIAAA,CAAA,UAAO;QACnB,mIAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/notifications/NotificationModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Modal from '@/components/common/Modal';\r\nimport NotificationItem from './NotificationItem';\r\nimport Loader from '@/components/Loader';\r\nimport { useNotifications } from '@/hooks/useNotifications';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport { AppNotification } from '@/types/notification';\r\n\r\ninterface NotificationModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nconst NotificationModal: React.FC<NotificationModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n}) => {\r\n  const {\r\n    notifications,\r\n    unreadCount,\r\n    loading,\r\n    error,\r\n    markAsRead,\r\n    markAllAsRead,\r\n    refreshNotifications,\r\n  } = useNotifications();\r\n  \r\n  const { showSuccess, showError } = useToast();\r\n  const [filter, setFilter] = useState<'all' | 'unread'>('unread');\r\n  const [localLoading, setLocalLoading] = useState(false);\r\n\r\n  // Filter notifications based on selected filter\r\n  const filteredNotifications = notifications.filter(notification => {\r\n    if (filter === 'unread') {\r\n      return !notification.read_at;\r\n    }\r\n    return true;\r\n  });\r\n\r\n\r\n  // Refresh notifications when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      refreshNotifications();\r\n    }\r\n  }, [isOpen, refreshNotifications]);\r\n\r\n  const handleMarkAsRead = async (id: string) => {\r\n    try {\r\n      await markAsRead(id);\r\n      showSuccess('Notification marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking notification as read:', error);\r\n      showError('Failed to mark notification as read');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    if (unreadCount === 0) {\r\n      showError('No unread notifications to mark');\r\n      return;\r\n    }\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await markAllAsRead();\r\n      showSuccess('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      showError('Failed to mark all notifications as read');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleNotificationClick = (notification: AppNotification) => {\r\n    // Handle notification click - could navigate to related page\r\n    if (notification.entity_type === 'application' && notification.entity_id) {\r\n      // Navigate to application details\r\n      window.location.href = `/customer/my-licenses?application_id=${notification.entity_id}`;\r\n    }\r\n  };\r\n\r\n  const handleRefresh = async () => {\r\n    setLocalLoading(true);\r\n    try {\r\n      await refreshNotifications();\r\n    } catch (error) {\r\n      console.error('Error refreshing notifications:', error);\r\n      showError('Failed to refresh notifications');\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Modal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title=\"Notifications\"\r\n      size=\"lg\"\r\n    >\r\n      <div className=\"flex flex-col h-96\">\r\n        {/* Header with filters and actions */}\r\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center space-x-4\">\r\n            {/* Filter buttons */}\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                onClick={() => setFilter('all')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'all'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                All ({notifications.length})\r\n              </button>\r\n              <button\r\n                onClick={() => setFilter('unread')}\r\n                className={`px-3 py-1 text-sm rounded-md transition-colors duration-200 ${\r\n                  filter === 'unread'\r\n                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'\r\n                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'\r\n                }`}\r\n              >\r\n                Unread ({unreadCount})\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action buttons */}\r\n          <div className=\"flex items-center space-x-2\">\r\n            <button\r\n              onClick={handleRefresh}\r\n              disabled={loading || localLoading}\r\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50\"\r\n              title=\"Refresh notifications\"\r\n            >\r\n              <i className={`ri-refresh-line ${(loading || localLoading) ? 'animate-spin' : ''}`}></i>\r\n            </button>\r\n            \r\n            {unreadCount > 0 && (\r\n              <button\r\n                onClick={handleMarkAllAsRead}\r\n                disabled={loading || localLoading}\r\n                className=\"px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50\"\r\n              >\r\n                Mark all as read\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Notifications list */}\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          {loading && notifications.length === 0 ? (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <Loader message=\"Loading notifications...\" />\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-error-warning-line text-4xl text-red-500 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                Error Loading Notifications\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n                {error}\r\n              </p>\r\n              <button\r\n                onClick={handleRefresh}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200\"\r\n              >\r\n                Try Again\r\n              </button>\r\n            </div>\r\n          ) : filteredNotifications.length === 0 ? (\r\n            <div className=\"flex flex-col items-center justify-center h-full text-center p-4\">\r\n              <i className=\"ri-notification-off-line text-4xl text-gray-400 mb-2\"></i>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                {filter === 'unread' ? 'No Unread Notifications' : 'No Notifications'}\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {filter === 'unread' \r\n                  ? 'All caught up! You have no unread notifications.'\r\n                  : 'You have no notifications at this time.'\r\n                }\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\r\n              {filteredNotifications.map((notification) => (\r\n                <NotificationItem\r\n                  key={notification.notification_id}\r\n                  notification={notification}\r\n                  onMarkAsRead={handleMarkAsRead}\r\n                  onNotificationClick={handleNotificationClick}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        {filteredNotifications.length > 0 && (\r\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700 text-center\">\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Showing {filteredNotifications.length} of {notifications.length} notifications\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default NotificationModal;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAeA,MAAM,oBAAsD;QAAC,EAC3D,MAAM,EACN,OAAO,EACR;;IACC,MAAM,EACJ,aAAa,EACb,WAAW,EACX,OAAO,EACP,KAAK,EACL,UAAU,EACV,aAAa,EACb,oBAAoB,EACrB,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gDAAgD;IAChD,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAA;QACjD,IAAI,WAAW,UAAU;YACvB,OAAO,CAAC,aAAa,OAAO;QAC9B;QACA,OAAO;IACT;IAGA,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,QAAQ;gBACV;YACF;QACF;sCAAG;QAAC;QAAQ;KAAqB;IAEjC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW;YACjB,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,GAAG;YACrB,UAAU;YACV;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,6DAA6D;QAC7D,IAAI,aAAa,WAAW,KAAK,iBAAiB,aAAa,SAAS,EAAE;YACxE,kCAAkC;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,AAAC,wCAA8D,OAAvB,aAAa,SAAS;QACvF;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,UAAU;QACZ,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,wIAAA,CAAA,UAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAEb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,+DAIX,OAHC,WAAW,QACP,qEACA;;4CAEP;4CACO,cAAc,MAAM;4CAAC;;;;;;;kDAE7B,6LAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,+DAIX,OAHC,WAAW,WACP,qEACA;;4CAEP;4CACU;4CAAY;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC;wCAAE,WAAW,AAAC,mBAAkE,OAAhD,AAAC,WAAW,eAAgB,iBAAiB;;;;;;;;;;;gCAG/E,cAAc,mBACb,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;8BACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+HAAA,CAAA,UAAM;4BAAC,SAAQ;;;;;;;;;;mEAEhB,sBACF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,6LAAC;gCAAE,WAAU;0CACV;;;;;;0CAEH,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;mEAID,sBAAsB,MAAM,KAAK,kBACnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;gCAAG,WAAU;0CACX,WAAW,WAAW,4BAA4B;;;;;;0CAErD,6LAAC;gCAAE,WAAU;0CACV,WAAW,WACR,qDACA;;;;;;;;;;;iFAKR,6LAAC;wBAAI,WAAU;kCACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC,0JAAA,CAAA,UAAgB;gCAEf,cAAc;gCACd,cAAc;gCACd,qBAAqB;+BAHhB,aAAa,eAAe;;;;;;;;;;;;;;;gBAW1C,sBAAsB,MAAM,GAAG,mBAC9B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAA2C;4BAC7C,sBAAsB,MAAM;4BAAC;4BAAK,cAAc,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;;;;;AAO9E;GAzMM;;QAYA,mIAAA,CAAA,mBAAgB;QAEe,mIAAA,CAAA,WAAQ;;;KAdvC;uCA2MS", "debugId": null}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { notificationService } from '@/services/notificationService';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useToast } from '@/contexts/ToastContext';\r\nimport NotificationModal from '../notifications/NotificationModal';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n}\r\n\r\nconst NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {\r\n  const { user } = useAuth();\r\n  const { showError } = useToast();\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  // Fetch notification count\r\n  const fetchNotificationCount = async () => {\r\n    if (!user) return;\r\n    \r\n    try {\r\n      const data = await notificationService.getNotificationCount();\r\n      setUnreadCount(data.unread);\r\n    } catch (error) {\r\n      console.error('Error fetching notification count:', error);\r\n      showError('Failed to load notification count');\r\n    }\r\n  };\r\n\r\n  // Initial fetch\r\n  useEffect(() => {\r\n    fetchNotificationCount();\r\n  }, [user]);\r\n\r\n  // Poll for new notifications every 30 seconds\r\n  useEffect(() => {\r\n    const interval = setInterval(fetchNotificationCount, 30000);\r\n    return () => clearInterval(interval);\r\n  }, [user]);\r\n\r\n  // Handle modal close and refresh count\r\n  const handleModalClose = () => {\r\n    setIsModalOpen(false);\r\n    fetchNotificationCount(); // Refresh count when modal closes\r\n  };\r\n\r\n  if (!user) return null;\r\n\r\n  return (\r\n    <>\r\n      <div className={`relative ${className}`}>\r\n        {/* Notification Bell */}\r\n        <button\r\n          onClick={() => setIsModalOpen(true)}\r\n          className=\"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n          title=\"Notifications\"\r\n        >\r\n          <i className=\"ri-notification-line text-xl\"></i>\r\n          {unreadCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1\">\r\n              {unreadCount > 99 ? '99+' : unreadCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </div>\r\n\r\n      {/* Notification Modal */}\r\n      <NotificationModal\r\n        isOpen={isModalOpen}\r\n        onClose={handleModalClose}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default NotificationBell;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYA,MAAM,mBAAoD;QAAC,EAAE,YAAY,EAAE,EAAE;;IAC3E,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,MAAM,OAAO,MAAM,yIAAA,CAAA,sBAAmB,CAAC,oBAAoB;YAC3D,eAAe,KAAK,MAAM;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG;QAAC;KAAK;IAET,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW,YAAY,wBAAwB;YACrD;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;KAAK;IAET,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,eAAe;QACf,0BAA0B,kCAAkC;IAC9D;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,6LAAC;gBAAI,WAAW,AAAC,YAAqB,OAAV;0BAE1B,cAAA,6LAAC;oBACC,SAAS,IAAM,eAAe;oBAC9B,WAAU;oBACV,OAAM;;sCAEN,6LAAC;4BAAE,WAAU;;;;;;wBACZ,cAAc,mBACb,6LAAC;4BAAK,WAAU;sCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAOpC,6LAAC,2JAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS;;;;;;;;AAIjB;GA/DM;;QACa,kIAAA,CAAA,UAAO;QACF,mIAAA,CAAA,WAAQ;;;KAF1B;uCAiES", "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\nimport NotificationBell from '../common/NotificationBell';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'My Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Invoices & Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Data Protection',\r\n      href: '/customer/data-protection',\r\n      icon: 'ri-shield-keyhole-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/data-protection',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/my-licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/apply/': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/data-protection': 'Loading Data Protection...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <NotificationBell className=\"mr-4\" />\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAqBA,MAAM,iBAAgD;QAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM;gBACpC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;aACD;kDAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM;gBACjC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;+CAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,MAAM,gBAAgB;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,cAAc,OAAO;kEAAC,CAAA;4BACpB,OAAO,QAAQ,CAAC;wBAClB;;gBACF;;YAEA,4DAA4D;YAC5D,MAAM,QAAQ,WAAW,eAAe;YACxC;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,uBAAuB,CAAC;QAC1B;0DAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAChD,MAAM,eAA0C;gBAC9C,aAAa;gBACb,yBAAyB;gBACzB,0BAA0B;gBAC1B,iCAAiC;gBACjC,sBAAsB;gBACtB,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,6BAA6B;gBAC7B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;YAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,AAAC,WAAe,OAAL,MAAK;YACtD,WAAW;YACX,uBAAuB;QACzB;qDAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,2CAA2C;YAC3C,OAAO,QAAQ,CAAC;QAClB;qDAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,sBAAsB,CAAC;QACzB;yDAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,qCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,6LAAC;gBAAM,WAAW,AAAC,6KAE8D,OAA7E,sBAAsB,kBAAkB,sCAAqC;0BAE/E,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,AAAC,0JAKT,OAHC,KAAK,OAAO,GACV,8GACA,yHACH;;8DAGH,6LAAC;oDAAI,WAAW,AAAC,iDAAqG,OAArD,KAAK,OAAO,GAAG,mCAAmC;8DACjH,cAAA,6LAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAqB5B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,6LAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;iHAGb,6LAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mJAAA,CAAA,UAAgB;4CAAC,WAAU;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,gIAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,CAAA,iBAAA,2BAAA,KAAM,aAAa,KAAI;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,6LAAC,qIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlTM;;QAGa,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACP,kIAAA,CAAA,UAAO;QACD,qIAAA,CAAA,aAAU;;;KAN7B;uCAoTS", "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { customerApi } from '../lib/customer-api';\r\nimport { License, PaginatedResponse } from '@/types';\r\n\r\n\r\nexport const licenseService = {\r\n  // Get all licenses with pagination\r\n  async getLicenses(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    status?: string;\r\n    licenseType?: string;\r\n    dateRange?: string;\r\n  }): Promise<PaginatedResponse<License> > {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    if (params?.licenseType) queryParams.append('filter.licenseType', params.licenseType);\r\n    if (params?.dateRange) queryParams.append('filter.dateRange', params.dateRange);\r\n\r\n    const response = await apiClient.get(`/licenses?${queryParams.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get single license by ID\r\n  async getLicense(id: string): Promise<License> {\r\n    const response = await apiClient.get(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license by application ID\r\n  async getLicenseByApplication(applicationId: string): Promise<License | null> {\r\n    try {\r\n      // Since there's no direct endpoint, we'll get all licenses and filter by application_id\r\n      // In a real implementation, you'd want to add this endpoint to the backend\r\n      const response = await apiClient.get(`/licenses?filter.application_id=${applicationId}`);\r\n      const result = processApiResponse(response);\r\n      \r\n      if (result.data && result.data.length > 0) {\r\n        return result.data[0]; // Return the first (and should be only) license for this application\r\n      }\r\n      \r\n      return null;\r\n    } catch (error) {\r\n      console.error('Error getting license by application:', error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  // Get licenses by applicant\r\n  async getLicensesByApplicant(applicantId: string): Promise<License[]> {\r\n    const response = await apiClient.get(`/licenses/by-applicant/${applicantId}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get license by license number\r\n  async getLicenseByNumber(licenseNumber: string): Promise<License> {\r\n    const response = await apiClient.get(`/licenses/by-number/${licenseNumber}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Download license PDF\r\n  async downloadLicensePDF(licenseId: string): Promise<Blob> {\r\n    try {\r\n      console.log('Attempting to download license PDF for ID:', licenseId);\r\n\r\n      // Use the existing customer API method\r\n      const blob = await customerApi.downloadLicensePDF(licenseId);\r\n\r\n      console.log('PDF download successful via customer API');\r\n      return blob;\r\n    } catch (error: any) {\r\n      console.error('Error downloading license PDF:', {\r\n        licenseId,\r\n        error: error.message,\r\n        status: error.response?.status,\r\n        statusText: error.response?.statusText,\r\n        data: error.response?.data,\r\n        url: error.config?.url,\r\n        baseURL: error.config?.baseURL\r\n      });\r\n\r\n      // Provide more specific error messages\r\n      if (error.response?.status === 404) {\r\n        throw new Error('License not found or PDF generation failed');\r\n      } else if (error.response?.status === 403) {\r\n        throw new Error('Access denied - insufficient permissions to download this license');\r\n      } else if (error.response?.status === 401) {\r\n        throw new Error('Authentication required - please log in again');\r\n      } else if (error.code === 'ERR_NETWORK') {\r\n        throw new Error('Network error - please check your connection and try again');\r\n      } else {\r\n        throw new Error(`Failed to download license PDF: ${error.message || 'Unknown error'}`);\r\n      }\r\n    }\r\n  },\r\n\r\n  // Get license statistics\r\n  async getLicenseStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/licenses/stats');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get licenses expiring soon\r\n  async getExpiringSoon(days: number = 30): Promise<License[]> {\r\n    const response = await apiClient.get(`/licenses/expiring-soon?days=${days}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new license (admin only)\r\n  async createLicense(data: {\r\n    license_number: string;\r\n    application_id: string;\r\n    applicant_id: string;\r\n    license_type_id: string;\r\n    status?: string;\r\n    issue_date: string;\r\n    expiry_date: string;\r\n    issued_by: string;\r\n    conditions?: string;\r\n  }): Promise<License> {\r\n    const response = await apiClient.post('/licenses', data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update license (admin only)\r\n  async updateLicense(id: string, data: Partial<License>): Promise<License> {\r\n    const response = await apiClient.put(`/licenses/${id}`, data);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete license (admin only)\r\n  async deleteLicense(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/licenses/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAIO,MAAM,iBAAiB;IAC5B,mCAAmC;IACnC,MAAM,aAAY,MASjB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,mBAAA,6BAAA,OAAQ,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QACvE,IAAI,mBAAA,6BAAA,OAAQ,MAAM,EAAE,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QACrE,IAAI,mBAAA,6BAAA,OAAQ,WAAW,EAAE,YAAY,MAAM,CAAC,sBAAsB,OAAO,WAAW;QACpF,IAAI,mBAAA,6BAAA,OAAQ,SAAS,EAAE,YAAY,MAAM,CAAC,oBAAoB,OAAO,SAAS;QAE9E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAmC,OAAvB,YAAY,QAAQ;QACtE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,2BAA2B;IAC3B,MAAM,YAAW,EAAU;QACzB,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAe,OAAH;QAClD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,yBAAwB,aAAqB;QACjD,IAAI;YACF,wFAAwF;YACxF,2EAA2E;YAC3E,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,mCAAgD,OAAd;YACxE,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAElC,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;gBACzC,OAAO,OAAO,IAAI,CAAC,EAAE,EAAE,qEAAqE;YAC9F;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,wBAAuB,WAAmB;QAC9C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,0BAAqC,OAAZ;QAC/D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gCAAgC;IAChC,MAAM,oBAAmB,aAAqB;QAC5C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,uBAAoC,OAAd;QAC5D,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,uBAAuB;IACvB,MAAM,oBAAmB,SAAiB;QACxC,IAAI;YACF,QAAQ,GAAG,CAAC,8CAA8C;YAE1D,uCAAuC;YACvC,MAAM,OAAO,MAAM,gIAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;YAElD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAY;gBAIT,iBACI,kBACN,kBACD,eACI,gBAIP,kBAEO,kBAEA;YAfX,QAAQ,KAAK,CAAC,kCAAkC;gBAC9C;gBACA,OAAO,MAAM,OAAO;gBACpB,MAAM,GAAE,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM;gBAC9B,UAAU,GAAE,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,UAAU;gBACtC,IAAI,GAAE,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,IAAI;gBAC1B,GAAG,GAAE,gBAAA,MAAM,MAAM,cAAZ,oCAAA,cAAc,GAAG;gBACtB,OAAO,GAAE,iBAAA,MAAM,MAAM,cAAZ,qCAAA,eAAc,OAAO;YAChC;YAEA,uCAAuC;YACvC,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB,OAAO,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBACzC,MAAM,IAAI,MAAM;YAClB,OAAO,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBACzC,MAAM,IAAI,MAAM;YAClB,OAAO,IAAI,MAAM,IAAI,KAAK,eAAe;gBACvC,MAAM,IAAI,MAAM;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM,AAAC,mCAAmE,OAAjC,MAAM,OAAO,IAAI;YACtE;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,6BAA6B;IAC7B,MAAM;YAAgB,OAAA,iEAAe;QACnC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,gCAAoC,OAAL;QACrE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kCAAkC;IAClC,MAAM,eAAc,IAUnB;QACC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,aAAa;QACnD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU,EAAE,IAAsB;QACpD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,aAAe,OAAH,KAAM;QACxD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,EAAU;QAC5B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,aAAe,OAAH;QACrD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 2291, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Pagination.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\n// Import the pagination response type from the existing structure\r\ninterface PaginationMeta {\r\n  itemsPerPage: number;\r\n  totalItems: number;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  sortBy: [string, string][];\r\n  searchBy: string[];\r\n  search: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\ninterface PaginationProps {\r\n  meta: PaginationMeta;\r\n  onPageChange: (page: number) => void;\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  showFirstLast?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showInfo?: boolean;\r\n  maxVisiblePages?: number;\r\n  pageSizeOptions?: number[];\r\n  className?: string;\r\n}\r\n\r\nconst Pagination: React.FC<PaginationProps> = ({\r\n  meta,\r\n  onPageChange,\r\n  onPageSizeChange,\r\n  showFirstLast = true,\r\n  showPageSizeSelector = true,\r\n  showInfo = true,\r\n  maxVisiblePages = 7,\r\n  pageSizeOptions = [10, 25, 50, 100],\r\n  className = ''\r\n}) => {\r\n  const { currentPage, totalPages, totalItems, itemsPerPage } = meta;\r\n\r\n  // Don't render if there's only one page or no pages and no additional features\r\n  if (totalPages <= 1 && !showPageSizeSelector && !showInfo) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate current items range\r\n  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;\r\n  const endItem = Math.min(currentPage * itemsPerPage, totalItems);\r\n\r\n  // Calculate which pages to show\r\n  const getVisiblePages = (): (number | string)[] => {\r\n    const pages: (number | string)[] = [];\r\n    \r\n    // If total pages is less than or equal to maxVisiblePages, show all\r\n    if (totalPages <= maxVisiblePages) {\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        pages.push(i);\r\n      }\r\n      return pages;\r\n    }\r\n\r\n    // Always show first page\r\n    pages.push(1);\r\n\r\n    // Calculate start and end of the visible range around current page\r\n    const sidePages = Math.floor((maxVisiblePages - 3) / 2); // -3 for first, last, and current\r\n    let startPage = Math.max(2, currentPage - sidePages);\r\n    let endPage = Math.min(totalPages - 1, currentPage + sidePages);\r\n\r\n    // Adjust if we're near the beginning\r\n    if (currentPage <= sidePages + 2) {\r\n      startPage = 2;\r\n      endPage = Math.min(totalPages - 1, maxVisiblePages - 1);\r\n    }\r\n\r\n    // Adjust if we're near the end\r\n    if (currentPage >= totalPages - sidePages - 1) {\r\n      startPage = Math.max(2, totalPages - maxVisiblePages + 2);\r\n      endPage = totalPages - 1;\r\n    }\r\n\r\n    // Add ellipsis after first page if needed\r\n    if (startPage > 2) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Add pages in the visible range\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    // Add ellipsis before last page if needed\r\n    if (endPage < totalPages - 1) {\r\n      pages.push('...');\r\n    }\r\n\r\n    // Always show last page (if it's not already included)\r\n    if (totalPages > 1) {\r\n      pages.push(totalPages);\r\n    }\r\n\r\n    return pages;\r\n  };\r\n\r\n  const visiblePages = getVisiblePages();\r\n\r\n  const handlePageClick = (page: number | string) => {\r\n    if (typeof page === 'number' && page !== currentPage) {\r\n      onPageChange(page);\r\n    }\r\n  };\r\n\r\n  const handlePageSizeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {\r\n    const newPageSize = parseInt(event.target.value);\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(newPageSize);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentPage > 1) {\r\n      onPageChange(currentPage - 1);\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentPage < totalPages) {\r\n      onPageChange(currentPage + 1);\r\n    }\r\n  };\r\n\r\n  const handleFirst = () => {\r\n    if (currentPage !== 1) {\r\n      onPageChange(1);\r\n    }\r\n  };\r\n\r\n  const handleLast = () => {\r\n    if (currentPage !== totalPages) {\r\n      onPageChange(totalPages);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 px-6 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 ${className}`}>\r\n      {/* Left side - Info and page size selector */}\r\n      <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4\">\r\n        {/* Items info */}\r\n        {showInfo && totalItems > 0 && (\r\n          <div className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n            Showing <span className=\"font-medium\">{startItem}</span> to{' '}\r\n            <span className=\"font-medium\">{endItem}</span> of{' '}\r\n            <span className=\"font-medium\">{totalItems}</span> results\r\n          </div>\r\n        )}\r\n\r\n        {/* Page size selector */}\r\n        {showPageSizeSelector && onPageSizeChange && (\r\n          <div className=\"flex items-center space-x-2\">\r\n            <select\r\n              value={itemsPerPage}\r\n              onChange={handlePageSizeChange}\r\n              className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent\"\r\n            >\r\n              {pageSizeOptions.map((size) => (\r\n                <option key={size} value={size}>\r\n                  {size} per page\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Right side - Pagination controls */}\r\n      {totalPages > 1 && (\r\n        <nav className=\"flex items-center space-x-1\" aria-label=\"Pagination\">\r\n          {/* First page button */}\r\n          {showFirstLast && currentPage > 1 && (\r\n            <button\r\n              onClick={handleFirst}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to first page\"\r\n            >\r\n              <i className=\"ri-skip-back-line\"></i>\r\n            </button>\r\n          )}\r\n\r\n          {/* Previous button */}\r\n          <button\r\n            onClick={handlePrevious}\r\n            disabled={currentPage === 1}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === 1\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage > 1 ? '' : 'rounded-l-md'}`}\r\n            aria-label=\"Go to previous page\"\r\n          >\r\n            <i className=\"ri-arrow-left-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Page numbers */}\r\n          {visiblePages.map((page, index) => (\r\n            <React.Fragment key={index}>\r\n              {page === '...' ? (\r\n                <span className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400\">\r\n                  ...\r\n                </span>\r\n              ) : (\r\n                <button\r\n                  onClick={() => handlePageClick(page)}\r\n                  className={`inline-flex items-center px-4 py-2 text-sm font-medium border ${\r\n                    page === currentPage\r\n                      ? 'text-white bg-red-600 border-red-600 hover:bg-red-700'\r\n                      : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n                  }`}\r\n                  aria-label={`Go to page ${page}`}\r\n                  aria-current={page === currentPage ? 'page' : undefined}\r\n                >\r\n                  {page}\r\n                </button>\r\n              )}\r\n            </React.Fragment>\r\n          ))}\r\n\r\n          {/* Next button */}\r\n          <button\r\n            onClick={handleNext}\r\n            disabled={currentPage === totalPages}\r\n            className={`inline-flex items-center px-2 py-2 text-sm font-medium border ${\r\n              currentPage === totalPages\r\n                ? 'text-gray-300 bg-gray-100 border-gray-300 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600 dark:text-gray-500'\r\n                : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300'\r\n            } ${showFirstLast && currentPage < totalPages ? '' : 'rounded-r-md'}`}\r\n            aria-label=\"Go to next page\"\r\n          >\r\n            <i className=\"ri-arrow-right-s-line\"></i>\r\n          </button>\r\n\r\n          {/* Last page button */}\r\n          {showFirstLast && currentPage < totalPages && (\r\n            <button\r\n              onClick={handleLast}\r\n              className=\"inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300\"\r\n              aria-label=\"Go to last page\"\r\n            >\r\n              <i className=\"ri-skip-forward-line\"></i>\r\n            </button>\r\n          )}\r\n        </nav>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Pagination;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AA4BA,MAAM,aAAwC;QAAC,EAC7C,IAAI,EACJ,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,WAAW,IAAI,EACf,kBAAkB,CAAC,EACnB,kBAAkB;QAAC;QAAI;QAAI;QAAI;KAAI,EACnC,YAAY,EAAE,EACf;IACC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;IAE9D,+EAA+E;IAC/E,IAAI,cAAc,KAAK,CAAC,wBAAwB,CAAC,UAAU;QACzD,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,YAAY,aAAa,IAAI,CAAC,cAAc,CAAC,IAAI,eAAe,IAAI;IAC1E,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,cAAc;IAErD,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,MAAM,QAA6B,EAAE;QAErC,oEAAoE;QACpE,IAAI,cAAc,iBAAiB;YACjC,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;YACA,OAAO;QACT;QAEA,yBAAyB;QACzB,MAAM,IAAI,CAAC;QAEX,mEAAmE;QACnE,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,IAAI,kCAAkC;QAC3F,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;QAC1C,IAAI,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;QAErD,qCAAqC;QACrC,IAAI,eAAe,YAAY,GAAG;YAChC,YAAY;YACZ,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,kBAAkB;QACvD;QAEA,+BAA+B;QAC/B,IAAI,eAAe,aAAa,YAAY,GAAG;YAC7C,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,kBAAkB;YACvD,UAAU,aAAa;QACzB;QAEA,0CAA0C;QAC1C,IAAI,YAAY,GAAG;YACjB,MAAM,IAAI,CAAC;QACb;QAEA,iCAAiC;QACjC,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;YACzC,MAAM,IAAI,CAAC;QACb;QAEA,0CAA0C;QAC1C,IAAI,UAAU,aAAa,GAAG;YAC5B,MAAM,IAAI,CAAC;QACb;QAEA,uDAAuD;QACvD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB,CAAC;QACvB,IAAI,OAAO,SAAS,YAAY,SAAS,aAAa;YACpD,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc,SAAS,MAAM,MAAM,CAAC,KAAK;QAC/C,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,aAAa,cAAc;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,gBAAgB,GAAG;YACrB,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB,YAAY;YAC9B,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,mKAA4K,OAAV;;0BAEjL,6LAAC;gBAAI,WAAU;;oBAEZ,YAAY,aAAa,mBACxB,6LAAC;wBAAI,WAAU;;4BAA2C;0CAChD,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAiB;4BAAI;0CAC5D,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAe;4BAAI;0CAClD,6LAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAkB;;;;;;;oBAKpD,wBAAwB,kCACvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,OAAO;4BACP,UAAU;4BACV,WAAU;sCAET,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;oCAAkB,OAAO;;wCACvB;wCAAK;;mCADK;;;;;;;;;;;;;;;;;;;;;YAUtB,aAAa,mBACZ,6LAAC;gBAAI,WAAU;gBAA8B,cAAW;;oBAErD,iBAAiB,cAAc,mBAC9B,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;kCAKjB,6LAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,AAAC,iEAIR,OAHF,gBAAgB,IACZ,0HACA,wLACL,KAA0D,OAAvD,iBAAiB,cAAc,IAAI,KAAK;wBAC5C,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;sCACZ,SAAS,sBACR,6LAAC;gCAAK,WAAU;0CAAgK;;;;;yFAIhL,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,AAAC,iEAIX,OAHC,SAAS,cACL,0DACA;gCAEN,cAAY,AAAC,cAAkB,OAAL;gCAC1B,gBAAc,SAAS,cAAc,SAAS;0CAE7C;;;;;;2BAhBc;;;;;kCAuBvB,6LAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB;wBAC1B,WAAW,AAAC,iEAIR,OAHF,gBAAgB,aACZ,0HACA,wLACL,KAAmE,OAAhE,iBAAiB,cAAc,aAAa,KAAK;wBACrD,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;oBAId,iBAAiB,cAAc,4BAC9B,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAO3B;KAnOM;uCAqOS", "debugId": null}}, {"offset": {"line": 2588, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/DataTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { PaginateQuery } from '../../types';\r\nimport Pagination from './Pagination';\r\nimport '../../styles/DataTable.css';\r\n\r\n// Generic paginated response interface to handle different response types\r\ninterface GenericPaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select?: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\ninterface Column<T> {\r\n  key: keyof T | string;\r\n  label: string;\r\n  sortable?: boolean;\r\n  searchable?: boolean;\r\n  render?: (value: unknown, item: T) => React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface DataTableProps<T> {\r\n  columns: Column<T>[];\r\n  data: GenericPaginatedResponse<T> | null;\r\n  loading?: boolean;\r\n  onQueryChange: (query: PaginateQuery) => void;\r\n  searchPlaceholder?: string;\r\n  className?: string;\r\n  emptyStateIcon?: string;\r\n  emptyStateMessage?: string;\r\n}\r\n\r\nexport default function DataTable<T extends Record<string, unknown>>({\r\n  columns,\r\n  data,\r\n  loading = false,\r\n  onQueryChange,\r\n  searchPlaceholder = \"Search...\",\r\n  className = \"\",\r\n  emptyStateIcon = \"ri-inbox-line\",\r\n  emptyStateMessage = \"No data found\",\r\n}: DataTableProps<T>) {\r\n  const [query, setQuery] = useState<PaginateQuery>({\r\n    page: 1,\r\n    limit: 10,\r\n    search: '',\r\n    sortBy: [],\r\n  });\r\n  const [searchInput, setSearchInput] = useState('');\r\n\r\n  const handleSearch = useCallback((search: string) => {\r\n    try {\r\n      const newQuery = { ...query, search, page: 1 };\r\n      setQuery(newQuery);\r\n      onQueryChange(newQuery);\r\n    } catch (error) {\r\n      console.error('Error handling search:', error);\r\n    }\r\n  }, [query, onQueryChange]);\r\n\r\n  // Debounced search effect\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (searchInput !== query.search) {\r\n        handleSearch(searchInput);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [searchInput, query.search, handleSearch]);\r\n\r\n  const handleSort = (columnKey: string) => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    let newSortBy: string[] = [];\r\n\r\n    if (!currentSort) {\r\n      newSortBy = [`${columnKey}:ASC`];\r\n    } else if (currentSort.endsWith(':ASC')) {\r\n      newSortBy = [`${columnKey}:DESC`];\r\n    } else {\r\n      newSortBy = [];\r\n    }\r\n\r\n    const newQuery = { ...query, sortBy: newSortBy, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const handlePageChange = (page: number) => {\r\n    const newQuery = { ...query, page };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  const getSortDirection = (columnKey: string): 'asc' | 'desc' | null => {\r\n    const currentSort = query.sortBy?.find(sort => sort.startsWith(columnKey));\r\n    if (!currentSort) return null;\r\n    return currentSort.endsWith(':ASC') ? 'asc' : 'desc';\r\n  };\r\n\r\n  // Handle null data case early\r\n  if (!data) {\r\n    return (\r\n      <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n        <div className=\"p-6 text-center text-gray-500 dark:text-gray-400\">\r\n          <div className=\"flex items-center justify-center\">\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n            <span className=\"ml-2\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    const newQuery = { ...query, limit: newPageSize, page: 1 };\r\n    setQuery(newQuery);\r\n    onQueryChange(newQuery);\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden ${className}`}>\r\n      {/* Search Bar */}\r\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <i className=\"ri-search-line text-gray-400 dark:text-gray-500\"></i>\r\n          </div>\r\n          <input\r\n            type=\"text\"\r\n            placeholder={searchPlaceholder}\r\n            value={searchInput}\r\n            onChange={(e) => setSearchInput(e.target.value)}\r\n            className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-gray-900 dark:text-gray-100\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Table */}\r\n      <div className=\"overflow-x-auto data-table-container\">\r\n        <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n          <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n            <tr>\r\n              {columns.map((column) => (\r\n                <th\r\n                  key={String(column.key)}\r\n                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${\r\n                    column.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800' : ''\r\n                  } ${column.className || ''}`}\r\n                  onClick={() => column.sortable && handleSort(String(column.key))}\r\n                >\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <span>{column.label}</span>\r\n                    {column.sortable && (\r\n                      <div className=\"flex flex-col\">\r\n                        <i className={`ri-arrow-up-s-line text-xs ${\r\n                          getSortDirection(String(column.key)) === 'asc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                        <i className={`ri-arrow-down-s-line text-xs -mt-1 ${\r\n                          getSortDirection(String(column.key)) === 'desc' ? 'text-red-600' : 'text-gray-400'\r\n                        }`}></i>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </th>\r\n              ))}\r\n            </tr>\r\n          </thead>\r\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n            {loading ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex items-center justify-center\">\r\n                    <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-red-600\"></div>\r\n                    <span className=\"ml-2\">Loading...</span>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : !data?.data || data.data.length === 0 ? (\r\n              <tr>\r\n                <td colSpan={columns.length} className=\"px-6 py-4 text-center text-gray-500 dark:text-gray-400\">\r\n                  <div className=\"flex flex-col items-center justify-center py-8\">\r\n                    <i className={`${emptyStateIcon} text-4xl mb-2`}></i>\r\n                    <p>{emptyStateMessage}</p>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            ) : (\r\n              data.data.map((item, index) => (\r\n                <tr key={index} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  {columns.map((column) => (\r\n                    <td key={String(column.key)} className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100\">\r\n                      {column.render\r\n                        ? column.render(item[column.key as keyof T], item)\r\n                        : String(item[column.key as keyof T] || '')\r\n                      }\r\n                    </td>\r\n                  ))}\r\n                </tr>\r\n              ))\r\n            )}\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      {/* Pagination */}\r\n      {data?.meta && data.meta.totalItems !== undefined && data.meta.currentPage !== undefined && data.meta.totalPages !== undefined && (\r\n        <Pagination\r\n          meta={{\r\n            ...data.meta,\r\n            totalItems: data.meta.totalItems,\r\n            currentPage: data.meta.currentPage,\r\n            totalPages: data.meta.totalPages,\r\n          }}\r\n          onPageChange={handlePageChange}\r\n          onPageSizeChange={handlePageSizeChange}\r\n          showFirstLast={true}\r\n          showPageSizeSelector={true}\r\n          showInfo={true}\r\n          maxVisiblePages={7}\r\n          pageSizeOptions={[10, 25, 50, 100]}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;;AAkDe,SAAS,UAA6C,KASjD;QATiD,EACnE,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,aAAa,EACb,oBAAoB,WAAW,EAC/B,YAAY,EAAE,EACd,iBAAiB,eAAe,EAChC,oBAAoB,eAAe,EACjB,GATiD;;IAUnE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ,EAAE;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YAChC,IAAI;gBACF,MAAM,WAAW;oBAAE,GAAG,KAAK;oBAAE;oBAAQ,MAAM;gBAAE;gBAC7C,SAAS;gBACT,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;QACF;8CAAG;QAAC;QAAO;KAAc;IAEzB,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,YAAY;iDAAW;oBAC3B,IAAI,gBAAgB,MAAM,MAAM,EAAE;wBAChC,aAAa;oBACf;gBACF;gDAAG;YAEH;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;QAAa,MAAM,MAAM;QAAE;KAAa;IAE5C,MAAM,aAAa,CAAC;YACE;QAApB,MAAM,eAAc,gBAAA,MAAM,MAAM,cAAZ,oCAAA,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,YAAsB,EAAE;QAE5B,IAAI,CAAC,aAAa;YAChB,YAAY;gBAAE,GAAY,OAAV,WAAU;aAAM;QAClC,OAAO,IAAI,YAAY,QAAQ,CAAC,SAAS;YACvC,YAAY;gBAAE,GAAY,OAAV,WAAU;aAAO;QACnC,OAAO;YACL,YAAY,EAAE;QAChB;QAEA,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,QAAQ;YAAW,MAAM;QAAE;QACxD,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAK;QAClC,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAC;YACJ;QAApB,MAAM,eAAc,gBAAA,MAAM,MAAM,cAAZ,oCAAA,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,UAAU,CAAC;QAC/D,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,YAAY,QAAQ,CAAC,UAAU,QAAQ;IAChD;IAEA,8BAA8B;IAC9B,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAW,AAAC,+DAAwE,OAAV;sBAC7E,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE,OAAO;YAAa,MAAM;QAAE;QACzD,SAAS;QACT,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,+DAAwE,OAAV;;0BAE7E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,6LAAC;4BACC,MAAK;4BACL,aAAa;4BACb,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;0CACE,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAEC,WAAW,AAAC,qGAER,OADF,OAAO,QAAQ,GAAG,4DAA4D,IAC/E,KAA0B,OAAvB,OAAO,SAAS,IAAI;wCACxB,SAAS,IAAM,OAAO,QAAQ,IAAI,WAAW,OAAO,OAAO,GAAG;kDAE9D,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAM,OAAO,KAAK;;;;;;gDAClB,OAAO,QAAQ,kBACd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAW,AAAC,8BAEd,OADC,iBAAiB,OAAO,OAAO,GAAG,OAAO,QAAQ,iBAAiB;;;;;;sEAEpE,6LAAC;4DAAE,WAAW,AAAC,sCAEd,OADC,iBAAiB,OAAO,OAAO,GAAG,OAAO,SAAS,iBAAiB;;;;;;;;;;;;;;;;;;uCAdtE,OAAO,OAAO,GAAG;;;;;;;;;;;;;;;sCAuB9B,6LAAC;4BAAM,WAAU;sCACd,wBACC,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAO;;;;;;;;;;;;;;;;;;;;;uCAI3B,EAAC,iBAAA,2BAAA,KAAM,IAAI,KAAI,KAAK,IAAI,CAAC,MAAM,KAAK,kBACtC,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS,QAAQ,MAAM;oCAAE,WAAU;8CACrC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAW,AAAC,GAAiB,OAAf,gBAAe;;;;;;0DAChC,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;uCAKV,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;oCAAe,WAAU;8CACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;4CAA4B,WAAU;sDACpC,OAAO,MAAM,GACV,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAY,EAAE,QAC3C,OAAO,IAAI,CAAC,OAAO,GAAG,CAAY,IAAI;2CAHnC,OAAO,OAAO,GAAG;;;;;mCAFrB;;;;;;;;;;;;;;;;;;;;;YAiBlB,CAAA,iBAAA,2BAAA,KAAM,IAAI,KAAI,KAAK,IAAI,CAAC,UAAU,KAAK,aAAa,KAAK,IAAI,CAAC,WAAW,KAAK,aAAa,KAAK,IAAI,CAAC,UAAU,KAAK,2BACnH,6LAAC,6IAAA,CAAA,UAAU;gBACT,MAAM;oBACJ,GAAG,KAAK,IAAI;oBACZ,YAAY,KAAK,IAAI,CAAC,UAAU;oBAChC,aAAa,KAAK,IAAI,CAAC,WAAW;oBAClC,YAAY,KAAK,IAAI,CAAC,UAAU;gBAClC;gBACA,cAAc;gBACd,kBAAkB;gBAClB,eAAe;gBACf,sBAAsB;gBACtB,UAAU;gBACV,iBAAiB;gBACjB,iBAAiB;oBAAC;oBAAI;oBAAI;oBAAI;iBAAI;;;;;;;;;;;;AAK5C;GAjMwB;KAAA", "debugId": null}}, {"offset": {"line": 2993, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\r\n  label?: string;\r\n  error?: string;\r\n  helperText?: string;\r\n  required?: boolean;\r\n  options: SelectOption[];\r\n  placeholder?: string;\r\n  className?: string;\r\n  containerClassName?: string;\r\n  onChange?: (value: string) => void;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  required = false,\r\n  options = [],\r\n  placeholder = 'Select an option...',\r\n  className = '',\r\n  containerClassName = '',\r\n  onChange,\r\n  id,\r\n  value,\r\n  ...props\r\n}, ref) => {\r\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\r\n  \r\n  const baseSelectClasses = `\r\n    w-full px-3 py-2 border rounded-md shadow-sm \r\n    focus:outline-none focus:ring-2 focus:ring-offset-2 \r\n    disabled:opacity-50 disabled:cursor-not-allowed\r\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\r\n    transition-colors duration-200\r\n    appearance-none bg-white\r\n    bg-no-repeat bg-right bg-[length:16px_16px]\r\n    pr-10\r\n  `;\r\n  \r\n  const selectClasses = error\r\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\r\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\r\n    if (onChange) {\r\n      onChange(e.target.value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-1 ${containerClassName}`}>\r\n      {label && (\r\n        <label \r\n          htmlFor={selectId}\r\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\r\n        >\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <div className=\"relative\">\r\n        <select\r\n          ref={ref}\r\n          id={selectId}\r\n          value={value || ''}\r\n          onChange={handleChange}\r\n          className={`${selectClasses} ${className}`}\r\n          {...props}\r\n        >\r\n          {placeholder && (\r\n            <option value=\"\" disabled>\r\n              {placeholder}\r\n            </option>\r\n          )}\r\n          \r\n          {options.map((option) => (\r\n            <option \r\n              key={option.value} \r\n              value={option.value}\r\n              disabled={option.disabled}\r\n            >\r\n              {option.label}\r\n            </option>\r\n          ))}\r\n        </select>\r\n        \r\n        {/* Custom dropdown arrow */}\r\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\r\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\r\n        </div>\r\n      </div>\r\n      \r\n      {error && (\r\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAkC,QAavD;QAbwD,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ;IACC,MAAM,WAAW,MAAM,AAAC,UAAiD,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAEtE,MAAM,oBAAqB;IAW3B,MAAM,gBAAgB,QAClB,AAAC,GAAoB,OAAlB,mBAAkB,iFACrB,AAAC,GAAoB,OAAlB,mBAAkB;IAEzB,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAA+B,OAAnB;;YAC1B,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,AAAC,GAAmB,OAAjB,eAAc,KAAa,OAAV;wBAC9B,GAAG,KAAK;;4BAER,6BACC,6LAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/certificates/PostalServicesCertificate.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef } from 'react';\r\nimport { License } from '@/types';\r\n\r\ninterface PostalServicesCertificateProps {\r\n  license: License;\r\n}\r\n\r\nexport default function PostalServicesCertificate({ license }: PostalServicesCertificateProps) {\r\n  const qrCodeRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    // Generate QR code\r\n    if (qrCodeRef.current && typeof window !== 'undefined') {\r\n      import('qrcode').then((QRCode) => {\r\n        const qrData = `MACRA Postal License Verification\r\nRef: ${license.license_number}\r\nLicense No: ${license.license_number}\r\nLicensee: ${license.application.applicant?.name || 'N/A'}\r\nValid: ${license.issue_date} - ${license.expiry_date}\r\nVerify at: https://macra.mw/verify/${license.license_number}`;\r\n\r\n        // Create a canvas element\r\n        const canvas = document.createElement('canvas');\r\n\r\n        QRCode.toCanvas(canvas, qrData, {\r\n          width: 96,\r\n          margin: 1,\r\n          color: {\r\n            dark: '#000000',\r\n            light: '#FFFFFF'\r\n          }\r\n        }).then(() => {\r\n          // Clear the div and append the canvas\r\n          if (qrCodeRef.current) {\r\n            qrCodeRef.current.innerHTML = '';\r\n            qrCodeRef.current.appendChild(canvas);\r\n          }\r\n        }).catch((error) => {\r\n          console.error('QR Code generation failed:', error);\r\n          if (qrCodeRef.current) {\r\n            qrCodeRef.current.innerHTML = '<div style=\"font-size: 10px; text-align: center; color: #666;\">QR Code<br>Error</div>';\r\n          }\r\n        });\r\n      }).catch((error) => {\r\n        console.error('QRCode import failed:', error);\r\n        if (qrCodeRef.current) {\r\n          qrCodeRef.current.innerHTML = '<div style=\"font-size: 10px; text-align: center; color: #666;\">QR Code<br>Unavailable</div>';\r\n        }\r\n      });\r\n    }\r\n  }, [license]);\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const getValidityPeriod = () => {\r\n    const issueDate = new Date(license.issue_date);\r\n    const expiryDate = new Date(license.expiry_date);\r\n    const years = expiryDate.getFullYear() - issueDate.getFullYear();\r\n    return `${years} Years (${formatDate(license.issue_date)} - ${formatDate(license.expiry_date)})`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"postal-certificate\">\r\n      <style jsx>{`\r\n        .postal-certificate {\r\n          font-family: 'Times New Roman', serif;\r\n          background: white;\r\n          border: 4px solid #dc2626;\r\n          border-radius: 8px;\r\n          padding: 40px;\r\n          min-height: 842px;\r\n          position: relative;\r\n          overflow: hidden;\r\n        }\r\n        \r\n        .inner-border {\r\n          border: 2px solid #16a34a;\r\n          padding: 30px;\r\n          min-height: 100%;\r\n          position: relative;\r\n        }\r\n        \r\n        .watermark {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%) rotate(-15deg);\r\n          opacity: 0.05;\r\n          pointer-events: none;\r\n          z-index: 0;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          text-align: center;\r\n        }\r\n        \r\n        .watermark-text {\r\n          font-size: 36px;\r\n          font-weight: bold;\r\n          color: #dc2626;\r\n          letter-spacing: 3px;\r\n          line-height: 1.2;\r\n        }\r\n        \r\n        .content-wrapper {\r\n          position: relative;\r\n          z-index: 1;\r\n        }\r\n        \r\n        .header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n        }\r\n        \r\n        .logo-section {\r\n          margin-bottom: 20px;\r\n          text-align: center;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n        }\r\n\r\n        .logo-section img {\r\n          height: 80px;\r\n          width: auto;\r\n          object-fit: contain;\r\n          max-width: 120px;\r\n        }\r\n        \r\n        .authority-name {\r\n          border: 2px solid #dc2626;\r\n          display: inline-block;\r\n          padding: 12px 30px;\r\n          margin-bottom: 20px;\r\n        }\r\n        \r\n        .authority-title {\r\n          color: #dc2626;\r\n          font-weight: bold;\r\n          font-size: 16px;\r\n          letter-spacing: 1px;\r\n        }\r\n        \r\n        .reference-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          margin-bottom: 15px;\r\n          font-size: 14px;\r\n          color: #2563eb;\r\n        }\r\n        \r\n        .certificate-type {\r\n          background-color: #16a34a;\r\n          color: white;\r\n          padding: 10px 20px;\r\n          display: inline-block;\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n        \r\n        .certification-text {\r\n          font-size: 14px;\r\n          color: #374151;\r\n          margin-bottom: 25px;\r\n        }\r\n        \r\n        .license-details {\r\n          margin-bottom: 25px;\r\n          line-height: 1.8;\r\n        }\r\n        \r\n        .detail-row {\r\n          display: flex;\r\n          margin-bottom: 12px;\r\n          align-items: flex-start;\r\n        }\r\n        \r\n        .detail-label {\r\n          font-weight: 600;\r\n          min-width: 200px;\r\n          color: #374151;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .detail-colon {\r\n          margin: 0 10px;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        .detail-value {\r\n          font-weight: bold;\r\n          color: #111827;\r\n          flex: 1;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .services-section {\r\n          margin-bottom: 20px;\r\n        }\r\n        \r\n        .services-title {\r\n          font-weight: bold;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .services-list {\r\n          list-style: none;\r\n          padding-left: 20px;\r\n        }\r\n        \r\n        .services-list li {\r\n          margin-bottom: 4px;\r\n          font-size: 13px;\r\n          position: relative;\r\n        }\r\n        \r\n        .services-list li:before {\r\n          content: \"•\";\r\n          color: #16a34a;\r\n          font-weight: bold;\r\n          position: absolute;\r\n          left: -15px;\r\n        }\r\n        \r\n        .compliance-section {\r\n          text-align: justify;\r\n          line-height: 1.6;\r\n          margin-bottom: 30px;\r\n          font-size: 14px;\r\n          color: #111827;\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .footer-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: flex-end;\r\n          margin-top: 40px;\r\n        }\r\n        \r\n        .signature-area {\r\n          flex: 1;\r\n        }\r\n        \r\n        .issue-location {\r\n          font-size: 14px;\r\n          margin-bottom: 60px;\r\n          color: #111827;\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .signature-line {\r\n          text-align: center;\r\n          border-top: 1px solid black;\r\n          padding-top: 8px;\r\n          max-width: 200px;\r\n        }\r\n        \r\n        .dg-name {\r\n          font-weight: bold;\r\n          font-size: 14px;\r\n          color: #111827;\r\n        }\r\n        \r\n        .dg-title {\r\n          font-size: 14px;\r\n          color: #111827;\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .qr-section {\r\n          flex-shrink: 0;\r\n          margin-left: 40px;\r\n          text-align: center;\r\n        }\r\n        \r\n        .qr-code {\r\n          width: 100px;\r\n          height: 100px;\r\n          border: 2px solid #9ca3af;\r\n          background: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 10px;\r\n          color: #6b7280;\r\n          margin-bottom: 8px;\r\n        }\r\n        \r\n        .qr-text {\r\n          font-size: 10px;\r\n          color: #4b5563;\r\n          max-width: 100px;\r\n        }\r\n        \r\n        .security-footer {\r\n          background-color: #06b6d4;\r\n          color: white;\r\n          text-align: center;\r\n          padding: 12px;\r\n          font-size: 14px;\r\n          font-weight: 700;\r\n          margin-top: 20px;\r\n          margin: 20px -30px -30px -30px;\r\n          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"inner-border\">\r\n        {/* Watermark */}\r\n        <div className=\"watermark\">\r\n          <div className=\"watermark-text\">\r\n            MALAWI COMMUNICATIONS<br />\r\n            REGULATORY AUTHORITY\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"content-wrapper\">\r\n          {/* Header */}\r\n          <div className=\"header\">\r\n            {/* MACRA Logo */}\r\n            <div className=\"logo-section\">\r\n              <img\r\n                src=\"/macra-logo.png\"\r\n                alt=\"MACRA Logo\"\r\n                onError={(e) => {\r\n                  const target = e.target as HTMLImageElement;\r\n                  target.style.display = 'none';\r\n                }}\r\n              />\r\n            </div>\r\n\r\n            {/* Authority Name */}\r\n            <div className=\"authority-name\">\r\n              <h1 className=\"authority-title\">\r\n                MALAWI COMMUNICATIONS REGULATORY AUTHORITY\r\n              </h1>\r\n            </div>\r\n\r\n            {/* Reference and License Numbers */}\r\n            <div className=\"reference-section\">\r\n              <span>Ref: PS - {new Date().getFullYear()}</span>\r\n              <span>License No. {license.license_number}</span>\r\n            </div>\r\n\r\n            {/* Certificate Type */}\r\n            <div className=\"certificate-type\">\r\n              POSTAL SERVICES LICENSE\r\n            </div>\r\n            <p className=\"certification-text\">\r\n              This license authorizes the holder to operate postal services in Malawi\r\n            </p>\r\n          </div>\r\n\r\n          {/* License Details */}\r\n          <div className=\"license-details\">\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSEE NAME</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">BUSINESS REGISTRATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.business_registration_number || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">TAX IDENTIFICATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.tpin || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">REGISTERED ADDRESS</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <div className=\"detail-value\">\r\n                <div>{license.application.applicant?.name || 'N/A'}</div>\r\n                {/* <div>{license.application.applicant?.city || ''}, {license.application.applicant?.country || 'Malawi'}</div> */}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">SERVICE COVERAGE AREA</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">National</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSE VALIDITY PERIOD</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{getValidityPeriod()}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Authorized Services */}\r\n          <div className=\"services-section\">\r\n            <div className=\"services-title\">AUTHORIZED POSTAL SERVICES:</div>\r\n            <ul className=\"services-list\">\r\n              <li>Domestic mail collection, processing, and delivery</li>\r\n              <li>International mail services (inbound and outbound)</li>\r\n              <li>Express and courier services</li>\r\n              <li>Parcel and package delivery services</li>\r\n              <li>Postal financial services (money orders, postal banking)</li>\r\n              <li>Philatelic services</li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Compliance Statement */}\r\n          <div className=\"compliance-section\">\r\n            <p>\r\n              {license.conditions || \r\n                `This license is issued under the Communications Act, 2016, and authorizes the licensee \r\n                to provide postal services in Malawi subject to compliance with all applicable laws, \r\n                regulations, and license conditions. The licensee shall maintain adequate infrastructure, \r\n                qualified personnel, and service standards as prescribed by the Malawi Communications \r\n                Regulatory Authority. This license is non-transferable and must be renewed before expiration.`\r\n              }\r\n            </p>\r\n          </div>\r\n\r\n          {/* Footer with Signature and QR Code */}\r\n          <div className=\"footer-section\">\r\n            {/* Signature Section */}\r\n            <div className=\"signature-area\">\r\n              <div className=\"issue-location\">\r\n                <p>\r\n                  Issued at Lilongwe, this {formatDate(license.issue_date)}\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"signature-line\">\r\n                <p className=\"dg-name\">Daud Suleman</p>\r\n                <p className=\"dg-title\">Director General</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* QR Code Section */}\r\n            <div className=\"qr-section\">\r\n              <div className=\"qr-code\">\r\n                <div ref={qrCodeRef}>\r\n                  {/* Fallback SVG QR Code */}\r\n                  <svg width=\"96\" height=\"96\" viewBox=\"0 0 25 25\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <rect width=\"25\" height=\"25\" fill=\"white\"/>\r\n                    <rect x=\"0\" y=\"0\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"1\" y=\"1\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"2\" y=\"2\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"18\" y=\"0\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"19\" y=\"1\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"20\" y=\"2\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"0\" y=\"18\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"1\" y=\"19\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"2\" y=\"20\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"8\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"10\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"12\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"14\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"16\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"8\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"10\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"12\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"14\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"16\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"9\" y=\"9\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"10\" y=\"10\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"11\" y=\"11\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"8\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"10\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"12\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"14\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"16\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"8\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"10\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"12\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"14\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"16\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <p className=\"qr-text\">\r\n                Scan for verification\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Security Footer */}\r\n          <div className=\"security-footer\">\r\n            This licence is issued without any alterations and remains the property of MACRA\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;;AASe,SAAS,0BAA0B,KAA2C;QAA3C,EAAE,OAAO,EAAkC,GAA3C;QAyWJ,gCAMA,iCAMA,iCAOxB;;IA3XpB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR,mBAAmB;YACnB,IAAI,UAAU,OAAO,IAAI,aAAkB,aAAa;gBACtD,yIAAiB,IAAI;2DAAC,CAAC;4BAIjB;wBAHJ,MAAM,SAAS,AAAC,2CAEV,OADP,QAAQ,cAAc,EAAC,kBAElB,OADE,QAAQ,cAAc,EAAC,gBAE5B,OADG,EAAA,iCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,qDAAA,+BAA+B,IAAI,KAAI,OAAM,aACxB,OAAxB,QAAQ,UAAU,EAAC,OACS,OADJ,QAAQ,WAAW,EAAC,yCACO,OAAvB,QAAQ,cAAc;wBAEnD,0BAA0B;wBAC1B,MAAM,SAAS,SAAS,aAAa,CAAC;wBAEtC,OAAO,QAAQ,CAAC,QAAQ,QAAQ;4BAC9B,OAAO;4BACP,QAAQ;4BACR,OAAO;gCACL,MAAM;gCACN,OAAO;4BACT;wBACF,GAAG,IAAI;mEAAC;gCACN,sCAAsC;gCACtC,IAAI,UAAU,OAAO,EAAE;oCACrB,UAAU,OAAO,CAAC,SAAS,GAAG;oCAC9B,UAAU,OAAO,CAAC,WAAW,CAAC;gCAChC;4BACF;kEAAG,KAAK;mEAAC,CAAC;gCACR,QAAQ,KAAK,CAAC,8BAA8B;gCAC5C,IAAI,UAAU,OAAO,EAAE;oCACrB,UAAU,OAAO,CAAC,SAAS,GAAG;gCAChC;4BACF;;oBACF;0DAAG,KAAK;2DAAC,CAAC;wBACR,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,IAAI,UAAU,OAAO,EAAE;4BACrB,UAAU,OAAO,CAAC,SAAS,GAAG;wBAChC;oBACF;;YACF;QACF;8CAAG;QAAC;KAAQ;IAEZ,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,YAAY,IAAI,KAAK,QAAQ,UAAU;QAC7C,MAAM,aAAa,IAAI,KAAK,QAAQ,WAAW;QAC/C,MAAM,QAAQ,WAAW,WAAW,KAAK,UAAU,WAAW;QAC9D,OAAO,AAAC,GAAkB,OAAhB,OAAM,YAA8C,OAApC,WAAW,QAAQ,UAAU,GAAE,OAAqC,OAAhC,WAAW,QAAQ,WAAW,GAAE;IAChG;IAEA,qBACE,6LAAC;kDAAc;;;;;;0BAyPb,6LAAC;0DAAc;;kCAEb,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;gCAAiB;8CACT,6LAAC;;;;;;;gCAAK;;;;;;;;;;;;kCAK/B,6LAAC;kEAAc;;0CAEb,6LAAC;0EAAc;;kDAEb,6LAAC;kFAAc;kDACb,cAAA,6LAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,SAAS,CAAC;gDACR,MAAM,SAAS,EAAE,MAAM;gDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;4CACzB;;;;;;;;;;;;kDAKJ,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAa;sDAAkB;;;;;;;;;;;kDAMlC,6LAAC;kFAAc;;0DACb,6LAAC;;;oDAAK;oDAAW,IAAI,OAAO,WAAW;;;;;;;0DACvC,6LAAC;;;oDAAK;oDAAa,QAAQ,cAAc;;;;;;;;;;;;;kDAI3C,6LAAC;kFAAc;kDAAmB;;;;;;kDAGlC,6LAAC;kFAAY;kDAAqB;;;;;;;;;;;;0CAMpC,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAgB,EAAA,iCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,qDAAA,+BAA+B,IAAI,KAAI;;;;;;;;;;;;kDAGzE,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAgB,EAAA,kCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,sDAAA,gCAA+B,4BAA4B,KAAI;;;;;;;;;;;;kDAGjG,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAgB,EAAA,kCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,sDAAA,gCAA+B,IAAI,KAAI;;;;;;;;;;;;kDAGzE,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAc;0DACb,cAAA,6LAAC;;8DAAK,EAAA,kCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,sDAAA,gCAA+B,IAAI,KAAI;;;;;;;;;;;;;;;;;kDAKjD,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;;;;;;;kDAGjC,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAgB;;;;;;;;;;;;;;;;;;0CAKpC,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;kDAAiB;;;;;;kDAChC,6LAAC;kFAAa;;0DACZ,6LAAC;;0DAAG;;;;;;0DACJ,6LAAC;;0DAAG;;;;;;0DACJ,6LAAC;;0DAAG;;;;;;0DACJ,6LAAC;;0DAAG;;;;;;0DACJ,6LAAC;;0DAAG;;;;;;0DACJ,6LAAC;;0DAAG;;;;;;;;;;;;;;;;;;0CAKR,6LAAC;0EAAc;0CACb,cAAA,6LAAC;;8CACE,QAAQ,UAAU,IAChB;;;;;;;;;;;0CAUP,6LAAC;0EAAc;;kDAEb,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;0DACb,cAAA,6LAAC;;;wDAAE;wDACyB,WAAW,QAAQ,UAAU;;;;;;;;;;;;0DAI3D,6LAAC;0FAAc;;kEACb,6LAAC;kGAAY;kEAAU;;;;;;kEACvB,6LAAC;kGAAY;kEAAW;;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;0DACb,cAAA,6LAAC;oDAAI,KAAK;;8DAER,cAAA,6LAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,OAAM;;;0EACpD,6LAAC;gEAAK,OAAM;gEAAK,QAAO;gEAAK,MAAK;;;;;;;0EAClC,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;;;;;;;;;;;;;;;;;0DAIpD,6LAAC;0FAAY;0DAAU;;;;;;;;;;;;;;;;;;0CAO3B,6LAAC;0EAAc;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GAhfwB;KAAA", "debugId": null}}, {"offset": {"line": 4183, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationDeviceService.ts"], "sourcesContent": ["import { apiClient, processApiResponse } from '@/lib';\r\nimport { PaginatedResponse } from '@/types';\r\n\r\nexport interface ApplicationDevice {\r\n  device_id: string;\r\n  application_id: string;\r\n\r\n  // Manufacturer Information (embedded)\r\n  manufacturer_name: string;\r\n  manufacturer_address?: string;\r\n  manufacturer_country: string;\r\n\r\n  // Equipment Information (embedded)\r\n  brand_trade_name?: string;\r\n  product_type_name?: string;\r\n\r\n  equipment_category_id?: string;\r\n  equipment_model?: string;\r\n  imei: string;\r\n  device_type: string;\r\n  model_name: string;\r\n  device_serial_number: string;\r\n  approval_status: string;\r\n  device_approval_number?: string;\r\n  device_approval_date?: string;\r\n  approval_notes?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n\r\n  equipment_category?: {\r\n    category_id: string;\r\n    name: string;\r\n    description: string;\r\n  };\r\n}\r\n\r\nexport interface CreateApplicationDeviceDto {\r\n  application_id: string;\r\n\r\n  // Manufacturer Information (embedded)\r\n  manufacturer_name: string;\r\n  manufacturer_address?: string;\r\n  manufacturer_country: string;\r\n\r\n  // Equipment Information (embedded)\r\n  brand_trade_name?: string;\r\n  product_type_name?: string;\r\n\r\n  equipment_category_id?: string;\r\n  imei?: string;\r\n  approval_status?: string;\r\n  device_approval_number?: string;\r\n  device_approval_date?: string;\r\n  approval_notes?: string;\r\n}\r\n\r\nexport interface UpdateApplicationDeviceDto {\r\n  // Manufacturer Information (embedded)\r\n  manufacturer_name?: string;\r\n  manufacturer_address?: string;\r\n  manufacturer_country?: string;\r\n\r\n  // Equipment Information (embedded)\r\n  brand_trade_name?: string;\r\n  product_type_name?: string;\r\n\r\n  equipment_category_id?: string;\r\n  imei?: string;\r\n  device_type?: string;\r\n  model_name?: string;\r\n  device_serial_number?: string;\r\n  approval_status?: string;\r\n  device_approval_number?: string;\r\n  device_approval_date?: string;\r\n  approval_notes?: string;\r\n}\r\n\r\nclass ApplicationDeviceService {\r\n  private baseUrl = '/devices';\r\n\r\n  /**\r\n   * Clean device payload by converting empty strings to undefined for optional fields\r\n   */\r\n  private cleanDevicePayload(payload: any): any {\r\n    const cleaned = { ...payload };\r\n\r\n    // Convert empty strings to undefined for optional fields\r\n    const optionalFields = [\r\n      'manufacturer_address',\r\n      'brand_trade_name',\r\n      'product_type_name',\r\n      'equipment_category',\r\n      'approval_notes',\r\n      'device_approval_number',\r\n      'device_approval_date'\r\n    ];\r\n\r\n    optionalFields.forEach(field => {\r\n      if (cleaned[field] === '') {\r\n        cleaned[field] = undefined;\r\n      }\r\n    });\r\n\r\n    return cleaned;\r\n  }\r\n\r\n  /**\r\n   * Get devices for a specific application\r\n   */\r\n  async getDevicesByApplication(applicationId: string): Promise<PaginatedResponse<any>> {\r\n    try {\r\n      // Use the new backend endpoint that queries by application_id directly\r\n      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}`);\r\n      return processApiResponse(response);\r\n    } catch (error: any) {\r\n      console.error('❌ Error fetching application devices:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get a specific device by ID\r\n   */\r\n  async getDevice(deviceId: string): Promise<ApplicationDevice> {\r\n    try {\r\n      console.log('🔍 Fetching device:', deviceId);\r\n      \r\n      const response = await apiClient.get(`${this.baseUrl}/${deviceId}`);\r\n      const device = processApiResponse(response);\r\n      \r\n      console.log('✅ Found device:', device);\r\n      return device;\r\n    } catch (error: any) {\r\n      console.error('❌ Error fetching device:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a new device for an application\r\n   */\r\n  async createDevice(deviceData: CreateApplicationDeviceDto): Promise<ApplicationDevice> {\r\n    try {\r\n      console.log('🔍 Creating device for application:', deviceData.application_id);\r\n      console.log('📝 Device data:', deviceData);\r\n      \r\n      const cleanedData = this.cleanDevicePayload({\r\n        ...deviceData,\r\n        approval_status: deviceData.approval_status || 'pending'\r\n      });\r\n\r\n      const response = await apiClient.post(this.baseUrl, cleanedData);\r\n      \r\n      const createdDevice = processApiResponse(response);\r\n      console.log('✅ Device created:', createdDevice);\r\n      \r\n      return createdDevice;\r\n    } catch (error: any) {\r\n      console.error('❌ Error creating device:', error);\r\n      \r\n      // Handle specific error cases\r\n      if (error.response?.status === 409) {\r\n        const message = error.response?.data?.message || 'Device with this IMEI or serial number already exists';\r\n        throw new Error(message);\r\n      }\r\n      \r\n      if (error.response?.status === 400) {\r\n        const message = error.response?.data?.message || 'Invalid device data provided';\r\n        throw new Error(message);\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update an existing device\r\n   */\r\n  async updateDevice(deviceId: string, deviceData: UpdateApplicationDeviceDto): Promise<ApplicationDevice> {\r\n    try {\r\n      console.log('🔍 Updating device:', deviceId);\r\n      console.log('📝 Update data:', deviceData);\r\n      \r\n      const cleanedData = this.cleanDevicePayload(deviceData);\r\n      const response = await apiClient.put(`${this.baseUrl}/${deviceId}`, cleanedData);\r\n      const updatedDevice = processApiResponse(response);\r\n      \r\n      console.log('✅ Device updated:', updatedDevice);\r\n      return updatedDevice;\r\n    } catch (error: any) {\r\n      console.error('❌ Error updating device:', error);\r\n      \r\n      // Handle specific error cases\r\n      if (error.response?.status === 409) {\r\n        const message = error.response?.data?.message || 'Device with this IMEI or serial number already exists';\r\n        throw new Error(message);\r\n      }\r\n      \r\n      if (error.response?.status === 404) {\r\n        throw new Error('Device not found');\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Delete a device\r\n   */\r\n  async deleteDevice(deviceId: string): Promise<void> {\r\n    try {\r\n      console.log('🔍 Deleting device:', deviceId);\r\n      \r\n      await apiClient.delete(`${this.baseUrl}/${deviceId}`);\r\n      console.log('✅ Device deleted successfully');\r\n    } catch (error: any) {\r\n      console.error('❌ Error deleting device:', error);\r\n      \r\n      if (error.response?.status === 404) {\r\n        throw new Error('Device not found');\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get device by IMEI\r\n   */\r\n  async getDeviceByImei(imei: string): Promise<ApplicationDevice | null> {\r\n    try {\r\n      console.log('🔍 Fetching device by IMEI:', imei);\r\n      \r\n      const response = await apiClient.get(`${this.baseUrl}/imei/${imei}`);\r\n      const device = processApiResponse(response);\r\n      \r\n      console.log('✅ Found device by IMEI:', device);\r\n      return device;\r\n    } catch (error: any) {\r\n      console.error('❌ Error fetching device by IMEI:', error);\r\n      \r\n      // Return null if device not found\r\n      if (error.response?.status === 404) {\r\n        return null;\r\n      }\r\n      \r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate IMEI and get device information if exists\r\n   */\r\n  async validateImei(imei: string): Promise<{\r\n    isValid: boolean;\r\n    exists: boolean;\r\n    device?: ApplicationDevice;\r\n    message: string;\r\n  }> {\r\n    try {\r\n      console.log('🔍 Validating IMEI:', imei);\r\n      \r\n      // Clean IMEI (remove any non-digit characters)\r\n      const cleanImei = imei.replace(/\\D/g, '');\r\n      \r\n      if (cleanImei.length !== 15) {\r\n        return {\r\n          isValid: false,\r\n          exists: false,\r\n          message: 'IMEI must be exactly 15 digits'\r\n        };\r\n      }\r\n      \r\n      // Try to find device by IMEI\r\n      const device = await this.getDeviceByImei(cleanImei);\r\n      \r\n      if (device) {\r\n        return {\r\n          isValid: true,\r\n          exists: true,\r\n          device,\r\n          message: 'IMEI found in database'\r\n        };\r\n      } else {\r\n        return {\r\n          isValid: true,\r\n          exists: false,\r\n          message: 'IMEI is valid but not found in database'\r\n        };\r\n      }\r\n    } catch (error: any) {\r\n      console.error('❌ Error validating IMEI:', error);\r\n      \r\n      return {\r\n        isValid: false,\r\n        exists: false,\r\n        message: 'Error validating IMEI'\r\n      };\r\n    }\r\n  }\r\n}\r\n\r\nexport const applicationDeviceService = new ApplicationDeviceService();\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;;AA6EA,MAAM;IAGJ;;GAEC,GACD,AAAQ,mBAAmB,OAAY,EAAO;QAC5C,MAAM,UAAU;YAAE,GAAG,OAAO;QAAC;QAE7B,yDAAyD;QACzD,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI;gBACzB,OAAO,CAAC,MAAM,GAAG;YACnB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,wBAAwB,aAAqB,EAAmC;QACpF,IAAI;YACF,uEAAuE;YACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAA8B,OAA5B,IAAI,CAAC,OAAO,EAAC,iBAA6B,OAAd;YACpE,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,UAAU,QAAgB,EAA8B;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAY,OAAT;YACxD,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAElC,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,UAAsC,EAA8B;QACrF,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC,WAAW,cAAc;YAC5E,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;gBAC1C,GAAG,UAAU;gBACb,iBAAiB,WAAW,eAAe,IAAI;YACjD;YAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YAEpD,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YACzC,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,OAAO;QACT,EAAE,OAAO,OAAY;gBAIf,iBAKA;YARJ,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,8BAA8B;YAC9B,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;oBAClB,sBAAA;gBAAhB,MAAM,UAAU,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;gBACjD,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;oBAClB,uBAAA;gBAAhB,MAAM,UAAU,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,wBAAA,iBAAgB,IAAI,cAApB,4CAAA,sBAAsB,OAAO,KAAI;gBACjD,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,QAAgB,EAAE,UAAsC,EAA8B;QACvG,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB;YACnC,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;YAC5C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAY,OAAT,WAAY;YACpE,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAEzC,QAAQ,GAAG,CAAC,qBAAqB;YACjC,OAAO;QACT,EAAE,OAAO,OAAY;gBAIf,iBAKA;YARJ,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,8BAA8B;YAC9B,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;oBAClB,sBAAA;gBAAhB,MAAM,UAAU,EAAA,mBAAA,MAAM,QAAQ,cAAd,wCAAA,uBAAA,iBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;gBACjD,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,QAAgB,EAAiB;QAClD,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,GAAkB,OAAhB,IAAI,CAAC,OAAO,EAAC,KAAY,OAAT;YAC1C,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAY;gBAGf;YAFJ,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,IAAY,EAAqC;QACrE,IAAI;YACF,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,GAAuB,OAArB,IAAI,CAAC,OAAO,EAAC,UAAa,OAAL;YAC7D,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAElC,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAY;gBAIf;YAHJ,QAAQ,KAAK,CAAC,oCAAoC;YAElD,kCAAkC;YAClC,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,OAAO;YACT;YAEA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,aAAa,IAAY,EAK5B;QACD,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,+CAA+C;YAC/C,MAAM,YAAY,KAAK,OAAO,CAAC,OAAO;YAEtC,IAAI,UAAU,MAAM,KAAK,IAAI;gBAC3B,OAAO;oBACL,SAAS;oBACT,QAAQ;oBACR,SAAS;gBACX;YACF;YAEA,6BAA6B;YAC7B,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe,CAAC;YAE1C,IAAI,QAAQ;gBACV,OAAO;oBACL,SAAS;oBACT,QAAQ;oBACR;oBACA,SAAS;gBACX;YACF,OAAO;gBACL,OAAO;oBACL,SAAS;oBACT,QAAQ;oBACR,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,SAAS;YACX;QACF;IACF;;QA7NA,+KAAQ,WAAU;;AA8NpB;AAEO,MAAM,2BAA2B,IAAI", "debugId": null}}, {"offset": {"line": 4387, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/certificates/TypeApprovalCertificate.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef, useCallback, useState } from 'react';\r\nimport { License } from '@/types';\r\nimport { applicationDeviceService, ApplicationDevice } from '@/services/applicationDeviceService';\r\n\r\ninterface DeviceInfo {\r\n  brand_trade_name?: string;\r\n  model_name?: string;\r\n  product_type_name?: string;\r\n  manufacturer_name?: string;\r\n  manufacturer_address?: string;\r\n  manufacturer_country?: string;\r\n}\r\n\r\ninterface TypeApprovalCertificateProps {\r\n  license: License;\r\n  deviceInfo?: DeviceInfo;\r\n}\r\n\r\nexport default function TypeApprovalCertificate({ license, deviceInfo }: TypeApprovalCertificateProps) {\r\n  const qrCodeRef = useRef<HTMLDivElement>(null);\r\n  const [deviceData, setDeviceData] = useState<ApplicationDevice | null>(null);\r\n  const [deviceLoading, setDeviceLoading] = useState(false);\r\n\r\n  const getCertificateNumber = useCallback(() => {\r\n    const issueDate = new Date(license.issue_date);\r\n    const year = issueDate.getFullYear();\r\n    const month = String(issueDate.getMonth() + 1).padStart(2, '0');\r\n    const day = String(issueDate.getDate()).padStart(2, '0');\r\n    return `TA-${year}-${month}-${day}`;\r\n  }, [license.issue_date]);\r\n\r\n  // Fetch device data from backend\r\n  useEffect(() => {\r\n    const fetchDeviceData = async () => {\r\n      if (!license.application?.application_id) return;\r\n\r\n      try {\r\n        setDeviceLoading(true);\r\n        const response = await applicationDeviceService.getDevicesByApplication(license.application.application_id);\r\n        const devices = response.data || [];\r\n\r\n        if (devices.length > 0) {\r\n          setDeviceData(devices[0]); // Use the first device\r\n        }\r\n      } catch (error) {\r\n        console.warn('Could not load device data for certificate:', error);\r\n        setDeviceData(null);\r\n      } finally {\r\n        setDeviceLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDeviceData();\r\n  }, [license.application?.application_id]);\r\n\r\n  // Helper function to get device information from various sources\r\n  const getDeviceData = useCallback(() => {\r\n    // First try the fetched device data from backend\r\n    if (deviceData) {\r\n      return {\r\n        brand_trade_name: deviceData.brand_trade_name,\r\n        model_name: deviceData.equipment_model || deviceData.model_name,\r\n        product_type_name: deviceData.product_type_name,\r\n        manufacturer_name: deviceData.manufacturer_name,\r\n        manufacturer_address: deviceData.manufacturer_address,\r\n        manufacturer_country: deviceData.manufacturer_country,\r\n      };\r\n    }\r\n\r\n    // Then try the deviceInfo prop\r\n    if (deviceInfo) {\r\n      return deviceInfo;\r\n    }\r\n\r\n    // Then try to extract from application_data if it exists\r\n    const appData = license.application?.application_data;\r\n    if (appData && typeof appData === 'object') {\r\n      return {\r\n        brand_trade_name: appData.brand_trade_name || appData.brandTradeName,\r\n        model_name: appData.model_name || appData.modelNumber || appData.model_number,\r\n        product_type_name: appData.product_type_name || appData.productTypeName,\r\n        manufacturer_name: appData.manufacturer_name || appData.manufacturerName,\r\n        manufacturer_address: appData.manufacturer_address || appData.manufacturerAddress,\r\n        manufacturer_country: appData.manufacturer_country || appData.manufacturerCountry,\r\n      };\r\n    }\r\n\r\n    // Fallback to default values\r\n    return {\r\n      brand_trade_name: 'VISTEON',\r\n      model_name: 'VCE CDC',\r\n      product_type_name: 'Infotainment controller',\r\n      manufacturer_name: 'Visteon Corporation',\r\n      manufacturer_address: 'One Village Center Dr\\n-\\nVan Buren Township, MI\\nUSA',\r\n      manufacturer_country: 'USA',\r\n    };\r\n  }, [deviceData, deviceInfo, license.application?.application_data]);\r\n\r\n  useEffect(() => {\r\n    // Generate QR code\r\n    if (qrCodeRef.current && typeof window !== 'undefined') {\r\n      import('qrcode').then((QRCode) => {\r\n        const certificateNumber = getCertificateNumber();\r\n        const qrData = `MACRA Type Approval Certificate Verification\r\nRef: ${license.license_number}\r\nCertificate No: ${certificateNumber}\r\nApplicant: ${license.application.applicant?.name || 'N/A'}\r\nValid: ${license.issue_date} - ${license.expiry_date}\r\nVerify at: https://macra.mw/verify/${certificateNumber}`;\r\n\r\n        // Create a canvas element\r\n        const canvas = document.createElement('canvas');\r\n\r\n        QRCode.toCanvas(canvas, qrData, {\r\n          width: 96,\r\n          margin: 1,\r\n          color: {\r\n            dark: '#000000',\r\n            light: '#FFFFFF'\r\n          }\r\n        }).then(() => {\r\n          // Clear the div and append the canvas\r\n          if (qrCodeRef.current) {\r\n            qrCodeRef.current.innerHTML = '';\r\n            qrCodeRef.current.appendChild(canvas);\r\n          }\r\n        }).catch((error) => {\r\n          console.error('QR Code generation failed:', error);\r\n          if (qrCodeRef.current) {\r\n            qrCodeRef.current.innerHTML = '<div style=\"font-size: 10px; text-align: center; color: #666;\">QR Code<br>Error</div>';\r\n          }\r\n        });\r\n      }).catch((error) => {\r\n        console.error('QRCode import failed:', error);\r\n        if (qrCodeRef.current) {\r\n          qrCodeRef.current.innerHTML = '<div style=\"font-size: 10px; text-align: center; color: #666;\">QR Code<br>Unavailable</div>';\r\n        }\r\n      });\r\n    }\r\n  }, [license, getCertificateNumber, getDeviceData]);\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"type-approval-certificate\">\r\n      <style jsx>{`\r\n        .type-approval-certificate {\r\n          font-family: 'Times New Roman', serif;\r\n          background: white;\r\n          border: 6px solid black;\r\n          border-radius: 8px;\r\n          padding: 15px;\r\n          min-height: 842px;\r\n          position: relative;\r\n          overflow: hidden;\r\n        }\r\n        \r\n        .red-border {\r\n          border: 6px solid #dc2626;\r\n          padding: 20px;\r\n          min-height: 100%;\r\n          position: relative;\r\n        }\r\n\r\n        .inner-border {\r\n          border: 6px solid #16a34a;\r\n          padding: 30px;\r\n          min-height: 100%;\r\n          position: relative;\r\n        }\r\n        \r\n        .watermark {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%) rotate(-15deg);\r\n          opacity: 0.05;\r\n          pointer-events: none;\r\n          z-index: 0;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          text-align: center;\r\n        }\r\n        \r\n        .watermark-text {\r\n          font-size: 36px;\r\n          font-weight: bold;\r\n          color: #dc2626;\r\n          letter-spacing: 3px;\r\n          line-height: 1.2;\r\n        }\r\n        \r\n        .content-wrapper {\r\n          position: relative;\r\n          z-index: 1;\r\n        }\r\n        \r\n        .header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n        }\r\n\r\n        .logo-section {\r\n          margin-bottom: 20px;\r\n          text-align: center;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n        }\r\n\r\n        .logo-section img {\r\n          height: 80px;\r\n          width: auto;\r\n          object-fit: contain;\r\n          max-width: 120px;\r\n        }\r\n\r\n        .authority-name {\r\n          border: 2px solid black;\r\n          display: inline-block;\r\n          padding: 12px 30px;\r\n          margin-bottom: 20px;\r\n        }\r\n        \r\n        .authority-title {\r\n          color: #dc2626;\r\n          font-weight: bold;\r\n          font-size: 16px;\r\n          letter-spacing: 1px;\r\n        }\r\n        \r\n        .reference-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          margin-bottom: 15px;\r\n          font-size: 14px;\r\n          color: #2563eb;\r\n        }\r\n        \r\n        .certificate-type {\r\n          color: #16a34a;\r\n          padding: 10px 20px;\r\n          display: inline-block;\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n        \r\n        .certification-text {\r\n          font-size: 14px;\r\n          color: #374151;\r\n          margin-bottom: 25px;\r\n        }\r\n        \r\n        .certificate-details {\r\n          margin-bottom: 25px;\r\n          line-height: 1.8;\r\n        }\r\n        \r\n        .detail-row {\r\n          display: flex;\r\n          margin-bottom: 12px;\r\n          align-items: flex-start;\r\n        }\r\n        \r\n        .detail-label {\r\n          font-weight: 600;\r\n          min-width: 200px;\r\n          color: #374151;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .detail-colon {\r\n          margin: 0 10px;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        .detail-value {\r\n          font-weight: bold;\r\n          color: #111827;\r\n          flex: 1;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .equipment-section {\r\n          margin-bottom: 20px;\r\n          background-color: #f9fafb;\r\n          padding: 15px;\r\n          border-radius: 6px;\r\n          border-left: 4px solid #7c3aed;\r\n        }\r\n        \r\n        .equipment-title {\r\n          font-weight: bold;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .compliance-section {\r\n          text-align: justify;\r\n          line-height: 1.6;\r\n          margin-bottom: 30px;\r\n          font-size: 14px;\r\n          color: #111827;\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .footer-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: flex-end;\r\n          margin-top: 40px;\r\n        }\r\n        \r\n        .signature-area {\r\n          flex: 1;\r\n        }\r\n        \r\n        .issue-location {\r\n          font-size: 14px;\r\n          margin-bottom: 60px;\r\n          color: #111827;\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .signature-line {\r\n          text-align: center;\r\n          border-top: 1px solid black;\r\n          padding-top: 8px;\r\n          max-width: 200px;\r\n        }\r\n        \r\n        .dg-name {\r\n          font-weight: bold;\r\n          font-size: 14px;\r\n          color: #111827;\r\n        }\r\n        \r\n        .dg-title {\r\n          font-size: 14px;\r\n          color: #111827;\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .qr-section {\r\n          flex-shrink: 0;\r\n          margin-left: 40px;\r\n          text-align: center;\r\n        }\r\n        \r\n        .qr-code {\r\n          width: 100px;\r\n          height: 100px;\r\n          border: 2px solid #9ca3af;\r\n          background: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 10px;\r\n          color: #6b7280;\r\n          margin-bottom: 8px;\r\n        }\r\n        \r\n        .qr-text {\r\n          font-size: 10px;\r\n          color: #4b5563;\r\n          max-width: 100px;\r\n        }\r\n        \r\n        .security-footer {\r\n          background-color: #06b6d4;\r\n          color: white;\r\n          text-align: center;\r\n          padding: 12px;\r\n          font-size: 14px;\r\n          font-weight: 700;\r\n          margin-top: 20px;\r\n          margin: 20px -30px -30px -30px;\r\n          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"red-border\">\r\n        <div className=\"inner-border\">\r\n        {/* Watermark */}\r\n        <div className=\"watermark\">\r\n          <div className=\"watermark-text\">\r\n            MALAWI COMMUNICATIONS<br />\r\n            REGULATORY AUTHORITY\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"content-wrapper\">\r\n          {/* Header */}\r\n          <div className=\"header\">\r\n            {/* MACRA Logo */}\r\n            <div className=\"logo-section\">\r\n              <img\r\n                src=\"/macra-logo.png\"\r\n                alt=\"MACRA Logo\"\r\n                onError={(e) => {\r\n                  const target = e.target as HTMLImageElement;\r\n                  target.style.display = 'none';\r\n                }}\r\n              />\r\n            </div>\r\n\r\n            {/* Authority Name */}\r\n            <div className=\"authority-name\">\r\n              <h1 className=\"authority-title\">\r\n                MALAWI COMMUNICATIONS REGULATORY AUTHORITY\r\n              </h1>\r\n            </div>\r\n\r\n            {/* Reference and Certificate Numbers */}\r\n            <div className=\"reference-section\">\r\n              <span></span>\r\n              <span>Certificate No. {getCertificateNumber()}</span>\r\n            </div>\r\n\r\n            {/* Certificate Type */}\r\n            <div className=\"certificate-type\">\r\n              TYPE APPROVAL CERTIFICATE\r\n            </div>\r\n            <p className=\"certification-text\">\r\n              This certificate confirms that the equipment described below has been type approved for use in Malawi\r\n            </p>\r\n          </div>\r\n\r\n          {/* Certificate Details */}\r\n          <div className=\"certificate-details\">\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">BRAND/TRADE NAME</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{getDeviceData().brand_trade_name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">MODEL NUMBER</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{getDeviceData().model_name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">PRODUCT TYPE/NAME</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{getDeviceData().product_type_name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">MANUFACTURER DETAILS</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <div className=\"detail-value\">\r\n                <div>{getDeviceData().manufacturer_name || 'N/A'}</div>\r\n                {getDeviceData().manufacturer_address &&\r\n                  getDeviceData().manufacturer_address.split('\\n').map((line: string, index: number) => (\r\n                    <div key={index}>{line}</div>\r\n                  ))\r\n                }\r\n                {!getDeviceData().manufacturer_address && <div>Address not provided</div>}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Compliance Statement */}\r\n          <div className=\"compliance-section\">\r\n            <p>\r\n              is authorized by the Malawi Communications Regulatory Authority (MACRA) for use in Malawi under section 6(2)(k) of the Communications Act, 2016. Subject to prevailing regulations and attached general operating conditions.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Footer with Signature and QR Code */}\r\n          <div className=\"footer-section\">\r\n            {/* Signature Section */}\r\n            <div className=\"signature-area\">\r\n              <div className=\"issue-location\">\r\n                <p>\r\n                  Issued at Lilongwe, this day of 2025\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"signature-line\">\r\n                <p className=\"dg-name\">Daud Suleman</p>\r\n                <p className=\"dg-title\">Director General</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* QR Code Section */}\r\n            <div className=\"qr-section\">\r\n              <div className=\"qr-code\">\r\n                <div ref={qrCodeRef}>\r\n                  {/* Fallback SVG QR Code */}\r\n                  <svg width=\"96\" height=\"96\" viewBox=\"0 0 25 25\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <rect width=\"25\" height=\"25\" fill=\"white\"/>\r\n                    <rect x=\"0\" y=\"0\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"1\" y=\"1\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"2\" y=\"2\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"18\" y=\"0\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"19\" y=\"1\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"20\" y=\"2\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"0\" y=\"18\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"1\" y=\"19\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"2\" y=\"20\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"8\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"10\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"12\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"14\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"16\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"8\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"10\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"12\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"14\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"16\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"9\" y=\"9\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"10\" y=\"10\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"11\" y=\"11\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"8\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"10\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"12\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"14\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"16\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"8\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"10\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"12\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"14\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"16\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <p className=\"qr-text\">\r\n                Scan for verification\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Security Footer */}\r\n          <div className=\"security-footer\">\r\n            This certificate is issued without any alterations and remains the property of MACRA\r\n          </div>\r\n        </div>\r\n      </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;;AAoBe,SAAS,wBAAwB,KAAqD;QAArD,EAAE,OAAO,EAAE,UAAU,EAAgC,GAArD;QAmC1C,sBA2CwB;;IA7E5B,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qEAAE;YACvC,MAAM,YAAY,IAAI,KAAK,QAAQ,UAAU;YAC7C,MAAM,OAAO,UAAU,WAAW;YAClC,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;YAC3D,MAAM,MAAM,OAAO,UAAU,OAAO,IAAI,QAAQ,CAAC,GAAG;YACpD,OAAO,AAAC,MAAa,OAAR,MAAK,KAAY,OAAT,OAAM,KAAO,OAAJ;QAChC;oEAAG;QAAC,QAAQ,UAAU;KAAC;IAEvB,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM;qEAAkB;wBACjB;oBAAL,IAAI,GAAC,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,qBAAqB,cAAc,GAAE;oBAE1C,IAAI;wBACF,iBAAiB;wBACjB,MAAM,WAAW,MAAM,8IAAA,CAAA,2BAAwB,CAAC,uBAAuB,CAAC,QAAQ,WAAW,CAAC,cAAc;wBAC1G,MAAM,UAAU,SAAS,IAAI,IAAI,EAAE;wBAEnC,IAAI,QAAQ,MAAM,GAAG,GAAG;4BACtB,cAAc,OAAO,CAAC,EAAE,GAAG,uBAAuB;wBACpD;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,IAAI,CAAC,+CAA+C;wBAC5D,cAAc;oBAChB,SAAU;wBACR,iBAAiB;oBACnB;gBACF;;YAEA;QACF;4CAAG;SAAC,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,qBAAqB,cAAc;KAAC;IAExC,iEAAiE;IACjE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;gBAmBhB;YAlBhB,iDAAiD;YACjD,IAAI,YAAY;gBACd,OAAO;oBACL,kBAAkB,WAAW,gBAAgB;oBAC7C,YAAY,WAAW,eAAe,IAAI,WAAW,UAAU;oBAC/D,mBAAmB,WAAW,iBAAiB;oBAC/C,mBAAmB,WAAW,iBAAiB;oBAC/C,sBAAsB,WAAW,oBAAoB;oBACrD,sBAAsB,WAAW,oBAAoB;gBACvD;YACF;YAEA,+BAA+B;YAC/B,IAAI,YAAY;gBACd,OAAO;YACT;YAEA,yDAAyD;YACzD,MAAM,WAAU,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,qBAAqB,gBAAgB;YACrD,IAAI,WAAW,OAAO,YAAY,UAAU;gBAC1C,OAAO;oBACL,kBAAkB,QAAQ,gBAAgB,IAAI,QAAQ,cAAc;oBACpE,YAAY,QAAQ,UAAU,IAAI,QAAQ,WAAW,IAAI,QAAQ,YAAY;oBAC7E,mBAAmB,QAAQ,iBAAiB,IAAI,QAAQ,eAAe;oBACvE,mBAAmB,QAAQ,iBAAiB,IAAI,QAAQ,gBAAgB;oBACxE,sBAAsB,QAAQ,oBAAoB,IAAI,QAAQ,mBAAmB;oBACjF,sBAAsB,QAAQ,oBAAoB,IAAI,QAAQ,mBAAmB;gBACnF;YACF;YAEA,6BAA6B;YAC7B,OAAO;gBACL,kBAAkB;gBAClB,YAAY;gBACZ,mBAAmB;gBACnB,mBAAmB;gBACnB,sBAAsB;gBACtB,sBAAsB;YACxB;QACF;6DAAG;QAAC;QAAY;SAAY,wBAAA,QAAQ,WAAW,cAAnB,4CAAA,sBAAqB,gBAAgB;KAAC;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,mBAAmB;YACnB,IAAI,UAAU,OAAO,IAAI,aAAkB,aAAa;gBACtD,yIAAiB,IAAI;yDAAC,CAAC;4BAKhB;wBAJL,MAAM,oBAAoB;wBAC1B,MAAM,SAAS,AAAC,sDAEN,OADX,QAAQ,cAAc,EAAC,sBAEjB,OADK,mBAAkB,iBAE3B,OADI,EAAA,iCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,qDAAA,+BAA+B,IAAI,KAAI,OAAM,aACzB,OAAxB,QAAQ,UAAU,EAAC,OACS,OADJ,QAAQ,WAAW,EAAC,yCACE,OAAlB;wBAE7B,0BAA0B;wBAC1B,MAAM,SAAS,SAAS,aAAa,CAAC;wBAEtC,OAAO,QAAQ,CAAC,QAAQ,QAAQ;4BAC9B,OAAO;4BACP,QAAQ;4BACR,OAAO;gCACL,MAAM;gCACN,OAAO;4BACT;wBACF,GAAG,IAAI;iEAAC;gCACN,sCAAsC;gCACtC,IAAI,UAAU,OAAO,EAAE;oCACrB,UAAU,OAAO,CAAC,SAAS,GAAG;oCAC9B,UAAU,OAAO,CAAC,WAAW,CAAC;gCAChC;4BACF;gEAAG,KAAK;iEAAC,CAAC;gCACR,QAAQ,KAAK,CAAC,8BAA8B;gCAC5C,IAAI,UAAU,OAAO,EAAE;oCACrB,UAAU,OAAO,CAAC,SAAS,GAAG;gCAChC;4BACF;;oBACF;wDAAG,KAAK;yDAAC,CAAC;wBACR,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,IAAI,UAAU,OAAO,EAAE;4BACrB,UAAU,OAAO,CAAC,SAAS,GAAG;wBAChC;oBACF;;YACF;QACF;4CAAG;QAAC;QAAS;QAAsB;KAAc;IAIjD,qBACE,6LAAC;kDAAc;;;;;;0BAgPb,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAc;;sCAEf,6LAAC;sEAAc;sCACb,cAAA,6LAAC;0EAAc;;oCAAiB;kDACT,6LAAC;;;;;;;oCAAK;;;;;;;;;;;;sCAK/B,6LAAC;sEAAc;;8CAEb,6LAAC;8EAAc;;sDAEb,6LAAC;sFAAc;sDACb,cAAA,6LAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,SAAS,CAAC;oDACR,MAAM,SAAS,EAAE,MAAM;oDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;gDACzB;;;;;;;;;;;;sDAKJ,6LAAC;sFAAc;sDACb,cAAA,6LAAC;0FAAa;0DAAkB;;;;;;;;;;;sDAMlC,6LAAC;sFAAc;;8DACb,6LAAC;;;;;;;8DACD,6LAAC;;;wDAAK;wDAAiB;;;;;;;;;;;;;sDAIzB,6LAAC;sFAAc;sDAAmB;;;;;;sDAGlC,6LAAC;sFAAY;sDAAqB;;;;;;;;;;;;8CAMpC,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;;8DACb,6LAAC;8FAAe;8DAAe;;;;;;8DAC/B,6LAAC;8FAAe;8DAAe;;;;;;8DAC/B,6LAAC;8FAAe;8DAAgB,gBAAgB,gBAAgB,IAAI;;;;;;;;;;;;sDAGtE,6LAAC;sFAAc;;8DACb,6LAAC;8FAAe;8DAAe;;;;;;8DAC/B,6LAAC;8FAAe;8DAAe;;;;;;8DAC/B,6LAAC;8FAAe;8DAAgB,gBAAgB,UAAU,IAAI;;;;;;;;;;;;sDAGhE,6LAAC;sFAAc;;8DACb,6LAAC;8FAAe;8DAAe;;;;;;8DAC/B,6LAAC;8FAAe;8DAAe;;;;;;8DAC/B,6LAAC;8FAAe;8DAAgB,gBAAgB,iBAAiB,IAAI;;;;;;;;;;;;sDAGvE,6LAAC;sFAAc;;8DACb,6LAAC;8FAAe;8DAAe;;;;;;8DAC/B,6LAAC;8FAAe;8DAAe;;;;;;8DAC/B,6LAAC;8FAAc;;sEACb,6LAAC;;sEAAK,gBAAgB,iBAAiB,IAAI;;;;;;wDAC1C,gBAAgB,oBAAoB,IACnC,gBAAgB,oBAAoB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAc,sBAClE,6LAAC;;0EAAiB;+DAAR;;;;;wDAGb,CAAC,gBAAgB,oBAAoB,kBAAI,6LAAC;;sEAAI;;;;;;;;;;;;;;;;;;;;;;;;8CAMrD,6LAAC;8EAAc;8CACb,cAAA,6LAAC;;kDAAE;;;;;;;;;;;8CAML,6LAAC;8EAAc;;sDAEb,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;;kEAAE;;;;;;;;;;;8DAKL,6LAAC;8FAAc;;sEACb,6LAAC;sGAAY;sEAAU;;;;;;sEACvB,6LAAC;sGAAY;sEAAW;;;;;;;;;;;;;;;;;;sDAK5B,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;wDAAI,KAAK;;kEAER,cAAA,6LAAC;4DAAI,OAAM;4DAAK,QAAO;4DAAK,SAAQ;4DAAY,OAAM;;;8EACpD,6LAAC;oEAAK,OAAM;oEAAK,QAAO;oEAAK,MAAK;;;;;;;8EAClC,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC5C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC5C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC5C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC5C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC5C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC5C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC9C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC9C,6LAAC;oEAAK,GAAE;oEAAI,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC9C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC9C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC9C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC9C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAI,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC7C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC9C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC9C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;8EAC9C,6LAAC;oEAAK,GAAE;oEAAK,GAAE;oEAAK,OAAM;oEAAI,QAAO;oEAAI,MAAK;;;;;;;;;;;;;;;;;;;;;;;8DAIpD,6LAAC;8FAAY;8DAAU;;;;;;;;;;;;;;;;;;8CAO3B,6LAAC;8EAAc;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;GAhhBwB;KAAA", "debugId": null}}, {"offset": {"line": 5391, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/certificates/StandardsCertificate.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef } from 'react';\r\nimport { License } from '@/types';\r\n\r\ninterface StandardsCertificateProps {\r\n  license: License;\r\n}\r\n\r\nexport default function StandardsCertificate({ license }: StandardsCertificateProps) {\r\n  const qrCodeRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    // Generate QR code\r\n    if (qrCodeRef.current && typeof window !== 'undefined') {\r\n      import('qrcode').then((QRCode) => {\r\n        const qrData = `MACRA License Verification\r\nRef: ${license.license_number}\r\nLicense No: ${license.license_number}\r\nLicensee: ${license.application.applicant?.name || 'N/A'}\r\nType: ${license.application.license_category?.name || 'N/A'}\r\nValid: ${license.issue_date} - ${license.expiry_date}\r\nVerify at: https://macra.mw/verify/${license.license_number}`;\r\n\r\n        // Create a canvas element\r\n        const canvas = document.createElement('canvas');\r\n\r\n        QRCode.toCanvas(canvas, qrData, {\r\n          width: 96,\r\n          margin: 1,\r\n          color: {\r\n            dark: '#000000',\r\n            light: '#FFFFFF'\r\n          }\r\n        }).then(() => {\r\n          // Clear the div and append the canvas\r\n          if (qrCodeRef.current) {\r\n            qrCodeRef.current.innerHTML = '';\r\n            qrCodeRef.current.appendChild(canvas);\r\n          }\r\n        }).catch((error) => {\r\n          console.error('QR Code generation failed:', error);\r\n          if (qrCodeRef.current) {\r\n            qrCodeRef.current.innerHTML = '<div style=\"font-size: 10px; text-align: center; color: #666;\">QR Code<br>Error</div>';\r\n          }\r\n        });\r\n      }).catch((error) => {\r\n        console.error('QRCode import failed:', error);\r\n        if (qrCodeRef.current) {\r\n          qrCodeRef.current.innerHTML = '<div style=\"font-size: 10px; text-align: center; color: #666;\">QR Code<br>Unavailable</div>';\r\n        }\r\n      });\r\n    }\r\n  }, [license]);\r\n\r\n  const formatDate = (dateString: string) => {\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'long',\r\n      day: 'numeric'\r\n    });\r\n  };\r\n\r\n  const getValidityPeriod = () => {\r\n    const issueDate = new Date(license.issue_date);\r\n    const expiryDate = new Date(license.expiry_date);\r\n    const years = expiryDate.getFullYear() - issueDate.getFullYear();\r\n    return `${years} Years (${formatDate(license.issue_date)} - ${formatDate(license.expiry_date)})`;\r\n  };\r\n\r\n  const getLicenseTypeTitle = () => {\r\n    switch (license.code) {\r\n      case 'telecommunications':\r\n        return 'TELECOMMUNICATIONS LICENSE';\r\n      case 'broadcasting':\r\n        return 'BROADCASTING LICENSE';\r\n      case 'spectrum_management':\r\n        return 'SPECTRUM MANAGEMENT LICENSE';\r\n      default:\r\n        return 'COMMUNICATIONS LICENSE';\r\n    }\r\n  };\r\n\r\n  const getLicenseDescription = () => {\r\n    switch (license.code) {\r\n      case 'telecommunications':\r\n        return 'This license authorizes the holder to provide telecommunications services in Malawi';\r\n      case 'broadcasting':\r\n        return 'This license authorizes the holder to operate broadcasting services in Malawi';\r\n      case 'spectrum_management':\r\n        return 'This license authorizes the holder to manage radio frequency spectrum in Malawi';\r\n      default:\r\n        return 'This license authorizes the holder to operate communications services in Malawi';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"standards-certificate\">\r\n      <style jsx>{`\r\n        .standards-certificate {\r\n          font-family: 'Times New Roman', serif;\r\n          background: white;\r\n          border: 4px solid #dc2626;\r\n          border-radius: 8px;\r\n          padding: 40px;\r\n          min-height: 842px;\r\n          position: relative;\r\n          overflow: hidden;\r\n        }\r\n        \r\n        .inner-border {\r\n          border: 2px solid #16a34a;\r\n          padding: 30px;\r\n          min-height: 100%;\r\n          position: relative;\r\n        }\r\n        \r\n        .watermark {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 50%;\r\n          transform: translate(-50%, -50%) rotate(-15deg);\r\n          opacity: 0.05;\r\n          pointer-events: none;\r\n          z-index: 0;\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          text-align: center;\r\n        }\r\n        \r\n        .watermark-text {\r\n          font-size: 36px;\r\n          font-weight: bold;\r\n          color: #dc2626;\r\n          letter-spacing: 3px;\r\n          line-height: 1.2;\r\n        }\r\n        \r\n        .content-wrapper {\r\n          position: relative;\r\n          z-index: 1;\r\n        }\r\n        \r\n        .header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n        }\r\n\r\n        .logo-section {\r\n          margin-bottom: 20px;\r\n          text-align: center;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n        }\r\n\r\n        .logo-section img {\r\n          height: 80px;\r\n          width: auto;\r\n          object-fit: contain;\r\n          max-width: 120px;\r\n        }\r\n\r\n        .authority-name {\r\n          border: 2px solid #dc2626;\r\n          display: inline-block;\r\n          padding: 12px 30px;\r\n          margin-bottom: 20px;\r\n        }\r\n        \r\n        .authority-title {\r\n          color: #dc2626;\r\n          font-weight: bold;\r\n          font-size: 16px;\r\n          letter-spacing: 1px;\r\n        }\r\n        \r\n        .reference-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          margin-bottom: 15px;\r\n          font-size: 14px;\r\n          color: #2563eb;\r\n        }\r\n        \r\n        .certificate-type {\r\n          background-color: #2563eb;\r\n          color: white;\r\n          padding: 10px 20px;\r\n          display: inline-block;\r\n          font-weight: bold;\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n        \r\n        .certification-text {\r\n          font-size: 14px;\r\n          color: #374151;\r\n          margin-bottom: 25px;\r\n        }\r\n        \r\n        .license-details {\r\n          margin-bottom: 25px;\r\n          line-height: 1.8;\r\n        }\r\n        \r\n        .detail-row {\r\n          display: flex;\r\n          margin-bottom: 12px;\r\n          align-items: flex-start;\r\n        }\r\n        \r\n        .detail-label {\r\n          font-weight: 600;\r\n          min-width: 200px;\r\n          color: #374151;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .detail-colon {\r\n          margin: 0 10px;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        .detail-value {\r\n          font-weight: bold;\r\n          color: #111827;\r\n          flex: 1;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .authorization-section {\r\n          margin-bottom: 20px;\r\n          background-color: #f9fafb;\r\n          padding: 15px;\r\n          border-radius: 6px;\r\n          border-left: 4px solid #2563eb;\r\n        }\r\n        \r\n        .authorization-title {\r\n          font-weight: bold;\r\n          color: #374151;\r\n          margin-bottom: 8px;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .compliance-section {\r\n          text-align: justify;\r\n          line-height: 1.6;\r\n          margin-bottom: 30px;\r\n          font-size: 14px;\r\n          color: #111827;\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .footer-section {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: flex-end;\r\n          margin-top: 40px;\r\n        }\r\n        \r\n        .signature-area {\r\n          flex: 1;\r\n        }\r\n        \r\n        .issue-location {\r\n          font-size: 14px;\r\n          margin-bottom: 60px;\r\n          color: #111827;\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .signature-line {\r\n          text-align: center;\r\n          border-top: 1px solid black;\r\n          padding-top: 8px;\r\n          max-width: 200px;\r\n        }\r\n        \r\n        .dg-name {\r\n          font-weight: bold;\r\n          font-size: 14px;\r\n          color: #111827;\r\n        }\r\n        \r\n        .dg-title {\r\n          font-size: 14px;\r\n          color: #111827;\r\n          font-weight: 500;\r\n        }\r\n        \r\n        .qr-section {\r\n          flex-shrink: 0;\r\n          margin-left: 40px;\r\n          text-align: center;\r\n        }\r\n        \r\n        .qr-code {\r\n          width: 100px;\r\n          height: 100px;\r\n          border: 2px solid #9ca3af;\r\n          background: white;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          font-size: 10px;\r\n          color: #6b7280;\r\n          margin-bottom: 8px;\r\n        }\r\n        \r\n        .qr-text {\r\n          font-size: 10px;\r\n          color: #4b5563;\r\n          max-width: 100px;\r\n        }\r\n        \r\n        .security-footer {\r\n          background-color: #06b6d4;\r\n          color: white;\r\n          text-align: center;\r\n          padding: 12px;\r\n          font-size: 14px;\r\n          font-weight: 700;\r\n          margin-top: 20px;\r\n          margin: 20px -30px -30px -30px;\r\n          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"inner-border\">\r\n        {/* Watermark */}\r\n        <div className=\"watermark\">\r\n          <div className=\"watermark-text\">\r\n            MALAWI COMMUNICATIONS<br />\r\n            REGULATORY AUTHORITY\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"content-wrapper\">\r\n          {/* Header */}\r\n          <div className=\"header\">\r\n            {/* MACRA Logo */}\r\n            <div className=\"logo-section\">\r\n              <img\r\n                src=\"/macra-logo.png\"\r\n                alt=\"MACRA Logo\"\r\n                onError={(e) => {\r\n                  const target = e.target as HTMLImageElement;\r\n                  target.style.display = 'none';\r\n                }}\r\n              />\r\n            </div>\r\n\r\n            {/* Authority Name */}\r\n            <div className=\"authority-name\">\r\n              <h1 className=\"authority-title\">\r\n                MALAWI COMMUNICATIONS REGULATORY AUTHORITY\r\n              </h1>\r\n            </div>\r\n\r\n            {/* Reference and License Numbers */}\r\n            <div className=\"reference-section\">\r\n              <span>Ref: {license.code?.toUpperCase().substring(0, 2) || 'GEN'} - {new Date().getFullYear()}</span>\r\n              <span>License No. {license.license_number}</span>\r\n            </div>\r\n\r\n            {/* Certificate Type */}\r\n            <div className=\"certificate-type\">\r\n              {getLicenseTypeTitle()}\r\n            </div>\r\n            <p className=\"certification-text\">\r\n              {getLicenseDescription()}\r\n            </p>\r\n          </div>\r\n\r\n          {/* License Details */}\r\n          <div className=\"license-details\">\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSEE NAME</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">BUSINESS REGISTRATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.business_registration_number || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">TAX IDENTIFICATION NO.</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.applicant?.tpin || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">REGISTERED ADDRESS</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <div className=\"detail-value\">\r\n                <div>{license.application.applicant?.name || 'N/A'}</div>\r\n                {/* <div>{license.application.applicant?.address || 'N/A'}</div> */}\r\n                {/* <div>{license.application.applicant?.city || ''}, {license.application.applicant?.country || 'Malawi'}</div> */}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSE CATEGORY</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{license.application.license_category?.name || 'N/A'}</span>\r\n            </div>\r\n\r\n            <div className=\"detail-row\">\r\n              <span className=\"detail-label\">LICENSE VALIDITY PERIOD</span>\r\n              <span className=\"detail-colon\">:</span>\r\n              <span className=\"detail-value\">{getValidityPeriod()}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Authorization Details */}\r\n          <div className=\"authorization-section\">\r\n            <div className=\"authorization-title\">AUTHORIZATION:</div>\r\n            <p>{license.application.license_category?.authorizes || 'This license authorizes the holder to operate communications services as specified in the license conditions and applicable regulations.'}</p>\r\n          </div>\r\n\r\n          {/* Compliance Statement */}\r\n          <div className=\"compliance-section\">\r\n            <p>\r\n              {license.conditions || \r\n                `This license is issued under the Communications Act, 2016, and authorizes the licensee \r\n                to provide the specified services in Malawi subject to compliance with all applicable laws, \r\n                regulations, and license conditions. The licensee shall maintain adequate infrastructure, \r\n                qualified personnel, and service standards as prescribed by the Malawi Communications \r\n                Regulatory Authority. This license is non-transferable and must be renewed before expiration.`\r\n              }\r\n            </p>\r\n          </div>\r\n\r\n          {/* Footer with Signature and QR Code */}\r\n          <div className=\"footer-section\">\r\n            {/* Signature Section */}\r\n            <div className=\"signature-area\">\r\n              <div className=\"issue-location\">\r\n                <p>\r\n                  Issued at Lilongwe, this {formatDate(license.issue_date)}\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"signature-line\">\r\n                <p className=\"dg-name\">Daud Suleman</p>\r\n                <p className=\"dg-title\">Director General</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* QR Code Section */}\r\n            <div className=\"qr-section\">\r\n              <div className=\"qr-code\">\r\n                <div ref={qrCodeRef}>\r\n                  {/* Fallback SVG QR Code */}\r\n                  <svg width=\"96\" height=\"96\" viewBox=\"0 0 25 25\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                    <rect width=\"25\" height=\"25\" fill=\"white\"/>\r\n                    <rect x=\"0\" y=\"0\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"1\" y=\"1\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"2\" y=\"2\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"18\" y=\"0\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"19\" y=\"1\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"20\" y=\"2\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"0\" y=\"18\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"1\" y=\"19\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"2\" y=\"20\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"8\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"10\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"12\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"14\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"16\" y=\"6\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"8\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"10\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"12\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"14\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"6\" y=\"16\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"9\" y=\"9\" width=\"7\" height=\"7\" fill=\"black\"/>\r\n                    <rect x=\"10\" y=\"10\" width=\"5\" height=\"5\" fill=\"white\"/>\r\n                    <rect x=\"11\" y=\"11\" width=\"3\" height=\"3\" fill=\"black\"/>\r\n                    <rect x=\"8\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"10\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"12\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"14\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"16\" y=\"17\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"8\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"10\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"12\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"14\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                    <rect x=\"17\" y=\"16\" width=\"1\" height=\"1\" fill=\"black\"/>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <p className=\"qr-text\">\r\n                Scan for verification\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Security Footer */}\r\n          <div className=\"security-footer\">\r\n            This licence is issued without any alterations and remains the property of MACRA\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;;AASe,SAAS,qBAAqB,KAAsC;QAAtC,EAAE,OAAO,EAA6B,GAAtC;QAmWnB,eAkBoB,gCAMA,iCAMA,iCAOxB,iCASwB,uCAa9B;;IA7Zd,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,mBAAmB;YACnB,IAAI,UAAU,OAAO,IAAI,aAAkB,aAAa;gBACtD,yIAAiB,IAAI;sDAAC,CAAC;4BAIjB,gCACJ;wBAJA,MAAM,SAAS,AAAC,oCAEV,OADP,QAAQ,cAAc,EAAC,kBAElB,OADE,QAAQ,cAAc,EAAC,gBAE7B,OADI,EAAA,iCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,qDAAA,+BAA+B,IAAI,KAAI,OAAM,YAEhD,OADD,EAAA,wCAAA,QAAQ,WAAW,CAAC,gBAAgB,cAApC,4DAAA,sCAAsC,IAAI,KAAI,OAAM,aAC3B,OAAxB,QAAQ,UAAU,EAAC,OACS,OADJ,QAAQ,WAAW,EAAC,yCACO,OAAvB,QAAQ,cAAc;wBAEnD,0BAA0B;wBAC1B,MAAM,SAAS,SAAS,aAAa,CAAC;wBAEtC,OAAO,QAAQ,CAAC,QAAQ,QAAQ;4BAC9B,OAAO;4BACP,QAAQ;4BACR,OAAO;gCACL,MAAM;gCACN,OAAO;4BACT;wBACF,GAAG,IAAI;8DAAC;gCACN,sCAAsC;gCACtC,IAAI,UAAU,OAAO,EAAE;oCACrB,UAAU,OAAO,CAAC,SAAS,GAAG;oCAC9B,UAAU,OAAO,CAAC,WAAW,CAAC;gCAChC;4BACF;6DAAG,KAAK;8DAAC,CAAC;gCACR,QAAQ,KAAK,CAAC,8BAA8B;gCAC5C,IAAI,UAAU,OAAO,EAAE;oCACrB,UAAU,OAAO,CAAC,SAAS,GAAG;gCAChC;4BACF;;oBACF;qDAAG,KAAK;sDAAC,CAAC;wBACR,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,IAAI,UAAU,OAAO,EAAE;4BACrB,UAAU,OAAO,CAAC,SAAS,GAAG;wBAChC;oBACF;;YACF;QACF;yCAAG;QAAC;KAAQ;IAEZ,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,YAAY,IAAI,KAAK,QAAQ,UAAU;QAC7C,MAAM,aAAa,IAAI,KAAK,QAAQ,WAAW;QAC/C,MAAM,QAAQ,WAAW,WAAW,KAAK,UAAU,WAAW;QAC9D,OAAO,AAAC,GAAkB,OAAhB,OAAM,YAA8C,OAApC,WAAW,QAAQ,UAAU,GAAE,OAAqC,OAAhC,WAAW,QAAQ,WAAW,GAAE;IAChG;IAEA,MAAM,sBAAsB;QAC1B,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB;QAC5B,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;kDAAc;;;;;;0BA0Ob,6LAAC;0DAAc;;kCAEb,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;gCAAiB;8CACT,6LAAC;;;;;;;gCAAK;;;;;;;;;;;;kCAK/B,6LAAC;kEAAc;;0CAEb,6LAAC;0EAAc;;kDAEb,6LAAC;kFAAc;kDACb,cAAA,6LAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,SAAS,CAAC;gDACR,MAAM,SAAS,EAAE,MAAM;gDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;4CACzB;;;;;;;;;;;;kDAKJ,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAa;sDAAkB;;;;;;;;;;;kDAMlC,6LAAC;kFAAc;;0DACb,6LAAC;;;oDAAK;oDAAM,EAAA,gBAAA,QAAQ,IAAI,cAAZ,oCAAA,cAAc,WAAW,GAAG,SAAS,CAAC,GAAG,OAAM;oDAAM;oDAAI,IAAI,OAAO,WAAW;;;;;;;0DAC3F,6LAAC;;;oDAAK;oDAAa,QAAQ,cAAc;;;;;;;;;;;;;kDAI3C,6LAAC;kFAAc;kDACZ;;;;;;kDAEH,6LAAC;kFAAY;kDACV;;;;;;;;;;;;0CAKL,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAgB,EAAA,iCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,qDAAA,+BAA+B,IAAI,KAAI;;;;;;;;;;;;kDAGzE,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAgB,EAAA,kCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,sDAAA,gCAA+B,4BAA4B,KAAI;;;;;;;;;;;;kDAGjG,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAgB,EAAA,kCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,sDAAA,gCAA+B,IAAI,KAAI;;;;;;;;;;;;kDAGzE,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAc;0DACb,cAAA,6LAAC;;8DAAK,EAAA,kCAAA,QAAQ,WAAW,CAAC,SAAS,cAA7B,sDAAA,gCAA+B,IAAI,KAAI;;;;;;;;;;;;;;;;;kDAMjD,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAgB,EAAA,wCAAA,QAAQ,WAAW,CAAC,gBAAgB,cAApC,4DAAA,sCAAsC,IAAI,KAAI;;;;;;;;;;;;kDAGhF,6LAAC;kFAAc;;0DACb,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAe;;;;;;0DAC/B,6LAAC;0FAAe;0DAAgB;;;;;;;;;;;;;;;;;;0CAKpC,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;kDAAsB;;;;;;kDACrC,6LAAC;;kDAAG,EAAA,yCAAA,QAAQ,WAAW,CAAC,gBAAgB,cAApC,6DAAA,uCAAsC,UAAU,KAAI;;;;;;;;;;;;0CAI1D,6LAAC;0EAAc;0CACb,cAAA,6LAAC;;8CACE,QAAQ,UAAU,IAChB;;;;;;;;;;;0CAUP,6LAAC;0EAAc;;kDAEb,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;0DACb,cAAA,6LAAC;;;wDAAE;wDACyB,WAAW,QAAQ,UAAU;;;;;;;;;;;;0DAI3D,6LAAC;0FAAc;;kEACb,6LAAC;kGAAY;kEAAU;;;;;;kEACvB,6LAAC;kGAAY;kEAAW;;;;;;;;;;;;;;;;;;kDAK5B,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;0DACb,cAAA,6LAAC;oDAAI,KAAK;;8DAER,cAAA,6LAAC;wDAAI,OAAM;wDAAK,QAAO;wDAAK,SAAQ;wDAAY,OAAM;;;0EACpD,6LAAC;gEAAK,OAAM;gEAAK,QAAO;gEAAK,MAAK;;;;;;;0EAClC,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC5C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAI,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC7C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;0EAC9C,6LAAC;gEAAK,GAAE;gEAAK,GAAE;gEAAK,OAAM;gEAAI,QAAO;gEAAI,MAAK;;;;;;;;;;;;;;;;;;;;;;;0DAIpD,6LAAC;0FAAY;0DAAU;;;;;;;;;;;;;;;;;;0CAO3B,6LAAC;0EAAc;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GAtfwB;KAAA", "debugId": null}}, {"offset": {"line": 6416, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/certificates/CertificateModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { License } from '@/types';\r\nimport { licenseService } from '@/services/licenseService';\r\nimport PostalServicesCertificate from './PostalServicesCertificate';\r\nimport TypeApprovalCertificate from './TypeApprovalCertificate';\r\nimport StandardsCertificate from './StandardsCertificate';\r\nimport toast from 'react-hot-toast';\r\n\r\ninterface CertificateModalProps {\r\n  license: License;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n}\r\n\r\nexport default function CertificateModal({ license, isOpen, onClose }: CertificateModalProps) {\r\n  const [isDownloading, setIsDownloading] = useState(false);\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const renderCertificate = () => {\r\n    // Determine which certificate to show based on license code\r\n    switch (license.code) {\r\n      case 'postal_services':\r\n        return <PostalServicesCertificate license={license} />;\r\n      case 'standards_compliance':\r\n        return <TypeApprovalCertificate license={license} />;\r\n      case 'telecommunications':\r\n      case 'broadcasting':\r\n      case 'spectrum_management':\r\n      default:\r\n        // Fallback to standards certificate for other types\r\n        return <StandardsCertificate license={license} />;\r\n    }\r\n  };\r\n\r\n  const handlePrint = () => {\r\n    window.print();\r\n  };\r\n\r\n  const handleDownloadPDF = async () => {\r\n    try {\r\n      setIsDownloading(true);\r\n      console.log('Starting PDF download for license:', license.license_id);\r\n\r\n      const blob = await licenseService.downloadLicensePDF(license.license_id);\r\n\r\n      // Verify the blob is valid\r\n      if (!blob || blob.size === 0) {\r\n        throw new Error('Received empty PDF file');\r\n      }\r\n\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `${license.license_number}_certificate.pdf`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n\r\n      console.log('PDF download completed successfully');\r\n      toast.success('Certificate PDF downloaded successfully');\r\n    } catch (error: any) {\r\n      console.error('Error downloading certificate PDF:', error);\r\n\r\n      // Show specific error message from the service\r\n      const errorMessage = error.message || 'Failed to download certificate PDF';\r\n      toast.error(errorMessage);\r\n    } finally {\r\n      setIsDownloading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div \r\n          className=\"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75\"\r\n          onClick={onClose}\r\n        />\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg\">\r\n          {/* Modal header */}\r\n          <div className=\"flex items-center justify-end mb-4 print:hidden\">\r\n            <div className=\"flex space-x-2\">\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleDownloadPDF}\r\n                disabled={isDownloading}\r\n                className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {isDownloading ? (\r\n                  <>\r\n                    <i className=\"ri-loader-4-line mr-2 animate-spin\"></i>\r\n                    Downloading...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <i className=\"ri-download-line mr-2\"></i>\r\n                    Download PDF\r\n                  </>\r\n                )}\r\n              </button>\r\n              <button\r\n                type=\"button\"\r\n                onClick={onClose}\r\n                className=\"inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              >\r\n                <i className=\"ri-close-line mr-2\"></i>\r\n                Close\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Certificate content */}\r\n          <div className=\"certificate-container\">\r\n            {renderCertificate()}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Print styles */}\r\n      <style jsx global>{`\r\n        @media print {\r\n          body * {\r\n            visibility: hidden;\r\n          }\r\n          .certificate-container,\r\n          .certificate-container * {\r\n            visibility: visible;\r\n          }\r\n          .certificate-container {\r\n            position: absolute;\r\n            left: 0;\r\n            top: 0;\r\n            width: 100%;\r\n          }\r\n          .print\\\\:hidden {\r\n            display: none !important;\r\n          }\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAgBe,SAAS,iBAAiB,KAAmD;QAAnD,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAyB,GAAnD;;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,oBAAoB;QACxB,4DAA4D;QAC5D,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,qBAAO,6LAAC,kKAAA,CAAA,UAAyB;oBAAC,SAAS;;;;;;YAC7C,KAAK;gBACH,qBAAO,6LAAC,gKAAA,CAAA,UAAuB;oBAAC,SAAS;;;;;;YAC3C,KAAK;YACL,KAAK;YACL,KAAK;YACL;gBACE,oDAAoD;gBACpD,qBAAO,6LAAC,6JAAA,CAAA,UAAoB;oBAAC,SAAS;;;;;;QAC1C;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,KAAK;IACd;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,iBAAiB;YACjB,QAAQ,GAAG,CAAC,sCAAsC,QAAQ,UAAU;YAEpE,MAAM,OAAO,MAAM,oIAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,QAAQ,UAAU;YAEvE,2BAA2B;YAC3B,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,GAAG;gBAC5B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,AAAC,GAAyB,OAAvB,QAAQ,cAAc,EAAC;YAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,QAAQ,GAAG,CAAC;YACZ,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sCAAsC;YAEpD,+CAA+C;YAC/C,MAAM,eAAe,MAAM,OAAO,IAAI;YACtC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,6LAAC;kDAAc;;0BACb,6LAAC;0DAAc;;kCAEb,6LAAC;wBAEC,SAAS;kEADC;;;;;;kCAKZ,6LAAC;kEAAc;;0CAEb,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU;sFACA;sDAET,8BACC;;kEACE,6LAAC;kGAAY;;;;;;oDAAyC;;6EAIxD;;kEACE,6LAAC;kGAAY;;;;;;oDAA4B;;;;;;;;sDAK/C,6LAAC;4CACC,MAAK;4CACL,SAAS;sFACC;;8DAEV,6LAAC;8FAAY;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;0CAO5C,6LAAC;0EAAc;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4Bb;GApIwB;KAAA", "debugId": null}}, {"offset": {"line": 6635, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/my-licenses/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport { licenseService } from '@/services/licenseService';\r\nimport { License, PaginatedResponse, PaginateQuery } from '@/types';\r\nimport DataTable from '@/components/common/DataTable';\r\nimport Select from '@/components/common/Select';\r\nimport CertificateModal from '@/components/certificates/CertificateModal';\r\nimport toast from 'react-hot-toast';\r\n\r\ninterface LicenseFilters {\r\n  status?: string;\r\n}\r\n\r\nconst MyLicensesPage: React.FC = () => {\r\n  const [licensesData, setLicensesData] = useState<PaginatedResponse<License> | null>(null);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [filters, setFilters] = useState<LicenseFilters>({});\r\n\r\n  // Certificate modal state\r\n  const [selectedLicense, setSelectedLicense] = useState<License | null>(null);\r\n  const [isCertificateModalOpen, setIsCertificateModalOpen] = useState(false);\r\n\r\n  // Load licenses function following the standard pattern\r\n  const loadLicenses = useCallback(async (query: PaginateQuery) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      // Backend automatically filters licenses for customer role\r\n      // No need for special filtering - just pass the standard parameters\r\n      const params = {\r\n        page: query.page,\r\n        limit: query.limit,\r\n        search: query.search,\r\n        sortBy: Array.isArray(query.sortBy) ? query.sortBy[0] : query.sortBy,\r\n        sortOrder: Array.isArray(query.sortBy) && query.sortBy.length > 1 ? query.sortBy[1] as 'ASC' | 'DESC' : undefined,\r\n        status: filters.status,\r\n      };\r\n\r\n      const response = await licenseService.getLicenses(params);\r\n      setLicensesData(response);\r\n    } catch (err: unknown) {\r\n      console.error('Error loading licenses:', err);\r\n\r\n      let errorMessage = 'Failed to load licenses';\r\n      if (err instanceof Error) {\r\n        errorMessage = err.message;\r\n      } else if (typeof err === 'string') {\r\n        errorMessage = err;\r\n      }\r\n      setError(errorMessage);\r\n\r\n      // Set empty data structure to prevent undefined errors\r\n      setLicensesData({\r\n        data: [],\r\n        meta: {\r\n          itemsPerPage: query.limit || 10,\r\n          totalItems: 0,\r\n          currentPage: query.page || 1,\r\n          totalPages: 0,\r\n          sortBy: [],\r\n          searchBy: [],\r\n          search: '',\r\n          select: [],\r\n          filter: {},\r\n        },\r\n        links: {\r\n          first: '',\r\n          current: '',\r\n          last: '',\r\n        },\r\n      });\r\n\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [filters]);\r\n\r\n  // Load licenses when component mounts or filters change\r\n  useEffect(() => {\r\n    loadLicenses({ page: 1, limit: 10 });\r\n  }, [loadLicenses]);\r\n\r\n  // Handle filter changes\r\n  const handleFilterChange = (key: keyof LicenseFilters, value: string) => {\r\n    const newFilters = { ...filters };\r\n\r\n    if (value && value.trim() !== '') {\r\n      newFilters[key] = value;\r\n    } else {\r\n      delete newFilters[key];\r\n    }\r\n\r\n    setFilters(newFilters);\r\n  };\r\n\r\n  // Handler for DataTable query changes (pagination, search, sorting)\r\n  const handleQueryChange = useCallback((query: PaginateQuery) => {\r\n    loadLicenses(query);\r\n  }, [loadLicenses]);\r\n\r\n  // Certificate modal handlers\r\n  const handleViewCertificate = (license: License) => {\r\n    setSelectedLicense(license);\r\n    setIsCertificateModalOpen(true);\r\n  };\r\n\r\n  const handleCloseCertificateModal = () => {\r\n    setIsCertificateModalOpen(false);\r\n    setSelectedLicense(null);\r\n  };\r\n\r\n  // Get status badge for license status\r\n  const getStatusBadge = (status: string) => {\r\n    const statusClasses: Record<string, string> = {\r\n      'active': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',\r\n      'expired': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',\r\n      'suspended': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',\r\n      'revoked': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',\r\n      'under_review': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',\r\n    };\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status] || statusClasses['active']}`}>\r\n        {status.replace('_', ' ').toUpperCase()}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  // Define license table columns following the standard pattern\r\n  const licenseColumns = [\r\n    {\r\n      key: 'license_number',\r\n      label: 'License Number',\r\n      sortable: true,\r\n      render: (_: unknown, item: License) => (\r\n        <div className=\"flex items-center\">\r\n          <div className=\"flex-shrink-0 h-10 w-10\">\r\n            <div className=\"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center\">\r\n              <i className=\"ri-award-line text-blue-600 dark:text-blue-400\"></i>\r\n            </div>\r\n          </div>\r\n          <div className=\"ml-4\">\r\n            <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n              {item.license_number}\r\n            </div>\r\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n              {item.code || 'N/A'}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'license_type',\r\n      label: 'License Type',\r\n      render: (_: unknown, item: License) => (\r\n        <div>\r\n          <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n            {item?.description || 'N/A'}\r\n          </div>\r\n          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n            {item?.code || 'N/A'}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      key: 'status',\r\n      label: 'Status',\r\n      sortable: true,\r\n      render: (_: unknown, item: License) => getStatusBadge(item.status),\r\n    },\r\n    {\r\n      key: 'issue_date',\r\n      label: 'Issue Date',\r\n      sortable: true,\r\n      render: (_: unknown, item: License) => (\r\n        <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n          {new Date(item.issue_date).toLocaleDateString()}\r\n        </span>\r\n      ),\r\n    },\r\n    {\r\n      key: 'expiry_date',\r\n      label: 'Expiry Date',\r\n      sortable: true,\r\n      render: (_: unknown, item: License) => {\r\n        const expiryDate = new Date(item.expiry_date);\r\n        const isExpired = expiryDate < new Date();\r\n        const isExpiringSoon = expiryDate < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\r\n\r\n        return (\r\n          <span className={`text-sm ${isExpired ? 'text-red-600 dark:text-red-400' : isExpiringSoon ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-500 dark:text-gray-400'}`}>\r\n            {expiryDate.toLocaleDateString()}\r\n          </span>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      key: 'actions',\r\n      label: 'Actions',\r\n      render: (_: unknown, item: License) => (\r\n        <div className=\"flex items-center justify-end space-x-2\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => handleViewCertificate(item)}\r\n            className=\"inline-flex items-center px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md hover:bg-green-100 dark:hover:bg-green-900/40 transition-colors\"\r\n          >\r\n            <i className=\"ri-eye-line mr-1\"></i>\r\n            View Certificate\r\n          </button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // Status filter options\r\n  const statusOptions = [\r\n    { value: '', label: 'All Statuses' },\r\n    { value: 'active', label: 'Active' },\r\n    { value: 'expired', label: 'Expired' },\r\n    { value: 'suspended', label: 'Suspended' },\r\n    { value: 'revoked', label: 'Revoked' },\r\n    { value: 'under_review', label: 'Under Review' },\r\n  ];\r\n\r\n  return (\r\n    <CustomerLayout>\r\n      <div className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          {/* Page header */}\r\n          <div className=\"mb-8\">\r\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n              <div>\r\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">My Licenses</h1>\r\n                <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\r\n                  View and manage your issued licenses.\r\n                </p>\r\n              </div>\r\n              <div className=\"flex space-x-3\">\r\n                <Link\r\n                  href=\"/customer/applications/apply\"\r\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\"\r\n                >\r\n                  <div className=\"w-4 h-4 flex items-center justify-center mr-2\">\r\n                    <i className=\"ri-add-line\"></i>\r\n                  </div>\r\n                  New Application\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Filters */}\r\n          <div className=\"mb-6 bg-white dark:bg-gray-800 rounded-lg shadow p-4\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                  Status\r\n                </label>\r\n                <Select\r\n                  value={filters.status || ''}\r\n                  onChange={(value) => handleFilterChange('status', value)}\r\n                  options={statusOptions}\r\n                  placeholder=\"Filter by status\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Error message */}\r\n          {error && (\r\n            <div className=\"mb-6\">\r\n              <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4\">\r\n                <div className=\"flex\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className=\"ri-error-warning-line text-red-400\"></i>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\r\n                      Error loading licenses\r\n                    </h3>\r\n                    <div className=\"mt-2 text-sm text-red-700 dark:text-red-300\">\r\n                      <p>{error}</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"ml-auto pl-3\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => setError(null)}\r\n                      className=\"inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800\"\r\n                    >\r\n                      <span className=\"sr-only\">Dismiss</span>\r\n                      <i className=\"ri-close-line text-sm\"></i>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Licenses Table */}\r\n          <DataTable<License>\r\n            columns={licenseColumns}\r\n            data={licensesData}\r\n            loading={loading}\r\n            onQueryChange={handleQueryChange}\r\n            searchPlaceholder=\"Search licenses by number or type...\"\r\n            emptyStateIcon=\"ri-award-line\"\r\n            emptyStateMessage=\"No licenses found. Apply for a license to get started.\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Certificate Modal */}\r\n      {selectedLicense && (\r\n        <CertificateModal\r\n          license={selectedLicense}\r\n          isOpen={isCertificateModalOpen}\r\n          onClose={handleCloseCertificateModal}\r\n        />\r\n      )}\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default MyLicensesPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AATA;;;;;;;;AAgBA,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IAExD,0BAA0B;IAC1B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,wDAAwD;IACxD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACtC,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,2DAA2D;gBAC3D,oEAAoE;gBACpE,MAAM,SAAS;oBACb,MAAM,MAAM,IAAI;oBAChB,OAAO,MAAM,KAAK;oBAClB,QAAQ,MAAM,MAAM;oBACpB,QAAQ,MAAM,OAAO,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,EAAE,GAAG,MAAM,MAAM;oBACpE,WAAW,MAAM,OAAO,CAAC,MAAM,MAAM,KAAK,MAAM,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM,MAAM,CAAC,EAAE,GAAqB;oBACxG,QAAQ,QAAQ,MAAM;gBACxB;gBAEA,MAAM,WAAW,MAAM,oIAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;gBAClD,gBAAgB;YAClB,EAAE,OAAO,KAAc;gBACrB,QAAQ,KAAK,CAAC,2BAA2B;gBAEzC,IAAI,eAAe;gBACnB,IAAI,eAAe,OAAO;oBACxB,eAAe,IAAI,OAAO;gBAC5B,OAAO,IAAI,OAAO,QAAQ,UAAU;oBAClC,eAAe;gBACjB;gBACA,SAAS;gBAET,uDAAuD;gBACvD,gBAAgB;oBACd,MAAM,EAAE;oBACR,MAAM;wBACJ,cAAc,MAAM,KAAK,IAAI;wBAC7B,YAAY;wBACZ,aAAa,MAAM,IAAI,IAAI;wBAC3B,YAAY;wBACZ,QAAQ,EAAE;wBACV,UAAU,EAAE;wBACZ,QAAQ;wBACR,QAAQ,EAAE;wBACV,QAAQ,CAAC;oBACX;oBACA,OAAO;wBACL,OAAO;wBACP,SAAS;wBACT,MAAM;oBACR;gBACF;YAEF,SAAU;gBACR,WAAW;YACb;QACF;mDAAG;QAAC;KAAQ;IAEZ,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,aAAa;gBAAE,MAAM;gBAAG,OAAO;YAAG;QACpC;mCAAG;QAAC;KAAa;IAEjB,wBAAwB;IACxB,MAAM,qBAAqB,CAAC,KAA2B;QACrD,MAAM,aAAa;YAAE,GAAG,OAAO;QAAC;QAEhC,IAAI,SAAS,MAAM,IAAI,OAAO,IAAI;YAChC,UAAU,CAAC,IAAI,GAAG;QACpB,OAAO;YACL,OAAO,UAAU,CAAC,IAAI;QACxB;QAEA,WAAW;IACb;IAEA,oEAAoE;IACpE,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACrC,aAAa;QACf;wDAAG;QAAC;KAAa;IAEjB,6BAA6B;IAC7B,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB;QACnB,0BAA0B;IAC5B;IAEA,MAAM,8BAA8B;QAClC,0BAA0B;QAC1B,mBAAmB;IACrB;IAEA,sCAAsC;IACtC,MAAM,iBAAiB,CAAC;QACtB,MAAM,gBAAwC;YAC5C,UAAU;YACV,WAAW;YACX,aAAa;YACb,WAAW;YACX,gBAAgB;QAClB;QAEA,qBACE,6LAAC;YAAK,WAAW,AAAC,2EAA2H,OAAjD,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,SAAS;sBACzI,OAAO,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;IAG3C;IAEA,8DAA8D;IAC9D,MAAM,iBAAiB;QACrB;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,GAAY,qBACnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;sCAGjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,KAAK,cAAc;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;;QAKxB;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAY,qBACnB,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;sCACZ,CAAA,iBAAA,2BAAA,KAAM,WAAW,KAAI;;;;;;sCAExB,6LAAC;4BAAI,WAAU;sCACZ,CAAA,iBAAA,2BAAA,KAAM,IAAI,KAAI;;;;;;;;;;;;QAIvB;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,GAAY,OAAkB,eAAe,KAAK,MAAM;QACnE;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,GAAY,qBACnB,6LAAC;oBAAK,WAAU;8BACb,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;QAGnD;QACA;YACE,KAAK;YACL,OAAO;YACP,UAAU;YACV,QAAQ,CAAC,GAAY;gBACnB,MAAM,aAAa,IAAI,KAAK,KAAK,WAAW;gBAC5C,MAAM,YAAY,aAAa,IAAI;gBACnC,MAAM,iBAAiB,aAAa,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;gBAE9E,qBACE,6LAAC;oBAAK,WAAW,AAAC,WAAsJ,OAA5I,YAAY,mCAAmC,iBAAiB,yCAAyC;8BAClI,WAAW,kBAAkB;;;;;;YAGpC;QACF;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAY,qBACnB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,SAAS,IAAM,sBAAsB;wBACrC,WAAU;;0CAEV,6LAAC;gCAAE,WAAU;;;;;;4BAAuB;;;;;;;;;;;;QAK5C;KACD;IAED,wBAAwB;IACxB,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAI,OAAO;QAAe;QACnC;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAgB,OAAO;QAAe;KAChD;IAED,qBACE,6LAAC,mJAAA,CAAA,UAAc;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsD;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;kDAIvD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;gDACT;;;;;;;;;;;;;;;;;;;;;;;sCAQd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC,yIAAA,CAAA,UAAM;4CACL,OAAO,QAAQ,MAAM,IAAI;4CACzB,UAAU,CAAC,QAAU,mBAAmB,UAAU;4CAClD,SAAS;4CACT,aAAY;;;;;;;;;;;;;;;;;;;;;;wBAOnB,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAqD;;;;;;8DAGnE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;kEAAG;;;;;;;;;;;;;;;;;sDAGR,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,SAAS;gDACxB,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASzB,6LAAC,4IAAA,CAAA,UAAS;4BACR,SAAS;4BACT,MAAM;4BACN,SAAS;4BACT,eAAe;4BACf,mBAAkB;4BAClB,gBAAe;4BACf,mBAAkB;;;;;;;;;;;;;;;;;YAMvB,iCACC,6LAAC,yJAAA,CAAA,UAAgB;gBACf,SAAS;gBACT,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAKnB;GAzTM;KAAA;uCA2TS", "debugId": null}}]}