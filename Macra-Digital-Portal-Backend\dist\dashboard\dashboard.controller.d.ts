import { DashboardService } from './dashboard.service';
export declare class DashboardController {
    private readonly dashboardService;
    constructor(dashboardService: DashboardService);
    getOverview(req: any): Promise<{
        applications: any;
        users: {
            total: number;
            active: number;
            newThisMonth: number;
            administrators: number;
        } | null;
        licenses: {
            total: number;
            active: number;
            expired: number;
            suspended: number;
            expiringSoon: number;
        };
        financial: {
            totalRevenue: number;
            thisMonth: number;
            pending: number;
            transactions: number;
        };
        timestamp: string;
    }>;
    getLicenseStats(req: any): Promise<{
        success: boolean;
        message: string;
        data: {
            total: number;
            active: number;
            expired: number;
            suspended: number;
            expiringSoon: number;
        };
    } | {
        success: boolean;
        message: string;
        data: {
            total: number;
            active: number;
            expiringSoon: number;
            expired: number;
        };
    }>;
    getUserStats(req: any): Promise<{
        success: boolean;
        message: string;
        data: null;
    } | {
        success: boolean;
        message: string;
        data: {
            total: number;
            active: number;
            newThisMonth: number;
            administrators: number;
        };
    }>;
    getFinancialStats(req: any): Promise<{
        success: boolean;
        message: string;
        data: {
            totalRevenue: number;
            thisMonth: number;
            pending: number;
            transactions: number;
        };
    }>;
    getRecentApplications(req: any): Promise<{
        success: boolean;
        message: string;
        data: import("../entities").Applications[];
    }>;
    getRecentActivities(req: any): Promise<{
        success: boolean;
        message: string;
        data: import("../entities/audit-trail.entity").AuditTrail[];
    }>;
}
