{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAAoD;AACpD,+CAAoD;AACpD,2CAAmE;AACnE,2CAA+C;AAC/C,kDAA8C;AAIvC,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YACU,WAAwB,EACxB,aAA4B;QAEpC,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,IAAI,2CAA2C,CAAC;QACtG,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QAE9E,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,MAAM;SACpB,CAAC,CAAC;QAVK,gBAAW,GAAX,WAAW,CAAa;QACxB,kBAAa,GAAb,aAAa,CAAe;IAUtC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAmB;QAChC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAChE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,EAAE,CAAC;QACpC,CAAC;QAGD,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;QAC/E,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CACzC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACxC,IAAI,KAAK,CAAC;QAGX,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,GAAG;YACpB,MAAM,EAAE,OAAO,CAAC,GAAG;YACnB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;YAC1B,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;IACJ,CAAC;CACF,CAAA;AAtCY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAGY,0BAAW;QACT,sBAAa;GAH3B,WAAW,CAsCvB"}