import { LicensesService } from './licenses.service';
import { CreateLicenseDto } from '../dto/licenses/create-license.dto';
import { UpdateLicenseDto } from '../dto/licenses/update-license.dto';
import { Licenses } from '../entities/licenses.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { Response } from 'express';
export declare class LicensesController {
    private readonly licensesService;
    private readonly logger;
    constructor(licensesService: LicensesService);
    create(createLicenseDto: CreateLicenseDto, req: any): Promise<Licenses>;
    findAll(query: PaginateQuery, req: any): Promise<PaginatedResult<Licenses>>;
    getStats(): Promise<any>;
    findExpiringSoon(days?: number): Promise<Licenses[]>;
    findByApplication(applicationId: string): Promise<Licenses[]>;
    findByLicenseNumber(licenseNumber: string): Promise<Licenses>;
    generatePDF(id: string, res: Response): Promise<void>;
    clearTemplateCache(): Promise<{
        message: string;
    }>;
    findOne(id: string): Promise<Licenses>;
    update(id: string, updateLicenseDto: UpdateLicenseDto, req: any): Promise<Licenses>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
