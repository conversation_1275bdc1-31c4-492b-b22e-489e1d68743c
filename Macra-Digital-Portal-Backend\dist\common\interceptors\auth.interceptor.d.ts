import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { AuditTrailService } from '../../audit-trail/audit-trail.service';
export declare class AuthInterceptor implements NestInterceptor {
    private readonly auditTrailService;
    private readonly logger;
    constructor(auditTrailService: AuditTrailService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private logAuthEvent;
    private getClientIp;
    private addStandardHeaders;
}
