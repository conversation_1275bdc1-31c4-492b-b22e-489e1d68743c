import { PaginateQuery } from 'nestjs-paginate';
import { EvaluationsService } from './evaluations.service';
import { CreateEvaluationDto } from '../dto/evaluations/create-evaluation.dto';
import { UpdateEvaluationDto } from '../dto/evaluations/update-evaluation.dto';
import { Evaluations } from '../entities/evaluations.entity';
export declare class EvaluationsController {
    private readonly evaluationsService;
    constructor(evaluationsService: EvaluationsService);
    create(createEvaluationDto: CreateEvaluationDto, req: any): Promise<Evaluations>;
    findAll(query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<Evaluations>>;
    getStats(): Promise<{
        total: number;
        draft: number;
        completed: number;
        approved: number;
        rejected: number;
        averageScore: number;
    }>;
    healthCheck(): Promise<{
        status: string;
        database: string;
        evaluationCount: number;
        timestamp: string;
        error?: undefined;
    } | {
        status: string;
        database: string;
        error: any;
        timestamp: string;
        evaluationCount?: undefined;
    }>;
    findOne(id: string): Promise<Evaluations>;
    findByApplication(applicationId: string): Promise<{
        success: boolean;
        message: string;
        data: null;
        meta: {
            applicationId: string;
            evaluationExists: boolean;
            evaluationId?: undefined;
            error?: undefined;
        };
    } | {
        success: boolean;
        message: string;
        data: Evaluations;
        meta: {
            applicationId: string;
            evaluationExists: boolean;
            evaluationId: string;
            error?: undefined;
        };
    } | {
        success: boolean;
        message: any;
        data: null;
        meta: {
            applicationId: string;
            error: any;
            evaluationExists?: undefined;
            evaluationId?: undefined;
        };
    }>;
    findCriteria(id: string): Promise<import("../entities").EvaluationCriteria[]>;
    update(id: string, updateEvaluationDto: UpdateEvaluationDto, req: any): Promise<Evaluations>;
    remove(id: string): Promise<void>;
}
