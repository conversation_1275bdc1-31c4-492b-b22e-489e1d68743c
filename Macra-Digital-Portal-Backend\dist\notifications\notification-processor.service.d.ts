import { Repository } from 'typeorm';
import { MailerService } from '@nestjs-modules/mailer';
import { Notifications } from '../entities/notifications.entity';
export declare class NotificationProcessorService {
    private notificationsRepository;
    private readonly mailerService;
    private readonly logger;
    constructor(notificationsRepository: Repository<Notifications>, mailerService: MailerService);
    processPendingNotifications(): Promise<void>;
    private processEmailNotification;
    processAllPendingNotifications(): Promise<{
        processed: number;
        failed: number;
    }>;
    getProcessingStats(): Promise<{
        pending: number;
        sent: number;
        failed: number;
        total: number;
    }>;
}
