'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { authService } from '@/services/auth.service';
import { XCircleIcon } from '@heroicons/react/24/solid';
import { useAuth } from '@/contexts/AuthContext';
import Cookies from 'js-cookie';
import Loader from '@/components/Loader';

export default function StaffSetupTwoFactorPage() {
  const router = useRouter();
  const { user, token: access_token, loading: authLoading } = useAuth();

  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [secret, setSecret] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loadingMessage, setLoadingMessage] = useState('Initializing 2FA setup...');
  const [alreadyEnabled, setAlreadyEnabled] = useState(false);
  const [setUpComplete, setSetUpComplete] = useState(false);
  const [loading, setLoading] = useState(true);
  const [unauthorizedAccess, setUnauthorizedAccess] = useState(false);
  const [userId, setUserId] = useState(JSON.parse(Cookies.get('auth_user') || '{}').user_id || '');

  const redirectLogin = () => router.replace('/auth/login');
  const redirectDashboard = () => router.push('/dashboard');

  useEffect(() => {
    if (authLoading) return;

    const setupUser = sessionStorage.getItem('2fa_setup_user');
    const setupToken = sessionStorage.getItem('2fa_setup_token');

    let currentUser = user;
    let currentToken = access_token;

    if (setupUser && setupToken) {
      try {
        currentUser = JSON.parse(setupUser);
        currentToken = setupToken;
        authService.setAuthToken(setupToken);
      } catch (err) {
        console.error('Failed to parse 2FA setup session data:', err);
      }
    }

    if (!currentUser || !currentToken) {
      setLoading(false);
      const hasSetupData = setupUser && setupToken;
      const hasAuthContext = user && access_token;

      if (!hasSetupData && !hasAuthContext) {
        setUnauthorizedAccess(true);
        setError('Unauthorized access. This page can only be accessed during 2FA setup after login.');
        setLoadingMessage('Please login to continue.');
      } else {
        setError('Your session has expired or is invalid. Please login again to continue.');
        setLoadingMessage('Session expired. Redirecting...');
      }

      setLoading(true);
      setTimeout(redirectLogin, 5000);
      return;
    }

    const initiate2FA = async () => {
      try {
        const { qr_code_data_url, secret, message } = await authService.setupTwoFactorAuth({
          user_id: currentUser.user_id,
        });

        setQrCodeUrl(qr_code_data_url);
        setSecret(secret);
        setSuccess(message || '2FA setup successful');
        setSetUpComplete(true);

        sessionStorage.removeItem('2fa_setup_user');
        sessionStorage.removeItem('2fa_setup_token');
        sessionStorage.removeItem('2fa_verify_url');
        Cookies.remove('2fa_verify_url');

        setTimeout(redirectLogin, 7000);
      } catch (err: any) {
        const msg: string =
          err?.response?.data?.message ||
          err?.message ||
          'Failed to initiate 2FA setup. Redirecting...';

        const isEnabled = msg.toLowerCase().includes('enabled');
        const isInitiated = msg.toLowerCase().includes('initiation');

        setAlreadyEnabled(isEnabled);
        setSetUpComplete(isInitiated);

        if (isEnabled) {
          setSuccess(msg);
          setTimeout(redirectDashboard, 2000);
        } else {
          setError(msg);
          setTimeout(redirectLogin, 5000);
        }
      } finally {
        setLoading(false);
      }
    };

    initiate2FA();
  }, [authLoading, user, access_token]);

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Loader message={loadingMessage} />
        </div>
      </div>
    );
  }

  const getPageTitle = () => {
    if (setUpComplete && !alreadyEnabled) {
      return '✅ 2FA Setup Complete!';
    }
    if (alreadyEnabled) {
      return '✅ 2FA Already Enabled';
    }
    return '🔐 Secure Your Account';
  };

  const getPageSubtitle = () => {
    if (setUpComplete && !alreadyEnabled) {
      return 'Your account is now protected with two-factor authentication';
    }
    if (alreadyEnabled) {
      return 'Your account is already secured with two-factor authentication';
    }
    return 'Enable two-factor authentication to protect your account with an extra layer of security';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50 dark:from-gray-900 dark:via-gray-800 dark:to-red-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>

      <div className="relative flex flex-col justify-center py-12 sm:px-6 lg:px-8 min-h-screen">
        {/* Header Section */}
        <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
          <div className="flex justify-center">
            <div className="relative">
              <Image
                src="/images/macra-logo.png"
                alt="MACRA Logo"
                width={50}
                height={50}
                className="mx-auto h-16 w-auto drop-shadow-lg animate-fadeLoop"
              />
              <div className="absolute -inset-2 rounded-full opacity-20 blur-lg animate-pulse"></div>
            </div>
          </div>

          <div className="mt-6">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium mb-3 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 animate-slideInFromTop animate-delay-100">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              Staff Portal Security
            </div>

            <h2 className="text-3xl font-extrabold text-gray-900 dark:text-white animate-slideInFromTop animate-delay-200">
              {getPageTitle()}
            </h2>

            <p className="mt-3 text-sm text-gray-600 dark:text-gray-400 animate-slideInFromTop animate-delay-300">
              {getPageSubtitle()}
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm py-8 px-6 shadow-2xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 animate-slideInFromBottom animate-delay-400">
            {/* Error State */}
            {error && !alreadyEnabled && (
              <div className="text-center animate-fadeIn">
                <div className="w-16 h-16 mb-4 rounded-full bg-gradient-to-r from-red-100 to-rose-100 dark:from-red-900/50 dark:to-rose-900/50 flex items-center justify-center mx-auto shadow-lg">
                  <XCircleIcon className="w-10 h-10 text-red-600 dark:text-red-400 animate-pulse" />
                </div>
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                    Setup Failed
                  </h3>
                  <div className="bg-gradient-to-r from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-xl text-sm">
                    {error}
                  </div>
                </div>
                {unauthorizedAccess && (
                  <button
                    onClick={redirectLogin}
                    className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl shadow-lg text-sm font-medium text-white bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200 transform hover:scale-105"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    Return to Login
                  </button>
                )}
              </div>
            )}

            {/* Success State */}
            {(success || alreadyEnabled || setUpComplete) && (
              <div className="flex flex-col items-center justify-center text-center text-gray-600 dark:text-gray-400">
                <div className="w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md">
                  <svg
                    className="w-8 h-8 text-green-600 dark:text-green-300"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="3"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  {success}
                  {alreadyEnabled && (
                    <p className="text-sm text-gray-400 dark:text-gray-200 p-2">
                      Two-Factor Authentication is already enabled. Please contact support if you need to reset it.
                    </p>
                  )}
                  {setUpComplete && (
                    <p className="text-sm text-gray-400 dark:text-gray-200 p-2">
                      This link is valid for 5 minutes and can only be used once.
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* QR Code Display */}
            {qrCodeUrl && !alreadyEnabled && (
              <div className="mt-6 text-center animate-fadeIn">
                <div className="bg-white dark:bg-gray-700 shadow-lg p-6 rounded-lg border border-gray-200 dark:border-gray-600 inline-block">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Scan QR Code</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Use your authenticator app</p>
                  </div>
                  <Image
                    src={qrCodeUrl}
                    alt="2FA QR Code"
                    width={200}
                    height={200}
                    className="rounded-lg"
                  />
                </div>

                {secret && (
                  <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <p className="font-medium text-gray-900 dark:text-white mb-2">Manual Setup Key:</p>
                    <p className="font-mono text-sm break-all text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-900 p-3 rounded border">
                      {secret}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                      Enter this key manually if you can't scan the QR code
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Button for OTP verification redirect to verify-2fa after setup complete */}
            {setUpComplete && (
              <div className="mt-6">
                <button
                  onClick={() => router.replace(`/auth/verify-2fa?i=${encodeURIComponent(userId)}&unique=${encodeURIComponent(secret)}&c=`)}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Verify OTP
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
