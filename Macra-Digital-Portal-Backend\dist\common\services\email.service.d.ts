import { MailerService } from '@nestjs-modules/mailer';
export interface EmailContext {
    [key: string]: any;
}
export interface EmailAttachment {
    filename: string;
    path: string;
    cid: string;
}
export declare class EmailService {
    private readonly mailerService;
    private readonly logger;
    constructor(mailerService: MailerService);
    sendEmail(to: string, template: string, subject: string, context: EmailContext, additionalAttachments?: EmailAttachment[]): Promise<void>;
    send2FAEmail(to: string, template: string, subject: string, context: {
        name: string;
        message: string;
        verifyUrl: string;
        otp: string;
    }): Promise<void>;
    sendLoginAlertEmail(to: string, subject: string, context: {
        userName: string;
        loginUrl: string;
        ip: string;
        country: string;
        city: string;
        userAgent: string;
        message: string;
    }): Promise<void>;
    sendPasswordResetEmail(to: string, context: {
        userName: string;
        loginUrl: string;
    }): Promise<void>;
    sendVerifyEmail(to: string, context: {
        userName: string;
        verifyUrl: string;
    }): Promise<void>;
    static createVerificationUrl(userId: string, secret: string, token: string, action: string, roles: Array<{
        name: string;
    }> | undefined): string;
}
