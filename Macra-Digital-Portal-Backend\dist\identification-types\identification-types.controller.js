"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentificationTypesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const identification_types_service_1 = require("./identification-types.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_identification_type_dto_1 = require("../dto/identification-types/create-identification-type.dto");
const update_identification_type_dto_1 = require("../dto/identification-types/update-identification-type.dto");
const nestjs_paginate_1 = require("nestjs-paginate");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let IdentificationTypesController = class IdentificationTypesController {
    constructor(identificationTypesService) {
        this.identificationTypesService = identificationTypesService;
    }
    async findAll(query) {
        return this.identificationTypesService.findAll(query);
    }
    async findAllSimple() {
        return this.identificationTypesService.findAllSimple();
    }
    async findOne(id) {
        return this.identificationTypesService.findOne(id);
    }
    async create(createIdentificationTypeDto, req) {
        return this.identificationTypesService.create(createIdentificationTypeDto, req.user.userId);
    }
    async update(id, updateIdentificationTypeDto, req) {
        return this.identificationTypesService.update(id, updateIdentificationTypeDto, req.user.userId);
    }
    async remove(id) {
        await this.identificationTypesService.remove(id);
        return { message: 'Identification type deleted successfully' };
    }
};
exports.IdentificationTypesController = IdentificationTypesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all identification types' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of identification types retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'IdentificationType',
        description: 'Viewed identification types list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IdentificationTypesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('simple'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all identification types (simple list)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Simple list of identification types retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IdentificationTypesController.prototype, "findAllSimple", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get identification type by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Identification type UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Identification type retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Identification type not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'IdentificationType',
        description: 'Viewed identification type details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], IdentificationTypesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new identification type' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Identification type created successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Identification type with this name already exists',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'IdentificationType',
        description: 'Created new identification type',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_identification_type_dto_1.CreateIdentificationTypeDto, Object]),
    __metadata("design:returntype", Promise)
], IdentificationTypesController.prototype, "create", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update identification type' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Identification type UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Identification type updated successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Identification type not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Identification type with this name already exists',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'IdentificationType',
        description: 'Updated identification type',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_identification_type_dto_1.UpdateIdentificationTypeDto, Object]),
    __metadata("design:returntype", Promise)
], IdentificationTypesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete identification type' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Identification type UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Identification type deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Identification type not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Cannot delete identification type that is being used by users',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.USER_MANAGEMENT,
        resourceType: 'IdentificationType',
        description: 'Deleted identification type',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], IdentificationTypesController.prototype, "remove", null);
exports.IdentificationTypesController = IdentificationTypesController = __decorate([
    (0, swagger_1.ApiTags)('Identification Types'),
    (0, common_1.Controller)('identification-types'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [identification_types_service_1.IdentificationTypesService])
], IdentificationTypesController);
//# sourceMappingURL=identification-types.controller.js.map