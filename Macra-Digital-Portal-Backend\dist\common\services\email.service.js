"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmailService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const mailer_1 = require("@nestjs-modules/mailer");
const path_1 = require("path");
const app_module_1 = require("../../app.module");
const auth_constants_1 = require("../constants/auth.constants");
let EmailService = EmailService_1 = class EmailService {
    constructor(mailerService) {
        this.mailerService = mailerService;
        this.logger = new common_1.Logger(EmailService_1.name);
    }
    async sendEmail(to, template, subject, context, additionalAttachments = []) {
        try {
            if (!process.env.EMAIL_USER || !process.env.EMAIL_PWD) {
                this.logger.warn(`Email service not configured. Skipping email to ${to} with template ${template}`);
                return;
            }
            const attachments = [
                {
                    filename: auth_constants_1.AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME,
                    path: (0, path_1.join)(app_module_1.assetsDir, auth_constants_1.AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME),
                    cid: auth_constants_1.AuthConstants.EMAIL_ATTACHMENT.LOGO_CID,
                },
                ...additionalAttachments,
            ];
            await this.mailerService.sendMail({
                to,
                subject,
                template,
                context: {
                    ...context,
                    year: auth_constants_1.AuthUtils.getCurrentYear(),
                },
                attachments,
            });
            this.logger.log(`Email sent successfully to ${to} with template ${template}`);
        }
        catch (error) {
            this.logger.error(`Failed to send email to ${to} with template ${template}:`, error);
            this.logger.error('Email error details:', error.stack);
            this.logger.warn('Email sending failed, but continuing with the process');
        }
    }
    async send2FAEmail(to, template, subject, context) {
        return this.sendEmail(to, template, subject, context);
    }
    async sendLoginAlertEmail(to, subject, context) {
        return this.sendEmail(to, 'login-alert', subject, context);
    }
    async sendPasswordResetEmail(to, context) {
        return this.sendEmail(to, 'reset', 'Password Reset - MACRA Digital Portal', context);
    }
    async sendVerifyEmail(to, context) {
        return this.sendEmail(to, '2fa', 'Verify Email - MACRA Digital Portal', context);
    }
    static createVerificationUrl(userId, secret, token, action, roles) {
        const urlPrefix = auth_constants_1.AuthUtils.getUrlPrefix(roles);
        const urlRedirect = auth_constants_1.AuthUtils.getRedirectUrl(action);
        console.log('URL utils', { urlPrefix: urlPrefix, urlRedirect: urlRedirect });
        return `${process.env.FRONTEND_URL}/${urlPrefix}/${urlRedirect}?i=${encodeURIComponent(userId)}&unique=${encodeURIComponent(secret)}&c=${encodeURIComponent(token)}`;
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = EmailService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [mailer_1.MailerService])
], EmailService);
//# sourceMappingURL=email.service.js.map