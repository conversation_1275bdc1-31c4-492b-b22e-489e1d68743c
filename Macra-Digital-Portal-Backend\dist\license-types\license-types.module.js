"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseTypesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const license_types_controller_1 = require("./license-types.controller");
const license_type_by_code_controller_1 = require("./license-type-by-code.controller");
const license_types_service_1 = require("./license-types.service");
const license_types_entity_1 = require("../entities/license-types.entity");
let LicenseTypesModule = class LicenseTypesModule {
};
exports.LicenseTypesModule = LicenseTypesModule;
exports.LicenseTypesModule = LicenseTypesModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([license_types_entity_1.LicenseTypes])],
        controllers: [license_types_controller_1.LicenseTypesController, license_type_by_code_controller_1.LicenseTypeByCodeController],
        providers: [license_types_service_1.LicenseTypesService],
        exports: [license_types_service_1.LicenseTypesService],
    })
], LicenseTypesModule);
//# sourceMappingURL=license-types.module.js.map