"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const login_dto_1 = require("../dto/auth/login.dto");
const register_dto_1 = require("../dto/auth/register.dto");
const forgot_password_dto_1 = require("../dto/auth/forgot-password.dto");
const two_factor_dto_1 = require("../dto/auth/two-factor.dto");
const throttler_1 = require("@nestjs/throttler");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async login(loginDto, req) {
        return this.authService.login(loginDto, req);
    }
    async register(registerDto) {
        return this.authService.register(registerDto);
    }
    async forgotPassword(forgotPasswordDto) {
        return this.authService.forgotPassword(forgotPasswordDto);
    }
    async resetPassword(resetPasswordDto) {
        return this.authService.resetPassword(resetPasswordDto);
    }
    async verifyEmailGet(userId, unique, code, req) {
        const twoFactorDto = {
            user_id: userId,
            unique: unique,
            code: code
        };
        return this.authService.verifyTwoFactorCode(twoFactorDto, req);
    }
    async resendVerification(email) {
        return this.authService.resendVerificationEmail(email);
    }
    async setupTwoFactorAuth(setup2FA) {
        return this.authService.setupTwoFactorAuth(setup2FA);
    }
    async generateTwoFactorCode(body) {
        return this.authService.generateTwoFactorCode(body.user_id, body.action);
    }
    async verifyTwoFactorCode(twoFactorDto, req) {
        console.log('AuthController: Received 2FA verification request:', {
            user_id: twoFactorDto.user_id,
            unique: twoFactorDto.unique,
            code: twoFactorDto.code
        });
        return this.authService.verifyTwoFactorCode(twoFactorDto, req);
    }
    async refresh(req) {
        const user = await this.authService.validateJwtPayload({
            email: req.user.email,
            sub: req.user.userId,
            roles: req.user.roles,
        });
        const payload = {
            email: user?.email,
            sub: user?.user_id,
            roles: user?.roles?.map(role => role.name) || [],
        };
        return {
            access_token: this.authService['jwtService'].sign(payload),
            user: {
                user_id: user?.user_id,
                email: user?.email,
                first_name: user?.first_name,
                last_name: user?.last_name,
                roles: user?.roles?.map(role => role.name) || [],
            },
        };
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'User login' }),
    (0, swagger_1.ApiBody)({ type: login_dto_1.LoginDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Login successful',
        schema: {
            example: {
                access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                user: {
                    user_id: 'uuid',
                    email: '<EMAIL>',
                    first_name: 'John',
                    last_name: 'Doe',
                    roles: ['customer']
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid credentials' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.LoginDto, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: 'User registration - requires email verification' }),
    (0, swagger_1.ApiBody)({ type: register_dto_1.RegisterDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Registration successful - email verification required',
        schema: {
            example: {
                access_token: '',
                user: {
                    user_id: 'uuid',
                    email: '<EMAIL>',
                    first_name: 'John',
                    last_name: 'Doe',
                    roles: ['customer'],
                    two_factor_enabled: false
                },
                requiresTwoFactor: false,
                message: 'Registration successful! Please check your email to verify your account.'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'User already exists' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [register_dto_1.RegisterDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.Post)('forgot-password'),
    (0, throttler_1.Throttle)({ default: { limit: 5, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({ summary: 'Request password reset' }),
    (0, swagger_1.ApiBody)({ type: forgot_password_dto_1.ForgotPasswordDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Password reset email sent',
        schema: {
            example: {
                message: 'If the email exists, a password reset link has been sent.'
            }
        }
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [forgot_password_dto_1.ForgotPasswordDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "forgotPassword", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [forgot_password_dto_1.ResetPasswordDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Get)('verify-email'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Verify email via GET request with query parameters' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Email verification successful',
        schema: {
            example: {
                access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                user: {
                    user_id: 'uuid',
                    email: '<EMAIL>',
                    first_name: 'John',
                    last_name: 'Doe',
                    roles: ['customer'],
                    two_factor_enabled: true
                },
                message: 'Email verified successfully! Your account is now active.'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid or expired verification link' }),
    __param(0, (0, common_1.Query)('i')),
    __param(1, (0, common_1.Query)('unique')),
    __param(2, (0, common_1.Query)('c')),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verifyEmailGet", null);
__decorate([
    (0, common_1.Post)('resend-verification'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Resend email verification code' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                email: {
                    type: 'string',
                    format: 'email',
                    example: '<EMAIL>'
                }
            },
            required: ['email']
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Verification email sent',
        schema: {
            example: {
                message: 'If the email exists and is not verified, a verification email has been sent.'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, throttler_1.Throttle)({ default: { limit: 3, ttl: 60000 } }),
    __param(0, (0, common_1.Body)('email')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "resendVerification", null);
__decorate([
    (0, common_1.Post)('setup-2fa'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [two_factor_dto_1.RequestTwoFactorDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "setupTwoFactorAuth", null);
__decorate([
    (0, common_1.Post)('generate-2fa'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Generate 2FA code for login verification' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                user_id: {
                    type: 'string',
                    example: 'user-uuid-here'
                },
                action: {
                    type: 'string',
                    enum: ['login', 'reset', 'verify', 'register'],
                    example: 'login'
                }
            },
            required: ['user_id', 'action']
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '2FA code generated and sent',
        schema: {
            example: {
                message: 'Two-factor authentication code has been sent to your email.'
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, throttler_1.Throttle)({ default: { limit: 5, ttl: 60000 } }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "generateTwoFactorCode", null);
__decorate([
    (0, common_1.Post)('verify-2fa'),
    (0, common_1.HttpCode)(200),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [two_factor_dto_1.TwoFactorDto, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verifyTwoFactorCode", null);
__decorate([
    (0, common_1.Post)('refresh'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refresh", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('Auth'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map